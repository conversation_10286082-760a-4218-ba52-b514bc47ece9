"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Calendar,
  Clock,
  MapPin,
  User,
  BookOpen,
  Download,
  Printer,
  ChevronLeft,
  ChevronRight,
  Filter,
  Bell,
  Star,
} from "lucide-react";

export default function StudentTimetable() {
  const [user, setUser] = useState<any>(null);
  const [selectedWeek, setSelectedWeek] = useState(0); // 0 for current week
  const [viewMode, setViewMode] = useState("week"); // week or day
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "student") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock timetable data
  const timeSlots = [
    "08:00 - 09:00",
    "09:00 - 10:00",
    "10:00 - 11:00",
    "11:30 - 12:30", // Break from 11:00-11:30
    "12:30 - 13:30",
    "14:30 - 15:30", // Lunch break from 13:30-14:30
    "15:30 - 16:30",
    "16:30 - 17:30",
  ];

  const weeklySchedule = {
    Monday: [
      { subject: "Mathematics", teacher: "Dr. Emily Wilson", room: "Room 101", type: "Theory" },
      { subject: "Physics", teacher: "Mr. David Brown", room: "Room 201", type: "Theory" },
      { subject: "Chemistry", teacher: "Dr. Sarah Johnson", room: "Room 301", type: "Theory" },
      { subject: "BREAK", teacher: "", room: "", type: "Break" },
      { subject: "English", teacher: "Ms. Lisa Anderson", room: "Room 102", type: "Theory" },
      { subject: "LUNCH", teacher: "", room: "", type: "Break" },
      { subject: "Computer Science", teacher: "Prof. Michael Chen", room: "Lab 1", type: "Lab" },
      { subject: "Study Hall", teacher: "Self Study", room: "Library", type: "Study" },
    ],
    Tuesday: [
      { subject: "Physics", teacher: "Mr. David Brown", room: "Room 201", type: "Theory" },
      { subject: "Mathematics", teacher: "Dr. Emily Wilson", room: "Room 101", type: "Theory" },
      { subject: "Biology", teacher: "Dr. Jennifer Lee", room: "Room 401", type: "Theory" },
      { subject: "BREAK", teacher: "", room: "", type: "Break" },
      { subject: "History", teacher: "Prof. Robert Taylor", room: "Room 105", type: "Theory" },
      { subject: "LUNCH", teacher: "", room: "", type: "Break" },
      { subject: "Physics Lab", teacher: "Mr. David Brown", room: "Lab 2", type: "Lab" },
      { subject: "Physical Education", teacher: "Coach Williams", room: "Gymnasium", type: "PE" },
    ],
    Wednesday: [
      { subject: "Mathematics", teacher: "Dr. Emily Wilson", room: "Room 101", type: "Theory" },
      { subject: "Chemistry", teacher: "Dr. Sarah Johnson", room: "Room 301", type: "Theory" },
      { subject: "English", teacher: "Ms. Lisa Anderson", room: "Room 102", type: "Theory" },
      { subject: "BREAK", teacher: "", room: "", type: "Break" },
      { subject: "Physics", teacher: "Mr. David Brown", room: "Room 201", type: "Theory" },
      { subject: "LUNCH", teacher: "", room: "", type: "Break" },
      { subject: "Chemistry Lab", teacher: "Dr. Sarah Johnson", room: "Lab 3", type: "Lab" },
      { subject: "Art", teacher: "Ms. Patricia Moore", room: "Art Room", type: "Creative" },
    ],
    Thursday: [
      { subject: "Chemistry", teacher: "Dr. Sarah Johnson", room: "Room 301", type: "Theory" },
      { subject: "English", teacher: "Ms. Lisa Anderson", room: "Room 102", type: "Theory" },
      { subject: "Mathematics", teacher: "Dr. Emily Wilson", room: "Room 101", type: "Theory" },
      { subject: "BREAK", teacher: "", room: "", type: "Break" },
      { subject: "Geography", teacher: "Mr. James Clark", room: "Room 106", type: "Theory" },
      { subject: "LUNCH", teacher: "", room: "", type: "Break" },
      { subject: "Biology Lab", teacher: "Dr. Jennifer Lee", room: "Lab 4", type: "Lab" },
      { subject: "Music", teacher: "Ms. Amanda White", room: "Music Room", type: "Creative" },
    ],
    Friday: [
      { subject: "Biology", teacher: "Dr. Jennifer Lee", room: "Room 401", type: "Theory" },
      { subject: "Physics", teacher: "Mr. David Brown", room: "Room 201", type: "Theory" },
      { subject: "Computer Science", teacher: "Prof. Michael Chen", room: "Lab 1", type: "Theory" },
      { subject: "BREAK", teacher: "", room: "", type: "Break" },
      { subject: "Mathematics", teacher: "Dr. Emily Wilson", room: "Room 101", type: "Theory" },
      { subject: "LUNCH", teacher: "", room: "", type: "Break" },
      { subject: "English", teacher: "Ms. Lisa Anderson", room: "Room 102", type: "Theory" },
      { subject: "Assembly", teacher: "All Staff", room: "Main Hall", type: "Assembly" },
    ],
  };

  const todaySchedule = weeklySchedule.Monday; // For demo, showing Monday as today

  const getSubjectColor = (type: string) => {
    switch (type) {
      case "Theory": return "bg-blue-100 text-blue-700 border-blue-200";
      case "Lab": return "bg-purple-100 text-purple-700 border-purple-200";
      case "PE": return "bg-green-100 text-green-700 border-green-200";
      case "Creative": return "bg-orange-100 text-orange-700 border-orange-200";
      case "Study": return "bg-gray-100 text-gray-700 border-gray-200";
      case "Assembly": return "bg-yellow-100 text-yellow-700 border-yellow-200";
      case "Break": return "bg-red-100 text-red-700 border-red-200";
      default: return "bg-gray-100 text-gray-700 border-gray-200";
    }
  };

  const getCurrentTimeSlot = () => {
    const now = new Date();
    const currentHour = now.getHours();
    const currentMinute = now.getMinutes();
    const currentTime = currentHour * 60 + currentMinute;

    const timeRanges = [
      { start: 8 * 60, end: 9 * 60 }, // 08:00 - 09:00
      { start: 9 * 60, end: 10 * 60 }, // 09:00 - 10:00
      { start: 10 * 60, end: 11 * 60 }, // 10:00 - 11:00
      { start: 11 * 60 + 30, end: 12 * 60 + 30 }, // 11:30 - 12:30
      { start: 12 * 60 + 30, end: 13 * 60 + 30 }, // 12:30 - 13:30
      { start: 14 * 60 + 30, end: 15 * 60 + 30 }, // 14:30 - 15:30
      { start: 15 * 60 + 30, end: 16 * 60 + 30 }, // 15:30 - 16:30
      { start: 16 * 60 + 30, end: 17 * 60 + 30 }, // 16:30 - 17:30
    ];

    for (let i = 0; i < timeRanges.length; i++) {
      if (currentTime >= timeRanges[i].start && currentTime <= timeRanges[i].end) {
        return i;
      }
    }
    return -1;
  };

  const currentTimeSlot = getCurrentTimeSlot();

  const upcomingClasses = todaySchedule
    .slice(currentTimeSlot + 1)
    .filter(cls => cls.type !== "Break")
    .slice(0, 3);

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">My Timetable</h1>
            <p className="text-gray-600">View your class schedule and upcoming sessions</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Bell className="h-4 w-4 mr-2" />
              Notifications
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button variant="outline">
              <Printer className="h-4 w-4 mr-2" />
              Print
            </Button>
          </div>
        </div>

        {/* Current Class & Upcoming */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Current Class */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Clock className="h-5 w-5 mr-2 text-green-600" />
                Current Class
              </CardTitle>
            </CardHeader>
            <CardContent>
              {currentTimeSlot >= 0 && currentTimeSlot < todaySchedule.length ? (
                <div className="space-y-3">
                  <div className="text-lg font-semibold text-gray-900">
                    {todaySchedule[currentTimeSlot].subject}
                  </div>
                  <div className="space-y-2 text-sm text-gray-600">
                    <div className="flex items-center">
                      <User className="h-4 w-4 mr-2" />
                      {todaySchedule[currentTimeSlot].teacher}
                    </div>
                    <div className="flex items-center">
                      <MapPin className="h-4 w-4 mr-2" />
                      {todaySchedule[currentTimeSlot].room}
                    </div>
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-2" />
                      {timeSlots[currentTimeSlot]}
                    </div>
                  </div>
                  <Badge className={getSubjectColor(todaySchedule[currentTimeSlot].type)}>
                    {todaySchedule[currentTimeSlot].type}
                  </Badge>
                </div>
              ) : (
                <div className="text-center py-4 text-gray-500">
                  <Clock className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                  <p>No class in session</p>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Upcoming Classes */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="h-5 w-5 mr-2 text-blue-600" />
                Upcoming Classes Today
              </CardTitle>
            </CardHeader>
            <CardContent>
              {upcomingClasses.length > 0 ? (
                <div className="space-y-3">
                  {upcomingClasses.map((cls, index) => {
                    const slotIndex = todaySchedule.indexOf(cls);
                    return (
                      <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div className="flex items-center space-x-4">
                          <div className="text-sm font-medium text-gray-600">
                            {timeSlots[slotIndex]}
                          </div>
                          <div>
                            <div className="font-medium text-gray-900">{cls.subject}</div>
                            <div className="text-sm text-gray-500">{cls.teacher} • {cls.room}</div>
                          </div>
                        </div>
                        <Badge className={getSubjectColor(cls.type)}>
                          {cls.type}
                        </Badge>
                      </div>
                    );
                  })}
                </div>
              ) : (
                <div className="text-center py-4 text-gray-500">
                  <Calendar className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                  <p>No more classes today</p>
                </div>
              )}
            </CardContent>
          </Card>
        </div>

        {/* Weekly Timetable */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center">
                <Calendar className="h-5 w-5 mr-2" />
                Weekly Schedule
              </CardTitle>
              <div className="flex items-center space-x-2">
                <Button variant="outline" size="sm">
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <span className="text-sm font-medium">Current Week</span>
                <Button variant="outline" size="sm">
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr>
                    <th className="border border-gray-200 p-3 bg-gray-50 text-left font-medium text-gray-900">
                      Time
                    </th>
                    {Object.keys(weeklySchedule).map((day) => (
                      <th key={day} className="border border-gray-200 p-3 bg-gray-50 text-center font-medium text-gray-900">
                        {day}
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {timeSlots.map((timeSlot, timeIndex) => (
                    <tr key={timeIndex} className={currentTimeSlot === timeIndex ? "bg-blue-50" : ""}>
                      <td className="border border-gray-200 p-3 font-medium text-gray-700 bg-gray-50">
                        {timeSlot}
                      </td>
                      {Object.entries(weeklySchedule).map(([day, schedule]) => {
                        const classInfo = schedule[timeIndex];
                        return (
                          <td key={day} className="border border-gray-200 p-2">
                            {classInfo && classInfo.subject !== "BREAK" && classInfo.subject !== "LUNCH" ? (
                              <div className={`p-2 rounded border ${getSubjectColor(classInfo.type)}`}>
                                <div className="font-medium text-sm">{classInfo.subject}</div>
                                <div className="text-xs opacity-75">{classInfo.teacher}</div>
                                <div className="text-xs opacity-75">{classInfo.room}</div>
                              </div>
                            ) : classInfo && (classInfo.subject === "BREAK" || classInfo.subject === "LUNCH") ? (
                              <div className="text-center text-sm text-gray-500 font-medium">
                                {classInfo.subject}
                              </div>
                            ) : (
                              <div className="h-16"></div>
                            )}
                          </td>
                        );
                      })}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Quick Stats */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <BookOpen className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">35</div>
                  <p className="text-sm text-gray-600">Classes/Week</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <User className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">8</div>
                  <p className="text-sm text-gray-600">Teachers</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <MapPin className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">12</div>
                  <p className="text-sm text-gray-600">Rooms</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">7</div>
                  <p className="text-sm text-gray-600">Hours/Day</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  );
}
