"use client";

import { useState } from "react";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Bus, 
  Search, 
  Plus, 
  Filter,
  MapPin,
  Clock,
  Users,
  Wrench,
  CheckCircle,
  AlertTriangle,
  Route
} from "lucide-react";

// Mock transport data
const mockRoutes = [
  {
    id: "1",
    routeName: "Route A - City Center",
    startPoint: "School Campus",
    endPoint: "City Center",
    stops: ["Main Gate", "Shopping Mall", "Bus Station", "City Center"],
    distance: 15.5,
    estimatedTime: "45 minutes",
    fee: 8000,
    status: "active",
    studentsCount: 45,
  },
  {
    id: "2",
    routeName: "Route B - Residential Area",
    startPoint: "School Campus",
    endPoint: "Green Valley",
    stops: ["Main Gate", "Park Avenue", "Hospital", "Green Valley"],
    distance: 12.0,
    estimatedTime: "35 minutes",
    fee: 6000,
    status: "active",
    studentsCount: 32,
  },
];

const mockVehicles = [
  {
    id: "1",
    vehicleNumber: "SCH-001",
    vehicleType: "bus",
    capacity: 50,
    driverName: "Rajesh Kumar",
    driverPhone: "9876543210",
    routeId: "1",
    routeName: "Route A - City Center",
    status: "active",
    lastMaintenance: "2024-01-01",
  },
  {
    id: "2",
    vehicleNumber: "SCH-002",
    vehicleType: "van",
    capacity: 15,
    driverName: "Suresh Patel",
    driverPhone: "9876543211",
    routeId: "2",
    routeName: "Route B - Residential Area",
    status: "maintenance",
    lastMaintenance: "2024-01-10",
  },
];

const statusColors = {
  active: "text-green-600 bg-green-100",
  maintenance: "text-yellow-600 bg-yellow-100",
  inactive: "text-red-600 bg-red-100",
};

const statusIcons = {
  active: CheckCircle,
  maintenance: Wrench,
  inactive: AlertTriangle,
};

export default function TransportPage() {
  const [search, setSearch] = useState("");
  const [activeTab, setActiveTab] = useState("routes");

  // Calculate transport statistics
  const totalVehicles = mockVehicles.length;
  const activeVehicles = mockVehicles.filter(v => v.status === "active").length;
  const maintenanceVehicles = mockVehicles.filter(v => v.status === "maintenance").length;
  const totalCapacity = mockVehicles.reduce((sum, v) => sum + v.capacity, 0);
  const totalStudents = mockRoutes.reduce((sum, r) => sum + r.studentsCount, 0);

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Transport Management</h1>
            <p className="text-gray-600">Manage vehicles, routes, and transportation</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Route className="h-4 w-4 mr-2" />
              Reports
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Vehicle
            </Button>
          </div>
        </div>

        {/* Transport Stats */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Bus className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {totalVehicles}
                  </div>
                  <p className="text-sm text-gray-600">Total Vehicles</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {activeVehicles}
                  </div>
                  <p className="text-sm text-gray-600">Active</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {totalCapacity}
                  </div>
                  <p className="text-sm text-gray-600">Total Capacity</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Route className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    {mockRoutes.length}
                  </div>
                  <p className="text-sm text-gray-600">Active Routes</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab("routes")}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === "routes"
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              Routes
            </button>
            <button
              onClick={() => setActiveTab("vehicles")}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === "vehicles"
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              Vehicles
            </button>
          </nav>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={`Search ${activeTab}...`}
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex gap-2">
                <select className="px-3 py-2 border border-gray-300 rounded-md text-sm bg-white">
                  <option value="">All Status</option>
                  <option value="active">Active</option>
                  <option value="maintenance">Maintenance</option>
                  <option value="inactive">Inactive</option>
                </select>
                <Button variant="outline" size="sm">
                  <Filter className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Routes Tab */}
        {activeTab === "routes" && (
          <Card>
            <CardHeader>
              <CardTitle>Transport Routes</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockRoutes.map((route) => (
                  <div
                    key={route.id}
                    className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <Route className="h-6 w-6 text-blue-600" />
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">
                          {route.routeName}
                        </div>
                        <div className="text-sm text-gray-500">
                          {route.startPoint} → {route.endPoint}
                        </div>
                        <div className="text-xs text-gray-400 flex items-center mt-1">
                          <MapPin className="h-3 w-3 mr-1" />
                          {route.distance} km • 
                          <Clock className="h-3 w-3 ml-2 mr-1" />
                          {route.estimatedTime}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center space-x-4">
                        <div className="text-sm">
                          <div className="font-medium text-green-600">
                            ₹{route.fee.toLocaleString()}/year
                          </div>
                          <div className="text-gray-500">
                            {route.studentsCount} students
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm">
                            View
                          </Button>
                          <Button variant="outline" size="sm">
                            Edit
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Vehicles Tab */}
        {activeTab === "vehicles" && (
          <Card>
            <CardHeader>
              <CardTitle>Fleet Management</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockVehicles.map((vehicle) => {
                  const StatusIcon = statusIcons[vehicle.status as keyof typeof statusIcons];
                  
                  return (
                    <div
                      key={vehicle.id}
                      className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-center space-x-4">
                        <div className={`p-2 rounded-full ${statusColors[vehicle.status as keyof typeof statusColors]}`}>
                          <StatusIcon className="h-4 w-4" />
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">
                            {vehicle.vehicleNumber}
                          </div>
                          <div className="text-sm text-gray-500">
                            {vehicle.vehicleType.toUpperCase()} • Capacity: {vehicle.capacity}
                          </div>
                          <div className="text-xs text-gray-400">
                            Driver: {vehicle.driverName} • {vehicle.driverPhone}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="flex items-center space-x-4">
                          <div className="text-sm">
                            <div className="font-medium text-gray-900">
                              {vehicle.routeName}
                            </div>
                            <div className="text-gray-500">
                              Last service: {vehicle.lastMaintenance}
                            </div>
                          </div>
                          <div className="flex gap-2">
                            {vehicle.status === "maintenance" ? (
                              <Button variant="outline" size="sm" className="text-green-600">
                                Mark Active
                              </Button>
                            ) : (
                              <Button variant="outline" size="sm" className="text-yellow-600">
                                Maintenance
                              </Button>
                            )}
                            <Button variant="outline" size="sm">
                              Edit
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Transport Analytics */}
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Route Utilization</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockRoutes.map((route) => {
                  const utilizationRate = (route.studentsCount / 50) * 100; // Assuming max 50 students per route
                  
                  return (
                    <div key={route.id}>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">{route.routeName}</span>
                        <span className="text-sm text-gray-500">
                          {route.studentsCount}/50 students
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: `${Math.min(utilizationRate, 100)}%` }}
                        />
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Fleet Status</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Active Vehicles</span>
                  <span className="font-medium text-green-600">{activeVehicles}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-green-600 h-2 rounded-full"
                    style={{ width: `${(activeVehicles / totalVehicles) * 100}%` }}
                  />
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm">Under Maintenance</span>
                  <span className="font-medium text-yellow-600">{maintenanceVehicles}</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div
                    className="bg-yellow-600 h-2 rounded-full"
                    style={{ width: `${(maintenanceVehicles / totalVehicles) * 100}%` }}
                  />
                </div>
                
                <div className="pt-4 border-t">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      {Math.round((totalStudents / totalCapacity) * 100)}%
                    </div>
                    <div className="text-sm text-gray-500">Overall Utilization</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  );
}
