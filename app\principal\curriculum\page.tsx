"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  ArrowLeft,
  Plus,
  Search,
  Filter,
  Download,
  RefreshCw,
  Eye,
  Edit,
  MoreHorizontal,
  BookOpen,
  CheckCircle,
  Clock
} from "lucide-react";
import { toast } from "sonner";

export default function PrincipalCurriculumPage() {
  const [user, setUser] = useState<any>(null);
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<any>({});
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    subject: "",
    grade: "",
    duration: "",
    objectives: "",
    prerequisites: ""
  });

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "principal") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
    loadData();
  }, [router]);

  const loadData = async () => {
    try {
      setLoading(true);

      // Mock curriculum data
      const mockData = {
        curriculums: [
          {
            id: 1,
            name: "Advanced Mathematics Curriculum",
            subject: "Mathematics",
            grade: "Grade 10-12",
            duration: "3 Years",
            status: "Active",
            lastUpdated: "2024-01-15"
          },
          {
            id: 2,
            name: "Science Laboratory Program",
            subject: "Science",
            grade: "Grade 6-12",
            duration: "7 Years",
            status: "Active",
            lastUpdated: "2024-01-10"
          },
          {
            id: 3,
            name: "English Literature & Communication",
            subject: "English",
            grade: "Grade 1-12",
            duration: "12 Years",
            status: "Under Review",
            lastUpdated: "2024-01-08"
          }
        ]
      };

      setData(mockData);
      toast.success("Curriculum data loaded successfully");
    } catch (error) {
      console.error("Error loading data:", error);
      toast.error("Failed to load data");
    } finally {
      setLoading(false);
    }
  };

  const handleAddCurriculum = () => {
    setFormData({
      name: "",
      description: "",
      subject: "",
      grade: "",
      duration: "",
      objectives: "",
      prerequisites: ""
    });
    setShowAddDialog(true);
  };

  const handleSubmitAdd = async () => {
    try {
      // TODO: Implement API call to add new curriculum
      console.log("Adding new curriculum:", formData);

      // Mock success
      toast.success(`New curriculum "${formData.name}" added successfully!`);
      setShowAddDialog(false);

      // Refresh data
      loadData();
    } catch (error) {
      console.error("Error adding curriculum:", error);
      toast.error("Failed to add new curriculum");
    }
  };

  if (!user) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.back()}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900"> - Curriculum</h1>
              <p className="text-gray-600">
                Principal curriculum management and oversight
              </p>
            </div>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={loadData}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
              <DialogTrigger asChild>
                <Button onClick={handleAddCurriculum}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add Curriculum
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Add New Curriculum</DialogTitle>
                  <DialogDescription>
                    Create a new curriculum for the academic program.
                  </DialogDescription>
                </DialogHeader>
                <div className="grid gap-4 py-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="name">Curriculum Name</Label>
                      <Input
                        id="name"
                        value={formData.name}
                        onChange={(e) => setFormData({...formData, name: e.target.value})}
                        placeholder="Enter curriculum name"
                      />
                    </div>
                    <div>
                      <Label htmlFor="subject">Subject</Label>
                      <Select value={formData.subject} onValueChange={(value) => setFormData({...formData, subject: value})}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select subject" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="mathematics">Mathematics</SelectItem>
                          <SelectItem value="science">Science</SelectItem>
                          <SelectItem value="english">English</SelectItem>
                          <SelectItem value="social-studies">Social Studies</SelectItem>
                          <SelectItem value="computer-science">Computer Science</SelectItem>
                          <SelectItem value="arts">Arts</SelectItem>
                          <SelectItem value="physical-education">Physical Education</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label htmlFor="grade">Grade Level</Label>
                      <Select value={formData.grade} onValueChange={(value) => setFormData({...formData, grade: value})}>
                        <SelectTrigger>
                          <SelectValue placeholder="Select grade level" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="1-5">Grade 1-5 (Primary)</SelectItem>
                          <SelectItem value="6-8">Grade 6-8 (Middle)</SelectItem>
                          <SelectItem value="9-10">Grade 9-10 (Secondary)</SelectItem>
                          <SelectItem value="11-12">Grade 11-12 (Senior Secondary)</SelectItem>
                          <SelectItem value="1-12">Grade 1-12 (All Levels)</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div>
                      <Label htmlFor="duration">Duration</Label>
                      <Input
                        id="duration"
                        value={formData.duration}
                        onChange={(e) => setFormData({...formData, duration: e.target.value})}
                        placeholder="e.g., 1 Year, 2 Semesters"
                      />
                    </div>
                  </div>
                  <div>
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      value={formData.description}
                      onChange={(e) => setFormData({...formData, description: e.target.value})}
                      placeholder="Enter curriculum description"
                      rows={3}
                    />
                  </div>
                  <div>
                    <Label htmlFor="objectives">Learning Objectives</Label>
                    <Textarea
                      id="objectives"
                      value={formData.objectives}
                      onChange={(e) => setFormData({...formData, objectives: e.target.value})}
                      placeholder="Enter learning objectives (one per line)"
                      rows={3}
                    />
                  </div>
                  <div>
                    <Label htmlFor="prerequisites">Prerequisites</Label>
                    <Input
                      id="prerequisites"
                      value={formData.prerequisites}
                      onChange={(e) => setFormData({...formData, prerequisites: e.target.value})}
                      placeholder="Enter prerequisites if any"
                    />
                  </div>
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setShowAddDialog(false)}>
                    Cancel
                  </Button>
                  <Button onClick={handleSubmitAdd} disabled={!formData.name || !formData.subject}>
                    Add Curriculum
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Content Area */}
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Curriculum Overview */}
            <div className="grid gap-4 md:grid-cols-3">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Curriculums</p>
                      <p className="text-2xl font-bold text-gray-900">{data.curriculums?.length || 0}</p>
                    </div>
                    <BookOpen className="h-8 w-8 text-blue-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Active Programs</p>
                      <p className="text-2xl font-bold text-gray-900">
                        {data.curriculums?.filter((c: any) => c.status === "Active").length || 0}
                      </p>
                    </div>
                    <CheckCircle className="h-8 w-8 text-green-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Under Review</p>
                      <p className="text-2xl font-bold text-gray-900">
                        {data.curriculums?.filter((c: any) => c.status === "Under Review").length || 0}
                      </p>
                    </div>
                    <Clock className="h-8 w-8 text-yellow-600" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Curriculum List */}
            <Card>
              <CardHeader>
                <CardTitle>Curriculum Programs</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {data.curriculums?.map((curriculum: any) => (
                    <div key={curriculum.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className="p-2 rounded-lg bg-blue-100">
                          <BookOpen className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                          <h4 className="font-medium">{curriculum.name}</h4>
                          <p className="text-sm text-gray-600">
                            {curriculum.subject} • {curriculum.grade} • {curriculum.duration}
                          </p>
                          <p className="text-xs text-gray-500">
                            Last updated: {new Date(curriculum.lastUpdated).toLocaleDateString()}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        <Badge variant={curriculum.status === "Active" ? "default" : "secondary"}>
                          {curriculum.status}
                        </Badge>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4 mr-2" />
                            View
                          </Button>
                          <Button variant="outline" size="sm">
                            <Edit className="h-4 w-4 mr-2" />
                            Edit
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>

                {(!data.curriculums || data.curriculums.length === 0) && (
                  <div className="text-center py-8">
                    <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">No Curriculums Found</h3>
                    <p className="text-gray-600 mb-4">Get started by creating your first curriculum.</p>
                    <Button onClick={handleAddCurriculum}>
                      <Plus className="h-4 w-4 mr-2" />
                      Add First Curriculum
                    </Button>
                  </div>
                )}
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </AppLayout>
  );
}