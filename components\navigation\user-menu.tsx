"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { useLogout } from "@/hooks/use-logout";
import { Button } from "@/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  User,
  Settings,
  LogOut,
  ChevronDown,
  Shield,
  GraduationCap,
  Users,
  UserCheck,
  Building,
  Bus,
  BookOpen,
  DollarSign,
  Library,
  Award,
  Home,
} from "lucide-react";
import { getNavigationForRole } from "@/lib/navigation-config";
import { toast } from "sonner";

interface UserMenuProps {
  className?: string;
}

const roleIcons: Record<string, any> = {
  super_admin: Shield,
  admin: Shield,
  teacher: GraduationCap,
  student: Award,
  parent: Home,
  admission_officer: Users,
  finance_manager: DollarSign,
  librarian: Library,
  transport_manager: Bus,
  hostel_manager: Building,
};

const roleLabels: Record<string, string> = {
  super_admin: "Super Admin",
  admin: "Administrator",
  teacher: "Teacher",
  student: "Student",
  parent: "Parent",
  admission_officer: "Admission Officer",
  finance_manager: "Finance Manager",
  librarian: "Librarian",
  transport_manager: "Transport Manager",
  hostel_manager: "Hostel Manager",
};

const roleColors: Record<string, { bg: string; text: string }> = {
  super_admin: { bg: "bg-red-500", text: "text-white" },
  admin: { bg: "bg-purple-500", text: "text-white" },
  teacher: { bg: "bg-blue-500", text: "text-white" },
  student: { bg: "bg-green-500", text: "text-white" },
  parent: { bg: "bg-orange-500", text: "text-white" },
  admission_officer: { bg: "bg-teal-500", text: "text-white" },
  finance_manager: { bg: "bg-yellow-500", text: "text-white" },
  librarian: { bg: "bg-indigo-500", text: "text-white" },
  transport_manager: { bg: "bg-cyan-500", text: "text-white" },
  hostel_manager: { bg: "bg-pink-500", text: "text-white" },
};

export function UserMenu({ className }: UserMenuProps) {
  const [user, setUser] = useState<any>(null);
  const { logout } = useLogout();
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (userData) {
      setUser(JSON.parse(userData));
    }
  }, []);

  if (!user) {
    return null;
  }

  const RoleIcon = roleIcons[user.role] || User;
  const roleLabel = roleLabels[user.role] || "User";
  const roleColor = roleColors[user.role] || { bg: "bg-gray-500", text: "text-white" };

  const handleLogout = () => {
    logout();
  };

  const getInitials = (firstName: string, lastName: string) => {
    return `${firstName?.[0] || ''}${lastName?.[0] || ''}`.toUpperCase();
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button
          variant="ghost"
          className={`flex items-center space-x-3 px-3 py-2 h-auto ${className}`}
          onClick={() => {}} // Dropdown trigger handles the click
        >
          <div className="flex items-center space-x-3">
            <div className={`w-8 h-8 ${roleColor.bg} rounded-full flex items-center justify-center`}>
              <span className={`${roleColor.text} text-sm font-medium`}>
                {getInitials(user.firstName, user.lastName)}
              </span>
            </div>
            <div className="hidden md:block text-left">
              <p className="text-sm font-medium text-gray-900">
                {user.firstName} {user.lastName}
              </p>
              <p className="text-xs text-gray-500 flex items-center">
                <RoleIcon className="h-3 w-3 mr-1" />
                {roleLabel}
              </p>
            </div>
            <ChevronDown className="h-4 w-4 text-gray-500" />
          </div>
        </Button>
      </DropdownMenuTrigger>

      <DropdownMenuContent align="end" className="w-56">
        <DropdownMenuLabel>
          <div className="flex flex-col space-y-1">
            <p className="text-sm font-medium">
              {user.firstName} {user.lastName}
            </p>
            <p className="text-xs text-gray-500">{user.email}</p>
            <div className="flex items-center text-xs text-gray-500">
              <RoleIcon className="h-3 w-3 mr-1" />
              {roleLabel}
            </div>
          </div>
        </DropdownMenuLabel>

        <DropdownMenuSeparator />

        <DropdownMenuItem
          className="cursor-pointer"
          onClick={() => {
            // TODO: Implement profile page
            toast.error("Profile page not implemented yet");
          }}
        >
          <User className="h-4 w-4 mr-2" />
          Profile
        </DropdownMenuItem>

        <DropdownMenuItem
          className="cursor-pointer"
          onClick={() => router.push("/admin/settings")}
        >
          <Settings className="h-4 w-4 mr-2" />
          Settings
        </DropdownMenuItem>

        <DropdownMenuSeparator />

        <DropdownMenuItem
          className="cursor-pointer text-red-600 focus:text-red-600"
          onClick={handleLogout}
        >
          <LogOut className="h-4 w-4 mr-2" />
          Logout
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
