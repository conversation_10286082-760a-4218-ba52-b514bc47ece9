# Hono.js + TanStack Query Architecture Documentation

## Overview

This document explains how Hono.js is integrated with TanStack Query in our Next.js application, providing a comprehensive guide to the API architecture, type safety, and data fetching patterns.

## Table of Contents

1. [Architecture Overview](#architecture-overview)
2. [Hono.js Setup](#honojs-setup)
3. [API Route Structure](#api-route-structure)
4. [Type Safety with AppType](#type-safety-with-apptype)
5. [TanStack Query Integration](#tanstack-query-integration)
6. [Custom Hooks Pattern](#custom-hooks-pattern)
7. [Provider Setup](#provider-setup)
8. [Best Practices](#best-practices)

## Architecture Overview

Our application uses a modern full-stack TypeScript architecture:

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Hono Client   │    │   Hono Server   │
│   (React)       │◄──►│   (Type-safe)   │◄──►│   (API Routes)  │
│                 │    │                 │    │                 │
│ TanStack Query  │    │ hc<AppType>     │    │ Route Handlers  │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Key Benefits:
- **End-to-end type safety** from API to UI
- **Automatic type inference** for requests and responses
- **Efficient data fetching** with caching and background updates
- **Method chaining** for clean API route definitions

## Hono.js Setup

### Main API Entry Point

```typescript
// app/api/[[...route]]/route.ts
import { handle } from "hono/vercel";
import { Hono } from "hono";

import users from "./users"
import chat from "./chat"
import blogs from "./blogs"
import quote from "./quote"

export const runtime = "nodejs"

const app = new Hono().basePath("/api");

// Method chaining for route registration
const routes = app
    .route("/users", users)
    .route("/chat", chat)
    .route("/blogs", blogs)
    .route("/quote", quote)

// Export handlers for Next.js
export const GET = handle(app)
export const POST = handle(app)

// Export type for client-side usage
export type Apptype = typeof routes;
```

### Client Configuration

```typescript
// lib/hono.ts
import { hc } from "hono/client";
import { Apptype } from "@/app/api/[[...route]]/route";

export const client = hc<Apptype>(process.env.NEXT_PUBLIC_APP_URL!)
```

## API Route Structure

### Method Chaining Pattern

Hono.js supports elegant method chaining for defining multiple routes:

```typescript
// Example: app/api/[[...route]]/blogs.ts
import { Hono } from "hono";

const app = new Hono()
    .get("/getAllBlogs", async (c) => {
        const data = await db.select().from(blogsTable)
        return c.json({ data })
    })
    .get("/:blogId", async (c) => {
        const blogId = c.req.param("blogId")
        const [data] = await db.select()
            .from(blogsTable)
            .where(eq(blogsTable.slug, blogId))
        return c.json({ data })
    })

export default app;
```

### Validation with Zod

```typescript
// Example: app/api/[[...route]]/users.ts
import { zValidator } from "@hono/zod-validator"

const app = new Hono()
    .post(
        "/",
        zValidator("json", insertUsersSchema.pick({
            userName: true,
            email: true,
            number: true,
            socialId: true,
            intrest: true,
        })),
        async (c) => {
            const values = c.req.valid("json")

            const [data] = await db.insert(usersTable).values({
                id: createId(),
                date: new Date().toISOString(),
                ...values
            }).returning();

            return c.json({ data });
        }
    )
```

## Type Safety with AppType

### How AppType Works

The `AppType` export from the main route file provides complete type information:

```typescript
export type Apptype = typeof routes;
```

This type includes:
- All route paths
- HTTP methods (GET, POST, etc.)
- Request/response schemas
- Parameter types

### Type Inference in Action

```typescript
// Automatic type inference for API calls
type ResponseType = InferResponseType<typeof client.api.users.$post>;
type RequestType = InferRequestType<typeof client.api.users.$post>["json"]
```

## TanStack Query Integration

### Provider Setup

```typescript
// providers/query-provider.tsx
'use client'

import {
  isServer,
  QueryClient,
  QueryClientProvider,
} from '@tanstack/react-query'

function makeQueryClient() {
  return new QueryClient({
    defaultOptions: {
      queries: {
        staleTime: 60 * 1000, // 1 minute
      },
    },
  })
}

let browserQueryClient: QueryClient | undefined = undefined

function getQueryClient() {
  if (isServer) {
    return makeQueryClient()
  } else {
    if (!browserQueryClient) browserQueryClient = makeQueryClient()
    return browserQueryClient
  }
}

export function QueryProviders({ children }: { children: React.ReactNode }) {
  const queryClient = getQueryClient()
  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}
```

### Layout Integration

```typescript
// app/layout.tsx
import { QueryProviders } from "@/providers/query-provider";

export default function RootLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <html lang="en">
      <body>
        <QueryProviders>
          {/* Your app components */}
          {children}
        </QueryProviders>
      </body>
    </html>
  );
}
```

## Custom Hooks Pattern

The `features/api` directory contains all custom hooks that interact with the Hono API. These hooks provide a clean abstraction layer between your components and the API calls.

### Directory Structure
```
features/
└── api/
    ├── use-get-blogs.ts          # Query hook for fetching blogs
    ├── use-get-blog-by-id.ts     # Query hook with slug/ID parameter
    ├── use-create-request.ts     # Mutation hook for creating users
    ├── use-chat.ts               # Mutation hook for chat functionality
    └── use-get-free-quote.ts     # Mutation hook for quotes
```

### Basic Query Hook

```typescript
// features/api/use-get-blogs.ts
import { client } from "@/lib/hono";
import { useQuery } from "@tanstack/react-query";

export const useGetAllBlogs = () => {
    const query = useQuery({
        queryKey: ["blogs"],
        queryFn: async () => {
            const response = await client.api.blogs.getAllBlogs.$get();

            if (!response.ok) {
                throw new Error("Failed to fetch blogs");
            }

            const { data } = await response.json();
            return data;
        },
    });

    return query;
};
```

### Query Hook with Parameters (Slug/ID)

```typescript
// features/api/use-get-blog-by-id.ts
import { client } from "@/lib/hono";
import { useQuery } from "@tanstack/react-query";

export const useGetBlogById = (blogId: string) => {
    const query = useQuery({
        queryKey: ["blogs", blogId],
        queryFn: async () => {
            const response = await client.api.blogs[":blogId"].$get({
                param: { blogId }
            });

            if (!response.ok) {
                throw new Error("Failed to fetch blog");
            }

            const { data } = await response.json();
            return data;
        },
        enabled: !!blogId, // Only run query if blogId exists
    });

    return query;
};
```

### Query Hook with Query Parameters (Pagination)

```typescript
// features/api/use-get-blogs-paginated.ts
import { client } from "@/lib/hono";
import { useQuery } from "@tanstack/react-query";

interface PaginationParams {
    page?: number;
    limit?: number;
    search?: string;
}

export const useGetBlogsPaginated = ({ page = 1, limit = 10, search }: PaginationParams = {}) => {
    const query = useQuery({
        queryKey: ["blogs", "paginated", { page, limit, search }],
        queryFn: async () => {
            const response = await client.api.blogs.paginated.$get({
                query: {
                    page: page.toString(),
                    limit: limit.toString(),
                    ...(search && { search }),
                }
            });

            if (!response.ok) {
                throw new Error("Failed to fetch paginated blogs");
            }

            const { data, meta } = await response.json();
            return { data, meta };
        },
    });

    return query;
};
```

### Corresponding API Route with Query Parameters

```typescript
// app/api/[[...route]]/blogs.ts
import { Hono } from "hono";
import { blogsTable } from "@/db/schema";
import { db } from "@/db/drizzle";
import { eq, like, desc } from "drizzle-orm";

const app = new Hono()
    .get("/getAllBlogs", async (c) => {
        const data = await db.select().from(blogsTable)
        return c.json({ data })
    })
    .get("/paginated", async (c) => {
        // Extract query parameters
        const page = parseInt(c.req.query("page") || "1");
        const limit = parseInt(c.req.query("limit") || "10");
        const search = c.req.query("search");

        // Calculate offset
        const offset = (page - 1) * limit;

        // Build query
        let query = db.select().from(blogsTable);

        if (search) {
            query = query.where(like(blogsTable.title, `%${search}%`));
        }

        // Get paginated data
        const data = await query
            .orderBy(desc(blogsTable.createdAt))
            .limit(limit)
            .offset(offset);

        // Get total count for pagination meta
        const totalQuery = db.select({ count: sql`count(*)` }).from(blogsTable);
        if (search) {
            totalQuery.where(like(blogsTable.title, `%${search}%`));
        }
        const [{ count }] = await totalQuery;

        const meta = {
            page,
            limit,
            total: Number(count),
            totalPages: Math.ceil(Number(count) / limit),
            hasNext: page < Math.ceil(Number(count) / limit),
            hasPrev: page > 1,
        };

        return c.json({ data, meta });
    })
    .get("/:blogId", async (c) => {
        const blogId = c.req.param("blogId");

        const [data] = await db.select()
            .from(blogsTable)
            .where(eq(blogsTable.slug, blogId));

        return c.json({ data });
    })

export default app;
```

### Mutation Hook Example

```typescript
// features/api/use-create-request.ts
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { InferRequestType, InferResponseType } from "hono";
import { client } from "@/lib/hono";
import { toast } from "sonner";

type ResponseType = InferResponseType<typeof client.api.users.$post>;
type RequestType = InferRequestType<typeof client.api.users.$post>["json"]

export const useCreateRequest = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation<ResponseType, Error, RequestType>({
        mutationFn: async (json) => {
            const response = await client.api.users.$post({ json });
            return await response.json();
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["users"] });
            toast.success("Request created successfully!");
        },
        onError: (error) => {
            toast.error(`Failed to create request: ${error.message}`);
        },
    });

    return mutation;
}
```

### Chat Hook with Streaming

```typescript
// features/api/use-chat.ts
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { InferRequestType, InferResponseType } from "hono";
import { client } from "@/lib/hono";

type ResponseType = InferResponseType<typeof client.api.chat.chat.$post>;
type RequestType = InferRequestType<typeof client.api.chat.chat.$post>["json"]

export const useCreateChat = () => {
    const queryClient = useQueryClient();

    const mutation = useMutation<any, Error, RequestType>({
        mutationFn: async (json) => {
            const response = await client.api.chat.chat.$post({ json });
            return await response.json();
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["chats"] });
        },
    });

    return mutation;
}
```

### Quote Hook (Simple Mutation)

```typescript
// features/api/use-get-free-quote.ts
import { useMutation } from "@tanstack/react-query";
import { InferRequestType, InferResponseType } from "hono";
import { client } from "@/lib/hono";

type ResponseType = InferResponseType<typeof client.api.quote.$post>;
type RequestType = InferRequestType<typeof client.api.quote.$post>["json"]

export const useGetFreeQuote = () => {
    const mutation = useMutation<ResponseType, Error, RequestType>({
        mutationFn: async (json) => {
            const response = await client.api.quote.$post({ json });
            return await response.json();
        },
    });

    return mutation;
}
```

### Hook Usage Patterns

#### 1. Basic Data Fetching
```typescript
// In a component
import { useGetAllBlogs } from "@/features/api/use-get-blogs";

function BlogsPage() {
    const { data: blogs, isLoading, error } = useGetAllBlogs();

    if (isLoading) return <div>Loading blogs...</div>;
    if (error) return <div>Error: {error.message}</div>;

    return (
        <div>
            {blogs?.map(blog => (
                <BlogCard key={blog.id} blog={blog} />
            ))}
        </div>
    );
}
```

#### 2. Parameterized Queries
```typescript
// In a component with dynamic routing
import { useGetBlogById } from "@/features/api/use-get-blog-by-id";
import { useParams } from "next/navigation";

function BlogDetailPage() {
    const params = useParams();
    const blogId = params.blogId as string;

    const { data: blog, isLoading, error } = useGetBlogById(blogId);

    if (isLoading) return <div>Loading blog...</div>;
    if (error) return <div>Blog not found</div>;

    return (
        <article>
            <h1>{blog?.title}</h1>
            <div>{blog?.content}</div>
        </article>
    );
}
```

#### 3. Paginated Data with Search
```typescript
// In a component with pagination
import { useGetBlogsPaginated } from "@/features/api/use-get-blogs-paginated";
import { useState } from "react";

function BlogsWithPagination() {
    const [page, setPage] = useState(1);
    const [search, setSearch] = useState("");

    const {
        data,
        isLoading,
        error
    } = useGetBlogsPaginated({
        page,
        limit: 10,
        search: search || undefined
    });

    const blogs = data?.data || [];
    const meta = data?.meta;

    return (
        <div>
            <input
                type="text"
                placeholder="Search blogs..."
                value={search}
                onChange={(e) => setSearch(e.target.value)}
            />

            {isLoading && <div>Loading...</div>}
            {error && <div>Error: {error.message}</div>}

            <div>
                {blogs.map(blog => (
                    <BlogCard key={blog.id} blog={blog} />
                ))}
            </div>

            {meta && (
                <div className="pagination">
                    <button
                        disabled={!meta.hasPrev}
                        onClick={() => setPage(page - 1)}
                    >
                        Previous
                    </button>

                    <span>Page {meta.page} of {meta.totalPages}</span>

                    <button
                        disabled={!meta.hasNext}
                        onClick={() => setPage(page + 1)}
                    >
                        Next
                    </button>
                </div>
            )}
        </div>
    );
}
```

#### 4. Form Submission with Mutations
```typescript
// In a form component
import { useCreateRequest } from "@/features/api/use-create-request";
import { useState } from "react";

function ContactForm() {
    const [formData, setFormData] = useState({
        userName: "",
        email: "",
        number: "",
        socialId: "",
        intrest: "",
    });

    const createRequest = useCreateRequest();

    const handleSubmit = async (e: React.FormEvent) => {
        e.preventDefault();

        try {
            await createRequest.mutateAsync(formData);
            setFormData({ userName: "", email: "", number: "", socialId: "", intrest: "" });
        } catch (error) {
            console.error("Failed to submit:", error);
        }
    };

    return (
        <form onSubmit={handleSubmit}>
            <input
                type="text"
                placeholder="Name"
                value={formData.userName}
                onChange={(e) => setFormData(prev => ({ ...prev, userName: e.target.value }))}
                required
            />
            <input
                type="email"
                placeholder="Email"
                value={formData.email}
                onChange={(e) => setFormData(prev => ({ ...prev, email: e.target.value }))}
                required
            />
            {/* Other form fields */}

            <button
                type="submit"
                disabled={createRequest.isPending}
            >
                {createRequest.isPending ? "Submitting..." : "Submit Request"}
            </button>
        </form>
    );
}
```

## Best Practices

### 1. Route Organization
- Keep related routes in separate files
- Use descriptive file names
- Group by feature/domain

### 2. Type Safety
- Always export and use `AppType`
- Use `InferRequestType` and `InferResponseType`
- Validate inputs with Zod schemas

### 3. Error Handling
- Check response.ok before parsing JSON
- Throw meaningful error messages
- Handle errors in UI components

### 4. Query Keys
- Use consistent naming conventions
- Include relevant parameters in keys
- Use arrays for hierarchical keys

### 5. Cache Management
- Set appropriate staleTime values
- Invalidate queries after mutations
- Use optimistic updates when appropriate

## Method Chaining Examples

### Basic Chaining
```typescript
const app = new Hono()
    .get("/", handler1)
    .post("/", handler2)
    .put("/:id", handler3)
    .delete("/:id", handler4)
```

### Advanced Chaining with Middleware
```typescript
const app = new Hono()
    .use("*", authMiddleware)
    .get("/public", publicHandler)
    .use("/protected/*", protectedMiddleware)
    .get("/protected/data", protectedHandler)
```

### Route Grouping
```typescript
const routes = app
    .route("/users", usersRoutes)
    .route("/posts", postsRoutes)
    .route("/comments", commentsRoutes)
```

This architecture provides a robust, type-safe foundation for building scalable web applications with excellent developer experience and runtime performance.

## Advanced Patterns

### Streaming Responses

```typescript
// app/api/[[...route]]/chat.ts
const app = new Hono()
    .post("/chat", zValidator("json", chatSchema), async (c) => {
        const valid = c.req.valid("json") as ChatRequest;

        const stream = client.chatCompletionStream({
            provider: "hf-inference",
            model: "Qwen/Qwen2.5-Coder-32B-Instruct",
            messages,
            stream: true
        });

        return new Response(stream, {
            headers: {
                'Content-Type': 'text/plain; charset=utf-8',
                'Transfer-Encoding': 'chunked',
            },
        });
    })
```

### Parameter Handling

```typescript
// Dynamic route parameters
const app = new Hono()
    .get("/:blogId", async (c) => {
        const blogId = c.req.param("blogId")
        // Use blogId in your logic
    })

// Query parameters
const app = new Hono()
    .get("/search", async (c) => {
        const query = c.req.query("q")
        const page = c.req.query("page") || "1"
        // Handle search logic
    })
```

### Middleware Integration

```typescript
// Custom middleware
const authMiddleware = async (c: Context, next: Next) => {
    const token = c.req.header("Authorization")
    if (!token) {
        return c.json({ error: "Unauthorized" }, 401)
    }
    await next()
}

const app = new Hono()
    .use("/protected/*", authMiddleware)
    .get("/protected/data", protectedHandler)
```

## Real-World Usage Examples

### Component Integration

```typescript
// components/BlogList.tsx
import { useGetAllBlogs } from "@/features/api/use-get-blogs";

export function BlogList() {
    const { data: blogs, isLoading, error } = useGetAllBlogs();

    if (isLoading) return <div>Loading...</div>;
    if (error) return <div>Error: {error.message}</div>;

    return (
        <div>
            {blogs?.map(blog => (
                <BlogCard key={blog.id} blog={blog} />
            ))}
        </div>
    );
}
```

### Form Handling with Mutations

```typescript
// components/ContactForm.tsx
import { useCreateRequest } from "@/features/api/use-create-request";

export function ContactForm() {
    const createRequest = useCreateRequest();

    const handleSubmit = async (formData: FormData) => {
        try {
            await createRequest.mutateAsync({
                userName: formData.get("name") as string,
                email: formData.get("email") as string,
                // ... other fields
            });
            toast.success("Request submitted successfully!");
        } catch (error) {
            toast.error("Failed to submit request");
        }
    };

    return (
        <form onSubmit={handleSubmit}>
            {/* Form fields */}
            <button
                type="submit"
                disabled={createRequest.isPending}
            >
                {createRequest.isPending ? "Submitting..." : "Submit"}
            </button>
        </form>
    );
}
```

## Troubleshooting

### Common Issues

1. **Type Errors**: Ensure `AppType` is properly exported and imported
2. **CORS Issues**: Configure CORS middleware if needed
3. **Environment Variables**: Verify `NEXT_PUBLIC_APP_URL` is set correctly
4. **Query Invalidation**: Use correct query keys for cache invalidation

### Debugging Tips

```typescript
// Enable query devtools in development
import { ReactQueryDevtools } from '@tanstack/react-query-devtools'

export function QueryProviders({ children }: { children: React.ReactNode }) {
  const queryClient = getQueryClient()
  return (
    <QueryClientProvider client={queryClient}>
      {children}
      {process.env.NODE_ENV === 'development' && (
        <ReactQueryDevtools initialIsOpen={false} />
      )}
    </QueryClientProvider>
  )
}
```

## Performance Considerations

### Optimization Strategies

1. **Stale Time Configuration**: Set appropriate stale times for different data types
2. **Background Refetching**: Configure refetch intervals for real-time data
3. **Query Deduplication**: TanStack Query automatically deduplicates identical requests
4. **Infinite Queries**: Use for paginated data

```typescript
// Optimized query configuration
export const useGetBlogs = (page: number) => {
    return useQuery({
        queryKey: ["blogs", page],
        queryFn: () => fetchBlogs(page),
        staleTime: 5 * 60 * 1000, // 5 minutes
        gcTime: 10 * 60 * 1000,   // 10 minutes
        refetchOnWindowFocus: false,
    });
};
```

This comprehensive architecture enables building modern, scalable applications with excellent type safety and developer experience.
