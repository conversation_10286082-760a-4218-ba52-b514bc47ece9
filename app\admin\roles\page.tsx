"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  Shield,
  Search,
  Download,
  Plus,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Users,
  Settings,
  Loader2,
  RefreshCw,
  Lock,
  Unlock,
  UserCheck,
} from "lucide-react";
import { toast } from "sonner";

export default function AdminRoles() {
  const [user, setUser] = useState<any>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedRole, setSelectedRole] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(false);
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "super_admin") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  // Mock roles data - in production this would come from API
  const roles = [
    {
      id: "super_admin",
      name: "Super Admin",
      description: "Full system access with all permissions",
      userCount: 1,
      permissions: ["all"],
      isSystem: true,
      status: "active",
    },
    {
      id: "admin",
      name: "Admin",
      description: "Administrative access to manage school operations",
      userCount: 3,
      permissions: ["users.read", "users.write", "students.read", "students.write", "teachers.read", "teachers.write"],
      isSystem: true,
      status: "active",
    },
    {
      id: "teacher",
      name: "Teacher",
      description: "Access to classes, students, and academic functions",
      userCount: 25,
      permissions: ["students.read", "classes.read", "grades.write", "attendance.write"],
      isSystem: true,
      status: "active",
    },
    {
      id: "student",
      name: "Student",
      description: "Student portal access",
      userCount: 450,
      permissions: ["profile.read", "grades.read", "attendance.read"],
      isSystem: true,
      status: "active",
    },
    {
      id: "parent",
      name: "Parent",
      description: "Parent portal access to view child's progress",
      userCount: 320,
      permissions: ["children.read", "grades.read", "attendance.read", "fees.read"],
      isSystem: true,
      status: "active",
    },
    {
      id: "admission",
      name: "Admission Officer",
      description: "Manage student admissions and enrollment",
      userCount: 2,
      permissions: ["students.write", "admissions.write", "programs.read"],
      isSystem: false,
      status: "active",
    },
    {
      id: "finance",
      name: "Finance Manager",
      description: "Manage fees, payments, and financial operations",
      userCount: 2,
      permissions: ["fees.read", "fees.write", "payments.read", "payments.write", "reports.read"],
      isSystem: false,
      status: "active",
    },
    {
      id: "librarian",
      name: "Librarian",
      description: "Manage library resources and operations",
      userCount: 1,
      permissions: ["library.read", "library.write", "books.read", "books.write"],
      isSystem: false,
      status: "active",
    },
  ];

  const allPermissions = [
    { id: "users.read", name: "View Users", category: "User Management" },
    { id: "users.write", name: "Manage Users", category: "User Management" },
    { id: "students.read", name: "View Students", category: "Academic" },
    { id: "students.write", name: "Manage Students", category: "Academic" },
    { id: "teachers.read", name: "View Teachers", category: "Academic" },
    { id: "teachers.write", name: "Manage Teachers", category: "Academic" },
    { id: "classes.read", name: "View Classes", category: "Academic" },
    { id: "classes.write", name: "Manage Classes", category: "Academic" },
    { id: "grades.read", name: "View Grades", category: "Academic" },
    { id: "grades.write", name: "Manage Grades", category: "Academic" },
    { id: "attendance.read", name: "View Attendance", category: "Academic" },
    { id: "attendance.write", name: "Manage Attendance", category: "Academic" },
    { id: "fees.read", name: "View Fees", category: "Finance" },
    { id: "fees.write", name: "Manage Fees", category: "Finance" },
    { id: "payments.read", name: "View Payments", category: "Finance" },
    { id: "payments.write", name: "Manage Payments", category: "Finance" },
    { id: "library.read", name: "View Library", category: "Library" },
    { id: "library.write", name: "Manage Library", category: "Library" },
    { id: "reports.read", name: "View Reports", category: "Reports" },
    { id: "reports.write", name: "Generate Reports", category: "Reports" },
  ];

  const handleExport = () => {
    toast.info("Export functionality coming soon!");
  };

  const handleRefresh = () => {
    toast.success("Roles list refreshed!");
  };

  const handleDeleteRole = (roleId: string, roleName: string) => {
    toast.success(`Role "${roleName}" has been deleted`);
  };

  const filteredRoles = roles.filter(role =>
    role.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    role.description.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusColor = (status: string) => {
    return status === "active"
      ? "bg-green-100 text-green-800"
      : "bg-red-100 text-red-800";
  };

  const getRoleTypeColor = (isSystem: boolean) => {
    return isSystem
      ? "bg-blue-100 text-blue-800"
      : "bg-purple-100 text-purple-800";
  };

  if (!user) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-600" />
            <p className="mt-2 text-gray-600">Loading...</p>
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Roles & Permissions</h1>
            <p className="text-gray-600">Manage user roles and access permissions</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleRefresh} disabled={isLoading}>
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button variant="outline" onClick={handleExport}>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button onClick={() => router.push("/admin/roles/new")}>
              <Plus className="h-4 w-4 mr-2" />
              Add Role
            </Button>
          </div>
        </div>

        {/* Statistics Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Roles</CardTitle>
              <Shield className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{roles.length}</div>
              <p className="text-xs text-muted-foreground">
                {roles.filter(r => r.isSystem).length} system, {roles.filter(r => !r.isSystem).length} custom
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Users</CardTitle>
              <Users className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {roles.reduce((sum, role) => sum + role.userCount, 0)}
              </div>
              <p className="text-xs text-muted-foreground">
                Across all roles
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Permissions</CardTitle>
              <Lock className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{allPermissions.length}</div>
              <p className="text-xs text-muted-foreground">
                Available permissions
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Custom Roles</CardTitle>
              <UserCheck className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {roles.filter(r => !r.isSystem).length}
              </div>
              <p className="text-xs text-muted-foreground">
                Institution-specific
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Search */}
        <Card>
          <CardContent className="p-6">
            <div className="relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
              <Input
                type="text"
                placeholder="Search roles by name or description..."
                className="pl-10"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
              />
            </div>
          </CardContent>
        </Card>

        {/* Roles Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredRoles.map((role) => (
            <Card key={role.id} className="hover:shadow-md transition-shadow">
              <CardHeader>
                <div className="flex items-start justify-between">
                  <div className="flex-1">
                    <div className="flex items-center gap-2 mb-2">
                      <h3 className="text-lg font-semibold text-gray-900">{role.name}</h3>
                      <Badge className={getRoleTypeColor(role.isSystem)}>
                        {role.isSystem ? "System" : "Custom"}
                      </Badge>
                    </div>
                    <p className="text-sm text-gray-600 mb-3">{role.description}</p>
                    <div className="flex items-center gap-4 text-sm text-gray-500">
                      <div className="flex items-center gap-1">
                        <Users className="h-4 w-4" />
                        <span>{role.userCount} users</span>
                      </div>
                      <div className="flex items-center gap-1">
                        <Lock className="h-4 w-4" />
                        <span>{role.permissions.includes("all") ? "All" : role.permissions.length} permissions</span>
                      </div>
                    </div>
                  </div>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" size="sm">
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuItem
                        onClick={() => setSelectedRole(role)}
                      >
                        <Eye className="h-4 w-4 mr-2" />
                        View Permissions
                      </DropdownMenuItem>
                      <DropdownMenuItem
                        onClick={() => router.push(`/admin/roles/${role.id}/edit`)}
                        disabled={role.isSystem}
                      >
                        <Edit className="h-4 w-4 mr-2" />
                        Edit Role
                      </DropdownMenuItem>
                      {!role.isSystem && (
                        <AlertDialog>
                          <AlertDialogTrigger asChild>
                            <DropdownMenuItem
                              onSelect={(e) => e.preventDefault()}
                              className="text-red-600 focus:text-red-600"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Delete Role
                            </DropdownMenuItem>
                          </AlertDialogTrigger>
                          <AlertDialogContent>
                            <AlertDialogHeader>
                              <AlertDialogTitle>Delete Role</AlertDialogTitle>
                              <AlertDialogDescription>
                                Are you sure you want to delete &ldquo;{role.name}&rdquo;? This action cannot be undone.
                                Users with this role will lose their permissions.
                              </AlertDialogDescription>
                            </AlertDialogHeader>
                            <AlertDialogFooter>
                              <AlertDialogCancel>Cancel</AlertDialogCancel>
                              <AlertDialogAction
                                onClick={() => handleDeleteRole(role.id, role.name)}
                                className="bg-red-600 hover:bg-red-700"
                              >
                                Delete
                              </AlertDialogAction>
                            </AlertDialogFooter>
                          </AlertDialogContent>
                        </AlertDialog>
                      )}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Badge className={getStatusColor(role.status)}>
                    {role.status.charAt(0).toUpperCase() + role.status.slice(1)}
                  </Badge>
                  {role.permissions.includes("all") ? (
                    <div className="flex items-center gap-2 text-sm text-green-600">
                      <Unlock className="h-4 w-4" />
                      <span>Full System Access</span>
                    </div>
                  ) : (
                    <div className="text-xs text-gray-500">
                      Limited permissions: {role.permissions.slice(0, 3).join(", ")}
                      {role.permissions.length > 3 && ` +${role.permissions.length - 3} more`}
                    </div>
                  )}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Role Details Modal */}
        {selectedRole && (
          <AlertDialog open={!!selectedRole} onOpenChange={() => setSelectedRole(null)}>
            <AlertDialogContent className="max-w-2xl">
              <AlertDialogHeader>
                <AlertDialogTitle className="flex items-center gap-2">
                  <Shield className="h-5 w-5" />
                  {selectedRole.name} Permissions
                </AlertDialogTitle>
                <AlertDialogDescription>
                  Detailed permissions for the {selectedRole.name} role
                </AlertDialogDescription>
              </AlertDialogHeader>
              <div className="max-h-96 overflow-y-auto">
                {selectedRole.permissions.includes("all") ? (
                  <div className="text-center py-8">
                    <Unlock className="h-12 w-12 mx-auto text-green-600 mb-4" />
                    <h3 className="text-lg font-medium text-gray-900 mb-2">Full System Access</h3>
                    <p className="text-gray-600">This role has unrestricted access to all system features and data.</p>
                  </div>
                ) : (
                  <div className="space-y-4">
                    {Object.entries(
                      allPermissions.reduce((acc, permission) => {
                        if (!acc[permission.category]) acc[permission.category] = [];
                        acc[permission.category].push(permission);
                        return acc;
                      }, {} as Record<string, typeof allPermissions>)
                    ).map(([category, permissions]) => (
                      <div key={category}>
                        <h4 className="font-medium text-gray-900 mb-2">{category}</h4>
                        <div className="space-y-2">
                          {permissions.map((permission) => (
                            <div key={permission.id} className="flex items-center space-x-2">
                              <Checkbox
                                checked={selectedRole.permissions.includes(permission.id)}
                                disabled
                              />
                              <span className="text-sm text-gray-700">{permission.name}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
              <AlertDialogFooter>
                <AlertDialogCancel>Close</AlertDialogCancel>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        )}
      </div>
    </AppLayout>
  );
}
