"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  CreditCard,
  IndianRupee,
  Search,
  Filter,
  Download,
  Plus,
  Eye,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  TrendingUp,
  Calendar,
  Receipt,
  RefreshCw,
  Users,
  Banknote,
} from "lucide-react";

export default function FinancePayments() {
  const [user, setUser] = useState<any>(null);
  const [selectedFilter, setSelectedFilter] = useState("all");
  const [selectedMethod, setSelectedMethod] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "finance_manager" && parsedUser.role !== "admin" && parsedUser.role !== "super_admin") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock payments data
  const payments = [
    {
      id: "PAY001",
      transactionId: "TXN20240120001",
      studentName: "John Doe",
      studentId: "STU001",
      amount: 25000,
      feeType: "Tuition Fee - Grade 10",
      paymentDate: "2024-01-20",
      paymentTime: "14:30:25",
      paymentMethod: "Online Banking",
      status: "Completed",
      receiptNo: "RCP001",
      gateway: "Razorpay",
      gatewayTxnId: "pay_123456789",
      parentName: "Jane Doe",
      parentPhone: "******-567-8902",
      remarks: "Full payment for Q3",
    },
    {
      id: "PAY002",
      transactionId: "TXN20240119002",
      studentName: "Alice Smith",
      studentId: "STU002",
      amount: 22000,
      feeType: "Tuition Fee - Grade 11",
      paymentDate: "2024-01-19",
      paymentTime: "10:15:42",
      paymentMethod: "UPI",
      status: "Completed",
      receiptNo: "RCP002",
      gateway: "PhonePe",
      gatewayTxnId: "upi_987654321",
      parentName: "Bob Smith",
      parentPhone: "******-567-8904",
      remarks: "Quarterly payment",
    },
    {
      id: "PAY003",
      transactionId: "TXN20240118003",
      studentName: "Bob Johnson",
      studentId: "STU003",
      amount: 15000,
      feeType: "Partial Payment - Grade 10",
      paymentDate: "2024-01-18",
      paymentTime: "16:45:18",
      paymentMethod: "Cash",
      status: "Pending Verification",
      receiptNo: "RCP003",
      gateway: "Manual",
      gatewayTxnId: "CASH001",
      parentName: "Carol Johnson",
      parentPhone: "******-567-8906",
      remarks: "Cash payment at office",
    },
    {
      id: "PAY004",
      transactionId: "TXN20240117004",
      studentName: "Sarah Wilson",
      studentId: "STU004",
      amount: 5000,
      feeType: "Transport Fee",
      paymentDate: "2024-01-17",
      paymentTime: "11:20:33",
      paymentMethod: "Credit Card",
      status: "Failed",
      receiptNo: "RCP004",
      gateway: "Stripe",
      gatewayTxnId: "ch_failed123",
      parentName: "David Wilson",
      parentPhone: "******-567-8908",
      remarks: "Card declined - insufficient funds",
    },
    {
      id: "PAY005",
      transactionId: "TXN20240116005",
      studentName: "Mike Brown",
      studentId: "STU005",
      amount: 28000,
      feeType: "Annual Fee - Grade 12",
      paymentDate: "2024-01-16",
      paymentTime: "09:30:15",
      paymentMethod: "Bank Transfer",
      status: "Processing",
      receiptNo: "RCP005",
      gateway: "NEFT",
      gatewayTxnId: "NEFT789456",
      parentName: "Lisa Brown",
      parentPhone: "******-567-8910",
      remarks: "Bank transfer in progress",
    },
  ];

  const paymentStats = {
    totalPayments: payments.length,
    totalAmount: payments.reduce((sum, p) => sum + p.amount, 0),
    completedPayments: payments.filter(p => p.status === "Completed").length,
    pendingPayments: payments.filter(p => p.status === "Pending Verification" || p.status === "Processing").length,
    failedPayments: payments.filter(p => p.status === "Failed").length,
    todayAmount: 47000,
    monthlyTarget: 2500000,
  };

  const paymentMethods = [
    { method: "Online Banking", count: 1, amount: 25000, color: "bg-blue-500" },
    { method: "UPI", count: 1, amount: 22000, color: "bg-green-500" },
    { method: "Cash", count: 1, amount: 15000, color: "bg-yellow-500" },
    { method: "Credit Card", count: 1, amount: 5000, color: "bg-purple-500" },
    { method: "Bank Transfer", count: 1, amount: 28000, color: "bg-indigo-500" },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Completed": return "bg-green-100 text-green-700";
      case "Pending Verification": return "bg-yellow-100 text-yellow-700";
      case "Processing": return "bg-blue-100 text-blue-700";
      case "Failed": return "bg-red-100 text-red-700";
      case "Refunded": return "bg-gray-100 text-gray-700";
      default: return "bg-gray-100 text-gray-700";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Completed": return <CheckCircle className="h-4 w-4" />;
      case "Pending Verification": return <Clock className="h-4 w-4" />;
      case "Processing": return <RefreshCw className="h-4 w-4" />;
      case "Failed": return <XCircle className="h-4 w-4" />;
      case "Refunded": return <RefreshCw className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const getMethodIcon = (method: string) => {
    switch (method) {
      case "Online Banking": return <Banknote className="h-4 w-4" />;
      case "UPI": return <CreditCard className="h-4 w-4" />;
      case "Cash": return <IndianRupee className="h-4 w-4" />;
      case "Credit Card": return <CreditCard className="h-4 w-4" />;
      case "Bank Transfer": return <Banknote className="h-4 w-4" />;
      default: return <CreditCard className="h-4 w-4" />;
    }
  };

  const filteredPayments = payments.filter(payment => {
    const matchesSearch = payment.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         payment.studentId.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         payment.transactionId.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         payment.receiptNo.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesStatus = selectedFilter === "all" || payment.status.toLowerCase().replace(" ", "_") === selectedFilter;
    const matchesMethod = selectedMethod === "all" || payment.paymentMethod === selectedMethod;

    return matchesSearch && matchesStatus && matchesMethod;
  });

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Payment Management</h1>
            <p className="text-gray-600">Process and track all payment transactions</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Manual Payment
            </Button>
          </div>
        </div>

        {/* Payment Statistics */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CreditCard className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {paymentStats.totalPayments}
                  </div>
                  <p className="text-sm text-gray-600">Total Payments</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <IndianRupee className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    ₹{(paymentStats.totalAmount / 100000).toFixed(1)}L
                  </div>
                  <p className="text-sm text-gray-600">Total Amount</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {paymentStats.completedPayments}
                  </div>
                  <p className="text-sm text-gray-600">Completed</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <AlertTriangle className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    {paymentStats.pendingPayments + paymentStats.failedPayments}
                  </div>
                  <p className="text-sm text-gray-600">Needs Attention</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Daily Collection Progress */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Today&apos;s Collection</h3>
              <div className="text-sm text-gray-500">
                Target: ₹{(paymentStats.monthlyTarget / 30 / 1000).toFixed(0)}K daily
              </div>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-4 mb-2">
              <div
                className="bg-green-500 h-4 rounded-full"
                style={{ width: `${Math.min((paymentStats.todayAmount / (paymentStats.monthlyTarget / 30)) * 100, 100)}%` }}
              />
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Today: ₹{(paymentStats.todayAmount / 1000).toFixed(0)}K</span>
              <span className="text-green-600 font-medium">
                {paymentStats.todayAmount >= (paymentStats.monthlyTarget / 30)
                  ? '✓ Target Achieved'
                  : `₹${((paymentStats.monthlyTarget / 30 - paymentStats.todayAmount) / 1000).toFixed(0)}K to target`
                }
              </span>
            </div>
          </CardContent>
        </Card>

        {/* Filters and Search */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search by student name, ID, transaction ID, or receipt..."
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>

              <div className="flex gap-2 flex-wrap">
                <select
                  value={selectedMethod}
                  onChange={(e) => setSelectedMethod(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Methods</option>
                  {paymentMethods.map((method) => (
                    <option key={method.method} value={method.method}>{method.method}</option>
                  ))}
                </select>

                {[
                  { key: "all", label: "All Status" },
                  { key: "completed", label: "Completed" },
                  { key: "pending_verification", label: "Pending" },
                  { key: "processing", label: "Processing" },
                  { key: "failed", label: "Failed" },
                ].map((filter) => (
                  <Button
                    key={filter.key}
                    variant={selectedFilter === filter.key ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedFilter(filter.key)}
                  >
                    {filter.label}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Main Content Grid */}
        <div className="grid gap-6 lg:grid-cols-4">
          {/* Payments List */}
          <Card className="lg:col-span-3">
            <CardHeader>
              <CardTitle className="flex items-center">
                <CreditCard className="h-5 w-5 mr-2" />
                Payment Transactions ({filteredPayments.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredPayments.map((payment) => (
                  <div key={payment.id} className="border rounded-lg p-6 hover:shadow-md transition-shadow">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="text-lg font-semibold text-gray-900">{payment.studentName}</h3>
                          <Badge variant="outline" className="text-xs">
                            {payment.studentId}
                          </Badge>
                        </div>
                        <p className="text-gray-600 mb-1">{payment.feeType}</p>
                        <div className="text-2xl font-bold text-green-600 mb-2">
                          ₹{payment.amount.toLocaleString()}
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge className={getStatusColor(payment.status)}>
                          {getStatusIcon(payment.status)}
                          <span className="ml-1">{payment.status}</span>
                        </Badge>
                        <div className="text-sm text-gray-500 mt-1">
                          {payment.paymentDate} {payment.paymentTime}
                        </div>
                      </div>
                    </div>

                    <div className="grid gap-4 md:grid-cols-3 mb-4">
                      <div>
                        <div className="text-sm text-gray-600">Payment Method</div>
                        <div className="flex items-center space-x-2">
                          {getMethodIcon(payment.paymentMethod)}
                          <span className="font-medium">{payment.paymentMethod}</span>
                        </div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-600">Transaction ID</div>
                        <div className="font-medium text-blue-600">{payment.transactionId}</div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-600">Receipt No</div>
                        <div className="font-medium">{payment.receiptNo}</div>
                      </div>
                    </div>

                    <div className="grid gap-4 md:grid-cols-2 mb-4">
                      <div>
                        <div className="text-sm text-gray-600">Parent Contact</div>
                        <div className="font-medium">{payment.parentName}</div>
                        <div className="text-sm text-gray-500">{payment.parentPhone}</div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-600">Gateway Details</div>
                        <div className="font-medium">{payment.gateway}</div>
                        <div className="text-sm text-gray-500">{payment.gatewayTxnId}</div>
                      </div>
                    </div>

                    {payment.remarks && (
                      <div className="mb-4">
                        <div className="text-sm text-gray-600">Remarks</div>
                        <div className="text-sm text-gray-900 bg-gray-50 p-2 rounded">
                          {payment.remarks}
                        </div>
                      </div>
                    )}

                    <div className="flex items-center justify-between pt-4 border-t">
                      <div className="text-sm text-gray-500">
                        Gateway: {payment.gateway}
                      </div>
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-1" />
                          View Details
                        </Button>
                        <Button variant="outline" size="sm">
                          <Receipt className="h-4 w-4 mr-1" />
                          Receipt
                        </Button>
                        {payment.status === "Failed" && (
                          <Button size="sm">
                            <RefreshCw className="h-4 w-4 mr-1" />
                            Retry
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Payment Methods Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <TrendingUp className="h-5 w-5 mr-2" />
                Payment Methods
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {paymentMethods.map((method, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className={`w-3 h-3 ${method.color} rounded-full`} />
                      <div>
                        <div className="font-medium text-gray-900">{method.method}</div>
                        <div className="text-sm text-gray-500">{method.count} transactions</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-gray-900">
                        ₹{(method.amount / 1000).toFixed(0)}K
                      </div>
                    </div>
                  </div>
                ))}

                <div className="pt-4 border-t">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900">
                      ₹{(paymentStats.totalAmount / 100000).toFixed(1)}L
                    </div>
                    <div className="text-sm text-gray-500">Total Collected</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  );
}
