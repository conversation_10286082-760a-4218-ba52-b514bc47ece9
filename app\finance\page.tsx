"use client";

import { useState } from "react";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import {
  IndianRupee,
  CreditCard,
  TrendingUp,
  TrendingDown,
  Search,
  Plus,
  Download,
  Filter,
  Calendar,
  CheckCircle,
  XCircle,
  Clock
} from "lucide-react";

// Mock finance data
const mockFeeStructures = [
  {
    id: "1",
    name: "Grade 10 Annual Fees",
    grade: "10",
    totalFee: 67500,
    dueDate: "2024-04-30",
    studentsEnrolled: 120,
    collected: 85,
    pending: 35,
  },
  {
    id: "2",
    name: "Grade 11 Annual Fees",
    grade: "11",
    totalFee: 68500,
    dueDate: "2024-04-30",
    studentsEnrolled: 110,
    collected: 92,
    pending: 18,
  },
];

const mockRecentPayments = [
  {
    id: "1",
    studentName: "<PERSON>",
    grade: "10A",
    amount: 67500,
    method: "Online",
    date: "2024-01-15",
    status: "completed",
    receiptNo: "RCP001",
  },
  {
    id: "2",
    studentName: "Alice Smith",
    grade: "10A",
    amount: 30000,
    method: "Bank Transfer",
    date: "2024-01-14",
    status: "completed",
    receiptNo: "RCP002",
  },
  {
    id: "3",
    studentName: "Michael Johnson",
    grade: "11B",
    amount: 68500,
    method: "Cash",
    date: "2024-01-13",
    status: "pending",
    receiptNo: "RCP003",
  },
];

const statusColors = {
  completed: "text-green-600 bg-green-100",
  pending: "text-yellow-600 bg-yellow-100",
  failed: "text-red-600 bg-red-100",
};

const statusIcons = {
  completed: CheckCircle,
  pending: Clock,
  failed: XCircle,
};

export default function FinancePage() {
  const [search, setSearch] = useState("");
  const [selectedPeriod, setSelectedPeriod] = useState("current");

  // Calculate totals
  const totalCollected = mockRecentPayments
    .filter(p => p.status === "completed")
    .reduce((sum, p) => sum + p.amount, 0);

  const totalPending = mockRecentPayments
    .filter(p => p.status === "pending")
    .reduce((sum, p) => sum + p.amount, 0);

  const collectionRate = mockFeeStructures.reduce((acc, fee) => {
    return acc + (fee.collected / fee.studentsEnrolled) * 100;
  }, 0) / mockFeeStructures.length;

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Finance Management</h1>
            <p className="text-gray-600">Manage fees, payments, and financial operations</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Payment
            </Button>
          </div>
        </div>

        {/* Financial Stats */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <IndianRupee className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    ₹{(totalCollected / 100000).toFixed(1)}L
                  </div>
                  <p className="text-sm text-gray-600">Total Collected</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-yellow-600">
                    ₹{(totalPending / 100000).toFixed(1)}L
                  </div>
                  <p className="text-sm text-gray-600">Pending Collection</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <TrendingUp className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {Math.round(collectionRate)}%
                  </div>
                  <p className="text-sm text-gray-600">Collection Rate</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CreditCard className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {mockRecentPayments.length}
                  </div>
                  <p className="text-sm text-gray-600">Transactions</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Fee Structures */}
        <Card>
          <CardHeader>
            <CardTitle>Fee Structures</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockFeeStructures.map((fee) => (
                <div key={fee.id} className="p-4 border rounded-lg">
                  <div className="flex items-center justify-between mb-3">
                    <div>
                      <h3 className="font-medium text-gray-900">{fee.name}</h3>
                      <p className="text-sm text-gray-500">
                        Grade {fee.grade} • Due: {fee.dueDate}
                      </p>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-gray-900">
                        ₹{fee.totalFee.toLocaleString()}
                      </div>
                      <div className="text-sm text-gray-500">
                        {fee.studentsEnrolled} students
                      </div>
                    </div>
                  </div>

                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-green-600 font-medium">
                        Collected: {fee.collected}
                      </span>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                        <div
                          className="bg-green-600 h-2 rounded-full"
                          style={{
                            width: `${(fee.collected / fee.studentsEnrolled) * 100}%`,
                          }}
                        />
                      </div>
                    </div>
                    <div>
                      <span className="text-red-600 font-medium">
                        Pending: {fee.pending}
                      </span>
                      <div className="w-full bg-gray-200 rounded-full h-2 mt-1">
                        <div
                          className="bg-red-600 h-2 rounded-full"
                          style={{
                            width: `${(fee.pending / fee.studentsEnrolled) * 100}%`,
                          }}
                        />
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Payments */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle>Recent Payments</CardTitle>
              <div className="flex gap-2">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search payments..."
                    value={search}
                    onChange={(e) => setSearch(e.target.value)}
                    className="pl-10 w-64"
                  />
                </div>
                <Button variant="outline" size="sm">
                  <Filter className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockRecentPayments.map((payment) => {
                const StatusIcon = statusIcons[payment.status as keyof typeof statusIcons];

                return (
                  <div
                    key={payment.id}
                    className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center space-x-4">
                      <div className={`p-2 rounded-full ${statusColors[payment.status as keyof typeof statusColors]}`}>
                        <StatusIcon className="h-4 w-4" />
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">
                          {payment.studentName}
                        </div>
                        <div className="text-sm text-gray-500">
                          {payment.grade} • {payment.method} • {payment.date}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-gray-900">
                        ₹{payment.amount.toLocaleString()}
                      </div>
                      <div className="text-sm text-gray-500">
                        {payment.receiptNo}
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Payment Methods Analysis */}
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Payment Methods</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Online Payments</span>
                  <span className="font-medium">65%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-600 h-2 rounded-full w-[65%]" />
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm">Bank Transfer</span>
                  <span className="font-medium">25%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-green-600 h-2 rounded-full w-[25%]" />
                </div>

                <div className="flex items-center justify-between">
                  <span className="text-sm">Cash</span>
                  <span className="font-medium">10%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-yellow-600 h-2 rounded-full w-[10%]" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Monthly Collection Trend</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">January 2024</span>
                  <span className="font-medium text-green-600">₹12.5L</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">December 2023</span>
                  <span className="font-medium text-blue-600">₹11.8L</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">November 2023</span>
                  <span className="font-medium text-gray-600">₹10.2L</span>
                </div>
                <div className="pt-4 border-t">
                  <Button variant="outline" className="w-full">
                    View Detailed Report
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  );
}
