import { Hono } from "hono";
import { zValidator } from "@hono/zod-validator";
import { mockTimetables } from "@/lib/mock-db";
import { timetableSchema } from "@/lib/schemas";
import { generateId } from "@/lib/utils";

const app = new Hono()
  .get("/", async (c) => {
    const page = parseInt(c.req.query("page") || "1");
    const limit = parseInt(c.req.query("limit") || "10");
    const classId = c.req.query("classId");
    const teacherId = c.req.query("teacherId");
    const subject = c.req.query("subject");
    const day = c.req.query("day");
    const academicYear = c.req.query("academicYear");
    const semester = c.req.query("semester");
    const status = c.req.query("status");

    let filteredTimetables = [...mockTimetables];

    if (classId) {
      filteredTimetables = filteredTimetables.filter((tt) => tt.classId === classId);
    }

    if (teacherId) {
      filteredTimetables = filteredTimetables.filter((tt) => tt.teacherId === teacherId);
    }

    if (subject) {
      filteredTimetables = filteredTimetables.filter((tt) => 
        tt.subject.toLowerCase().includes(subject.toLowerCase())
      );
    }

    if (day) {
      filteredTimetables = filteredTimetables.filter((tt) => tt.day === day);
    }

    if (academicYear) {
      filteredTimetables = filteredTimetables.filter((tt) => tt.academicYear === academicYear);
    }

    if (semester) {
      filteredTimetables = filteredTimetables.filter((tt) => tt.semester === semester);
    }

    if (status) {
      filteredTimetables = filteredTimetables.filter((tt) => tt.status === status);
    }

    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedTimetables = filteredTimetables.slice(startIndex, endIndex);

    return c.json({
      data: paginatedTimetables,
      pagination: {
        page,
        limit,
        total: filteredTimetables.length,
        totalPages: Math.ceil(filteredTimetables.length / limit),
      },
    });
  })
  .post(
    "/",
    zValidator("json", timetableSchema.omit({ id: true, createdAt: true, updatedAt: true })),
    async (c) => {
      const values = c.req.valid("json");
      
      // Check for conflicts
      const conflicts = mockTimetables.filter(tt => 
        tt.classId === values.classId &&
        tt.day === values.day &&
        tt.status === "active" &&
        ((values.startTime >= tt.startTime && values.startTime < tt.endTime) ||
         (values.endTime > tt.startTime && values.endTime <= tt.endTime) ||
         (values.startTime <= tt.startTime && values.endTime >= tt.endTime))
      );

      if (conflicts.length > 0) {
        return c.json({ error: "Time slot conflict detected" }, 400);
      }

      const newTimetable = {
        ...values,
        id: generateId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      mockTimetables.push(newTimetable);

      return c.json({ data: newTimetable }, 201);
    }
  )
  .get("/:id", async (c) => {
    const id = c.req.param("id");
    const timetable = mockTimetables.find((tt) => tt.id === id);

    if (!timetable) {
      return c.json({ error: "Timetable entry not found" }, 404);
    }

    return c.json({ data: timetable });
  })
  .put(
    "/:id",
    zValidator("json", timetableSchema.omit({ id: true, createdAt: true, updatedAt: true })),
    async (c) => {
      const id = c.req.param("id");
      const values = c.req.valid("json");

      const timetableIndex = mockTimetables.findIndex((tt) => tt.id === id);

      if (timetableIndex === -1) {
        return c.json({ error: "Timetable entry not found" }, 404);
      }

      // Check for conflicts (excluding current entry)
      const conflicts = mockTimetables.filter(tt => 
        tt.id !== id &&
        tt.classId === values.classId &&
        tt.day === values.day &&
        tt.status === "active" &&
        ((values.startTime >= tt.startTime && values.startTime < tt.endTime) ||
         (values.endTime > tt.startTime && values.endTime <= tt.endTime) ||
         (values.startTime <= tt.startTime && values.endTime >= tt.endTime))
      );

      if (conflicts.length > 0) {
        return c.json({ error: "Time slot conflict detected" }, 400);
      }

      mockTimetables[timetableIndex] = {
        ...mockTimetables[timetableIndex],
        ...values,
        updatedAt: new Date().toISOString(),
      };

      return c.json({ data: mockTimetables[timetableIndex] });
    }
  )
  .delete("/:id", async (c) => {
    const id = c.req.param("id");
    const timetableIndex = mockTimetables.findIndex((tt) => tt.id === id);

    if (timetableIndex === -1) {
      return c.json({ error: "Timetable entry not found" }, 404);
    }

    mockTimetables.splice(timetableIndex, 1);

    return c.json({ message: "Timetable entry deleted successfully" });
  })

  // Get timetable for a specific class
  .get("/class/:classId", async (c) => {
    const classId = c.req.param("classId");
    const academicYear = c.req.query("academicYear");
    const semester = c.req.query("semester");

    let classTimetables = mockTimetables.filter(tt => 
      tt.classId === classId && tt.status === "active"
    );

    if (academicYear) {
      classTimetables = classTimetables.filter(tt => tt.academicYear === academicYear);
    }

    if (semester) {
      classTimetables = classTimetables.filter(tt => tt.semester === semester);
    }

    // Group by day and sort by time
    const timetableByDay = classTimetables.reduce((acc, tt) => {
      if (!acc[tt.day]) {
        acc[tt.day] = [];
      }
      acc[tt.day].push(tt);
      return acc;
    }, {} as Record<string, typeof classTimetables>);

    // Sort each day's timetable by start time
    Object.keys(timetableByDay).forEach(day => {
      timetableByDay[day].sort((a, b) => a.startTime.localeCompare(b.startTime));
    });

    return c.json({ data: timetableByDay });
  })

  // Get timetable for a specific teacher
  .get("/teacher/:teacherId", async (c) => {
    const teacherId = c.req.param("teacherId");
    const academicYear = c.req.query("academicYear");
    const semester = c.req.query("semester");

    let teacherTimetables = mockTimetables.filter(tt => 
      tt.teacherId === teacherId && tt.status === "active"
    );

    if (academicYear) {
      teacherTimetables = teacherTimetables.filter(tt => tt.academicYear === academicYear);
    }

    if (semester) {
      teacherTimetables = teacherTimetables.filter(tt => tt.semester === semester);
    }

    // Group by day and sort by time
    const timetableByDay = teacherTimetables.reduce((acc, tt) => {
      if (!acc[tt.day]) {
        acc[tt.day] = [];
      }
      acc[tt.day].push(tt);
      return acc;
    }, {} as Record<string, typeof teacherTimetables>);

    // Sort each day's timetable by start time
    Object.keys(timetableByDay).forEach(day => {
      timetableByDay[day].sort((a, b) => a.startTime.localeCompare(b.startTime));
    });

    return c.json({ data: timetableByDay });
  })

  // Get today's schedule for a class
  .get("/today/:classId", async (c) => {
    const classId = c.req.param("classId");
    const today = new Date();
    const dayNames = ["sunday", "monday", "tuesday", "wednesday", "thursday", "friday", "saturday"];
    const currentDay = dayNames[today.getDay()];

    const todaySchedule = mockTimetables.filter(tt => 
      tt.classId === classId && 
      tt.day === currentDay && 
      tt.status === "active"
    );

    // Sort by start time
    todaySchedule.sort((a, b) => a.startTime.localeCompare(b.startTime));

    return c.json({ data: todaySchedule });
  })

  // Get weekly schedule summary
  .get("/weekly-summary", async (c) => {
    const academicYear = c.req.query("academicYear");
    const semester = c.req.query("semester");

    let timetables = mockTimetables.filter(tt => tt.status === "active");

    if (academicYear) {
      timetables = timetables.filter(tt => tt.academicYear === academicYear);
    }

    if (semester) {
      timetables = timetables.filter(tt => tt.semester === semester);
    }

    const summary = {
      totalClasses: timetables.length,
      classesByDay: timetables.reduce((acc, tt) => {
        acc[tt.day] = (acc[tt.day] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      subjectDistribution: timetables.reduce((acc, tt) => {
        acc[tt.subject] = (acc[tt.subject] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      roomUtilization: timetables.reduce((acc, tt) => {
        acc[tt.room] = (acc[tt.room] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
      teacherWorkload: timetables.reduce((acc, tt) => {
        acc[tt.teacherId] = (acc[tt.teacherId] || 0) + 1;
        return acc;
      }, {} as Record<string, number>),
    };

    return c.json({ data: summary });
  })

  // Check for conflicts
  .post("/check-conflicts", async (c) => {
    const { classId, teacherId, day, startTime, endTime, excludeId } = await c.req.json();

    const conflicts = [];

    // Check class conflicts
    if (classId) {
      const classConflicts = mockTimetables.filter(tt => 
        tt.id !== excludeId &&
        tt.classId === classId &&
        tt.day === day &&
        tt.status === "active" &&
        ((startTime >= tt.startTime && startTime < tt.endTime) ||
         (endTime > tt.startTime && endTime <= tt.endTime) ||
         (startTime <= tt.startTime && endTime >= tt.endTime))
      );
      
      if (classConflicts.length > 0) {
        conflicts.push({
          type: "class",
          message: "Class already has a scheduled period at this time",
          conflicts: classConflicts
        });
      }
    }

    // Check teacher conflicts
    if (teacherId) {
      const teacherConflicts = mockTimetables.filter(tt => 
        tt.id !== excludeId &&
        tt.teacherId === teacherId &&
        tt.day === day &&
        tt.status === "active" &&
        ((startTime >= tt.startTime && startTime < tt.endTime) ||
         (endTime > tt.startTime && endTime <= tt.endTime) ||
         (startTime <= tt.startTime && endTime >= tt.endTime))
      );
      
      if (teacherConflicts.length > 0) {
        conflicts.push({
          type: "teacher",
          message: "Teacher already has a class scheduled at this time",
          conflicts: teacherConflicts
        });
      }
    }

    return c.json({ 
      hasConflicts: conflicts.length > 0,
      conflicts 
    });
  });

export default app;
