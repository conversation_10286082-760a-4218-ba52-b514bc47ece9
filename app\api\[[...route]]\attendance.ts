import { Hono } from "hono";
import { zValidator } from "@hono/zod-validator";
import { insertAttendanceSchema } from "@/lib/schemas";
import { mockAttendance } from "@/lib/mock-db";
import { generateId } from "@/lib/utils";

const app = new Hono()
  .get("/", async (c) => {
    const page = parseInt(c.req.query("page") || "1");
    const limit = parseInt(c.req.query("limit") || "10");
    const studentId = c.req.query("studentId");
    const classId = c.req.query("classId");
    const date = c.req.query("date");
    const status = c.req.query("status");

    let filteredAttendance = [...mockAttendance];

    if (studentId) {
      filteredAttendance = filteredAttendance.filter(
        (att) => att.studentId === studentId
      );
    }

    if (classId) {
      filteredAttendance = filteredAttendance.filter(
        (att) => att.classId === classId
      );
    }

    if (date) {
      filteredAttendance = filteredAttendance.filter(
        (att) => att.date === date
      );
    }

    if (status) {
      filteredAttendance = filteredAttendance.filter(
        (att) => att.status === status
      );
    }

    const offset = (page - 1) * limit;
    const paginatedAttendance = filteredAttendance.slice(offset, offset + limit);

    const meta = {
      page,
      limit,
      total: filteredAttendance.length,
      totalPages: Math.ceil(filteredAttendance.length / limit),
      hasNext: page < Math.ceil(filteredAttendance.length / limit),
      hasPrev: page > 1,
    };

    return c.json({ data: paginatedAttendance, meta });
  })
  .get("/:id", async (c) => {
    const id = c.req.param("id");
    const attendance = mockAttendance.find((att) => att.id === id);

    if (!attendance) {
      return c.json({ error: "Attendance record not found" }, 404);
    }

    return c.json({ data: attendance });
  })
  .post(
    "/",
    zValidator("json", insertAttendanceSchema),
    async (c) => {
      const values = c.req.valid("json");

      const newAttendance = {
        id: generateId(),
        ...values,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      mockAttendance.push(newAttendance);

      return c.json({ data: newAttendance }, 201);
    }
  )
  .put(
    "/:id",
    zValidator("json", insertAttendanceSchema),
    async (c) => {
      const id = c.req.param("id");
      const values = c.req.valid("json");

      const attendanceIndex = mockAttendance.findIndex((att) => att.id === id);
      if (attendanceIndex === -1) {
        return c.json({ error: "Attendance record not found" }, 404);
      }

      const updatedAttendance = {
        ...mockAttendance[attendanceIndex],
        ...values,
        updatedAt: new Date().toISOString(),
      };

      mockAttendance[attendanceIndex] = updatedAttendance;

      return c.json({ data: updatedAttendance });
    }
  )
  .delete("/:id", async (c) => {
    const id = c.req.param("id");
    const attendanceIndex = mockAttendance.findIndex((att) => att.id === id);

    if (attendanceIndex === -1) {
      return c.json({ error: "Attendance record not found" }, 404);
    }

    const deletedAttendance = mockAttendance.splice(attendanceIndex, 1)[0];
    return c.json({ data: deletedAttendance });
  });

export default app;
