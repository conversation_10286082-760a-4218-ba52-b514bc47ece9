"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  BarChart3,
  FileText,
  Download,
  Calendar,
  Users,
  Award,
  TrendingUp,
  TrendingDown,
  CheckCircle,
  AlertTriangle,
  Clock,
  Target,
  PieChart,
  Filter,
  RefreshCw,
  Eye,
  Share,
  Printer,
  BookOpen,
} from "lucide-react";

export default function TeacherReports() {
  const [user, setUser] = useState<any>(null);
  const [selectedPeriod, setSelectedPeriod] = useState("monthly");
  const [selectedClass, setSelectedClass] = useState("all");
  const [selectedReport, setSelectedReport] = useState("performance");
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "teacher") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock data
  const teacherClasses = [
    { id: "10A", name: "Grade 10A", subject: "Mathematics", students: 45 },
    { id: "10B", name: "Grade 10B", subject: "Mathematics", students: 44 },
    { id: "11A", name: "Grade 11A", subject: "Algebra", students: 43 },
    { id: "12A", name: "Grade 12A", subject: "Statistics", students: 41 },
  ];

  const reportStats = {
    totalReports: 24,
    generatedThisMonth: 8,
    studentsTracked: 173,
    averagePerformance: 78.5,
    improvementRate: 12.3,
    attendanceRate: 92.1,
  };

  const performanceData = [
    { class: "Grade 10A", avgScore: 82.5, attendance: 94.2, assignments: 12, improvement: "+5.2%" },
    { class: "Grade 10B", avgScore: 78.3, attendance: 91.8, assignments: 12, improvement: "+3.1%" },
    { class: "Grade 11A", avgScore: 85.1, attendance: 93.5, assignments: 10, improvement: "+7.8%" },
    { class: "Grade 12A", avgScore: 88.7, attendance: 95.1, assignments: 8, improvement: "+4.5%" },
  ];

  const studentProgress = [
    { name: "John Doe", class: "Grade 10A", currentGrade: "A", previousGrade: "B+", trend: "up", improvement: "+8%" },
    { name: "Alice Smith", class: "Grade 10A", currentGrade: "A+", previousGrade: "A", trend: "up", improvement: "+3%" },
    { name: "Michael Johnson", class: "Grade 11A", currentGrade: "B", previousGrade: "C+", trend: "up", improvement: "+15%" },
    { name: "Emma Davis", class: "Grade 12A", currentGrade: "A+", previousGrade: "A+", trend: "stable", improvement: "0%" },
    { name: "Robert Wilson", class: "Grade 10B", currentGrade: "B+", previousGrade: "A", trend: "down", improvement: "-5%" },
  ];

  const attendanceReports = [
    { class: "Grade 10A", totalClasses: 20, avgAttendance: 94.2, perfectAttendance: 8, lowAttendance: 2 },
    { class: "Grade 10B", totalClasses: 20, avgAttendance: 91.8, perfectAttendance: 6, lowAttendance: 4 },
    { class: "Grade 11A", totalClasses: 18, avgAttendance: 93.5, perfectAttendance: 7, lowAttendance: 3 },
    { class: "Grade 12A", totalClasses: 16, avgAttendance: 95.1, perfectAttendance: 9, lowAttendance: 1 },
  ];

  const assignmentReports = [
    { title: "Mid-term Exam", class: "Grade 10A", submitted: 43, total: 45, avgScore: 78.5, topScore: 96 },
    { title: "Quadratic Equations", class: "Grade 10B", submitted: 42, total: 44, avgScore: 82.1, topScore: 98 },
    { title: "Algebra Test", class: "Grade 11A", submitted: 41, total: 43, avgScore: 85.3, topScore: 100 },
    { title: "Statistics Project", class: "Grade 12A", submitted: 39, total: 41, avgScore: 88.7, topScore: 95 },
  ];

  const quickReports = [
    { name: "Class Performance", icon: BarChart3, color: "bg-blue-500", description: "Overall class performance summary" },
    { name: "Attendance Report", icon: CheckCircle, color: "bg-green-500", description: "Student attendance analysis" },
    { name: "Grade Distribution", icon: PieChart, color: "bg-purple-500", description: "Grade distribution across classes" },
    { name: "Student Progress", icon: TrendingUp, color: "bg-orange-500", description: "Individual student progress tracking" },
    { name: "Assignment Analysis", icon: FileText, color: "bg-indigo-500", description: "Assignment submission and scores" },
    { name: "Parent Report", icon: Users, color: "bg-pink-500", description: "Reports for parent meetings" },
  ];

  const getGradeColor = (grade: string) => {
    switch (grade) {
      case "A+": return "text-green-600 bg-green-100";
      case "A": return "text-blue-600 bg-blue-100";
      case "B+": return "text-yellow-600 bg-yellow-100";
      case "B": return "text-orange-600 bg-orange-100";
      case "C+": return "text-red-600 bg-red-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case "up": return <TrendingUp className="h-4 w-4 text-green-500" />;
      case "down": return <TrendingDown className="h-4 w-4 text-red-500" />;
      default: return <Target className="h-4 w-4 text-gray-500" />;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case "up": return "text-green-600";
      case "down": return "text-red-600";
      default: return "text-gray-600";
    }
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Student Reports</h1>
            <p className="text-gray-600">Generate and analyze student progress reports</p>
          </div>
          <div className="flex gap-2">
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="weekly">Weekly</option>
              <option value="monthly">Monthly</option>
              <option value="quarterly">Quarterly</option>
              <option value="yearly">Yearly</option>
            </select>
            <Button variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button>
              <FileText className="h-4 w-4 mr-2" />
              Generate Report
            </Button>
          </div>
        </div>

        {/* Report Overview Stats */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <FileText className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {reportStats.totalReports}
                  </div>
                  <p className="text-sm text-gray-600">Total Reports</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {reportStats.studentsTracked}
                  </div>
                  <p className="text-sm text-gray-600">Students Tracked</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Award className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {reportStats.averagePerformance}%
                  </div>
                  <p className="text-sm text-gray-600">Avg Performance</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600">+{reportStats.improvementRate}%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    {reportStats.attendanceRate}%
                  </div>
                  <p className="text-sm text-gray-600">Attendance Rate</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600">+2.1%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Report Generation */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Reports</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3 lg:grid-cols-6">
              {quickReports.map((report, index) => (
                <Button
                  key={index}
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center space-y-2"
                >
                  <div className={`w-12 h-12 ${report.color} rounded-lg flex items-center justify-center`}>
                    <report.icon className="h-6 w-6 text-white" />
                  </div>
                  <div className="text-center">
                    <div className="font-medium text-sm">{report.name}</div>
                    <div className="text-xs text-gray-500">{report.description}</div>
                  </div>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Report Tabs */}
        <Card>
          <CardHeader>
            <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
              {[
                { key: "performance", label: "Performance" },
                { key: "attendance", label: "Attendance" },
                { key: "assignments", label: "Assignments" },
                { key: "progress", label: "Progress" },
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setSelectedReport(tab.key)}
                  className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                    selectedReport === tab.key
                      ? "bg-white text-blue-600 shadow-sm"
                      : "text-gray-600 hover:text-gray-900"
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </div>
          </CardHeader>
          <CardContent>
            {/* Performance Report */}
            {selectedReport === "performance" && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Class Performance Overview</h3>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Class</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Avg Score</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Attendance</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Assignments</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Improvement</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {performanceData.map((classData, index) => (
                        <tr key={index} className="border-b hover:bg-gray-50">
                          <td className="py-3 px-4 font-medium text-gray-900">{classData.class}</td>
                          <td className="py-3 px-4">
                            <span className={`font-medium ${
                              classData.avgScore >= 85 ? 'text-green-600' :
                              classData.avgScore >= 75 ? 'text-blue-600' :
                              classData.avgScore >= 65 ? 'text-yellow-600' : 'text-red-600'
                            }`}>
                              {classData.avgScore}%
                            </span>
                          </td>
                          <td className="py-3 px-4 text-gray-600">{classData.attendance}%</td>
                          <td className="py-3 px-4 text-gray-600">{classData.assignments}</td>
                          <td className="py-3 px-4">
                            <span className="text-green-600 font-medium">{classData.improvement}</span>
                          </td>
                          <td className="py-3 px-4">
                            <div className="flex gap-2">
                              <Button variant="outline" size="sm">
                                <Eye className="h-3 w-3" />
                              </Button>
                              <Button variant="outline" size="sm">
                                <Download className="h-3 w-3" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* Attendance Report */}
            {selectedReport === "attendance" && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Attendance Analysis</h3>
                <div className="grid gap-4 md:grid-cols-2">
                  {attendanceReports.map((report, index) => (
                    <div key={index} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium text-gray-900">{report.class}</h4>
                        <span className={`text-sm font-bold ${
                          report.avgAttendance >= 95 ? 'text-green-600' :
                          report.avgAttendance >= 90 ? 'text-blue-600' :
                          report.avgAttendance >= 85 ? 'text-yellow-600' : 'text-red-600'
                        }`}>
                          {report.avgAttendance}%
                        </span>
                      </div>
                      
                      <div className="grid gap-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Total Classes:</span>
                          <span className="font-medium">{report.totalClasses}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Perfect Attendance:</span>
                          <span className="font-medium text-green-600">{report.perfectAttendance}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Low Attendance:</span>
                          <span className="font-medium text-red-600">{report.lowAttendance}</span>
                        </div>
                      </div>

                      <div className="w-full bg-gray-200 rounded-full h-2 mt-3">
                        <div 
                          className={`h-2 rounded-full ${
                            report.avgAttendance >= 95 ? 'bg-green-500' :
                            report.avgAttendance >= 90 ? 'bg-blue-500' :
                            report.avgAttendance >= 85 ? 'bg-yellow-500' : 'bg-red-500'
                          }`}
                          style={{ width: `${report.avgAttendance}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Assignments Report */}
            {selectedReport === "assignments" && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Assignment Analysis</h3>
                <div className="space-y-4">
                  {assignmentReports.map((assignment, index) => (
                    <div key={index} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-3">
                        <div>
                          <h4 className="font-medium text-gray-900">{assignment.title}</h4>
                          <p className="text-sm text-gray-600">{assignment.class}</p>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-bold text-blue-600">{assignment.avgScore}%</div>
                          <div className="text-sm text-gray-500">Average Score</div>
                        </div>
                      </div>
                      
                      <div className="grid gap-3 md:grid-cols-4 text-sm">
                        <div className="text-center">
                          <div className="text-lg font-bold text-green-600">{assignment.submitted}</div>
                          <div className="text-gray-500">Submitted</div>
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-bold text-blue-600">{assignment.total}</div>
                          <div className="text-gray-500">Total Students</div>
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-bold text-purple-600">{assignment.topScore}</div>
                          <div className="text-gray-500">Top Score</div>
                        </div>
                        <div className="text-center">
                          <div className="text-lg font-bold text-orange-600">
                            {((assignment.submitted / assignment.total) * 100).toFixed(1)}%
                          </div>
                          <div className="text-gray-500">Submission Rate</div>
                        </div>
                      </div>

                      <div className="w-full bg-gray-200 rounded-full h-2 mt-3">
                        <div 
                          className="bg-blue-500 h-2 rounded-full"
                          style={{ width: `${(assignment.submitted / assignment.total) * 100}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Progress Report */}
            {selectedReport === "progress" && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Student Progress Tracking</h3>
                <div className="space-y-3">
                  {studentProgress.map((student, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                          <span className="font-medium text-blue-600">
                            {student.name.split(' ').map(n => n[0]).join('')}
                          </span>
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">{student.name}</div>
                          <div className="text-sm text-gray-600">{student.class}</div>
                        </div>
                      </div>
                      
                      <div className="flex items-center space-x-6">
                        <div className="text-center">
                          <div className="text-sm text-gray-500">Previous</div>
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getGradeColor(student.previousGrade)}`}>
                            {student.previousGrade}
                          </span>
                        </div>
                        
                        <div className="flex items-center">
                          {getTrendIcon(student.trend)}
                        </div>
                        
                        <div className="text-center">
                          <div className="text-sm text-gray-500">Current</div>
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getGradeColor(student.currentGrade)}`}>
                            {student.currentGrade}
                          </span>
                        </div>
                        
                        <div className="text-center">
                          <div className="text-sm text-gray-500">Change</div>
                          <span className={`font-medium ${getTrendColor(student.trend)}`}>
                            {student.improvement}
                          </span>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
