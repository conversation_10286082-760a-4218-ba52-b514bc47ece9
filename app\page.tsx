"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { getDashboardPathForRole } from "@/lib/navigation-config";

export default function Home() {
  const router = useRouter();

  useEffect(() => {
    // Check if user is logged in
    const user = localStorage.getItem("user");
    if (!user) {
      router.push("/login");
    } else {
      try {
        // Redirect to appropriate dashboard based on role
        const userData = JSON.parse(user);
        const redirectPath = getDashboardPathForRole(userData.role);
        router.push(redirectPath);
      } catch (error) {
        // Invalid user data, clear and redirect to login
        localStorage.removeItem("user");
        localStorage.removeItem("token");
        router.push("/login");
      }
    }
  }, [router]);

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 to-indigo-100 flex items-center justify-center">
      <div className="text-center">
        <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <p className="text-gray-600">Loading...</p>
      </div>
    </div>
  );
}
