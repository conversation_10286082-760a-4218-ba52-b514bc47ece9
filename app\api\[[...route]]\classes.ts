import { <PERSON><PERSON> } from "hono";
import { z<PERSON>alida<PERSON> } from "@hono/zod-validator";
import { z } from "zod";
import { db } from "@/lib/db";
import {
  classes,
  academicPrograms,
  academicBatches,
  teachers,
  users,
  students,
  classEnrollments
} from "@/lib/db/schema";
import { eq, count, and, sql } from "drizzle-orm";

// Enhanced class schema for the new database structure
const enhancedClassSchema = z.object({
  name: z.string().min(2, "Class name must be at least 2 characters"),
  programId: z.string().uuid("Invalid program ID"),
  batchId: z.string().uuid("Invalid batch ID"),
  grade: z.string().min(1, "Grade is required"),
  section: z.string().min(1, "Section is required"),
  subject: z.string().min(1, "Subject is required"),
  teacherId: z.string().uuid("Invalid teacher ID"),
  assistantTeacherId: z.string().uuid().optional(),
  classType: z.enum(["theory", "practical", "lab", "tutorial"]).optional(),
  room: z.string().optional(),
  capacity: z.number().min(1, "Capacity must be at least 1"),
  academicYear: z.string().min(1, "Academic year is required"),
  // Removed semester, credits, isElective, prerequisites - school-only mode doesn't use these
});

const updateClassSchema = z.object({
  name: z.string().min(2, "Class name must be at least 2 characters").optional(),
  programId: z.string().uuid("Invalid program ID").optional(),
  batchId: z.string().uuid("Invalid batch ID").optional(),
  grade: z.string().min(1, "Grade is required").optional(),
  section: z.string().min(1, "Section is required").optional(),
  subject: z.string().min(1, "Subject is required").optional(),
  teacherId: z.string().uuid("Invalid teacher ID").optional(),
  assistantTeacherId: z.string().uuid().optional(),
  classType: z.enum(["theory", "practical", "lab", "tutorial"]).optional(),
  room: z.string().optional(),
  capacity: z.number().min(1, "Capacity must be at least 1").optional(),
  academicYear: z.string().min(1, "Academic year is required").optional(),
  // Removed semester, credits, isElective, prerequisites - school-only mode doesn't use these
});

const app = new Hono()
  .get("/", async (c) => {
    try {
      const page = parseInt(c.req.query("page") || "1");
      const limit = parseInt(c.req.query("limit") || "10");
      const search = c.req.query("search");
      const grade = c.req.query("grade");
      const programId = c.req.query("programId");
      const batchId = c.req.query("batchId");
      const teacherId = c.req.query("teacherId");
      const status = c.req.query("status");

      // Build query conditions
      let whereConditions = [];

      if (grade) {
        whereConditions.push(eq(classes.grade, grade));
      }

      if (programId) {
        whereConditions.push(eq(classes.programId, programId));
      }

      if (batchId) {
        whereConditions.push(eq(classes.batchId, batchId));
      }

      if (teacherId) {
        whereConditions.push(eq(classes.teacherId, teacherId));
      }

      if (status) {
        whereConditions.push(eq(classes.status, status as any));
      }

      // For development with mock data, provide fallback when database query fails
      let allClasses: any[];
      try {
        // Get classes with related data
        allClasses = await db
          .select({
            id: classes.id,
            name: classes.name,
            grade: classes.grade,
            section: classes.section,
            subject: classes.subject,
            classType: classes.classType,
            room: classes.room,
            capacity: classes.capacity,
            enrolledStudents: classes.enrolledStudents,
            availableSeats: classes.availableSeats,
            academicYear: classes.academicYear,
            // Removed semester - school-only mode doesn't use semesters
            // Removed credits - school-only mode doesn't use credits
            // Removed isElective - school-only mode doesn't use electives
            // Removed prerequisites - school-only mode doesn't use prerequisites
            status: classes.status,
            createdAt: classes.createdAt,
            updatedAt: classes.updatedAt,
            // Program details
            programName: academicPrograms.name,
            programCode: academicPrograms.code,
            // Batch details
            batchName: academicBatches.batchName,
            // Teacher details
            teacherName: sql<string>`CONCAT(${users.firstName}, ' ', ${users.lastName})`,
            teacherEmail: users.email,
          })
          .from(classes)
          .leftJoin(academicPrograms, eq(classes.programId, academicPrograms.id))
          .leftJoin(academicBatches, eq(classes.batchId, academicBatches.id))
          .leftJoin(teachers, eq(classes.teacherId, teachers.id))
          .leftJoin(users, eq(teachers.userId, users.id))
          .where(whereConditions.length > 0 ? and(...whereConditions) : undefined);
      } catch (dbError) {
        console.log("Database query failed, using mock data for classes");

        // Return mock classes for development
        if (teacherId) {
          // Mock classes for specific teacher
          allClasses = [
            {
              id: "CLASS001",
              name: "10A - Mathematics",
              grade: "10",
              section: "A",
              subject: "Mathematics",
              classType: "core",
              room: "Room 101",
              capacity: 35,
              enrolledStudents: 32,
              availableSeats: 3,
              academicYear: "2024-25",
              // Removed semester, credits, isElective, prerequisites - school-only mode doesn't use these
              status: "active",
              createdAt: new Date(),
              updatedAt: new Date(),
              programName: "Secondary Education",
              programCode: "SEC",
              batchName: "2024-26",
              teacherName: "John Smith",
              teacherEmail: "<EMAIL>",
            },
            {
              id: "CLASS002",
              name: "10B - Mathematics",
              grade: "10",
              section: "B",
              subject: "Mathematics",
              classType: "core",
              room: "Room 102",
              capacity: 35,
              enrolledStudents: 28,
              availableSeats: 7,
              academicYear: "2024-25",
              // Removed semester, credits, isElective, prerequisites - school-only mode doesn't use these
              status: "active",
              createdAt: new Date(),
              updatedAt: new Date(),
              programName: "Secondary Education",
              programCode: "SEC",
              batchName: "2024-26",
              teacherName: "John Smith",
              teacherEmail: "<EMAIL>",
            },
            {
              id: "CLASS003",
              name: "11A - Advanced Mathematics",
              grade: "11",
              section: "A",
              subject: "Advanced Mathematics",
              classType: "core",
              room: "Room 201",
              capacity: 30,
              enrolledStudents: 25,
              availableSeats: 5,
              academicYear: "2024-25",
              // Removed semester, credits, isElective, prerequisites - school-only mode doesn't use these
              status: "active",
              createdAt: new Date(),
              updatedAt: new Date(),
              programName: "Higher Secondary Education",
              programCode: "HSE",
              batchName: "2023-25",
              teacherName: "John Smith",
              teacherEmail: "<EMAIL>",
            },
          ];
        } else {
          // Mock classes for general listing
          allClasses = [];
        }
      }

      // Apply search filter if provided
      let filteredClasses = allClasses;
      if (search) {
        const searchLower = search.toLowerCase();
        filteredClasses = allClasses.filter(cls =>
          cls.name.toLowerCase().includes(searchLower) ||
          cls.subject.toLowerCase().includes(searchLower) ||
          cls.room?.toLowerCase().includes(searchLower) ||
          cls.teacherName?.toLowerCase().includes(searchLower) ||
          cls.programName?.toLowerCase().includes(searchLower)
        );
      }

      // Get enrollment statistics for each class
      const classesWithStats = await Promise.all(
        filteredClasses.map(async (classItem) => {
          try {
            // Get current enrollment count
            const [enrollmentCount] = await db
              .select({ count: count() })
              .from(classEnrollments)
              .where(
                and(
                  eq(classEnrollments.classId, classItem.id),
                  eq(classEnrollments.status, "active")
                )
              );

            // Get student type breakdown
            const studentTypes = await db
              .select({
                studentType: students.studentType,
                count: count(),
              })
              .from(classEnrollments)
              .leftJoin(students, eq(classEnrollments.studentId, students.id))
              .where(
                and(
                  eq(classEnrollments.classId, classItem.id),
                  eq(classEnrollments.status, "active")
                )
              )
              .groupBy(students.studentType);

            return {
              ...classItem,
              currentEnrollment: enrollmentCount.count,
              studentTypeBreakdown: studentTypes,
              occupancyRate: classItem.capacity > 0
                ? Math.round((enrollmentCount.count / classItem.capacity) * 100)
                : 0,
            };
          } catch (statsError) {
            // For mock data, use the enrolledStudents field directly
            return {
              ...classItem,
              currentEnrollment: classItem.enrolledStudents || 0,
              studentTypeBreakdown: [
                { studentType: "regular", count: Math.floor((classItem.enrolledStudents || 0) * 0.8) },
                { studentType: "lateral", count: Math.floor((classItem.enrolledStudents || 0) * 0.15) },
                { studentType: "distance", count: Math.floor((classItem.enrolledStudents || 0) * 0.05) },
              ],
              occupancyRate: classItem.capacity > 0
                ? Math.round(((classItem.enrolledStudents || 0) / classItem.capacity) * 100)
                : 0,
            };
          }
        })
      );

      // Pagination
      const offset = (page - 1) * limit;
      const paginatedClasses = classesWithStats.slice(offset, offset + limit);

      const meta = {
        page,
        limit,
        total: filteredClasses.length,
        totalPages: Math.ceil(filteredClasses.length / limit),
        hasNext: page < Math.ceil(filteredClasses.length / limit),
        hasPrev: page > 1,
      };

      return c.json({ data: paginatedClasses, meta });
    } catch (error) {
      console.error("Error fetching classes:", error);
      return c.json({ error: "Failed to fetch classes" }, 500);
    }
  })
  .get("/:id", async (c) => {
    try {
      const id = c.req.param("id");

      const [classItem] = await db
        .select({
          id: classes.id,
          name: classes.name,
          grade: classes.grade,
          section: classes.section,
          subject: classes.subject,
          classType: classes.classType,
          room: classes.room,
          capacity: classes.capacity,
          enrolledStudents: classes.enrolledStudents,
          availableSeats: classes.availableSeats,
          academicYear: classes.academicYear,
          // Removed semester, credits, isElective, prerequisites - school-only mode doesn't use these
          status: classes.status,
          createdAt: classes.createdAt,
          updatedAt: classes.updatedAt,
          // Program details
          programName: academicPrograms.name,
          programCode: academicPrograms.code,
          // Batch details
          batchName: academicBatches.batchName,
          // Teacher details
          teacherName: sql<string>`CONCAT(${users.firstName}, ' ', ${users.lastName})`,
          teacherEmail: users.email,
        })
        .from(classes)
        .leftJoin(academicPrograms, eq(classes.programId, academicPrograms.id))
        .leftJoin(academicBatches, eq(classes.batchId, academicBatches.id))
        .leftJoin(teachers, eq(classes.teacherId, teachers.id))
        .leftJoin(users, eq(teachers.userId, users.id))
        .where(eq(classes.id, id));

      if (!classItem) {
        return c.json({ error: "Class not found" }, 404);
      }

      // Get enrolled students
      const enrolledStudents = await db
        .select({
          studentId: students.id,
          rollNumber: students.rollNumber,
          firstName: users.firstName,
          lastName: users.lastName,
          studentType: students.studentType,
          isLateralEntry: students.isLateralEntry,
          enrollmentDate: classEnrollments.enrollmentDate,
        })
        .from(classEnrollments)
        .leftJoin(students, eq(classEnrollments.studentId, students.id))
        .leftJoin(users, eq(students.userId, users.id))
        .where(
          and(
            eq(classEnrollments.classId, id),
            eq(classEnrollments.status, "active")
          )
        )
        .orderBy(students.rollNumber);

      return c.json({
        data: {
          ...classItem,
          enrolledStudents,
          currentEnrollment: enrolledStudents.length,
        },
      });
    } catch (error) {
      console.error("Error fetching class:", error);
      return c.json({ error: "Failed to fetch class" }, 500);
    }
  })
  .post(
    "/",
    zValidator("json", enhancedClassSchema),
    async (c) => {
      try {
        const values = c.req.valid("json");

        // Validate program and batch exist
        const [program] = await db
          .select()
          .from(academicPrograms)
          .where(eq(academicPrograms.id, values.programId));

        if (!program) {
          return c.json({ error: "Academic program not found" }, 404);
        }

        const [batch] = await db
          .select()
          .from(academicBatches)
          .where(eq(academicBatches.id, values.batchId));

        if (!batch) {
          return c.json({ error: "Academic batch not found" }, 404);
        }

        // Validate teacher exists and check workload
        const [teacher] = await db
          .select()
          .from(teachers)
          .where(eq(teachers.id, values.teacherId));

        if (!teacher) {
          return c.json({ error: "Teacher not found" }, 404);
        }

        // Check teacher workload
        const currentLoad = teacher.currentClassLoad || 0;
        const maxLoad = teacher.maxClassesPerWeek || 10;
        if (currentLoad >= maxLoad) {
          return c.json({ error: "Teacher has reached maximum class load" }, 400);
        }

        // Create class
        const [newClass] = await db
          .insert(classes)
          .values({
            ...values,
            availableSeats: values.capacity,
            enrolledStudents: 0,
          })
          .returning();

        // Update teacher's current class load
        await db
          .update(teachers)
          .set({
            currentClassLoad: currentLoad + 1,
            updatedAt: new Date(),
          })
          .where(eq(teachers.id, values.teacherId));

        return c.json({ data: newClass }, 201);
      } catch (error) {
        console.error("Error creating class:", error);
        return c.json({ error: "Failed to create class" }, 500);
      }
    }
  )
  .put(
    "/:id",
    zValidator("json", updateClassSchema),
    async (c) => {
      try {
        const id = c.req.param("id");
        const values = c.req.valid("json");

        // Check if class exists
        const [existingClass] = await db
          .select()
          .from(classes)
          .where(eq(classes.id, id));

        if (!existingClass) {
          return c.json({ error: "Class not found" }, 404);
        }

        // If teacher is being changed, validate workload
        if (values.teacherId && values.teacherId !== existingClass.teacherId) {
          const [newTeacher] = await db
            .select()
            .from(teachers)
            .where(eq(teachers.id, values.teacherId));

          if (!newTeacher) {
            return c.json({ error: "New teacher not found" }, 404);
          }

          const newTeacherCurrentLoad = newTeacher.currentClassLoad || 0;
          const newTeacherMaxLoad = newTeacher.maxClassesPerWeek || 10;
          if (newTeacherCurrentLoad >= newTeacherMaxLoad) {
            return c.json({ error: "New teacher has reached maximum class load" }, 400);
          }

          // Update old teacher's load if there was one
          if (existingClass.teacherId) {
            await db
              .update(teachers)
              .set({
                currentClassLoad: sql`${teachers.currentClassLoad} - 1`,
                updatedAt: new Date(),
              })
              .where(eq(teachers.id, existingClass.teacherId));
          }

          // Update new teacher's load
          await db
            .update(teachers)
            .set({
              currentClassLoad: sql`${teachers.currentClassLoad} + 1`,
              updatedAt: new Date(),
            })
            .where(eq(teachers.id, values.teacherId));
        }

        // Update class
        const [updatedClass] = await db
          .update(classes)
          .set({
            ...values,
            updatedAt: new Date(),
          })
          .where(eq(classes.id, id))
          .returning();

        return c.json({ data: updatedClass });
      } catch (error) {
        console.error("Error updating class:", error);
        return c.json({ error: "Failed to update class" }, 500);
      }
    }
  )
  .delete("/:id", async (c) => {
    try {
      const id = c.req.param("id");

      // Get class details before deletion
      const [classItem] = await db
        .select()
        .from(classes)
        .where(eq(classes.id, id));

      if (!classItem) {
        return c.json({ error: "Class not found" }, 404);
      }

      // Check if class has enrolled students
      const [enrollmentCount] = await db
        .select({ count: count() })
        .from(classEnrollments)
        .where(
          and(
            eq(classEnrollments.classId, id),
            eq(classEnrollments.status, "active")
          )
        );

      if (enrollmentCount.count > 0) {
        return c.json({
          error: "Cannot delete class with enrolled students"
        }, 400);
      }

      // Update teacher's class load
      if (classItem.teacherId) {
        await db
          .update(teachers)
          .set({
            currentClassLoad: sql`${teachers.currentClassLoad} - 1`,
            updatedAt: new Date(),
          })
          .where(eq(teachers.id, classItem.teacherId));
      }

      // Delete class
      const [deletedClass] = await db
        .delete(classes)
        .where(eq(classes.id, id))
        .returning();

      return c.json({ data: deletedClass });
    } catch (error) {
      console.error("Error deleting class:", error);
      return c.json({ error: "Failed to delete class" }, 500);
    }
  })

  // Additional endpoint to get classes by program and batch
  .get("/program/:programId/batch/:batchId", async (c) => {
    try {
      const programId = c.req.param("programId");
      const batchId = c.req.param("batchId");

      const classesInBatch = await db
        .select({
          id: classes.id,
          name: classes.name,
          subject: classes.subject,
          section: classes.section,
          teacherName: sql<string>`CONCAT(${users.firstName}, ' ', ${users.lastName})`,
          room: classes.room,
          capacity: classes.capacity,
          enrolledStudents: classes.enrolledStudents,
          classType: classes.classType,
        })
        .from(classes)
        .leftJoin(teachers, eq(classes.teacherId, teachers.id))
        .leftJoin(users, eq(teachers.userId, users.id))
        .where(
          and(
            eq(classes.programId, programId),
            eq(classes.batchId, batchId),
            eq(classes.status, "active")
          )
        )
        .orderBy(classes.section, classes.subject);

      return c.json({ data: classesInBatch });
    } catch (error) {
      console.error("Error fetching classes by batch:", error);
      return c.json({ error: "Failed to fetch classes" }, 500);
    }
  });

export default app;
