"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  User,
  Calendar,
  Award,
  Clock,
  CheckCircle,
  AlertTriangle,
  FileText,
  BookOpen,
  TrendingUp,
  MessageCircle,
  Bell,
  IndianRupee
} from "lucide-react";

export default function ParentDashboard() {
  const [user, setUser] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "parent") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock child data
  const childData = {
    name: "<PERSON>",
    grade: "10A",
    rollNumber: "STU001",
    currentGPA: 3.8,
    attendance: 92,
    pendingFees: 0,
    upcomingExams: 2,
  };

  const recentGrades = [
    { subject: "Mathematics", grade: "A", score: 92, date: "2024-01-15", teacher: "Dr. Emily Wilson" },
    { subject: "Physics", grade: "B+", score: 87, date: "2024-01-12", teacher: "Mr. David Brown" },
    { subject: "Chemistry", grade: "A-", score: 89, date: "2024-01-10", teacher: "Dr. Sarah Johnson" },
    { subject: "English", grade: "A", score: 94, date: "2024-01-08", teacher: "Ms. Lisa Anderson" },
  ];

  const attendanceRecord = [
    { date: "2024-01-15", status: "present", subjects: 6 },
    { date: "2024-01-14", status: "present", subjects: 6 },
    { date: "2024-01-13", status: "late", subjects: 6 },
    { date: "2024-01-12", status: "present", subjects: 6 },
    { date: "2024-01-11", status: "absent", subjects: 6 },
  ];

  const upcomingEvents = [
    {
      title: "Parent-Teacher Meeting",
      date: "2024-01-20",
      time: "2:00 PM",
      type: "meeting",
      teacher: "Dr. Emily Wilson",
    },
    {
      title: "Mathematics Mid-term Exam",
      date: "2024-01-25",
      time: "9:00 AM",
      type: "exam",
      subject: "Mathematics",
    },
    {
      title: "Science Fair",
      date: "2024-01-30",
      time: "10:00 AM",
      type: "event",
      description: "Annual science exhibition",
    },
  ];

  const notifications = [
    {
      title: "Assignment Submitted",
      message: "John submitted Mathematics homework",
      time: "2 hours ago",
      type: "success",
      icon: CheckCircle,
    },
    {
      title: "Upcoming Exam",
      message: "Physics exam scheduled for Jan 25",
      time: "1 day ago",
      type: "warning",
      icon: AlertTriangle,
    },
    {
      title: "Fee Payment Due",
      message: "Monthly fee payment due in 5 days",
      time: "2 days ago",
      type: "info",
      icon: IndianRupee,
    },
  ];

  const weeklySchedule = [
    { day: "Monday", subjects: ["Math", "Physics", "Chemistry", "English"] },
    { day: "Tuesday", subjects: ["Physics", "Math", "Biology", "History"] },
    { day: "Wednesday", subjects: ["Math", "Chemistry", "English", "Physics Lab"] },
    { day: "Thursday", subjects: ["Chemistry", "English", "Math", "Geography"] },
    { day: "Friday", subjects: ["Biology", "Physics", "Chemistry Lab", "PE"] },
  ];

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Welcome Header */}
        <div className="bg-gradient-to-r from-purple-500 to-pink-600 rounded-lg p-6 text-white">
          <h1 className="text-2xl font-bold mb-2">
            Welcome, {user.firstName}!
          </h1>
          <p className="text-purple-100">
            Stay connected with {childData.name}&apos;s academic journey
          </p>
        </div>

        {/* Child Overview Stats */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Award className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-yellow-600">
                    {childData.currentGPA}
                  </div>
                  <p className="text-sm text-gray-600">Current GPA</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {childData.attendance}%
                  </div>
                  <p className="text-sm text-gray-600">Attendance</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Calendar className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {childData.upcomingExams}
                  </div>
                  <p className="text-sm text-gray-600">Upcoming Exams</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <IndianRupee className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    ₹{childData.pendingFees.toLocaleString()}
                  </div>
                  <p className="text-sm text-gray-600">Pending Fees</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Grid */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Recent Grades */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center">
                <TrendingUp className="h-5 w-5 mr-2" />
                Recent Grades
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentGrades.map((grade, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <BookOpen className="h-6 w-6 text-blue-600" />
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">{grade.subject}</div>
                        <div className="text-sm text-gray-500">
                          {grade.teacher} • {grade.date}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-blue-600">{grade.grade}</div>
                      <div className="text-sm text-gray-500">{grade.score}%</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Notifications */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Bell className="h-5 w-5 mr-2" />
                Notifications
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {notifications.map((notification, index) => (
                  <div key={index} className="flex items-start space-x-3 p-3 hover:bg-gray-50 rounded-lg transition-colors">
                    <div className={`p-2 rounded-full ${
                      notification.type === "success" ? "bg-green-100 text-green-600" :
                      notification.type === "warning" ? "bg-yellow-100 text-yellow-600" :
                      "bg-blue-100 text-blue-600"
                    }`}>
                      <notification.icon className="h-4 w-4" />
                    </div>
                    <div className="flex-1">
                      <div className="font-medium text-gray-900 text-sm">
                        {notification.title}
                      </div>
                      <div className="text-sm text-gray-500">
                        {notification.message}
                      </div>
                      <div className="text-xs text-gray-400 mt-1">
                        {notification.time}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Attendance and Schedule */}
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Attendance Record */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Clock className="h-5 w-5 mr-2" />
                Recent Attendance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {attendanceRecord.map((record, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className={`w-3 h-3 rounded-full ${
                        record.status === "present" ? "bg-green-500" :
                        record.status === "late" ? "bg-yellow-500" : "bg-red-500"
                      }`} />
                      <div>
                        <div className="font-medium text-gray-900">{record.date}</div>
                        <div className="text-sm text-gray-500">
                          {record.subjects} subjects
                        </div>
                      </div>
                    </div>
                    <div className={`text-sm font-medium capitalize ${
                      record.status === "present" ? "text-green-600" :
                      record.status === "late" ? "text-yellow-600" : "text-red-600"
                    }`}>
                      {record.status}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Weekly Schedule */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="h-5 w-5 mr-2" />
                Weekly Schedule
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {weeklySchedule.map((day, index) => (
                  <div key={index} className="p-3 border rounded-lg">
                    <div className="font-medium text-gray-900 mb-2">{day.day}</div>
                    <div className="flex flex-wrap gap-2">
                      {day.subjects.map((subject, subIndex) => (
                        <span
                          key={subIndex}
                          className="px-2 py-1 bg-blue-100 text-blue-800 text-xs rounded-full"
                        >
                          {subject}
                        </span>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Upcoming Events */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              Upcoming Events
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {upcomingEvents.map((event, index) => (
                <div key={index} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors">
                  <div className="flex items-center space-x-4">
                    <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                      event.type === "meeting" ? "bg-purple-100" :
                      event.type === "exam" ? "bg-red-100" : "bg-green-100"
                    }`}>
                      {event.type === "meeting" ? (
                        <MessageCircle className={`h-6 w-6 ${
                          event.type === "meeting" ? "text-purple-600" :
                          event.type === "exam" ? "text-red-600" : "text-green-600"
                        }`} />
                      ) : event.type === "exam" ? (
                        <FileText className="h-6 w-6 text-red-600" />
                      ) : (
                        <Calendar className="h-6 w-6 text-green-600" />
                      )}
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">{event.title}</div>
                      <div className="text-sm text-gray-500">
                        {event.date} • {event.time}
                      </div>
                      {event.teacher && (
                        <div className="text-xs text-gray-400">
                          with {event.teacher}
                        </div>
                      )}
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      Details
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Academic Progress */}
        <Card>
          <CardHeader>
            <CardTitle>Academic Progress Overview</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 md:grid-cols-3">
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 mb-2">A-</div>
                <div className="text-sm text-gray-600">Overall Grade</div>
                <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-green-600 h-2 rounded-full w-[85%]" />
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">92%</div>
                <div className="text-sm text-gray-600">Attendance Rate</div>
                <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-600 h-2 rounded-full w-[92%]" />
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600 mb-2">15</div>
                <div className="text-sm text-gray-600">Assignments Completed</div>
                <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-purple-600 h-2 rounded-full w-[83%]" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
