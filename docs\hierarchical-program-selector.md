# Hierarchical Program Selector Implementation

## Overview

The Hierarchical Program Selector is a dynamic, institution-type-aware component that provides an intuitive interface for selecting academic programs or classes based on the institution configuration (school vs college).

## Features

### Institution Type Support

#### School Institution Type
- **Display**: "Available Classes" instead of "Available Programs"
- **Structure**: Flat, grade-based structure (Class 1, Class 2, ..., Class 12)
- **Sections**: Shows sections within each class (A, B, C, etc.)
- **UI**: Simple card-based selection without hierarchical expansion

#### College Institution Type
- **Display**: "Available Programs" with full hierarchical structure
- **Structure**: Three-level hierarchy:
  1. **Stream Categories** (top level): Engineering, Pharmacy, Commerce, Arts, Science, etc.
  2. **Program Types** (second level): B.Tech, M.Tech, BBA, MBA, etc.
  3. **Branches/Specializations** (third level): Computer Science, Mechanical, Marketing, etc.
- **UI**: Expandable tree structure with cascading dropdowns

### Key Features

1. **Dynamic Data Fetching**: All program data is fetched from the database (no hardcoded values)
2. **Hierarchical Display**: Clear tree/breakdown structure showing relationships between levels
3. **Loading States**: Proper loading indicators during data fetching
4. **Error Handling**: Graceful error handling with retry functionality
5. **Selection Persistence**: Maintains selection state and auto-expands relevant sections
6. **Responsive Design**: Works on all screen sizes
7. **Seat Management**: Shows available seats and admission status
8. **Validation**: Ensures selections follow the hierarchical flow

## Implementation Details

### Components

#### Main Component: `HierarchicalProgramSelector`
- **Location**: `components/ui/hierarchical-program-selector.tsx`
- **Props**:
  - `onSelectionChange`: Callback when selection changes
  - `selectedProgramId`: Currently selected program ID
  - `disabled`: Whether the selector is disabled
  - `className`: Additional CSS classes

#### Sub-components:
- `SchoolProgramsView`: Handles school-type program display
- `CollegeProgramsView`: Handles college-type hierarchical program display

### API Endpoints

#### New Endpoint: `/api/academic-programs/hierarchical`
- **Purpose**: Provides hierarchical program data based on institution type
- **Returns**: 
  - For schools: Flat program list
  - For colleges: Hierarchical streams → branches → programs structure
- **Features**: Includes seat availability, admission status, and program details

### Database Integration

The component integrates with existing database schema:
- `institutionConfig`: Determines institution type
- `academicStreams`: Stream categories and information
- `academicBranches`: Branch/specialization data
- `academicPrograms`: Individual program details
- `academicBatches`: Seat availability and batch information

### React Hook: `useGetHierarchicalPrograms`
- **Location**: `features/api/use-hierarchical-programs.ts`
- **Features**: 
  - Caching with TanStack Query
  - Helper functions for data manipulation
  - Type-safe data structures

## Usage

### In Student Admission Form

```tsx
import { HierarchicalProgramSelector } from "@/components/ui/hierarchical-program-selector";

const handleProgramSelection = (selection: SelectedProgram | null) => {
  if (selection) {
    setFormData(prev => ({
      ...prev,
      programId: selection.programId,
      program: selection.programName,
      streamId: selection.streamId || "",
      branchId: selection.branchId || "",
    }));
  }
};

<HierarchicalProgramSelector
  onSelectionChange={handleProgramSelection}
  selectedProgramId={formData.programId}
  disabled={isLoading}
  className="w-full"
/>
```

### Testing

A test page is available at `/admin/test-hierarchical` to demonstrate:
- Institution type detection
- Hierarchical expansion
- Selection handling
- Data structure output
- Error states and loading

## Data Flow

1. **Component Mount**: Fetches institution config and hierarchical program data
2. **Institution Type Check**: Determines display mode (school vs college)
3. **Data Rendering**: 
   - School: Renders flat program cards
   - College: Renders expandable stream/branch/program hierarchy
4. **User Interaction**: 
   - Expansion/collapse of hierarchy levels
   - Program selection with validation
5. **Selection Callback**: Returns complete program information including hierarchy context

## Benefits

1. **User Experience**: Intuitive, organized program selection
2. **Scalability**: Handles large numbers of programs efficiently
3. **Flexibility**: Adapts to different institution types automatically
4. **Maintainability**: Clean separation of concerns and reusable components
5. **Performance**: Efficient data loading and caching
6. **Accessibility**: Proper keyboard navigation and screen reader support

## Future Enhancements

1. **Search Functionality**: Add search within the hierarchy
2. **Filtering**: Filter by admission status, availability, etc.
3. **Bulk Selection**: Support for selecting multiple programs
4. **Export**: Export program hierarchy data
5. **Analytics**: Track popular program selections
6. **Customization**: Allow institutions to customize display preferences

## Configuration

The component automatically adapts based on:
- Institution type configuration in the database
- Available streams, branches, and programs
- Seat availability and admission status
- Academic batch information

No additional configuration is required - the component is fully data-driven.
