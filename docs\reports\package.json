{"name": "school-management-system", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "db:generate": "drizzle-kit generate", "db:migrate": "drizzle-kit migrate", "db:push": "drizzle-kit push", "db:studio": "drizzle-kit studio", "db:seed": "tsx lib/db/seed.ts", "db:migrate-school": "tsx lib/db/migrate-to-school-only.ts", "db:seed-classes": "tsx lib/db/seed-simple-classes.ts"}, "dependencies": {"@hono/zod-validator": "^0.4.1", "@hookform/resolvers": "^5.0.1", "@neondatabase/serverless": "^1.0.0", "@radix-ui/react-alert-dialog": "^1.1.14", "@radix-ui/react-avatar": "^1.1.0", "@radix-ui/react-checkbox": "^1.3.2", "@radix-ui/react-dialog": "^1.1.1", "@radix-ui/react-dropdown-menu": "^2.1.15", "@radix-ui/react-label": "^2.1.7", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.2.5", "@radix-ui/react-tabs": "^1.1.0", "@tanstack/react-query": "^5.59.0", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "drizzle-orm": "^0.43.1", "hono": "^4.6.3", "lucide-react": "^0.446.0", "next": "15.3.2", "react": "^19.0.0", "react-dom": "^19.0.0", "react-hook-form": "^7.56.4", "recharts": "^2.12.7", "sonner": "^1.5.0", "tailwind-merge": "^2.5.2", "zod": "^3.23.8"}, "devDependencies": {"@eslint/eslintrc": "^3", "@types/node": "^20", "@types/react": "^19", "@types/react-dom": "^19", "autoprefixer": "^10.4.20", "drizzle-kit": "^0.31.1", "eslint": "^9", "eslint-config-next": "15.3.2", "postcss": "^8.4.47", "tailwindcss": "^3.4.13", "tsx": "^4.19.4", "typescript": "^5"}}