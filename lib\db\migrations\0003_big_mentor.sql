CREATE TYPE "public"."stream_category" AS ENUM('engineering', 'diploma', 'pharmacy', 'science', 'commerce', 'arts', 'management', 'medical', 'other');--> statement-breakpoint
CREATE TABLE "academic_branches" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"stream_id" uuid NOT NULL,
	"name" varchar(200) NOT NULL,
	"code" varchar(20) NOT NULL,
	"short_name" varchar(50) NOT NULL,
	"description" text,
	"eligibility_criteria" text,
	"duration" integer DEFAULT 4 NOT NULL,
	"department" varchar(100),
	"total_seats" integer DEFAULT 60 NOT NULL,
	"is_active" boolean DEFAULT true NOT NULL,
	"display_order" integer DEFAULT 0,
	"status" "status" DEFAULT 'active' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "academic_branches_code_unique" UNIQUE("code")
);
--> statement-breakpoint
ALTER TABLE "academic_streams" ALTER COLUMN "duration" SET DEFAULT 4;--> statement-breakpoint
ALTER TABLE "academic_programs" ADD COLUMN "stream_id" uuid;--> statement-breakpoint
ALTER TABLE "academic_programs" ADD COLUMN "branch_id" uuid;--> statement-breakpoint
ALTER TABLE "academic_sections" ADD COLUMN "branch_id" uuid;--> statement-breakpoint
ALTER TABLE "academic_streams" ADD COLUMN "category" "stream_category" NOT NULL;--> statement-breakpoint
ALTER TABLE "students" ADD COLUMN "branch_id" uuid;--> statement-breakpoint
ALTER TABLE "academic_branches" ADD CONSTRAINT "academic_branches_stream_id_academic_streams_id_fk" FOREIGN KEY ("stream_id") REFERENCES "public"."academic_streams"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "academic_programs" ADD CONSTRAINT "academic_programs_stream_id_academic_streams_id_fk" FOREIGN KEY ("stream_id") REFERENCES "public"."academic_streams"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "academic_programs" ADD CONSTRAINT "academic_programs_branch_id_academic_branches_id_fk" FOREIGN KEY ("branch_id") REFERENCES "public"."academic_branches"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "academic_sections" ADD CONSTRAINT "academic_sections_branch_id_academic_branches_id_fk" FOREIGN KEY ("branch_id") REFERENCES "public"."academic_branches"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "students" ADD CONSTRAINT "students_branch_id_academic_branches_id_fk" FOREIGN KEY ("branch_id") REFERENCES "public"."academic_branches"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "academic_streams" DROP COLUMN "type";