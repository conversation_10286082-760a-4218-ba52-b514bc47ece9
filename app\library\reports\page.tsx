"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  BookOpen,
  Users,
  Calendar,
  Download,
  Filter,
  Eye,
  FileText,
  PieChart,
  Target,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  IndianRupee,
  BookMarked,
  History,
} from "lucide-react";

export default function LibraryReports() {
  const [user, setUser] = useState<any>(null);
  const [selectedPeriod, setSelectedPeriod] = useState("month");
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "library") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  const libraryMetrics = {
    totalBooks: 15420,
    totalMembers: 1234,
    booksIssued: 2530,
    booksReturned: 2345,
    overdueBooks: 187,
    reservations: 245,
    finesCollected: 12450,
    newMemberships: 23,
    popularGenres: [
      { genre: "Science Fiction", count: 456, percentage: 18.2 },
      { genre: "Mathematics", count: 389, percentage: 15.5 },
      { genre: "Literature", count: 334, percentage: 13.3 },
      { genre: "History", count: 298, percentage: 11.9 },
      { genre: "Physics", count: 267, percentage: 10.6 },
    ],
    monthlyStats: [
      { month: "Jan", issued: 234, returned: 198, overdue: 15 },
      { month: "Feb", issued: 267, returned: 245, overdue: 18 },
      { month: "Mar", issued: 298, returned: 276, overdue: 22 },
      { month: "Apr", issued: 312, returned: 289, overdue: 25 },
      { month: "May", issued: 345, returned: 321, overdue: 28 },
      { month: "Jun", issued: 378, returned: 356, overdue: 31 },
    ],
  };

  const reportCategories = [
    {
      title: "Circulation Reports",
      description: "Book issue, return, and circulation statistics",
      icon: BookOpen,
      reports: [
        { name: "Daily Circulation Report", description: "Books issued and returned today" },
        { name: "Monthly Circulation Summary", description: "Monthly circulation statistics" },
        { name: "Overdue Books Report", description: "List of overdue books and members" },
        { name: "Popular Books Report", description: "Most frequently issued books" },
      ],
    },
    {
      title: "Member Reports",
      description: "Library membership and user analytics",
      icon: Users,
      reports: [
        { name: "Member Activity Report", description: "Member usage patterns and statistics" },
        { name: "New Members Report", description: "Recently registered members" },
        { name: "Inactive Members Report", description: "Members with no recent activity" },
        { name: "Member Fine Report", description: "Outstanding fines by member" },
      ],
    },
    {
      title: "Collection Reports",
      description: "Book inventory and collection analysis",
      icon: BookMarked,
      reports: [
        { name: "Collection Summary", description: "Overview of library collection" },
        { name: "Genre Analysis Report", description: "Books by genre and category" },
        { name: "Acquisition Report", description: "Recently added books" },
        { name: "Damaged Books Report", description: "Books requiring repair or replacement" },
      ],
    },
    {
      title: "Financial Reports",
      description: "Fines, fees, and financial analytics",
      icon: IndianRupee,
      reports: [
        { name: "Fine Collection Report", description: "Fines collected and outstanding" },
        { name: "Revenue Summary", description: "Library revenue from various sources" },
        { name: "Budget Analysis", description: "Budget allocation and spending" },
        { name: "Cost per Circulation", description: "Cost analysis per book circulation" },
      ],
    },
  ];

  const generateReport = (reportName: string) => {
    alert(`Generating ${reportName}...`);
  };

  const exportReport = (format: string) => {
    alert(`Exporting report in ${format} format...`);
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Library Reports & Analytics</h1>
            <p className="text-gray-600">Generate comprehensive library reports and analytics</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              Filter Period
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export All
            </Button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <BookOpen className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {libraryMetrics.totalBooks.toLocaleString()}
                  </div>
                  <p className="text-sm text-gray-600">Total Books</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {libraryMetrics.totalMembers.toLocaleString()}
                  </div>
                  <p className="text-sm text-gray-600">Active Members</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <TrendingUp className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {libraryMetrics.booksIssued.toLocaleString()}
                  </div>
                  <p className="text-sm text-gray-600">Books Issued</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <IndianRupee className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    ₹{libraryMetrics.finesCollected.toLocaleString()}
                  </div>
                  <p className="text-sm text-gray-600">Fines Collected</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Period Selection */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Report Period</h3>
              <div className="flex gap-2">
                {["week", "month", "quarter", "year"].map((period) => (
                  <Button
                    key={period}
                    variant={selectedPeriod === period ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedPeriod(period)}
                    className="capitalize"
                  >
                    {period}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Quick Stats */}
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Popular Genres */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <PieChart className="h-5 w-5 mr-2" />
                Popular Genres
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {libraryMetrics.popularGenres.map((genre, index) => (
                  <div key={genre.genre} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-sm font-medium text-blue-600">
                        {index + 1}
                      </div>
                      <div>
                        <div className="font-medium">{genre.genre}</div>
                        <div className="text-sm text-gray-500">{genre.count} books</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">{genre.percentage}%</div>
                      <div className="w-20 bg-gray-200 rounded-full h-2 mt-1">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: `${genre.percentage}%` }}
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Monthly Trends */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="h-5 w-5 mr-2" />
                Monthly Circulation Trends
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {libraryMetrics.monthlyStats.map((stat) => (
                  <div key={stat.month} className="flex items-center justify-between">
                    <div className="font-medium w-12">{stat.month}</div>
                    <div className="flex-1 mx-4">
                      <div className="flex justify-between text-sm mb-1">
                        <span>Issued: {stat.issued}</span>
                        <span>Returned: {stat.returned}</span>
                        <span className="text-red-600">Overdue: {stat.overdue}</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-green-600 h-2 rounded-full"
                          style={{ width: `${(stat.returned / stat.issued) * 100}%` }}
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Report Categories */}
        <div className="grid gap-6 lg:grid-cols-2">
          {reportCategories.map((category) => (
            <Card key={category.title}>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <category.icon className="h-5 w-5 mr-2" />
                  {category.title}
                </CardTitle>
                <p className="text-sm text-gray-600">{category.description}</p>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {category.reports.map((report) => (
                    <div key={report.name} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                      <div className="flex-1">
                        <div className="font-medium text-gray-900">{report.name}</div>
                        <div className="text-sm text-gray-500">{report.description}</div>
                      </div>
                      <div className="flex space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => generateReport(report.name)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => exportReport("PDF")}
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Recent Activity Summary */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <History className="h-5 w-5 mr-2" />
              Recent Activity Summary
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-green-600 mb-2">
                  {libraryMetrics.booksReturned}
                </div>
                <div className="text-sm text-gray-600">Books Returned This Month</div>
                <div className="flex items-center justify-center mt-2 text-green-600">
                  <TrendingUp className="h-4 w-4 mr-1" />
                  <span className="text-xs">+12% from last month</span>
                </div>
              </div>

              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-red-600 mb-2">
                  {libraryMetrics.overdueBooks}
                </div>
                <div className="text-sm text-gray-600">Overdue Books</div>
                <div className="flex items-center justify-center mt-2 text-red-600">
                  <TrendingDown className="h-4 w-4 mr-1" />
                  <span className="text-xs">-5% from last month</span>
                </div>
              </div>

              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-blue-600 mb-2">
                  {libraryMetrics.newMemberships}
                </div>
                <div className="text-sm text-gray-600">New Memberships</div>
                <div className="flex items-center justify-center mt-2 text-blue-600">
                  <TrendingUp className="h-4 w-4 mr-1" />
                  <span className="text-xs">+8% from last month</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Export Options */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              Export Options
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-4">
              <Button
                variant="outline"
                className="h-20 flex flex-col items-center justify-center"
                onClick={() => exportReport("PDF")}
              >
                <FileText className="h-6 w-6 mb-2" />
                Export as PDF
              </Button>

              <Button
                variant="outline"
                className="h-20 flex flex-col items-center justify-center"
                onClick={() => exportReport("Excel")}
              >
                <BarChart3 className="h-6 w-6 mb-2" />
                Export as Excel
              </Button>

              <Button
                variant="outline"
                className="h-20 flex flex-col items-center justify-center"
                onClick={() => exportReport("CSV")}
              >
                <Download className="h-6 w-6 mb-2" />
                Export as CSV
              </Button>

              <Button
                variant="outline"
                className="h-20 flex flex-col items-center justify-center"
                onClick={() => exportReport("Print")}
              >
                <FileText className="h-6 w-6 mb-2" />
                Print Report
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}