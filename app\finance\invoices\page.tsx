"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  FileText,
  Plus,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Send,
  IndianRupee,
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Users,
  Receipt,
  Mail,
  Printer,
} from "lucide-react";

export default function FinanceInvoices() {
  const [user, setUser] = useState<any>(null);
  const [selectedFilter, setSelectedFilter] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "finance_manager" && parsedUser.role !== "admin" && parsedUser.role !== "super_admin") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock invoices data
  const invoices = [
    {
      id: "INV001",
      invoiceNumber: "INV-2024-001",
      studentName: "John Doe",
      studentId: "STU001",
      class: "Grade 10A",
      parentName: "Jane Doe",
      parentEmail: "<EMAIL>",
      issueDate: "2024-01-15",
      dueDate: "2024-02-15",
      amount: 25000,
      paidAmount: 25000,
      balanceAmount: 0,
      status: "Paid",
      items: [
        { description: "Tuition Fee Q3", amount: 15000 },
        { description: "Lab Fee", amount: 3000 },
        { description: "Library Fee", amount: 1000 },
        { description: "Sports Fee", amount: 2000 },
        { description: "Transport Fee", amount: 4000 },
      ],
      paymentHistory: [
        { date: "2024-01-20", amount: 25000, method: "Online Banking", status: "Completed" }
      ],
      sentDate: "2024-01-15",
      remindersSent: 0,
    },
    {
      id: "INV002",
      invoiceNumber: "INV-2024-002",
      studentName: "Alice Smith",
      studentId: "STU002",
      class: "Grade 11B",
      parentName: "Bob Smith",
      parentEmail: "<EMAIL>",
      issueDate: "2024-01-10",
      dueDate: "2024-02-10",
      amount: 22000,
      paidAmount: 15000,
      balanceAmount: 7000,
      status: "Partially Paid",
      items: [
        { description: "Tuition Fee Q3", amount: 14000 },
        { description: "Computer Lab Fee", amount: 2000 },
        { description: "Library Fee", amount: 1000 },
        { description: "Activity Fee", amount: 1500 },
        { description: "Transport Fee", amount: 3500 },
      ],
      paymentHistory: [
        { date: "2024-01-12", amount: 15000, method: "UPI", status: "Completed" }
      ],
      sentDate: "2024-01-10",
      remindersSent: 1,
    },
    {
      id: "INV003",
      invoiceNumber: "INV-2024-003",
      studentName: "Bob Johnson",
      studentId: "STU003",
      class: "Grade 10B",
      parentName: "Carol Johnson",
      parentEmail: "<EMAIL>",
      issueDate: "2024-01-08",
      dueDate: "2024-02-08",
      amount: 25000,
      paidAmount: 0,
      balanceAmount: 25000,
      status: "Overdue",
      items: [
        { description: "Tuition Fee Q3", amount: 15000 },
        { description: "Lab Fee", amount: 3000 },
        { description: "Library Fee", amount: 1000 },
        { description: "Sports Fee", amount: 2000 },
        { description: "Transport Fee", amount: 4000 },
      ],
      paymentHistory: [],
      sentDate: "2024-01-08",
      remindersSent: 3,
    },
    {
      id: "INV004",
      invoiceNumber: "INV-2024-004",
      studentName: "Sarah Wilson",
      studentId: "STU004",
      class: "Grade 9A",
      parentName: "David Wilson",
      parentEmail: "<EMAIL>",
      issueDate: "2024-01-20",
      dueDate: "2024-02-20",
      amount: 20000,
      paidAmount: 0,
      balanceAmount: 20000,
      status: "Pending",
      items: [
        { description: "Tuition Fee Q3", amount: 12000 },
        { description: "Library Fee", amount: 1000 },
        { description: "Project Fee", amount: 2000 },
        { description: "Cultural Activity Fee", amount: 2000 },
        { description: "Transport Fee", amount: 3000 },
      ],
      paymentHistory: [],
      sentDate: "2024-01-20",
      remindersSent: 0,
    },
    {
      id: "INV005",
      invoiceNumber: "INV-2024-005",
      studentName: "Mike Brown",
      studentId: "STU005",
      class: "Grade 12A",
      parentName: "Lisa Brown",
      parentEmail: "<EMAIL>",
      issueDate: "2024-01-05",
      dueDate: "2024-02-05",
      amount: 28000,
      paidAmount: 0,
      balanceAmount: 28000,
      status: "Draft",
      items: [
        { description: "Tuition Fee Q3", amount: 16000 },
        { description: "Lab Fee", amount: 4000 },
        { description: "Library Fee", amount: 1000 },
        { description: "Exam Fee", amount: 3000 },
        { description: "Transport Fee", amount: 4000 },
      ],
      paymentHistory: [],
      sentDate: null,
      remindersSent: 0,
    },
  ];

  const invoiceStats = {
    totalInvoices: invoices.length,
    totalAmount: invoices.reduce((sum, inv) => sum + inv.amount, 0),
    paidAmount: invoices.reduce((sum, inv) => sum + inv.paidAmount, 0),
    pendingAmount: invoices.reduce((sum, inv) => sum + inv.balanceAmount, 0),
    overdueInvoices: invoices.filter(inv => inv.status === "Overdue").length,
    draftInvoices: invoices.filter(inv => inv.status === "Draft").length,
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Paid": return "bg-green-100 text-green-700";
      case "Partially Paid": return "bg-blue-100 text-blue-700";
      case "Pending": return "bg-yellow-100 text-yellow-700";
      case "Overdue": return "bg-red-100 text-red-700";
      case "Draft": return "bg-gray-100 text-gray-700";
      case "Cancelled": return "bg-red-100 text-red-700";
      default: return "bg-gray-100 text-gray-700";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Paid": return <CheckCircle className="h-4 w-4" />;
      case "Partially Paid": return <Clock className="h-4 w-4" />;
      case "Pending": return <Clock className="h-4 w-4" />;
      case "Overdue": return <AlertTriangle className="h-4 w-4" />;
      case "Draft": return <FileText className="h-4 w-4" />;
      case "Cancelled": return <XCircle className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  const getDaysOverdue = (dueDate: string) => {
    const due = new Date(dueDate);
    const today = new Date();
    const diffTime = today.getTime() - due.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays > 0 ? diffDays : 0;
  };

  const filteredInvoices = invoices.filter(invoice => {
    const matchesSearch = invoice.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         invoice.studentId.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         invoice.invoiceNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         invoice.parentName.toLowerCase().includes(searchTerm.toLowerCase());

    if (selectedFilter === "all") return matchesSearch;
    return matchesSearch && invoice.status.toLowerCase().replace(" ", "_") === selectedFilter;
  });

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Invoice Management</h1>
            <p className="text-gray-600">Generate and manage student fee invoices</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Invoice
            </Button>
          </div>
        </div>

        {/* Invoice Statistics */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <FileText className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {invoiceStats.totalInvoices}
                  </div>
                  <p className="text-sm text-gray-600">Total Invoices</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <IndianRupee className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    ₹{(invoiceStats.paidAmount / 100000).toFixed(1)}L
                  </div>
                  <p className="text-sm text-gray-600">Amount Collected</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    ₹{(invoiceStats.pendingAmount / 100000).toFixed(1)}L
                  </div>
                  <p className="text-sm text-gray-600">Pending Amount</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <AlertTriangle className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-red-600">
                    {invoiceStats.overdueInvoices}
                  </div>
                  <p className="text-sm text-gray-600">Overdue Invoices</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Collection Rate */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Collection Rate</h3>
              <div className="text-sm text-gray-500">
                Target: 95%
              </div>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-4 mb-2">
              <div
                className="bg-green-500 h-4 rounded-full"
                style={{ width: `${(invoiceStats.paidAmount / invoiceStats.totalAmount) * 100}%` }}
              />
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">
                Current: {((invoiceStats.paidAmount / invoiceStats.totalAmount) * 100).toFixed(1)}%
              </span>
              <span className="text-green-600 font-medium">
                ₹{(invoiceStats.paidAmount / 100000).toFixed(1)}L of ₹{(invoiceStats.totalAmount / 100000).toFixed(1)}L
              </span>
            </div>
          </CardContent>
        </Card>

        {/* Filters and Search */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search by student name, ID, invoice number, or parent..."
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
              <div className="flex gap-2 flex-wrap">
                {[
                  { key: "all", label: "All Status" },
                  { key: "paid", label: "Paid" },
                  { key: "partially_paid", label: "Partial" },
                  { key: "pending", label: "Pending" },
                  { key: "overdue", label: "Overdue" },
                  { key: "draft", label: "Draft" },
                ].map((filter) => (
                  <Button
                    key={filter.key}
                    variant={selectedFilter === filter.key ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedFilter(filter.key)}
                  >
                    {filter.label}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Invoices List */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              Invoices ({filteredInvoices.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {filteredInvoices.map((invoice) => (
                <div key={invoice.id} className="border rounded-lg p-6 hover:shadow-md transition-shadow">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">{invoice.invoiceNumber}</h3>
                        <Badge variant="outline" className="text-xs">
                          {invoice.studentId}
                        </Badge>
                      </div>
                      <p className="text-gray-600 mb-1">{invoice.studentName} - {invoice.class}</p>
                      <p className="text-sm text-gray-500">Parent: {invoice.parentName}</p>
                    </div>
                    <div className="text-right">
                      <Badge className={getStatusColor(invoice.status)}>
                        {getStatusIcon(invoice.status)}
                        <span className="ml-1">{invoice.status}</span>
                      </Badge>
                      {invoice.status === "Overdue" && (
                        <div className="text-sm text-red-600 mt-1">
                          {getDaysOverdue(invoice.dueDate)} days overdue
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="grid gap-4 md:grid-cols-4 mb-4">
                    <div>
                      <div className="text-sm text-gray-600">Invoice Amount</div>
                      <div className="text-xl font-bold text-gray-900">
                        ₹{invoice.amount.toLocaleString()}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-600">Paid Amount</div>
                      <div className="text-lg font-bold text-green-600">
                        ₹{invoice.paidAmount.toLocaleString()}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-600">Balance</div>
                      <div className={`text-lg font-bold ${
                        invoice.balanceAmount > 0 ? "text-red-600" : "text-green-600"
                      }`}>
                        ₹{invoice.balanceAmount.toLocaleString()}
                      </div>
                    </div>
                    <div>
                      <div className="text-sm text-gray-600">Due Date</div>
                      <div className={`font-medium ${
                        invoice.status === "Overdue" ? "text-red-600" : "text-gray-900"
                      }`}>
                        {invoice.dueDate}
                      </div>
                    </div>
                  </div>

                  <div className="mb-4">
                    <div className="text-sm text-gray-600 mb-2">Invoice Items:</div>
                    <div className="grid gap-2 md:grid-cols-2">
                      {invoice.items.slice(0, 4).map((item, index) => (
                        <div key={index} className="flex justify-between text-sm">
                          <span>{item.description}</span>
                          <span className="font-medium">₹{item.amount.toLocaleString()}</span>
                        </div>
                      ))}
                      {invoice.items.length > 4 && (
                        <div className="text-sm text-gray-500">
                          +{invoice.items.length - 4} more items
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center justify-between pt-4 border-t">
                    <div className="text-sm text-gray-500">
                      {invoice.sentDate ? `Sent: ${invoice.sentDate}` : "Not sent"} •
                      Reminders: {invoice.remindersSent}
                    </div>
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4 mr-1" />
                        View
                      </Button>
                      <Button variant="outline" size="sm">
                        <Printer className="h-4 w-4 mr-1" />
                        Print
                      </Button>
                      {invoice.status === "Draft" ? (
                        <Button size="sm">
                          <Send className="h-4 w-4 mr-1" />
                          Send
                        </Button>
                      ) : (
                        <Button variant="outline" size="sm">
                          <Mail className="h-4 w-4 mr-1" />
                          Reminder
                        </Button>
                      )}
                      {invoice.status !== "Paid" && invoice.status !== "Draft" && (
                        <Button size="sm">
                          <Receipt className="h-4 w-4 mr-1" />
                          Collect
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
