import {
  Student, Teacher, Class, Attendance, Grade, Assignment, User,
  FeeStructure, FeePayment, Book, BookIssue, Vehicle, TransportRoute,
  Hostel, HostelRoom, Exam, Timetable
} from "./schemas";

// Mock users for authentication
export const mockUsers: User[] = [
  {
    id: "1",
    email: "<EMAIL>",
    password: "admin123", // In real app, this would be hashed
    role: "super_admin",
    firstName: "Super",
    lastName: "Admin",
    phone: "1234567890",
    avatar: "",
    isActive: true,
    lastLogin: "2024-01-15T10:00:00Z",
    permissions: ["all"],
    createdAt: "2023-01-01T00:00:00Z",
    updatedAt: "2024-01-15T10:00:00Z",
  },
  {
    id: "2",
    email: "<EMAIL>",
    password: "principal123",
    role: "principal",
    firstName: "<PERSON>",
    lastName: "Principal",
    phone: "1234567891",
    avatar: "",
    isActive: true,
    lastLogin: "2024-01-15T09:00:00Z",
    permissions: ["school_leadership", "academic_oversight", "staff_management", "student_affairs", "financial_oversight", "strategic_planning", "parent_relations", "school_analytics"],
    createdAt: "2023-01-01T00:00:00Z",
    updatedAt: "2024-01-15T09:00:00Z",
  },
  {
    id: "3",
    email: "<EMAIL>",
    password: "admin123",
    role: "admin",
    firstName: "Sarah",
    lastName: "Admin",
    phone: "1234567892",
    avatar: "",
    isActive: true,
    lastLogin: "2024-01-15T08:30:00Z",
    permissions: ["students", "teachers", "classes", "reports", "settings", "operations"],
    createdAt: "2023-01-01T00:00:00Z",
    updatedAt: "2024-01-15T08:30:00Z",
  },
  {
    id: "4",
    email: "<EMAIL>",
    password: "teacher123",
    role: "teacher",
    firstName: "Emily",
    lastName: "Wilson",
    phone: "1234567892",
    avatar: "",
    isActive: true,
    lastLogin: "2024-01-15T08:00:00Z",
    permissions: ["classes", "grades", "attendance"],
    createdAt: "2023-01-01T00:00:00Z",
    updatedAt: "2024-01-15T08:00:00Z",
  },
  {
    id: "5",
    email: "<EMAIL>",
    password: "student123",
    role: "student",
    firstName: "John",
    lastName: "Doe",
    phone: "1234567893",
    avatar: "",
    isActive: true,
    lastLogin: "2024-01-15T07:00:00Z",
    permissions: ["view_grades", "view_attendance", "view_assignments"],
    createdAt: "2023-01-01T00:00:00Z",
    updatedAt: "2024-01-15T07:00:00Z",
  },
  {
    id: "6",
    email: "<EMAIL>",
    password: "parent123",
    role: "parent",
    firstName: "Jane",
    lastName: "Doe",
    phone: "1234567894",
    avatar: "",
    isActive: true,
    lastLogin: "2024-01-15T06:00:00Z",
    permissions: ["view_child_grades", "view_child_attendance"],
    createdAt: "2023-01-01T00:00:00Z",
    updatedAt: "2024-01-15T06:00:00Z",
  },
  {
    id: "7",
    email: "<EMAIL>",
    password: "admission123",
    role: "admission_officer",
    firstName: "Sarah",
    lastName: "Admission",
    phone: "1234567895",
    avatar: "",
    isActive: true,
    lastLogin: "2024-01-15T05:00:00Z",
    permissions: ["student_admission", "student_registration"],
    createdAt: "2023-01-01T00:00:00Z",
    updatedAt: "2024-01-15T05:00:00Z",
  },
  {
    id: "8",
    email: "<EMAIL>",
    password: "finance123",
    role: "finance_manager",
    firstName: "Michael",
    lastName: "Finance",
    phone: "1234567896",
    avatar: "",
    isActive: true,
    lastLogin: "2024-01-15T04:00:00Z",
    permissions: ["fee_management", "payments", "financial_reports"],
    createdAt: "2023-01-01T00:00:00Z",
    updatedAt: "2024-01-15T04:00:00Z",
  },
  {
    id: "9",
    email: "<EMAIL>",
    password: "librarian123",
    role: "librarian",
    firstName: "Lisa",
    lastName: "Library",
    phone: "1234567897",
    avatar: "",
    isActive: true,
    lastLogin: "2024-01-15T03:00:00Z",
    permissions: ["library_management", "book_issue", "book_return", "library_reports"],
    createdAt: "2023-01-01T00:00:00Z",
    updatedAt: "2024-01-15T03:00:00Z",
  },
  {
    id: "10",
    email: "<EMAIL>",
    password: "transport123",
    role: "transport_manager",
    firstName: "Robert",
    lastName: "Transport",
    phone: "1234567898",
    avatar: "",
    isActive: true,
    lastLogin: "2024-01-15T02:00:00Z",
    permissions: ["transport_management", "routes", "vehicles", "transport_reports"],
    createdAt: "2023-01-01T00:00:00Z",
    updatedAt: "2024-01-15T02:00:00Z",
  },
  {
    id: "11",
    email: "<EMAIL>",
    password: "hostel123",
    role: "hostel_manager",
    firstName: "Helen",
    lastName: "Hostel",
    phone: "1234567899",
    avatar: "",
    isActive: true,
    lastLogin: "2024-01-15T01:00:00Z",
    permissions: ["hostel_management", "room_allocation", "hostel_reports"],
    createdAt: "2023-01-01T00:00:00Z",
    updatedAt: "2024-01-15T01:00:00Z",
  },
];

// Mock data for development
export const mockStudents: Student[] = [
  {
    id: "1",
    firstName: "John",
    lastName: "Doe",
    email: "<EMAIL>",
    phone: "1234567890",
    dateOfBirth: "2008-05-15",
    address: "123 Main St, City, State 12345",
    enrollmentDate: "2023-09-01",
    studentId: "STU001",
    grade: "10",
    section: "A",
    parentName: "Jane Doe",
    parentPhone: "1234567891",
    parentEmail: "<EMAIL>",
    status: "active",
    createdAt: "2023-09-01T00:00:00Z",
    updatedAt: "2023-09-01T00:00:00Z",
  },
  {
    id: "2",
    firstName: "Alice",
    lastName: "Smith",
    email: "<EMAIL>",
    phone: "2345678901",
    dateOfBirth: "2008-08-22",
    address: "456 Oak Ave, City, State 12345",
    enrollmentDate: "2023-09-01",
    studentId: "STU002",
    grade: "10",
    section: "A",
    parentName: "Bob Smith",
    parentPhone: "2345678902",
    parentEmail: "<EMAIL>",
    status: "active",
    createdAt: "2023-09-01T00:00:00Z",
    updatedAt: "2023-09-01T00:00:00Z",
  },
  {
    id: "3",
    firstName: "Michael",
    lastName: "Johnson",
    email: "<EMAIL>",
    phone: "3456789012",
    dateOfBirth: "2007-12-10",
    address: "789 Pine St, City, State 12345",
    enrollmentDate: "2023-09-01",
    studentId: "STU003",
    grade: "11",
    section: "B",
    parentName: "Sarah Johnson",
    parentPhone: "3456789013",
    parentEmail: "<EMAIL>",
    status: "active",
    createdAt: "2023-09-01T00:00:00Z",
    updatedAt: "2023-09-01T00:00:00Z",
  },
];

export const mockTeachers: Teacher[] = [
  {
    id: "1",
    firstName: "Dr. Emily",
    lastName: "Wilson",
    email: "<EMAIL>",
    phone: "5551234567",
    dateOfBirth: "1985-03-20",
    address: "321 Teacher Lane, City, State 12345",
    hireDate: "2020-08-15",
    employeeId: "TCH001",
    department: "Secondary",
    subject: "Algebra",
    qualification: "PhD in Mathematics",
    experience: 8,
    salary: 65000,
    status: "active",
    createdAt: "2020-08-15T00:00:00Z",
    updatedAt: "2023-09-01T00:00:00Z",
  },
  {
    id: "2",
    firstName: "Mr. David",
    lastName: "Brown",
    email: "<EMAIL>",
    phone: "5552345678",
    dateOfBirth: "1982-07-12",
    address: "654 Education Blvd, City, State 12345",
    hireDate: "2019-01-10",
    employeeId: "TCH002",
    department: "Senior Secondary",
    subject: "Physics",
    qualification: "MS in Physics",
    experience: 12,
    salary: 68000,
    status: "active",
    createdAt: "2019-01-10T00:00:00Z",
    updatedAt: "2023-09-01T00:00:00Z",
  },
];

export const mockClasses: Class[] = [
  {
    id: "1",
    name: "Algebra I",
    grade: "10",
    section: "A",
    teacherId: "1",
    subject: "Mathematics",
    room: "101",
    capacity: 30,
    schedule: "MWF",
    startTime: "09:00",
    endTime: "10:00",
    days: ["Monday", "Wednesday", "Friday"],
    status: "active",
    createdAt: "2023-09-01T00:00:00Z",
    updatedAt: "2023-09-01T00:00:00Z",
  },
  {
    id: "2",
    name: "Physics I",
    grade: "11",
    section: "B",
    teacherId: "2",
    subject: "Science",
    room: "201",
    capacity: 25,
    schedule: "TTH",
    startTime: "10:00",
    endTime: "11:30",
    days: ["Tuesday", "Thursday"],
    status: "active",
    createdAt: "2023-09-01T00:00:00Z",
    updatedAt: "2023-09-01T00:00:00Z",
  },
];

export const mockAttendance: Attendance[] = [
  {
    id: "1",
    studentId: "1",
    classId: "1",
    date: "2024-01-15",
    status: "present",
    notes: "",
    createdAt: "2024-01-15T09:00:00Z",
    updatedAt: "2024-01-15T09:00:00Z",
  },
  {
    id: "2",
    studentId: "2",
    classId: "1",
    date: "2024-01-15",
    status: "absent",
    notes: "Sick leave",
    createdAt: "2024-01-15T09:00:00Z",
    updatedAt: "2024-01-15T09:00:00Z",
  },
];

export const mockGrades: Grade[] = [
  {
    id: "1",
    studentId: "1",
    classId: "1",
    assignmentName: "Midterm Exam",
    grade: 85,
    maxGrade: 100,
    type: "exam",
    date: "2024-01-10",
    notes: "Good performance",
    createdAt: "2024-01-10T00:00:00Z",
    updatedAt: "2024-01-10T00:00:00Z",
  },
  {
    id: "2",
    studentId: "2",
    classId: "1",
    assignmentName: "Homework 1",
    grade: 92,
    maxGrade: 100,
    type: "assignment",
    date: "2024-01-05",
    notes: "Excellent work",
    createdAt: "2024-01-05T00:00:00Z",
    updatedAt: "2024-01-05T00:00:00Z",
  },
];

export const mockAssignments: Assignment[] = [
  {
    id: "1",
    classId: "1",
    title: "Quadratic Equations Practice",
    description: "Solve the given quadratic equations using various methods",
    dueDate: "2024-01-20",
    maxGrade: 100,
    type: "assignment",
    status: "published",
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  },
  {
    id: "2",
    classId: "2",
    title: "Newton's Laws Lab Report",
    description: "Write a comprehensive lab report on Newton's three laws of motion",
    dueDate: "2024-01-25",
    maxGrade: 100,
    type: "project",
    status: "published",
    createdAt: "2024-01-02T00:00:00Z",
    updatedAt: "2024-01-02T00:00:00Z",
  },
];

// Finance Management Mock Data
export const mockFeeStructures: FeeStructure[] = [
  {
    id: "1",
    name: "Grade 10 Annual Fees",
    grade: "10",
    academicYear: "2023-24",
    tuitionFee: 50000,
    admissionFee: 5000,
    examFee: 2000,
    libraryFee: 1000,
    transportFee: 8000,
    hostelFee: 0,
    otherFees: 1500,
    totalFee: 67500,
    dueDate: "2024-04-30",
    status: "active",
    createdAt: "2023-04-01T00:00:00Z",
    updatedAt: "2023-04-01T00:00:00Z",
  },
  {
    id: "2",
    name: "Grade 11 Annual Fees",
    grade: "11",
    academicYear: "2023-24",
    tuitionFee: 55000,
    admissionFee: 0,
    examFee: 2500,
    libraryFee: 1000,
    transportFee: 8000,
    hostelFee: 0,
    otherFees: 2000,
    totalFee: 68500,
    dueDate: "2024-04-30",
    status: "active",
    createdAt: "2023-04-01T00:00:00Z",
    updatedAt: "2023-04-01T00:00:00Z",
  },
];

export const mockFeePayments: FeePayment[] = [
  {
    id: "1",
    studentId: "1",
    feeStructureId: "1",
    amountPaid: 67500,
    paymentMethod: "online",
    transactionId: "TXN123456789",
    paymentDate: "2024-01-15",
    receiptNumber: "RCP001",
    status: "completed",
    notes: "Full payment for academic year",
    createdAt: "2024-01-15T00:00:00Z",
    updatedAt: "2024-01-15T00:00:00Z",
  },
  {
    id: "2",
    studentId: "2",
    feeStructureId: "1",
    amountPaid: 30000,
    paymentMethod: "bank_transfer",
    transactionId: "TXN987654321",
    paymentDate: "2024-01-10",
    receiptNumber: "RCP002",
    status: "completed",
    notes: "Partial payment - installment 1",
    createdAt: "2024-01-10T00:00:00Z",
    updatedAt: "2024-01-10T00:00:00Z",
  },
];

// Library Management Mock Data
export const mockBooks: Book[] = [
  {
    id: "1",
    title: "Advanced Mathematics",
    author: "Dr. John Smith",
    isbn: "978-**********",
    category: "Mathematics",
    publisher: "Academic Press",
    publishedYear: 2022,
    totalCopies: 50,
    availableCopies: 45,
    location: "Section A, Shelf 1",
    price: 1200,
    status: "available",
    createdAt: "2023-01-01T00:00:00Z",
    updatedAt: "2024-01-15T00:00:00Z",
  },
  {
    id: "2",
    title: "Physics Fundamentals",
    author: "Dr. Sarah Johnson",
    isbn: "978-**********",
    category: "Physics",
    publisher: "Science Publications",
    publishedYear: 2023,
    totalCopies: 40,
    availableCopies: 38,
    location: "Section B, Shelf 2",
    price: 1500,
    status: "available",
    createdAt: "2023-01-01T00:00:00Z",
    updatedAt: "2024-01-15T00:00:00Z",
  },
];

export const mockBookIssues: BookIssue[] = [
  {
    id: "1",
    bookId: "1",
    studentId: "1",
    issueDate: "2024-01-10",
    dueDate: "2024-01-24",
    status: "issued",
    fineAmount: 0,
    notes: "",
    createdAt: "2024-01-10T00:00:00Z",
    updatedAt: "2024-01-10T00:00:00Z",
  },
  {
    id: "2",
    bookId: "2",
    teacherId: "1",
    issueDate: "2024-01-05",
    dueDate: "2024-02-05",
    status: "issued",
    fineAmount: 0,
    notes: "Reference for class preparation",
    createdAt: "2024-01-05T00:00:00Z",
    updatedAt: "2024-01-05T00:00:00Z",
  },
];

// Transport Management Mock Data
export const mockTransportRoutes: TransportRoute[] = [
  {
    id: "1",
    routeName: "Route A - City Center",
    startPoint: "School Campus",
    endPoint: "City Center",
    stops: ["Main Gate", "Shopping Mall", "Bus Station", "City Center"],
    distance: 15.5,
    estimatedTime: "45 minutes",
    fee: 8000,
    status: "active",
    createdAt: "2023-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  },
  {
    id: "2",
    routeName: "Route B - Residential Area",
    startPoint: "School Campus",
    endPoint: "Green Valley",
    stops: ["Main Gate", "Park Avenue", "Hospital", "Green Valley"],
    distance: 12.0,
    estimatedTime: "35 minutes",
    fee: 6000,
    status: "active",
    createdAt: "2023-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  },
];

export const mockVehicles: Vehicle[] = [
  {
    id: "1",
    vehicleNumber: "SCH-001",
    vehicleType: "bus",
    capacity: 50,
    driverName: "Rajesh Kumar",
    driverPhone: "**********",
    driverLicense: "DL123456789",
    routeId: "1",
    status: "active",
    createdAt: "2023-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  },
  {
    id: "2",
    vehicleNumber: "SCH-002",
    vehicleType: "van",
    capacity: 15,
    driverName: "Suresh Patel",
    driverPhone: "**********",
    driverLicense: "DL987654321",
    routeId: "2",
    status: "active",
    createdAt: "2023-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  },
];

// Hostel Management Mock Data
export const mockHostels: Hostel[] = [
  {
    id: "1",
    name: "Boys Hostel Block A",
    type: "boys",
    totalRooms: 50,
    occupiedRooms: 45,
    wardenName: "Mr. Ramesh Sharma",
    wardenPhone: "**********",
    address: "School Campus, Block A",
    facilities: ["WiFi", "Mess", "Study Hall", "Recreation Room", "Laundry"],
    monthlyFee: 15000,
    status: "active",
    createdAt: "2023-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  },
  {
    id: "2",
    name: "Girls Hostel Block B",
    type: "girls",
    totalRooms: 40,
    occupiedRooms: 38,
    wardenName: "Mrs. Priya Gupta",
    wardenPhone: "**********",
    address: "School Campus, Block B",
    facilities: ["WiFi", "Mess", "Study Hall", "Common Room", "Laundry", "Security"],
    monthlyFee: 15000,
    status: "active",
    createdAt: "2023-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  },
];

export const mockHostelRooms: HostelRoom[] = [
  {
    id: "1",
    hostelId: "1",
    roomNumber: "A101",
    roomType: "double",
    capacity: 2,
    occupiedBeds: 2,
    monthlyRent: 7500,
    facilities: ["Attached Bathroom", "Study Table", "Wardrobe"],
    status: "occupied",
    createdAt: "2023-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  },
  {
    id: "2",
    hostelId: "1",
    roomNumber: "A102",
    roomType: "single",
    capacity: 1,
    occupiedBeds: 0,
    monthlyRent: 10000,
    facilities: ["Attached Bathroom", "Study Table", "Wardrobe", "AC"],
    status: "available",
    createdAt: "2023-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  },
];

// Examination Management Mock Data
export const mockExams: Exam[] = [
  {
    id: "1",
    name: "Mid-term Examination",
    type: "midterm",
    grade: "10",
    subject: "Mathematics",
    date: "2024-02-15",
    startTime: "09:00",
    endTime: "12:00",
    totalMarks: 100,
    passingMarks: 35,
    room: "Exam Hall A",
    invigilator: "Dr. Emily Wilson",
    instructions: "Bring calculator and geometry box",
    status: "scheduled",
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  },
  {
    id: "2",
    name: "Physics Practical",
    type: "practical",
    grade: "11",
    subject: "Physics",
    date: "2024-02-20",
    startTime: "10:00",
    endTime: "13:00",
    totalMarks: 50,
    passingMarks: 18,
    room: "Physics Lab",
    invigilator: "Mr. David Brown",
    instructions: "Bring lab manual and observation notebook",
    status: "scheduled",
    createdAt: "2024-01-01T00:00:00Z",
    updatedAt: "2024-01-01T00:00:00Z",
  },
];

// Timetable Mock Data
export const mockTimetables: Timetable[] = [
  {
    id: "1",
    classId: "1",
    teacherId: "1",
    subject: "Mathematics",
    day: "monday",
    startTime: "09:00",
    endTime: "10:00",
    room: "Room 101",
    academicYear: "2023-24",
    semester: "1",
    status: "active",
    createdAt: "2023-04-01T00:00:00Z",
    updatedAt: "2023-04-01T00:00:00Z",
  },
  {
    id: "2",
    classId: "1",
    teacherId: "1",
    subject: "Mathematics",
    day: "wednesday",
    startTime: "09:00",
    endTime: "10:00",
    room: "Room 101",
    academicYear: "2023-24",
    semester: "1",
    status: "active",
    createdAt: "2023-04-01T00:00:00Z",
    updatedAt: "2023-04-01T00:00:00Z",
  },
  {
    id: "3",
    classId: "2",
    teacherId: "2",
    subject: "Physics",
    day: "tuesday",
    startTime: "10:00",
    endTime: "11:30",
    room: "Room 201",
    academicYear: "2023-24",
    semester: "1",
    status: "active",
    createdAt: "2023-04-01T00:00:00Z",
    updatedAt: "2023-04-01T00:00:00Z",
  },
];
