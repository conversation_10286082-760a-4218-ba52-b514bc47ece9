"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Building,
  Users,
  Calendar,
  Download,
  Filter,
  Eye,
  FileText,
  PieChart,
  Target,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  IndianRupee,
  Bed,
  Home,
  UserCheck,
} from "lucide-react";

export default function HostelReports() {
  const [user, setUser] = useState<any>(null);
  const [selectedPeriod, setSelectedPeriod] = useState("month");
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "hostel_manager") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  const hostelMetrics = {
    totalRooms: 120,
    occupiedRooms: 98,
    totalStudents: 185,
    monthlyRevenue: 2775000,
    operationalCost: 425000,
    maintenanceCost: 85000,
    staffSalaries: 180000,
    utilityCost: 160000,
    occupancyRate: 81.7,
    collectionRate: 94.2,
    satisfactionScore: 4.3,
    maintenanceRequests: 67,
    avgStayDuration: 18.5,
    renewalRate: 87.3,
  };

  const blockPerformance = [
    { block: "Block A", rooms: 40, occupied: 35, revenue: 945000, satisfaction: 4.5 },
    { block: "Block B", rooms: 50, occupied: 42, revenue: 1134000, satisfaction: 4.2 },
    { block: "Block C", rooms: 30, occupied: 21, revenue: 696000, satisfaction: 4.1 },
  ];

  const monthlyTrends = [
    { month: "Aug", revenue: 2650000, occupancy: 78, students: 175, satisfaction: 4.1 },
    { month: "Sep", revenue: 2720000, occupancy: 80, students: 180, satisfaction: 4.2 },
    { month: "Oct", revenue: 2750000, occupancy: 81, students: 182, satisfaction: 4.3 },
    { month: "Nov", revenue: 2775000, occupancy: 82, students: 185, satisfaction: 4.3 },
    { month: "Dec", revenue: 2800000, occupancy: 83, students: 187, satisfaction: 4.4 },
    { month: "Jan", revenue: 2775000, occupancy: 82, students: 185, satisfaction: 4.3 },
  ];

  const reportCategories = [
    {
      title: "Occupancy Reports",
      description: "Room occupancy, allocation, and utilization reports",
      icon: Building,
      reports: [
        { name: "Room Occupancy Report", description: "Current room occupancy and availability" },
        { name: "Student Allocation Report", description: "Student room assignments and history" },
        { name: "Block Utilization Report", description: "Utilization rates by hostel block" },
        { name: "Vacancy Analysis Report", description: "Available rooms and booking trends" },
      ],
    },
    {
      title: "Financial Reports",
      description: "Revenue, fees, and financial analytics",
      icon: IndianRupee,
      reports: [
        { name: "Fee Collection Report", description: "Monthly fee collection and outstanding dues" },
        { name: "Revenue Analysis Report", description: "Revenue trends and projections" },
        { name: "Cost Breakdown Report", description: "Operational cost analysis" },
        { name: "Profit & Loss Report", description: "Hostel department P&L statement" },
      ],
    },
    {
      title: "Student Reports",
      description: "Student demographics and behavior analytics",
      icon: Users,
      reports: [
        { name: "Student Demographics Report", description: "Student distribution by class, gender, etc." },
        { name: "Check-in/Check-out Report", description: "Student movement and attendance" },
        { name: "Disciplinary Report", description: "Student conduct and disciplinary actions" },
        { name: "Satisfaction Survey Report", description: "Student feedback and satisfaction scores" },
      ],
    },
    {
      title: "Maintenance Reports",
      description: "Facility maintenance and service reports",
      icon: AlertTriangle,
      reports: [
        { name: "Maintenance Request Report", description: "Maintenance requests and resolution status" },
        { name: "Facility Condition Report", description: "Overall facility condition assessment" },
        { name: "Preventive Maintenance Report", description: "Scheduled maintenance activities" },
        { name: "Vendor Performance Report", description: "Service provider performance analysis" },
      ],
    },
  ];

  const generateReport = (reportName: string) => {
    alert(`Generating ${reportName}...`);
  };

  const exportReport = (format: string) => {
    alert(`Exporting report in ${format} format...`);
  };

  const profitMargin = Math.round(((hostelMetrics.monthlyRevenue - hostelMetrics.operationalCost) / hostelMetrics.monthlyRevenue) * 100);

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Hostel Reports & Analytics</h1>
            <p className="text-gray-600">Comprehensive hostel performance and analytics dashboard</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              Filter Period
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export All
            </Button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <IndianRupee className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    ₹{(hostelMetrics.monthlyRevenue / 100000).toFixed(1)}L
                  </div>
                  <p className="text-sm text-gray-600">Monthly Revenue</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Target className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {hostelMetrics.occupancyRate}%
                  </div>
                  <p className="text-sm text-gray-600">Occupancy Rate</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {hostelMetrics.collectionRate}%
                  </div>
                  <p className="text-sm text-gray-600">Collection Rate</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <UserCheck className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    {hostelMetrics.satisfactionScore}
                  </div>
                  <p className="text-sm text-gray-600">Satisfaction Score</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Period Selection */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Report Period</h3>
              <div className="flex gap-2">
                {["week", "month", "quarter", "year"].map((period) => (
                  <Button
                    key={period}
                    variant={selectedPeriod === period ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedPeriod(period)}
                    className="capitalize"
                  >
                    {period}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Performance Overview */}
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Block Performance */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Building className="h-5 w-5 mr-2" />
                Block Performance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {blockPerformance.map((block, index) => (
                  <div key={block.block} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-sm font-medium text-blue-600">
                        {block.block.charAt(block.block.length - 1)}
                      </div>
                      <div>
                        <div className="font-medium">{block.block}</div>
                        <div className="text-sm text-gray-500">{block.occupied}/{block.rooms} occupied</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">₹{(block.revenue / 100000).toFixed(1)}L</div>
                      <div className="text-sm text-gray-500">{Math.round((block.occupied / block.rooms) * 100)}% occupancy</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Monthly Trends */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="h-5 w-5 mr-2" />
                Monthly Trends
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {monthlyTrends.map((trend) => (
                  <div key={trend.month} className="flex items-center justify-between">
                    <div className="font-medium w-12">{trend.month}</div>
                    <div className="flex-1 mx-4">
                      <div className="flex justify-between text-sm mb-1">
                        <span>Revenue: ₹{(trend.revenue / 100000).toFixed(1)}L</span>
                        <span>Occupancy: {trend.occupancy}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-green-600 h-2 rounded-full"
                          style={{ width: `${trend.occupancy}%` }}
                        />
                      </div>
                    </div>
                    <div className="text-sm text-gray-500 w-16 text-right">
                      {trend.students} students
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Report Categories */}
        <div className="grid gap-6 lg:grid-cols-2">
          {reportCategories.map((category) => (
            <Card key={category.title}>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <category.icon className="h-5 w-5 mr-2" />
                  {category.title}
                </CardTitle>
                <p className="text-sm text-gray-600">{category.description}</p>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {category.reports.map((report) => (
                    <div key={report.name} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                      <div className="flex-1">
                        <div className="font-medium text-gray-900">{report.name}</div>
                        <div className="text-sm text-gray-500">{report.description}</div>
                      </div>
                      <div className="flex space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => generateReport(report.name)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => exportReport("PDF")}
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Cost Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <PieChart className="h-5 w-5 mr-2" />
              Cost Breakdown Analysis
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-4">
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-red-600 mb-2">
                  ₹{(hostelMetrics.maintenanceCost / 1000).toFixed(0)}K
                </div>
                <div className="text-sm text-gray-600">Maintenance</div>
                <div className="text-xs text-gray-500 mt-1">
                  {Math.round((hostelMetrics.maintenanceCost / hostelMetrics.operationalCost) * 100)}% of total
                </div>
              </div>

              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-yellow-600 mb-2">
                  ₹{(hostelMetrics.staffSalaries / 1000).toFixed(0)}K
                </div>
                <div className="text-sm text-gray-600">Staff Salaries</div>
                <div className="text-xs text-gray-500 mt-1">
                  {Math.round((hostelMetrics.staffSalaries / hostelMetrics.operationalCost) * 100)}% of total
                </div>
              </div>

              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-blue-600 mb-2">
                  ₹{(hostelMetrics.utilityCost / 1000).toFixed(0)}K
                </div>
                <div className="text-sm text-gray-600">Utilities</div>
                <div className="text-xs text-gray-500 mt-1">
                  {Math.round((hostelMetrics.utilityCost / hostelMetrics.operationalCost) * 100)}% of total
                </div>
              </div>

              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-green-600 mb-2">
                  ₹{((hostelMetrics.monthlyRevenue - hostelMetrics.operationalCost) / 1000).toFixed(0)}K
                </div>
                <div className="text-sm text-gray-600">Net Profit</div>
                <div className="flex items-center justify-center mt-1 text-green-600">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  <span className="text-xs">{profitMargin}% margin</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Export Options */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              Export Options
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-4">
              <Button
                variant="outline"
                className="h-20 flex flex-col items-center justify-center"
                onClick={() => exportReport("PDF")}
              >
                <FileText className="h-6 w-6 mb-2" />
                Export as PDF
              </Button>

              <Button
                variant="outline"
                className="h-20 flex flex-col items-center justify-center"
                onClick={() => exportReport("Excel")}
              >
                <BarChart3 className="h-6 w-6 mb-2" />
                Export as Excel
              </Button>

              <Button
                variant="outline"
                className="h-20 flex flex-col items-center justify-center"
                onClick={() => exportReport("CSV")}
              >
                <Download className="h-6 w-6 mb-2" />
                Export as CSV
              </Button>

              <Button
                variant="outline"
                className="h-20 flex flex-col items-center justify-center"
                onClick={() => exportReport("Print")}
              >
                <FileText className="h-6 w-6 mb-2" />
                Print Report
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}