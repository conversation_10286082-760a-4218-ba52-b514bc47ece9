"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  User,
  Search,
  Filter,
  Download,
  Plus,
  Eye,
  Edit,
  Trash2,
  Phone,
  Mail,
  Calendar,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  Car,
  MapPin,
  Award,
  FileText,
  Users,
} from "lucide-react";

export default function TransportDrivers() {
  const [user, setUser] = useState<any>(null);
  const [selectedTab, setSelectedTab] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedDriver, setSelectedDriver] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "transport_manager") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  const driverStats = {
    totalDrivers: 15,
    activeDrivers: 12,
    onLeaveDrivers: 2,
    unassignedDrivers: 3,
    experiencedDrivers: 8,
    newDrivers: 4,
  };

  const mockDrivers = [
    {
      id: "DR001",
      name: "Rajesh Kumar",
      employeeId: "EMP001",
      phone: "+91 98765 43210",
      email: "<EMAIL>",
      licenseNumber: "DL-**********",
      licenseExpiry: "2025-06-15",
      experience: "8 years",
      joinDate: "2020-03-15",
      vehicleAssigned: "DL-1CA-1234",
      routeAssigned: "Route A - Central Delhi",
      status: "Active",
      rating: 4.8,
      totalTrips: 1245,
      safetyRecord: "Excellent",
      lastMedicalCheckup: "2024-01-10",
      address: "123 Main Street, Delhi",
      emergencyContact: "+91 98765 43211",
    },
    {
      id: "DR002",
      name: "Suresh Singh",
      employeeId: "EMP002",
      phone: "+91 98765 43212",
      email: "<EMAIL>",
      licenseNumber: "DL-**********",
      licenseExpiry: "2024-12-20",
      experience: "12 years",
      joinDate: "2018-08-10",
      vehicleAssigned: "DL-1CB-5678",
      routeAssigned: "Route B - South Delhi",
      status: "Active",
      rating: 4.9,
      totalTrips: 2156,
      safetyRecord: "Excellent",
      lastMedicalCheckup: "2024-01-15",
      address: "456 Park Avenue, Delhi",
      emergencyContact: "+91 98765 43213",
    },
    {
      id: "DR003",
      name: "Amit Sharma",
      employeeId: "EMP003",
      phone: "+91 98765 43214",
      email: "<EMAIL>",
      licenseNumber: "DL-**********",
      licenseExpiry: "2025-03-10",
      experience: "5 years",
      joinDate: "2021-11-20",
      vehicleAssigned: "DL-1CD-3456",
      routeAssigned: "Route C - East Delhi",
      status: "Active",
      rating: 4.6,
      totalTrips: 876,
      safetyRecord: "Good",
      lastMedicalCheckup: "2024-01-08",
      address: "789 Garden Road, Delhi",
      emergencyContact: "+91 98765 43215",
    },
    {
      id: "DR004",
      name: "Vikram Yadav",
      employeeId: "EMP004",
      phone: "+91 98765 43216",
      email: "<EMAIL>",
      licenseNumber: "DL-**********",
      licenseExpiry: "2024-09-25",
      experience: "15 years",
      joinDate: "2016-05-12",
      vehicleAssigned: "Not Assigned",
      routeAssigned: "Not Assigned",
      status: "On Leave",
      rating: 4.7,
      totalTrips: 3245,
      safetyRecord: "Excellent",
      lastMedicalCheckup: "2023-12-20",
      address: "321 Hill View, Delhi",
      emergencyContact: "+91 98765 43217",
    },
    {
      id: "DR005",
      name: "Ravi Gupta",
      employeeId: "EMP005",
      phone: "+91 98765 43218",
      email: "<EMAIL>",
      licenseNumber: "DL-**********",
      licenseExpiry: "2025-11-30",
      experience: "2 years",
      joinDate: "2023-01-15",
      vehicleAssigned: "Not Assigned",
      routeAssigned: "Not Assigned",
      status: "Available",
      rating: 4.3,
      totalTrips: 234,
      safetyRecord: "Good",
      lastMedicalCheckup: "2024-01-05",
      address: "654 Valley Street, Delhi",
      emergencyContact: "+91 98765 43219",
    },
  ];

  const filteredDrivers = mockDrivers.filter((driver) => {
    const matchesSearch = driver.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         driver.employeeId.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         driver.phone.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         driver.vehicleAssigned.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         driver.routeAssigned.toLowerCase().includes(searchTerm.toLowerCase());

    if (selectedTab === "all") return matchesSearch;
    if (selectedTab === "active") return matchesSearch && driver.status === "Active";
    if (selectedTab === "available") return matchesSearch && driver.status === "Available";
    if (selectedTab === "on_leave") return matchesSearch && driver.status === "On Leave";
    if (selectedTab === "unassigned") return matchesSearch && driver.vehicleAssigned === "Not Assigned";

    return matchesSearch;
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Active":
        return <Badge className="bg-green-100 text-green-800">Active</Badge>;
      case "Available":
        return <Badge className="bg-blue-100 text-blue-800">Available</Badge>;
      case "On Leave":
        return <Badge className="bg-yellow-100 text-yellow-800">On Leave</Badge>;
      case "Suspended":
        return <Badge className="bg-red-100 text-red-800">Suspended</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }
  };

  const getSafetyBadge = (safety: string) => {
    switch (safety) {
      case "Excellent":
        return <Badge variant="outline" className="text-green-600 border-green-200">Excellent</Badge>;
      case "Good":
        return <Badge variant="outline" className="text-blue-600 border-blue-200">Good</Badge>;
      case "Fair":
        return <Badge variant="outline" className="text-yellow-600 border-yellow-200">Fair</Badge>;
      case "Poor":
        return <Badge variant="outline" className="text-red-600 border-red-200">Poor</Badge>;
      default:
        return <Badge variant="outline">{safety}</Badge>;
    }
  };

  const viewDriverDetails = (driver: any) => {
    setSelectedDriver(driver);
  };

  const editDriver = (driverId: string) => {
    alert(`Editing driver ${driverId}`);
  };

  const assignVehicle = (driverId: string) => {
    alert(`Assigning vehicle to driver ${driverId}`);
  };

  const scheduleTraining = (driverId: string) => {
    alert(`Scheduling training for driver ${driverId}`);
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Driver Management</h1>
            <p className="text-gray-600">Manage transport drivers, assignments, and performance</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Driver List
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Driver
            </Button>
          </div>
        </div>

        {/* Driver Statistics */}
        <div className="grid gap-4 md:grid-cols-3 lg:grid-cols-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {driverStats.totalDrivers}
                  </div>
                  <p className="text-sm text-gray-600">Total Drivers</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {driverStats.activeDrivers}
                  </div>
                  <p className="text-sm text-gray-600">Active</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-yellow-600">
                    {driverStats.onLeaveDrivers}
                  </div>
                  <p className="text-sm text-gray-600">On Leave</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Car className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {driverStats.unassignedDrivers}
                  </div>
                  <p className="text-sm text-gray-600">Unassigned</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Award className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    {driverStats.experiencedDrivers}
                  </div>
                  <p className="text-sm text-gray-600">Experienced</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <User className="h-8 w-8 text-indigo-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-indigo-600">
                    {driverStats.newDrivers}
                  </div>
                  <p className="text-sm text-gray-600">New Drivers</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filter */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search by name, employee ID, phone, vehicle, or route..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </div>

            {/* Tabs */}
            <div className="flex flex-wrap gap-2 mt-4">
              {[
                { key: "all", label: "All Drivers", count: driverStats.totalDrivers },
                { key: "active", label: "Active", count: driverStats.activeDrivers },
                { key: "available", label: "Available", count: 3 },
                { key: "on_leave", label: "On Leave", count: driverStats.onLeaveDrivers },
                { key: "unassigned", label: "Unassigned", count: driverStats.unassignedDrivers },
              ].map((tab) => (
                <Button
                  key={tab.key}
                  variant={selectedTab === tab.key ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedTab(tab.key)}
                  className="text-xs"
                >
                  {tab.label} ({tab.count})
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Drivers Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Driver Directory ({filteredDrivers.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-4 font-medium text-gray-900">Driver Details</th>
                    <th className="text-left p-4 font-medium text-gray-900">Contact & License</th>
                    <th className="text-left p-4 font-medium text-gray-900">Assignment</th>
                    <th className="text-left p-4 font-medium text-gray-900">Performance</th>
                    <th className="text-left p-4 font-medium text-gray-900">Status</th>
                    <th className="text-left p-4 font-medium text-gray-900">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredDrivers.map((driver) => (
                    <tr key={driver.id} className="border-b hover:bg-gray-50">
                      <td className="p-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <User className="h-6 w-6 text-blue-600" />
                          </div>
                          <div>
                            <div className="font-medium text-gray-900">{driver.name}</div>
                            <div className="text-sm text-gray-500">{driver.employeeId}</div>
                            <div className="text-sm text-gray-500">{driver.experience} experience</div>
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-1">
                          <div className="text-sm">
                            <Phone className="h-3 w-3 inline mr-1" />
                            {driver.phone}
                          </div>
                          <div className="text-sm">
                            <FileText className="h-3 w-3 inline mr-1" />
                            {driver.licenseNumber}
                          </div>
                          <div className="text-sm text-gray-500">
                            Expires: {new Date(driver.licenseExpiry).toLocaleDateString()}
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-1">
                          <div className="font-medium text-gray-900">{driver.vehicleAssigned}</div>
                          <div className="text-sm text-gray-500">{driver.routeAssigned}</div>
                          <div className="text-xs text-gray-400">
                            Joined: {new Date(driver.joinDate).toLocaleDateString()}
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-2">
                          <div className="flex items-center space-x-2">
                            <span className="text-sm font-medium">Rating:</span>
                            <span className="text-sm text-yellow-600">★ {driver.rating}</span>
                          </div>
                          <div className="text-sm text-gray-500">
                            {driver.totalTrips.toLocaleString()} trips
                          </div>
                          {getSafetyBadge(driver.safetyRecord)}
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-2">
                          {getStatusBadge(driver.status)}
                          <div className="text-xs text-gray-500">
                            Medical: {new Date(driver.lastMedicalCheckup).toLocaleDateString()}
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => viewDriverDetails(driver)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => editDriver(driver.id)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          {driver.vehicleAssigned === "Not Assigned" && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => assignVehicle(driver.id)}
                            >
                              <Car className="h-4 w-4" />
                            </Button>
                          )}
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => scheduleTraining(driver.id)}
                          >
                            <Award className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}