"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";

export default function AdminHostelRedirect() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the main hostel management page
    router.replace("/hostel");
  }, [router]);

  return (
    <div className="flex items-center justify-center h-64">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p className="mt-2 text-gray-600">Redirecting to Hostel Management...</p>
      </div>
    </div>
  );
}
