"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  BookOpen,
  Users,
  Calendar,
  Clock,
  MapPin,
  Plus,
  Edit,
  Eye,
  BarChart3,
  FileText,
  CheckCircle,
  AlertTriangle,
  TrendingUp,
  GraduationCap,
} from "lucide-react";

export default function TeacherClasses() {
  const [user, setUser] = useState<any>(null);
  const [selectedClass, setSelectedClass] = useState<string | null>(null);
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "teacher") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock classes data
  const myClasses = [
    {
      id: "CLS001",
      name: "Mathematics - Grade 10A",
      subject: "Mathematics",
      grade: "10A",
      students: 32,
      schedule: [
        { day: "Monday", time: "08:00 - 09:00", room: "Room 101" },
        { day: "Wednesday", time: "10:00 - 11:00", room: "Room 101" },
        { day: "Friday", time: "12:30 - 13:30", room: "Room 101" },
      ],
      averageGrade: 85.5,
      attendanceRate: 94.2,
      assignmentsPending: 5,
      nextClass: "Monday, 08:00 - 09:00",
      syllabus: "Calculus, Trigonometry, Algebra",
      description: "Advanced mathematics covering calculus and trigonometry for grade 10 students.",
    },
    {
      id: "CLS002", 
      name: "Mathematics - Grade 10B",
      subject: "Mathematics",
      grade: "10B",
      students: 28,
      schedule: [
        { day: "Tuesday", time: "09:00 - 10:00", room: "Room 101" },
        { day: "Thursday", time: "11:30 - 12:30", room: "Room 101" },
        { day: "Friday", time: "14:30 - 15:30", room: "Room 101" },
      ],
      averageGrade: 82.3,
      attendanceRate: 91.8,
      assignmentsPending: 3,
      nextClass: "Tuesday, 09:00 - 10:00",
      syllabus: "Calculus, Trigonometry, Algebra",
      description: "Advanced mathematics covering calculus and trigonometry for grade 10 students.",
    },
    {
      id: "CLS003",
      name: "Advanced Mathematics - Grade 11A",
      subject: "Mathematics",
      grade: "11A",
      students: 25,
      schedule: [
        { day: "Monday", time: "10:00 - 11:00", room: "Room 102" },
        { day: "Wednesday", time: "14:30 - 15:30", room: "Room 102" },
        { day: "Thursday", time: "08:00 - 09:00", room: "Room 102" },
      ],
      averageGrade: 88.7,
      attendanceRate: 96.5,
      assignmentsPending: 2,
      nextClass: "Monday, 10:00 - 11:00",
      syllabus: "Advanced Calculus, Statistics, Probability",
      description: "Advanced mathematics for grade 11 students preparing for competitive exams.",
    },
    {
      id: "CLS004",
      name: "Mathematics Remedial - Grade 9",
      subject: "Mathematics",
      grade: "9",
      students: 15,
      schedule: [
        { day: "Tuesday", time: "15:30 - 16:30", room: "Room 103" },
        { day: "Thursday", time: "15:30 - 16:30", room: "Room 103" },
      ],
      averageGrade: 72.1,
      attendanceRate: 88.3,
      assignmentsPending: 8,
      nextClass: "Tuesday, 15:30 - 16:30",
      syllabus: "Basic Algebra, Geometry, Number Systems",
      description: "Remedial mathematics class for students who need additional support.",
    },
    {
      id: "CLS005",
      name: "Mathematics Olympiad Prep",
      subject: "Mathematics",
      grade: "Mixed",
      students: 12,
      schedule: [
        { day: "Saturday", time: "09:00 - 11:00", room: "Room 104" },
      ],
      averageGrade: 95.2,
      attendanceRate: 98.7,
      assignmentsPending: 1,
      nextClass: "Saturday, 09:00 - 11:00",
      syllabus: "Competition Mathematics, Problem Solving",
      description: "Preparation class for mathematics olympiad and competitions.",
    },
  ];

  const recentActivities = [
    { type: "assignment", class: "Grade 10A", activity: "Calculus Assignment 3 graded", time: "2 hours ago" },
    { type: "attendance", class: "Grade 11A", activity: "Attendance marked for today", time: "4 hours ago" },
    { type: "announcement", class: "Grade 10B", activity: "Posted exam schedule", time: "1 day ago" },
    { type: "grade", class: "Olympiad Prep", activity: "Updated quiz scores", time: "2 days ago" },
  ];

  const upcomingClasses = [
    { class: "Mathematics - Grade 10A", time: "Tomorrow, 08:00 - 09:00", room: "Room 101", students: 32 },
    { class: "Mathematics - Grade 10B", time: "Tomorrow, 09:00 - 10:00", room: "Room 101", students: 28 },
    { class: "Advanced Mathematics - Grade 11A", time: "Tomorrow, 10:00 - 11:00", room: "Room 102", students: 25 },
  ];

  const classStats = {
    totalClasses: myClasses.length,
    totalStudents: myClasses.reduce((sum, cls) => sum + cls.students, 0),
    averageAttendance: myClasses.reduce((sum, cls) => sum + cls.attendanceRate, 0) / myClasses.length,
    pendingAssignments: myClasses.reduce((sum, cls) => sum + cls.assignmentsPending, 0),
  };

  const getGradeColor = (grade: number) => {
    if (grade >= 90) return "text-green-600";
    if (grade >= 80) return "text-blue-600";
    if (grade >= 70) return "text-yellow-600";
    return "text-red-600";
  };

  const getAttendanceColor = (rate: number) => {
    if (rate >= 95) return "text-green-600";
    if (rate >= 90) return "text-blue-600";
    if (rate >= 85) return "text-yellow-600";
    return "text-red-600";
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">My Classes</h1>
            <p className="text-gray-600">Manage your classes and track student progress</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <FileText className="h-4 w-4 mr-2" />
              Reports
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Assignment
            </Button>
          </div>
        </div>

        {/* Class Statistics */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <BookOpen className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {classStats.totalClasses}
                  </div>
                  <p className="text-sm text-gray-600">Total Classes</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {classStats.totalStudents}
                  </div>
                  <p className="text-sm text-gray-600">Total Students</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {classStats.averageAttendance.toFixed(1)}%
                  </div>
                  <p className="text-sm text-gray-600">Avg Attendance</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <AlertTriangle className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    {classStats.pendingAssignments}
                  </div>
                  <p className="text-sm text-gray-600">Pending Grades</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Grid */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Classes List */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center">
                <GraduationCap className="h-5 w-5 mr-2" />
                My Classes
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {myClasses.map((classInfo) => (
                  <div key={classInfo.id} className="border rounded-lg p-6 hover:shadow-md transition-shadow">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900 mb-2">{classInfo.name}</h3>
                        <p className="text-gray-600 text-sm mb-3">{classInfo.description}</p>
                        
                        <div className="grid gap-4 md:grid-cols-3 mb-4">
                          <div className="flex items-center space-x-2">
                            <Users className="h-4 w-4 text-gray-500" />
                            <span className="text-sm text-gray-600">{classInfo.students} students</span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <BarChart3 className="h-4 w-4 text-gray-500" />
                            <span className={`text-sm font-medium ${getGradeColor(classInfo.averageGrade)}`}>
                              {classInfo.averageGrade}% avg grade
                            </span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <CheckCircle className="h-4 w-4 text-gray-500" />
                            <span className={`text-sm font-medium ${getAttendanceColor(classInfo.attendanceRate)}`}>
                              {classInfo.attendanceRate}% attendance
                            </span>
                          </div>
                        </div>

                        <div className="mb-4">
                          <h4 className="text-sm font-medium text-gray-900 mb-2">Schedule:</h4>
                          <div className="space-y-1">
                            {classInfo.schedule.map((schedule, index) => (
                              <div key={index} className="flex items-center space-x-4 text-sm text-gray-600">
                                <span className="w-20">{schedule.day}</span>
                                <span className="w-32">{schedule.time}</span>
                                <span>{schedule.room}</span>
                              </div>
                            ))}
                          </div>
                        </div>

                        {classInfo.assignmentsPending > 0 && (
                          <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
                            {classInfo.assignmentsPending} assignments to grade
                          </Badge>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center justify-between pt-4 border-t">
                      <div className="text-sm text-gray-500">
                        Next class: {classInfo.nextClass}
                      </div>
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4 mr-1" />
                          Manage
                        </Button>
                        <Button size="sm">
                          <Users className="h-4 w-4 mr-1" />
                          Students
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Upcoming Classes */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Clock className="h-5 w-5 mr-2" />
                  Upcoming Classes
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {upcomingClasses.map((cls, index) => (
                    <div key={index} className="p-3 bg-gray-50 rounded-lg">
                      <div className="font-medium text-gray-900 text-sm">{cls.class}</div>
                      <div className="text-sm text-gray-500 mt-1">{cls.time}</div>
                      <div className="flex items-center justify-between mt-2">
                        <span className="text-xs text-gray-500">{cls.room}</span>
                        <span className="text-xs text-gray-500">{cls.students} students</span>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Recent Activities */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <TrendingUp className="h-5 w-5 mr-2" />
                  Recent Activities
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {recentActivities.map((activity, index) => (
                    <div key={index} className="flex items-start space-x-3">
                      <div className={`w-2 h-2 rounded-full mt-2 ${
                        activity.type === 'assignment' ? 'bg-blue-500' :
                        activity.type === 'attendance' ? 'bg-green-500' :
                        activity.type === 'announcement' ? 'bg-yellow-500' : 'bg-purple-500'
                      }`} />
                      <div className="flex-1">
                        <div className="text-sm font-medium text-gray-900">{activity.activity}</div>
                        <div className="text-xs text-gray-500">{activity.class} • {activity.time}</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
