"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Plus, Edit, Grid3X3, Search, Users, Building } from "lucide-react";
import { toast } from "sonner";

interface AcademicSection {
  id: string;
  name: string;
  displayName: string;
  capacity: number;
  occupiedSeats: number;
  availableSeats: number;
  room?: string;
  academicYear: string;
  semester: string;
  isActive: boolean;
  status: string;
  program?: {
    id: string;
    name: string;
    code: string;
  };
  batch?: {
    id: string;
    batchName: string;
    startYear: number;
    endYear: number;
  };
  stream?: {
    id: string;
    name: string;
    code: string;
  };
  classTeacher?: {
    id: string;
    firstName: string;
    lastName: string;
  };
}

interface Program {
  id: string;
  name: string;
  code: string;
}

interface Batch {
  id: string;
  batchName: string;
  programId: string;
}

interface Stream {
  id: string;
  name: string;
  code: string;
}

interface Teacher {
  id: string;
  firstName: string;
  lastName: string;
}

export default function AcademicSectionsPage() {
  const [user, setUser] = useState<any>(null);
  const [sections, setSections] = useState<AcademicSection[]>([]);
  const [programs, setPrograms] = useState<Program[]>([]);
  const [batches, setBatches] = useState<Batch[]>([]);
  const [streams, setStreams] = useState<Stream[]>([]);
  const [teachers, setTeachers] = useState<Teacher[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [editingSection, setEditingSection] = useState<AcademicSection | null>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [filterProgram, setFilterProgram] = useState<string>("all");
  const [filterBatch, setFilterBatch] = useState<string>("all");
  const [isSaving, setIsSaving] = useState(false);
  const router = useRouter();

  const [formData, setFormData] = useState({
    programId: "",
    batchId: "",
    streamId: "",
    name: "",
    displayName: "",
    capacity: 30,
    classTeacherId: "",
    room: "",
    academicYear: "2024-25",
    semester: "1",
  });

  const fetchInitialData = useCallback(async () => {
    try {
      await Promise.all([
        fetchSections(),
        fetchPrograms(),
        fetchStreams(),
        fetchTeachers(),
      ]);
    } catch (error) {
      console.error("Error fetching initial data:", error);
      toast.error("Failed to load data");
    } finally {
      setIsLoading(false);
    }
  }, []);

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (!["super_admin", "admin"].includes(parsedUser.role)) {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
    fetchInitialData();
  }, [router, fetchInitialData]);

  const fetchSections = async () => {
    try {
      const response = await fetch("/api/academic-sections");
      const result = await response.json();

      if (response.ok) {
        setSections(result.data);
      } else {
        toast.error(result.error || "Failed to fetch sections");
      }
    } catch (error) {
      console.error("Error fetching sections:", error);
    }
  };

  const fetchPrograms = async () => {
    try {
      const response = await fetch("/api/academic-programs");
      const result = await response.json();

      if (response.ok) {
        setPrograms(result.data);
      }
    } catch (error) {
      console.error("Error fetching programs:", error);
    }
  };

  const fetchBatches = async (programId?: string) => {
    try {
      const params = new URLSearchParams();
      if (programId) {
        params.append("programId", programId);
      }

      const response = await fetch(`/api/academic-batches?${params}`);
      const result = await response.json();

      if (response.ok) {
        setBatches(result.data);
      }
    } catch (error) {
      console.error("Error fetching batches:", error);
    }
  };

  const fetchStreams = async () => {
    try {
      const response = await fetch("/api/academic-streams?isActive=true");
      const result = await response.json();

      if (response.ok) {
        setStreams(result.data);
      }
    } catch (error) {
      console.error("Error fetching streams:", error);
    }
  };

  const fetchTeachers = async () => {
    try {
      const response = await fetch("/api/teachers");
      const result = await response.json();

      if (response.ok) {
        setTeachers(result.data);
      }
    } catch (error) {
      console.error("Error fetching teachers:", error);
    }
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSaving(true);

    try {
      const url = editingSection ? `/api/academic-sections/${editingSection.id}` : "/api/academic-sections";
      const method = editingSection ? "PUT" : "POST";

      const response = await fetch(url, {
        method,
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(formData),
      });

      const result = await response.json();

      if (response.ok) {
        toast.success(result.message);
        setIsDialogOpen(false);
        resetForm();
        fetchSections();
      } else {
        toast.error(result.error || "Failed to save section");
      }
    } catch (error) {
      console.error("Error saving section:", error);
      toast.error("Failed to save academic section");
    } finally {
      setIsSaving(false);
    }
  };

  const handleEdit = (section: AcademicSection) => {
    setEditingSection(section);
    setFormData({
      programId: section.program?.id || "",
      batchId: section.batch?.id || "",
      streamId: section.stream?.id || "",
      name: section.name,
      displayName: section.displayName,
      capacity: section.capacity,
      classTeacherId: section.classTeacher?.id || "",
      room: section.room || "",
      academicYear: section.academicYear,
      semester: section.semester,
    });

    // Fetch batches for the selected program
    if (section.program?.id) {
      fetchBatches(section.program.id);
    }

    setIsDialogOpen(true);
  };

  const resetForm = () => {
    setFormData({
      programId: "",
      batchId: "",
      streamId: "",
      name: "",
      displayName: "",
      capacity: 30,
      classTeacherId: "",
      room: "",
      academicYear: "2024-25",
      semester: "1",
    });
    setEditingSection(null);
    setBatches([]);
  };

  const handleProgramChange = (programId: string) => {
    setFormData(prev => ({ ...prev, programId, batchId: "" }));
    fetchBatches(programId);
  };

  const filteredSections = sections.filter(section => {
    const matchesSearch = section.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         section.displayName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         section.program?.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesProgram = filterProgram === "all" || section.program?.id === filterProgram;
    const matchesBatch = filterBatch === "all" || section.batch?.id === filterBatch;
    return matchesSearch && matchesProgram && matchesBatch;
  });

  if (isLoading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading sections...</p>
          </div>
        </div>
      </AppLayout>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
              <Grid3X3 className="h-6 w-6" />
              Academic Sections
            </h1>
            <p className="text-gray-600">
              Manage sections within programs and batches
            </p>
          </div>
          <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
            <DialogTrigger asChild>
              <Button onClick={resetForm}>
                <Plus className="h-4 w-4 mr-2" />
                Add Section
              </Button>
            </DialogTrigger>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle>
                  {editingSection ? "Edit" : "Add"} Academic Section
                </DialogTitle>
                <DialogDescription>
                  {editingSection ? "Update the" : "Create a new"} academic section.
                </DialogDescription>
              </DialogHeader>

              <form onSubmit={handleSubmit} className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="programId">Program *</Label>
                    <Select
                      value={formData.programId}
                      onValueChange={handleProgramChange}
                      required
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select program" />
                      </SelectTrigger>
                      <SelectContent>
                        {programs.map(program => (
                          <SelectItem key={program.id} value={program.id}>
                            {program.name} ({program.code})
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                  <div>
                    <Label htmlFor="batchId">Batch *</Label>
                    <Select
                      value={formData.batchId}
                      onValueChange={(value) => setFormData(prev => ({ ...prev, batchId: value }))}
                      required
                      disabled={!formData.programId}
                    >
                      <SelectTrigger>
                        <SelectValue placeholder="Select batch" />
                      </SelectTrigger>
                      <SelectContent>
                        {batches.map(batch => (
                          <SelectItem key={batch.id} value={batch.id}>
                            {batch.batchName}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <Label htmlFor="name">Section Name *</Label>
                    <Input
                      id="name"
                      value={formData.name}
                      onChange={(e) => setFormData(prev => ({ ...prev, name: e.target.value }))}
                      placeholder="Enter section name (e.g., A, B, C)"
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="displayName">Display Name *</Label>
                    <Input
                      id="displayName"
                      value={formData.displayName}
                      onChange={(e) => setFormData(prev => ({ ...prev, displayName: e.target.value }))}
                      placeholder="Enter display name (e.g., Section A)"
                      required
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div>
                    <Label htmlFor="capacity">Capacity *</Label>
                    <Input
                      id="capacity"
                      type="number"
                      min="1"
                      value={formData.capacity}
                      onChange={(e) => setFormData(prev => ({ ...prev, capacity: parseInt(e.target.value) }))}
                      required
                    />
                  </div>
                  <div>
                    <Label htmlFor="room">Room</Label>
                    <Input
                      id="room"
                      value={formData.room}
                      onChange={(e) => setFormData(prev => ({ ...prev, room: e.target.value }))}
                      placeholder="Enter room number"
                    />
                  </div>
                  <div>
                    <Label htmlFor="academicYear">Academic Year *</Label>
                    <Input
                      id="academicYear"
                      value={formData.academicYear}
                      onChange={(e) => setFormData(prev => ({ ...prev, academicYear: e.target.value }))}
                      placeholder="2024-25"
                      required
                    />
                  </div>
                </div>

                <div className="flex justify-end gap-2">
                  <Button type="button" variant="outline" onClick={() => setIsDialogOpen(false)}>
                    Cancel
                  </Button>
                  <Button type="submit" disabled={isSaving}>
                    {isSaving ? "Saving..." : editingSection ? "Update" : "Create"}
                  </Button>
                </div>
              </form>
            </DialogContent>
          </Dialog>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="pt-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search sections..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="w-full sm:w-48">
                <Select value={filterProgram} onValueChange={setFilterProgram}>
                  <SelectTrigger>
                    <SelectValue placeholder="Filter by program" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Programs</SelectItem>
                    {programs.map(program => (
                      <SelectItem key={program.id} value={program.id}>
                        {program.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Sections Table */}
        <Card>
          <CardHeader>
            <CardTitle>Academic Sections ({filteredSections.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>Section</TableHead>
                    <TableHead>Program</TableHead>
                    <TableHead>Batch</TableHead>
                    <TableHead>Capacity</TableHead>
                    <TableHead>Occupied</TableHead>
                    <TableHead>Available</TableHead>
                    <TableHead>Room</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead className="text-right">Actions</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {filteredSections.map((section) => (
                    <TableRow key={section.id}>
                      <TableCell className="font-medium">{section.displayName}</TableCell>
                      <TableCell>
                        <div>
                          <div className="font-medium">{section.program?.name}</div>
                          <div className="text-sm text-gray-500">{section.program?.code}</div>
                        </div>
                      </TableCell>
                      <TableCell>{section.batch?.batchName}</TableCell>
                      <TableCell>
                        <div className="flex items-center gap-1">
                          <Users className="h-3 w-3" />
                          {section.capacity}
                        </div>
                      </TableCell>
                      <TableCell>{section.occupiedSeats}</TableCell>
                      <TableCell>
                        <Badge variant={section.availableSeats > 0 ? "default" : "destructive"}>
                          {section.availableSeats}
                        </Badge>
                      </TableCell>
                      <TableCell>{section.room || "-"}</TableCell>
                      <TableCell>
                        <Badge variant={section.isActive ? "default" : "secondary"}>
                          {section.isActive ? "Active" : "Inactive"}
                        </Badge>
                      </TableCell>
                      <TableCell className="text-right">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleEdit(section)}
                        >
                          <Edit className="h-3 w-3" />
                        </Button>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>

              {filteredSections.length === 0 && (
                <div className="text-center py-8 text-gray-500">
                  No sections found
                </div>
              )}
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
