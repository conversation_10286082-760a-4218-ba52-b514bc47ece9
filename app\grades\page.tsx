"use client";

import { useState } from "react";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Plus, TrendingUp, Award, BookOpen, FileText } from "lucide-react";

// Mock grades data
const mockGrades = [
  {
    id: "1",
    studentId: "1",
    studentName: "<PERSON> Doe",
    studentGrade: "10A",
    classId: "1",
    className: "Algebra I",
    assignmentName: "Midterm Exam",
    grade: 85,
    maxGrade: 100,
    type: "exam",
    date: "2024-01-10",
    notes: "Good performance",
  },
  {
    id: "2",
    studentId: "2",
    studentName: "<PERSON> Smith",
    studentGrade: "10A",
    classId: "1",
    className: "Algebra I",
    assignmentName: "Homework 1",
    grade: 92,
    maxGrade: 100,
    type: "assignment",
    date: "2024-01-05",
    notes: "Excellent work",
  },
  {
    id: "3",
    studentId: "3",
    studentName: "<PERSON>",
    studentGrade: "11B",
    classId: "2",
    className: "Physics I",
    assignmentName: "Lab Report 1",
    grade: 78,
    maxGrade: 100,
    type: "project",
    date: "2024-01-08",
    notes: "Needs improvement in analysis",
  },
  {
    id: "4",
    studentId: "1",
    studentName: "John Doe",
    studentGrade: "10A",
    classId: "1",
    className: "Algebra I",
    assignmentName: "Quiz 1",
    grade: 88,
    maxGrade: 100,
    type: "quiz",
    date: "2024-01-03",
    notes: "",
  },
];

const typeIcons = {
  exam: Award,
  assignment: FileText,
  quiz: BookOpen,
  project: TrendingUp,
  participation: TrendingUp,
};

const typeColors = {
  exam: "text-red-600 bg-red-100",
  assignment: "text-blue-600 bg-blue-100",
  quiz: "text-green-600 bg-green-100",
  project: "text-purple-600 bg-purple-100",
  participation: "text-orange-600 bg-orange-100",
};

function getGradeColor(percentage: number) {
  if (percentage >= 90) return "text-green-600";
  if (percentage >= 80) return "text-blue-600";
  if (percentage >= 70) return "text-yellow-600";
  if (percentage >= 60) return "text-orange-600";
  return "text-red-600";
}

function getLetterGrade(percentage: number) {
  if (percentage >= 90) return "A";
  if (percentage >= 80) return "B";
  if (percentage >= 70) return "C";
  if (percentage >= 60) return "D";
  return "F";
}

export default function GradesPage() {
  const [search, setSearch] = useState("");
  const [filters, setFilters] = useState({
    class: "",
    type: "",
    student: "",
  });

  const filteredGrades = mockGrades.filter((grade) => {
    const matchesSearch = 
      grade.studentName.toLowerCase().includes(search.toLowerCase()) ||
      grade.className.toLowerCase().includes(search.toLowerCase()) ||
      grade.assignmentName.toLowerCase().includes(search.toLowerCase());
    
    const matchesClass = !filters.class || grade.className === filters.class;
    const matchesType = !filters.type || grade.type === filters.type;
    const matchesStudent = !filters.student || grade.studentName === filters.student;

    return matchesSearch && matchesClass && matchesType && matchesStudent;
  });

  // Calculate statistics
  const totalGrades = filteredGrades.length;
  const averageGrade = totalGrades > 0 
    ? Math.round(filteredGrades.reduce((sum, g) => sum + g.grade, 0) / totalGrades)
    : 0;
  
  const gradeDistribution = {
    A: filteredGrades.filter(g => g.grade >= 90).length,
    B: filteredGrades.filter(g => g.grade >= 80 && g.grade < 90).length,
    C: filteredGrades.filter(g => g.grade >= 70 && g.grade < 80).length,
    D: filteredGrades.filter(g => g.grade >= 60 && g.grade < 70).length,
    F: filteredGrades.filter(g => g.grade < 60).length,
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Grades</h1>
            <p className="text-gray-600">Manage student grades and assessments</p>
          </div>
          <Button className="sm:w-auto">
            <Plus className="h-4 w-4 mr-2" />
            Add Grade
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <TrendingUp className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {averageGrade}%
                  </div>
                  <p className="text-sm text-gray-600">Average Grade</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Award className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {gradeDistribution.A}
                  </div>
                  <p className="text-sm text-gray-600">A Grades</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <FileText className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {totalGrades}
                  </div>
                  <p className="text-sm text-gray-600">Total Grades</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <BookOpen className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    {getLetterGrade(averageGrade)}
                  </div>
                  <p className="text-sm text-gray-600">Average Letter</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search grades..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex gap-2">
                <select
                  value={filters.class}
                  onChange={(e) => setFilters({ ...filters, class: e.target.value })}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                >
                  <option value="">All Classes</option>
                  <option value="Algebra I">Algebra I</option>
                  <option value="Physics I">Physics I</option>
                  <option value="English Literature">English Literature</option>
                </select>
                <select
                  value={filters.type}
                  onChange={(e) => setFilters({ ...filters, type: e.target.value })}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                >
                  <option value="">All Types</option>
                  <option value="exam">Exam</option>
                  <option value="assignment">Assignment</option>
                  <option value="quiz">Quiz</option>
                  <option value="project">Project</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Grade Distribution Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Grade Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-5 gap-4">
              {Object.entries(gradeDistribution).map(([letter, count]) => (
                <div key={letter} className="text-center">
                  <div className="text-2xl font-bold text-gray-900">{count}</div>
                  <div className="text-sm text-gray-500">Grade {letter}</div>
                  <div className="mt-2 h-2 bg-gray-200 rounded-full">
                    <div
                      className="h-2 bg-blue-600 rounded-full"
                      style={{
                        width: totalGrades > 0 ? `${(count / totalGrades) * 100}%` : "0%",
                      }}
                    />
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Grades List */}
        <Card>
          <CardHeader>
            <CardTitle>
              Recent Grades
              <span className="text-sm font-normal text-gray-500 ml-2">
                ({filteredGrades.length} grades)
              </span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {filteredGrades.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">No grades found</p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredGrades.map((grade) => {
                  const TypeIcon = typeIcons[grade.type as keyof typeof typeIcons];
                  const percentage = Math.round((grade.grade / grade.maxGrade) * 100);
                  
                  return (
                    <div
                      key={grade.id}
                      className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-center space-x-4">
                        <div className={`p-2 rounded-full ${typeColors[grade.type as keyof typeof typeColors]}`}>
                          <TypeIcon className="h-4 w-4" />
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">
                            {grade.assignmentName}
                          </div>
                          <div className="text-sm text-gray-500">
                            {grade.studentName} • {grade.className}
                          </div>
                          <div className="text-xs text-gray-400 capitalize">
                            {grade.type} • {grade.date}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className={`text-lg font-bold ${getGradeColor(percentage)}`}>
                          {grade.grade}/{grade.maxGrade}
                        </div>
                        <div className={`text-sm font-medium ${getGradeColor(percentage)}`}>
                          {percentage}% ({getLetterGrade(percentage)})
                        </div>
                        {grade.notes && (
                          <div className="text-xs text-gray-500 mt-1 max-w-32 truncate">
                            {grade.notes}
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
