"use client";

import React, { useState } from "react";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardT<PERSON>le } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import {
  Calendar,
  Clock,
  Plus,
  Filter,
  BookOpen,
  User,
  MapPin,
  Grid3X3
} from "lucide-react";

// Mock timetable data
const mockTimetable = {
  monday: [
    { time: "09:00-10:00", subject: "Mathematics", teacher: "Dr. <PERSON>", room: "Room 101", class: "10A" },
    { time: "10:00-11:00", subject: "Physics", teacher: "Mr. <PERSON>", room: "Room 201", class: "11B" },
    { time: "11:30-12:30", subject: "Chemistry", teacher: "Dr. <PERSON>", room: "Room 301", class: "10A" },
    { time: "14:00-15:00", subject: "English", teacher: "<PERSON><PERSON> <PERSON>", room: "Room 102", class: "10A" },
  ],
  tuesday: [
    { time: "09:00-10:00", subject: "Physics", teacher: "Mr. <PERSON>", room: "Room 201", class: "10A" },
    { time: "10:00-11:00", subject: "Mathematics", teacher: "Dr. <PERSON>", room: "Room 101", class: "11B" },
    { time: "11:30-12:30", subject: "Biology", teacher: "Dr. <PERSON> <PERSON>", room: "Room 401", class: "10A" },
    { time: "14:00-15:00", subject: "History", teacher: "Mr. <PERSON> <PERSON>", room: "Room 103", class: "10A" },
  ],
  wednesday: [
    { time: "09:00-10:00", subject: "Mathematics", teacher: "Dr. Emily Wilson", room: "Room 101", class: "10A" },
    { time: "10:00-11:00", subject: "Chemistry", teacher: "Dr. Sarah Johnson", room: "Room 301", class: "11B" },
    { time: "11:30-12:30", subject: "English", teacher: "Ms. Lisa Anderson", room: "Room 102", class: "10A" },
    { time: "14:00-15:00", subject: "Physics Lab", teacher: "Mr. David Brown", room: "Physics Lab", class: "10A" },
  ],
  thursday: [
    { time: "09:00-10:00", subject: "Chemistry", teacher: "Dr. Sarah Johnson", room: "Room 301", class: "10A" },
    { time: "10:00-11:00", subject: "English", teacher: "Ms. Lisa Anderson", room: "Room 102", class: "11B" },
    { time: "11:30-12:30", subject: "Mathematics", teacher: "Dr. Emily Wilson", room: "Room 101", class: "10A" },
    { time: "14:00-15:00", subject: "Geography", teacher: "Ms. Jennifer White", room: "Room 104", class: "10A" },
  ],
  friday: [
    { time: "09:00-10:00", subject: "Biology", teacher: "Dr. Michael Green", room: "Room 401", class: "10A" },
    { time: "10:00-11:00", subject: "Physics", teacher: "Mr. David Brown", room: "Room 201", class: "11B" },
    { time: "11:30-12:30", subject: "Chemistry Lab", teacher: "Dr. Sarah Johnson", room: "Chemistry Lab", class: "10A" },
    { time: "14:00-15:00", subject: "Physical Education", teacher: "Mr. James Wilson", room: "Gymnasium", class: "10A" },
  ],
  saturday: [
    { time: "09:00-10:00", subject: "Mathematics", teacher: "Dr. Emily Wilson", room: "Room 101", class: "10A" },
    { time: "10:00-11:00", subject: "English", teacher: "Ms. Lisa Anderson", room: "Room 102", class: "11B" },
    { time: "11:30-12:30", subject: "Computer Science", teacher: "Mr. Alex Kumar", room: "Computer Lab", class: "10A" },
  ],
};

const days = ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday"];
const dayNames = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];

const subjectColors: Record<string, string> = {
  Mathematics: "bg-blue-100 text-blue-800 border-blue-200",
  Physics: "bg-green-100 text-green-800 border-green-200",
  Chemistry: "bg-purple-100 text-purple-800 border-purple-200",
  Biology: "bg-orange-100 text-orange-800 border-orange-200",
  English: "bg-pink-100 text-pink-800 border-pink-200",
  History: "bg-yellow-100 text-yellow-800 border-yellow-200",
  Geography: "bg-indigo-100 text-indigo-800 border-indigo-200",
  "Computer Science": "bg-gray-100 text-gray-800 border-gray-200",
  "Physical Education": "bg-red-100 text-red-800 border-red-200",
  "Physics Lab": "bg-green-100 text-green-800 border-green-200",
  "Chemistry Lab": "bg-purple-100 text-purple-800 border-purple-200",
};

export default function TimetablePage() {
  const [selectedClass, setSelectedClass] = useState("10A");
  const [selectedView, setSelectedView] = useState("weekly");

  // Calculate timetable statistics
  const totalPeriods = Object.values(mockTimetable).flat().length;
  const uniqueSubjects = new Set(Object.values(mockTimetable).flat().map(p => p.subject)).size;
  const uniqueTeachers = new Set(Object.values(mockTimetable).flat().map(p => p.teacher)).size;
  const uniqueRooms = new Set(Object.values(mockTimetable).flat().map(p => p.room)).size;

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Timetable Management</h1>
            <p className="text-gray-600">Manage class schedules and timetables</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Grid3X3 className="h-4 w-4 mr-2" />
              View Grid
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Period
            </Button>
          </div>
        </div>

        {/* Timetable Stats */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Calendar className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {totalPeriods}
                  </div>
                  <p className="text-sm text-gray-600">Total Periods</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <BookOpen className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {uniqueSubjects}
                  </div>
                  <p className="text-sm text-gray-600">Subjects</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <User className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {uniqueTeachers}
                  </div>
                  <p className="text-sm text-gray-600">Teachers</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <MapPin className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    {uniqueRooms}
                  </div>
                  <p className="text-sm text-gray-600">Rooms</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex gap-4 flex-1">
                <select
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm bg-white"
                  value={selectedClass}
                  onChange={(e) => setSelectedClass(e.target.value)}
                >
                  <option value="10A">Class 10A</option>
                  <option value="10B">Class 10B</option>
                  <option value="11A">Class 11A</option>
                  <option value="11B">Class 11B</option>
                  <option value="12A">Class 12A</option>
                  <option value="12B">Class 12B</option>
                </select>
                <select
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm bg-white"
                  value={selectedView}
                  onChange={(e) => setSelectedView(e.target.value)}
                >
                  <option value="weekly">Weekly View</option>
                  <option value="daily">Daily View</option>
                  <option value="teacher">Teacher View</option>
                  <option value="room">Room View</option>
                </select>
                <select className="px-3 py-2 border border-gray-300 rounded-md text-sm bg-white">
                  <option value="2023-24">Academic Year 2023-24</option>
                  <option value="2024-25">Academic Year 2024-25</option>
                </select>
              </div>
              <div className="flex gap-2">
                <Button variant="outline" size="sm">
                  <Filter className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Weekly Timetable */}
        <Card>
          <CardHeader>
            <CardTitle>Weekly Timetable - Class {selectedClass}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <div className="grid grid-cols-7 gap-4 min-w-[800px]">
                {/* Header */}
                <div className="font-semibold text-gray-900 p-3 bg-gray-50 rounded-lg text-center">
                  Time
                </div>
                {dayNames.map((day) => (
                  <div key={day} className="font-semibold text-gray-900 p-3 bg-gray-50 rounded-lg text-center">
                    {day}
                  </div>
                ))}

                {/* Time slots */}
                {["09:00-10:00", "10:00-11:00", "11:30-12:30", "14:00-15:00"].map((timeSlot) => (
                  <React.Fragment key={timeSlot}>
                    <div className="p-3 text-sm font-medium text-gray-600 bg-gray-50 rounded-lg text-center">
                      {timeSlot}
                    </div>
                    {days.map((day) => {
                      const period = mockTimetable[day as keyof typeof mockTimetable]?.find(
                        (p) => p.time === timeSlot
                      );

                      return (
                        <div key={`${day}-${timeSlot}`} className="min-h-[80px]">
                          {period ? (
                            <div className={`p-3 rounded-lg border-2 h-full ${subjectColors[period.subject] || "bg-gray-100 text-gray-800 border-gray-200"}`}>
                              <div className="font-medium text-sm">{period.subject}</div>
                              <div className="text-xs mt-1 opacity-80">
                                <div className="flex items-center">
                                  <User className="h-3 w-3 mr-1" />
                                  {period.teacher}
                                </div>
                                <div className="flex items-center mt-1">
                                  <MapPin className="h-3 w-3 mr-1" />
                                  {period.room}
                                </div>
                              </div>
                            </div>
                          ) : (
                            <div className="p-3 rounded-lg border-2 border-dashed border-gray-200 h-full flex items-center justify-center">
                              <span className="text-gray-400 text-sm">Free Period</span>
                            </div>
                          )}
                        </div>
                      );
                    })}
                  </React.Fragment>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Subject Distribution and Teacher Workload */}
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Subject Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(
                  Object.values(mockTimetable)
                    .flat()
                    .reduce((acc, period) => {
                      acc[period.subject] = (acc[period.subject] || 0) + 1;
                      return acc;
                    }, {} as Record<string, number>)
                ).map(([subject, count]) => {
                  const percentage = (count / totalPeriods) * 100;

                  return (
                    <div key={subject}>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">{subject}</span>
                        <span className="text-sm text-gray-500">
                          {count} periods ({Math.round(percentage)}%)
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: `${percentage}%` }}
                        />
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Teacher Workload</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(
                  Object.values(mockTimetable)
                    .flat()
                    .reduce((acc, period) => {
                      acc[period.teacher] = (acc[period.teacher] || 0) + 1;
                      return acc;
                    }, {} as Record<string, number>)
                ).map(([teacher, count]) => {
                  const percentage = (count / totalPeriods) * 100;

                  return (
                    <div key={teacher}>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">{teacher}</span>
                        <span className="text-sm text-gray-500">
                          {count} periods
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-green-600 h-2 rounded-full"
                          style={{ width: `${percentage}%` }}
                        />
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Today's Schedule */}
        <Card>
          <CardHeader>
            <CardTitle>Today&apos;s Schedule - Class {selectedClass}</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {mockTimetable.monday.map((period, index) => (
                <div key={index} className="flex items-center p-4 bg-gray-50 rounded-lg">
                  <div className="flex-shrink-0 w-20 text-sm font-medium text-gray-600">
                    {period.time}
                  </div>
                  <div className="ml-4 flex-1">
                    <div className="font-medium text-gray-900">{period.subject}</div>
                    <div className="text-sm text-gray-500 flex items-center">
                      <User className="h-3 w-3 mr-1" />
                      {period.teacher} •
                      <MapPin className="h-3 w-3 ml-2 mr-1" />
                      {period.room}
                    </div>
                  </div>
                  <div className="flex gap-2">
                    <Button variant="outline" size="sm">
                      Edit
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
