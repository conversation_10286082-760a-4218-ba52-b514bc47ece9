import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { client } from "@/lib/hono";
import { toast } from "sonner";

// Get all teachers with pagination and filters
interface GetTeachersParams {
  page?: number;
  limit?: number;
  search?: string;
  department?: string;
  status?: string;
}

export const useGetTeachers = (params: GetTeachersParams = {}) => {
  return useQuery({
    queryKey: ["teachers", params],
    queryFn: async () => {
      const response = await client.api.teachers.$get({
        query: {
          page: params.page?.toString() || "1",
          limit: params.limit?.toString() || "10",
          ...(params.search && { search: params.search }),
          ...(params.department && { department: params.department }),
          ...(params.status && { status: params.status }),
        },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch teachers");
      }

      const { data, meta } = await response.json();
      return { data, meta };
    },
  });
};

// Get teacher by ID
export const useGetTeacher = (id: string) => {
  return useQuery({
    queryKey: ["teachers", id],
    queryFn: async () => {
      const response = await client.api.teachers[":id"].$get({
        param: { id },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch teacher");
      }

      const { data } = await response.json();
      return data;
    },
    enabled: !!id,
  });
};

// Create teacher mutation
export const useCreateTeacher = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (json: any) => {
      const response = await client.api.teachers.$post({ json });

      if (!response.ok) {
        throw new Error("Failed to create teacher");
      }

      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["teachers"] });
      toast.success("Teacher created successfully!");
    },
    onError: (error: any) => {
      toast.error(`Failed to create teacher: ${error.message}`);
    },
  });
};

// Update teacher mutation
export const useUpdateTeacher = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: any }) => {
      const response = await client.api.teachers[":id"].$put({
        param: { id },
        json: data,
      });

      if (!response.ok) {
        throw new Error("Failed to update teacher");
      }

      return await response.json();
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ["teachers"] });
      queryClient.invalidateQueries({ queryKey: ["teachers", id] });
      toast.success("Teacher updated successfully!");
    },
    onError: (error: any) => {
      toast.error(`Failed to update teacher: ${error.message}`);
    },
  });
};

// Delete teacher mutation
export const useDeleteTeacher = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await client.api.teachers[":id"].$delete({
        param: { id },
      });

      if (!response.ok) {
        throw new Error("Failed to delete teacher");
      }

      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["teachers"] });
      toast.success("Teacher deleted successfully!");
    },
    onError: (error: any) => {
      toast.error(`Failed to delete teacher: ${error.message}`);
    },
  });
};
