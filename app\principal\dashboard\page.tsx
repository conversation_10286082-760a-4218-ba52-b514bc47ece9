"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  Users,
  GraduationCap,
  BookOpen,
  IndianRupee,
  TrendingUp,
  Award,
  Calendar,
  MessageCircle,
  BarChart3,
  FileText,
  School,
  Building,
  Shield,
  Target,
  Clock,
  CheckCircle,
  AlertTriangle,
  PieChart,
  Home,
  Settings
} from "lucide-react";

// Mock data for principal dashboard
const mockPrincipalStats = {
  totalStudents: 1247,
  totalTeachers: 89,
  totalClasses: 45,
  academicPerformance: 87.5,
  attendanceRate: 94.2,
  parentSatisfaction: 92.8,
  budgetUtilization: 78.5,
  pendingApprovals: 12
};

const mockRecentActivities = [
  {
    id: 1,
    type: "approval",
    title: "Budget Approval Request",
    description: "Science lab equipment purchase - ₹2,50,000",
    time: "2 hours ago",
    priority: "high",
    icon: IndianRupee
  },
  {
    id: 2,
    type: "achievement",
    title: "Student Achievement",
    description: "Class 12 student won National Science Olympiad",
    time: "4 hours ago",
    priority: "medium",
    icon: Award
  },
  {
    id: 3,
    type: "meeting",
    title: "Parent-Teacher Meeting",
    description: "Scheduled for next week - 150 parents registered",
    time: "6 hours ago",
    priority: "medium",
    icon: MessageCircle
  },
  {
    id: 4,
    type: "disciplinary",
    title: "Disciplinary Matter",
    description: "Grade 10 student incident requires attention",
    time: "1 day ago",
    priority: "high",
    icon: Shield
  }
];

const mockUpcomingEvents = [
  {
    id: 1,
    title: "Board Meeting",
    date: "2024-01-20",
    time: "10:00 AM",
    type: "meeting",
    attendees: 8
  },
  {
    id: 2,
    title: "Annual Sports Day",
    date: "2024-01-25",
    time: "9:00 AM",
    type: "event",
    attendees: 1200
  },
  {
    id: 3,
    title: "Teacher Training Workshop",
    date: "2024-01-22",
    time: "2:00 PM",
    type: "training",
    attendees: 45
  }
];

export default function PrincipalDashboard() {
  const [user, setUser] = useState<any>(null);
  const router = useRouter();
  const [currentTime, setCurrentTime] = useState(new Date());

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "principal") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);

    const timer = setInterval(() => {
      setCurrentTime(new Date());
    }, 1000);

    return () => clearInterval(timer);
  }, [router]);

  if (!user) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </AppLayout>
    );
  }

  const quickActions = [
    {
      title: "Review Approvals",
      description: `${mockPrincipalStats.pendingApprovals} items pending`,
      icon: CheckCircle,
      color: "bg-orange-500",
      href: "/principal/approvals",
      urgent: mockPrincipalStats.pendingApprovals > 10
    },
    {
      title: "Academic Performance",
      description: "View detailed academic analytics",
      icon: BarChart3,
      color: "bg-blue-500",
      href: "/principal/analytics/academic"
    },
    {
      title: "Staff Reviews",
      description: "Conduct teacher evaluations",
      icon: Users,
      color: "bg-green-500",
      href: "/principal/staff/reviews"
    },
    {
      title: "Parent Meeting",
      description: "Schedule parent conferences",
      icon: MessageCircle,
      color: "bg-purple-500",
      href: "/principal/parents/meetings"
    }
  ];

  const keyMetrics = [
    {
      title: "Total Students",
      value: mockPrincipalStats.totalStudents.toLocaleString(),
      change: "+2.5%",
      changeType: "positive",
      icon: Users,
      color: "text-blue-600"
    },
    {
      title: "Academic Performance",
      value: `${mockPrincipalStats.academicPerformance}%`,
      change: "+1.2%",
      changeType: "positive",
      icon: TrendingUp,
      color: "text-green-600"
    },
    {
      title: "Attendance Rate",
      value: `${mockPrincipalStats.attendanceRate}%`,
      change: "+0.8%",
      changeType: "positive",
      icon: Calendar,
      color: "text-purple-600"
    },
    {
      title: "Budget Utilization",
      value: `${mockPrincipalStats.budgetUtilization}%`,
      change: "+5.2%",
      changeType: "positive",
      icon: IndianRupee,
      color: "text-yellow-600"
    }
  ];

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">
              Principal Dashboard
            </h1>
            <p className="text-gray-600">
              Welcome back, {user.firstName} {user.lastName}. Leading excellence in education.
            </p>
            <p className="text-sm text-gray-500">
              {currentTime.toLocaleDateString()} • {currentTime.toLocaleTimeString()}
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => router.push("/principal/analytics")}>
              <BarChart3 className="h-4 w-4 mr-2" />
              Analytics
            </Button>
            <Button onClick={() => router.push("/principal/planning")}>
              <Target className="h-4 w-4 mr-2" />
              Strategic Planning
            </Button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          {keyMetrics.map((metric, index) => (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">
                      {metric.title}
                    </p>
                    <p className="text-2xl font-bold text-gray-900">
                      {metric.value}
                    </p>
                    <p className={`text-sm ${
                      metric.changeType === "positive" ? "text-green-600" : "text-red-600"
                    }`}>
                      {metric.change} from last month
                    </p>
                  </div>
                  <metric.icon className={`h-8 w-8 ${metric.color}`} />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Target className="h-5 w-5 mr-2" />
              Priority Actions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              {quickActions.map((action, index) => (
                <button
                  key={index}
                  className={`p-4 text-left border rounded-lg hover:bg-gray-50 transition-colors relative ${
                    action.urgent ? "border-orange-300 bg-orange-50" : ""
                  }`}
                  onClick={() => router.push(action.href)}
                >
                  {action.urgent && (
                    <AlertTriangle className="h-4 w-4 text-orange-500 absolute top-2 right-2" />
                  )}
                  <div className="flex items-center space-x-3">
                    <div className={`p-2 rounded-lg ${action.color}`}>
                      <action.icon className="h-5 w-5 text-white" />
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">
                        {action.title}
                      </div>
                      <div className="text-sm text-gray-500">
                        {action.description}
                      </div>
                    </div>
                  </div>
                </button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Recent Activities and Upcoming Events */}
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Recent Activities */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Clock className="h-5 w-5 mr-2" />
                Recent Activities
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockRecentActivities.map((activity) => (
                  <div key={activity.id} className="flex items-start space-x-3 p-3 rounded-lg hover:bg-gray-50">
                    <div className={`p-2 rounded-lg ${
                      activity.priority === "high" ? "bg-red-100" : "bg-blue-100"
                    }`}>
                      <activity.icon className={`h-4 w-4 ${
                        activity.priority === "high" ? "text-red-600" : "text-blue-600"
                      }`} />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium text-gray-900">{activity.title}</h4>
                        <span className="text-xs text-gray-500">{activity.time}</span>
                      </div>
                      <p className="text-sm text-gray-600">{activity.description}</p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Upcoming Events */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="h-5 w-5 mr-2" />
                Upcoming Events
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockUpcomingEvents.map((event) => (
                  <div key={event.id} className="flex items-center space-x-3 p-3 rounded-lg hover:bg-gray-50">
                    <div className="p-2 rounded-lg bg-purple-100">
                      <Calendar className="h-4 w-4 text-purple-600" />
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center justify-between">
                        <h4 className="font-medium text-gray-900">{event.title}</h4>
                        <span className="text-xs text-gray-500">{event.attendees} attendees</span>
                      </div>
                      <p className="text-sm text-gray-600">
                        {new Date(event.date).toLocaleDateString()} at {event.time}
                      </p>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  );
}
