import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { client } from "@/lib/hono";
import { toast } from "sonner";

// Types for program management
export interface AcademicProgram {
  id: string;
  name: string;
  code: string;
  type: "school"; // Fixed to school-only
  duration: number;
  description?: string;
  eligibilityCriteria?: string;
  totalSeats: number;
  admissionStatus: "open" | "closed" | "waitlist";
  status: "active" | "inactive";
  createdAt: string;
  updatedAt: string;
  // Removed stream and branch information - school-only mode
  statistics?: {
    totalBatches: number;
    totalStudents: number;
    totalClasses: number;
    currentBatch: any;
    availableSeats: number;
  };
}

export interface CreateProgramData {
  name: string;
  code: string;
  type: "school"; // School-only system
  duration: number;
  description?: string;
  eligibilityCriteria?: string;
  totalSeats: number;
  department?: string;
  admissionStatus?: "open" | "closed" | "waitlist";
}

// Get all programs with filtering and pagination
export const useGetPrograms = (params?: {
  page?: number;
  limit?: number;
  search?: string;
  type?: string;
  status?: string;
}) => {
  return useQuery({
    queryKey: ["programs", params],
    queryFn: async () => {
      const searchParams = new URLSearchParams();

      if (params?.page) searchParams.append("page", params.page.toString());
      if (params?.limit) searchParams.append("limit", params.limit.toString());
      if (params?.search) searchParams.append("search", params.search);
      if (params?.type) searchParams.append("type", params.type);
      if (params?.status) searchParams.append("status", params.status);

      const response = await client.api["academic-programs"].$get({
        query: Object.fromEntries(searchParams),
      });

      if (!response.ok) {
        throw new Error("Failed to fetch programs");
      }

      return await response.json();
    },
  });
};

// Get program by ID
export const useGetProgram = (id: string) => {
  return useQuery({
    queryKey: ["programs", id],
    queryFn: async () => {
      const response = await client.api["academic-programs"][":id"].$get({
        param: { id },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch program");
      }

      return await response.json();
    },
    enabled: !!id,
  });
};

// Create program mutation
export const useCreateProgram = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (json: CreateProgramData) => {
      const response = await client.api["academic-programs"].$post({ json });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create program");
      }

      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["programs"] });
      toast.success("Academic program created successfully!");
    },
    onError: (error: any) => {
      toast.error(`Failed to create program: ${error.message}`);
    },
  });
};

// Update program mutation
export const useUpdateProgram = (id: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (json: Partial<CreateProgramData>) => {
      const response = await client.api["academic-programs"][":id"].$put({
        param: { id },
        json,
      });

      if (!response.ok) {
        const errorData = await response.json() as { error?: string };
        throw new Error(errorData.error || "Failed to update program");
      }

      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["programs"] });
      queryClient.invalidateQueries({ queryKey: ["programs", id] });
      toast.success("Program updated successfully!");
    },
    onError: (error: any) => {
      toast.error(`Failed to update program: ${error.message}`);
    },
  });
};

// Delete program mutation
export const useDeleteProgram = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await client.api["academic-programs"][":id"].$delete({
        param: { id },
      });

      if (!response.ok) {
        const errorData = await response.json() as { error?: string };
        throw new Error(errorData.error || "Failed to delete program");
      }

      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["programs"] });
      toast.success("Program deleted successfully!");
    },
    onError: (error: any) => {
      toast.error(`Failed to delete program: ${error.message}`);
    },
  });
};

// Get program batches
export const useGetProgramBatches = (programId: string) => {
  return useQuery({
    queryKey: ["programs", programId, "batches"],
    queryFn: async () => {
      const response = await client.api["academic-programs"][":id"]["batches"].$get({
        param: { id: programId },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch program batches");
      }

      return await response.json();
    },
    enabled: !!programId,
  });
};

// Get program statistics
export const useGetProgramStats = () => {
  return useQuery({
    queryKey: ["programs", "stats"],
    queryFn: async () => {
      const response = await client.api["academic-programs"].$get({
        query: { limit: "1" }, // Just get count info
      });

      if (!response.ok) {
        throw new Error("Failed to fetch program stats");
      }

      const data = await response.json();
      return data.meta || { total: data.data?.length || 0, page: 1, limit: 10, totalPages: 1 };
    },
  });
};

// Get programs by type
export const useGetProgramsByType = (type: string) => {
  return useQuery({
    queryKey: ["programs", "type", type],
    queryFn: async () => {
      const response = await client.api["academic-programs"].$get({
        query: { type, limit: "1000" }, // Get all programs of this type
      });

      if (!response.ok) {
        throw new Error("Failed to fetch programs by type");
      }

      return await response.json();
    },
    enabled: !!type,
  });
};
