"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import {
  Target,
  Calendar,
  TrendingUp,
  CheckCircle,
  Clock,
  AlertTriangle,
  Users,
  BookOpen,
  Building,
  IndianRupee,
  Award,
  BarChart3,
  Plus,
  Edit,
  Eye,
  FileText,
  Lightbulb,
  Flag,
  Zap
} from "lucide-react";
import { toast } from "sonner";

interface Goal {
  id: string;
  title: string;
  description: string;
  category: "academic" | "infrastructure" | "financial" | "staff" | "student_welfare";
  priority: "high" | "medium" | "low";
  status: "planning" | "in_progress" | "completed" | "on_hold";
  progress: number;
  startDate: string;
  targetDate: string;
  budget?: number;
  assignedTo: string[];
  milestones: Array<{
    id: string;
    title: string;
    completed: boolean;
    dueDate: string;
  }>;
}

interface Initiative {
  id: string;
  title: string;
  description: string;
  type: "improvement" | "innovation" | "expansion" | "efficiency";
  impact: "high" | "medium" | "low";
  effort: "high" | "medium" | "low";
  status: "idea" | "proposal" | "approved" | "implementation" | "completed";
  proposedBy: string;
  estimatedCost?: number;
  expectedBenefits: string[];
}

export default function PrincipalPlanningPage() {
  const [user, setUser] = useState<any>(null);
  const router = useRouter();
  const [goals, setGoals] = useState<Goal[]>([]);
  const [initiatives, setInitiatives] = useState<Initiative[]>([]);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("goals");

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "principal") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
    loadPlanningData();
  }, [router]);

  const loadPlanningData = () => {
    // Mock data - in real app, fetch from API
    const mockGoals: Goal[] = [
      {
        id: "1",
        title: "Improve Academic Performance",
        description: "Increase overall school academic performance by 10% this academic year",
        category: "academic",
        priority: "high",
        status: "in_progress",
        progress: 65,
        startDate: "2024-01-01",
        targetDate: "2024-12-31",
        assignedTo: ["Academic Head", "Subject Coordinators"],
        milestones: [
          { id: "1", title: "Implement new teaching methods", completed: true, dueDate: "2024-03-01" },
          { id: "2", title: "Teacher training programs", completed: true, dueDate: "2024-04-01" },
          { id: "3", title: "Mid-year assessment", completed: false, dueDate: "2024-06-01" },
          { id: "4", title: "Final evaluation", completed: false, dueDate: "2024-11-01" }
        ]
      },
      {
        id: "2",
        title: "Infrastructure Modernization",
        description: "Upgrade school infrastructure including smart classrooms and labs",
        category: "infrastructure",
        priority: "high",
        status: "planning",
        progress: 25,
        startDate: "2024-02-01",
        targetDate: "2024-08-31",
        budget: 5000000,
        assignedTo: ["Facilities Manager", "IT Head"],
        milestones: [
          { id: "1", title: "Budget approval", completed: true, dueDate: "2024-02-15" },
          { id: "2", title: "Vendor selection", completed: false, dueDate: "2024-03-15" },
          { id: "3", title: "Installation phase 1", completed: false, dueDate: "2024-05-01" },
          { id: "4", title: "Installation phase 2", completed: false, dueDate: "2024-07-01" }
        ]
      },
      {
        id: "3",
        title: "Teacher Development Program",
        description: "Comprehensive professional development for all teaching staff",
        category: "staff",
        priority: "medium",
        status: "in_progress",
        progress: 40,
        startDate: "2024-01-15",
        targetDate: "2024-06-30",
        budget: 500000,
        assignedTo: ["HR Head", "Training Coordinator"],
        milestones: [
          { id: "1", title: "Training needs assessment", completed: true, dueDate: "2024-02-01" },
          { id: "2", title: "Program design", completed: true, dueDate: "2024-02-28" },
          { id: "3", title: "Phase 1 training", completed: false, dueDate: "2024-04-30" },
          { id: "4", title: "Phase 2 training", completed: false, dueDate: "2024-06-30" }
        ]
      }
    ];

    const mockInitiatives: Initiative[] = [
      {
        id: "1",
        title: "AI-Powered Learning Platform",
        description: "Implement AI-based personalized learning system for students",
        type: "innovation",
        impact: "high",
        effort: "high",
        status: "proposal",
        proposedBy: "IT Department",
        estimatedCost: 2000000,
        expectedBenefits: [
          "Personalized learning paths",
          "Improved student engagement",
          "Better learning outcomes",
          "Data-driven insights"
        ]
      },
      {
        id: "2",
        title: "Green Campus Initiative",
        description: "Transform school into an eco-friendly, sustainable campus",
        type: "improvement",
        impact: "medium",
        effort: "medium",
        status: "approved",
        proposedBy: "Environmental Club",
        estimatedCost: 1500000,
        expectedBenefits: [
          "Reduced carbon footprint",
          "Lower utility costs",
          "Environmental awareness",
          "Healthier campus environment"
        ]
      },
      {
        id: "3",
        title: "Parent Engagement App",
        description: "Mobile app for better parent-school communication and engagement",
        type: "innovation",
        impact: "medium",
        effort: "low",
        status: "implementation",
        proposedBy: "Parent Committee",
        estimatedCost: 300000,
        expectedBenefits: [
          "Better communication",
          "Real-time updates",
          "Increased parent involvement",
          "Streamlined processes"
        ]
      }
    ];

    setGoals(mockGoals);
    setInitiatives(mockInitiatives);
    setLoading(false);
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "academic": return BookOpen;
      case "infrastructure": return Building;
      case "financial": return IndianRupee;
      case "staff": return Users;
      case "student_welfare": return Award;
      default: return Target;
    }
  };

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "academic": return "bg-blue-100 text-blue-800";
      case "infrastructure": return "bg-purple-100 text-purple-800";
      case "financial": return "bg-green-100 text-green-800";
      case "staff": return "bg-yellow-100 text-yellow-800";
      case "student_welfare": return "bg-pink-100 text-pink-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed": return "bg-green-100 text-green-800";
      case "in_progress": return "bg-blue-100 text-blue-800";
      case "planning": return "bg-yellow-100 text-yellow-800";
      case "on_hold": return "bg-red-100 text-red-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "bg-red-100 text-red-800";
      case "medium": return "bg-yellow-100 text-yellow-800";
      case "low": return "bg-green-100 text-green-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  if (!user) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Strategic Planning</h1>
            <p className="text-gray-600">
              Plan, track, and manage school improvement goals and initiatives
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <FileText className="h-4 w-4 mr-2" />
              Generate Report
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Goal
            </Button>
          </div>
        </div>

        {/* Planning Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="goals">Strategic Goals</TabsTrigger>
            <TabsTrigger value="initiatives">Initiatives</TabsTrigger>
            <TabsTrigger value="overview">Overview</TabsTrigger>
          </TabsList>

          {/* Strategic Goals */}
          <TabsContent value="goals" className="space-y-6">
            {loading ? (
              <div className="flex items-center justify-center h-64">
                <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
              </div>
            ) : (
              <div className="grid gap-6">
                {goals.map((goal) => {
                  const CategoryIcon = getCategoryIcon(goal.category);
                  const completedMilestones = goal.milestones.filter(m => m.completed).length;
                  
                  return (
                    <Card key={goal.id} className="hover:shadow-md transition-shadow">
                      <CardContent className="p-6">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex items-start space-x-4 flex-1">
                            <div className={`p-3 rounded-lg ${getCategoryColor(goal.category)}`}>
                              <CategoryIcon className="h-5 w-5" />
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center gap-2 mb-2">
                                <h3 className="font-semibold text-gray-900">{goal.title}</h3>
                                <Badge className={getPriorityColor(goal.priority)}>
                                  {goal.priority}
                                </Badge>
                                <Badge className={getStatusColor(goal.status)}>
                                  {goal.status.replace('_', ' ')}
                                </Badge>
                              </div>
                              <p className="text-gray-600 mb-3">{goal.description}</p>
                              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                                <div>
                                  <span className="text-gray-500">Progress:</span>
                                  <div className="flex items-center gap-2 mt-1">
                                    <Progress value={goal.progress} className="flex-1" />
                                    <span className="font-medium">{goal.progress}%</span>
                                  </div>
                                </div>
                                <div>
                                  <span className="text-gray-500">Milestones:</span>
                                  <div className="font-medium mt-1">
                                    {completedMilestones}/{goal.milestones.length}
                                  </div>
                                </div>
                                <div>
                                  <span className="text-gray-500">Target Date:</span>
                                  <div className="font-medium mt-1">
                                    {new Date(goal.targetDate).toLocaleDateString()}
                                  </div>
                                </div>
                                {goal.budget && (
                                  <div>
                                    <span className="text-gray-500">Budget:</span>
                                    <div className="font-medium text-green-600 mt-1">
                                      ₹{goal.budget.toLocaleString()}
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center gap-2">
                            <Button variant="outline" size="sm">
                              <Eye className="h-4 w-4 mr-2" />
                              View
                            </Button>
                            <Button variant="outline" size="sm">
                              <Edit className="h-4 w-4 mr-2" />
                              Edit
                            </Button>
                          </div>
                        </div>
                        
                        {/* Milestones */}
                        <div className="border-t pt-4">
                          <h4 className="font-medium mb-3">Milestones</h4>
                          <div className="grid gap-2 md:grid-cols-2">
                            {goal.milestones.map((milestone) => (
                              <div key={milestone.id} className="flex items-center gap-3 p-2 rounded-lg bg-gray-50">
                                <div className={`p-1 rounded-full ${
                                  milestone.completed ? "bg-green-100" : "bg-gray-200"
                                }`}>
                                  <CheckCircle className={`h-4 w-4 ${
                                    milestone.completed ? "text-green-600" : "text-gray-400"
                                  }`} />
                                </div>
                                <div className="flex-1">
                                  <div className={`font-medium ${
                                    milestone.completed ? "text-gray-900" : "text-gray-600"
                                  }`}>
                                    {milestone.title}
                                  </div>
                                  <div className="text-xs text-gray-500">
                                    Due: {new Date(milestone.dueDate).toLocaleDateString()}
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  );
                })}
              </div>
            )}
          </TabsContent>

          {/* Initiatives */}
          <TabsContent value="initiatives" className="space-y-6">
            <div className="grid gap-6">
              {initiatives.map((initiative) => (
                <Card key={initiative.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-2">
                          <h3 className="font-semibold text-gray-900">{initiative.title}</h3>
                          <Badge variant="outline">{initiative.type}</Badge>
                          <Badge className={getStatusColor(initiative.status)}>
                            {initiative.status}
                          </Badge>
                        </div>
                        <p className="text-gray-600 mb-3">{initiative.description}</p>
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm mb-4">
                          <div>
                            <span className="text-gray-500">Impact:</span>
                            <Badge className={getPriorityColor(initiative.impact)} size="sm">
                              {initiative.impact}
                            </Badge>
                          </div>
                          <div>
                            <span className="text-gray-500">Effort:</span>
                            <Badge className={getPriorityColor(initiative.effort)} size="sm">
                              {initiative.effort}
                            </Badge>
                          </div>
                          <div>
                            <span className="text-gray-500">Proposed by:</span>
                            <div className="font-medium">{initiative.proposedBy}</div>
                          </div>
                          {initiative.estimatedCost && (
                            <div>
                              <span className="text-gray-500">Est. Cost:</span>
                              <div className="font-medium text-green-600">
                                ₹{initiative.estimatedCost.toLocaleString()}
                              </div>
                            </div>
                          )}
                        </div>
                        <div>
                          <h4 className="font-medium mb-2">Expected Benefits:</h4>
                          <div className="flex flex-wrap gap-2">
                            {initiative.expectedBenefits.map((benefit, index) => (
                              <Badge key={index} variant="secondary" className="text-xs">
                                {benefit}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2 ml-4">
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-2" />
                          Review
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          {/* Overview */}
          <TabsContent value="overview" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Target className="h-5 w-5 mr-2" />
                    Active Goals
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-blue-600">
                    {goals.filter(g => g.status === "in_progress").length}
                  </div>
                  <p className="text-sm text-gray-600">Goals in progress</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <CheckCircle className="h-5 w-5 mr-2" />
                    Completed Goals
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-green-600">
                    {goals.filter(g => g.status === "completed").length}
                  </div>
                  <p className="text-sm text-gray-600">Goals completed</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Lightbulb className="h-5 w-5 mr-2" />
                    New Initiatives
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-purple-600">
                    {initiatives.filter(i => i.status === "idea" || i.status === "proposal").length}
                  </div>
                  <p className="text-sm text-gray-600">Pending review</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <TrendingUp className="h-5 w-5 mr-2" />
                    Overall Progress
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-orange-600">
                    {Math.round(goals.reduce((acc, goal) => acc + goal.progress, 0) / goals.length)}%
                  </div>
                  <p className="text-sm text-gray-600">Average completion</p>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </AppLayout>
  );
}
