CREATE TABLE "system_backups" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(255) NOT NULL,
	"description" text,
	"backup_type" varchar(50) NOT NULL,
	"status" varchar(20) DEFAULT 'pending' NOT NULL,
	"file_path" varchar(500),
	"file_size" integer,
	"started_at" timestamp,
	"completed_at" timestamp,
	"created_by" uuid,
	"error" text,
	"created_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "system_settings" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"category" varchar(50) NOT NULL,
	"key" varchar(100) NOT NULL,
	"value" text NOT NULL,
	"data_type" varchar(20) DEFAULT 'string' NOT NULL,
	"description" text,
	"is_system" boolean DEFAULT false NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
ALTER TABLE "system_backups" ADD CONSTRAINT "system_backups_created_by_users_id_fk" FOREIGN KEY ("created_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
CREATE INDEX "system_backups_status_idx" ON "system_backups" USING btree ("status");--> statement-breakpoint
CREATE INDEX "system_backups_type_idx" ON "system_backups" USING btree ("backup_type");--> statement-breakpoint
CREATE INDEX "system_backups_created_at_idx" ON "system_backups" USING btree ("created_at");--> statement-breakpoint
CREATE INDEX "system_backups_created_by_idx" ON "system_backups" USING btree ("created_by");--> statement-breakpoint
CREATE INDEX "system_settings_category_key_idx" ON "system_settings" USING btree ("category","key");--> statement-breakpoint
CREATE INDEX "system_settings_category_idx" ON "system_settings" USING btree ("category");--> statement-breakpoint
CREATE INDEX "system_settings_key_idx" ON "system_settings" USING btree ("key");--> statement-breakpoint
CREATE INDEX "system_settings_category_key_unique" ON "system_settings" USING btree ("category","key");