const fs = require('fs');
const path = require('path');

console.log("=== CREATING MISSING PRINCIPAL ROUTES ===\n");

// All principal routes from navigation config
const principalRoutes = [
  // Main navigation routes
  "/principal/dashboard",
  "/principal/academic",
  "/principal/curriculum", 
  "/principal/calendar",
  "/principal/performance",
  "/principal/staff",
  "/principal/staff/reviews",
  "/principal/staff/development",
  "/principal/staff/reports",
  "/principal/students",
  "/principal/students/discipline",
  "/principal/students/achievements",
  "/principal/students/welfare",
  "/principal/finance",
  "/principal/finance/approvals",
  "/principal/finance/reports",
  "/principal/finance/fees",
  "/principal/parents",
  "/principal/parents/meetings",
  "/principal/parents/feedback",
  "/principal/parents/communication",
  "/principal/planning",
  "/principal/planning/quality",
  "/principal/planning/external",
  "/principal/analytics",
  "/principal/analytics/academic",
  "/principal/analytics/performance",
  "/principal/analytics/comparison",
  "/principal/approvals"
];

// Additional routes from dashboard quick actions
const additionalRoutes = [
  "/principal/analytics/academic",
  "/principal/staff/reviews",
  "/principal/parents/meetings"
];

// Combine and deduplicate
const allRoutes = [...new Set([...principalRoutes, ...additionalRoutes])];

console.log(`Found ${allRoutes.length} principal routes to check/create:\n`);

let existingRoutes = 0;
let missingRoutes = [];
let createdRoutes = 0;

// Check which routes exist
allRoutes.forEach(route => {
  const filePath = path.join(__dirname, 'app', route, 'page.tsx');
  if (fs.existsSync(filePath)) {
    console.log(`✅ ${route} - EXISTS`);
    existingRoutes++;
  } else {
    console.log(`❌ ${route} - MISSING`);
    missingRoutes.push(route);
  }
});

console.log(`\n📊 Summary:`);
console.log(`✅ Existing: ${existingRoutes}`);
console.log(`❌ Missing: ${missingRoutes.length}`);
console.log(`📝 Total: ${allRoutes.length}`);

if (missingRoutes.length > 0) {
  console.log(`\n🔧 Creating missing routes...\n`);
  
  missingRoutes.forEach(route => {
    try {
      // Create directory structure
      const routePath = path.join(__dirname, 'app', route);
      const filePath = path.join(routePath, 'page.tsx');
      
      // Create directories if they don't exist
      fs.mkdirSync(routePath, { recursive: true });
      
      // Generate page content based on route
      const pageContent = generatePageContent(route);
      
      // Write the file
      fs.writeFileSync(filePath, pageContent);
      
      console.log(`✅ Created: ${route}/page.tsx`);
      createdRoutes++;
    } catch (error) {
      console.log(`❌ Failed to create ${route}: ${error.message}`);
    }
  });
  
  console.log(`\n🎉 Successfully created ${createdRoutes} new principal routes!`);
} else {
  console.log(`\n🎉 All principal routes already exist!`);
}

function generatePageContent(route) {
  const routeParts = route.split('/');
  const pageName = routeParts[routeParts.length - 1];
  const section = routeParts[2]; // academic, staff, students, etc.
  
  const pageTitle = formatTitle(route);
  const componentName = formatComponentName(route);
  
  return `"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  ArrowLeft,
  Plus,
  Search,
  Filter,
  Download,
  RefreshCw,
  Eye,
  Edit,
  MoreHorizontal
} from "lucide-react";
import { toast } from "sonner";

export default function ${componentName}() {
  const [user, setUser] = useState<any>(null);
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState([]);

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "principal") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
    loadData();
  }, [router]);

  const loadData = async () => {
    try {
      setLoading(true);
      // TODO: Implement API call for ${route}
      // const response = await fetch('/api${route}');
      // const result = await response.json();
      // setData(result.data);
      
      // Mock data for now
      setData([]);
      toast.success("Data loaded successfully");
    } catch (error) {
      console.error("Error loading data:", error);
      toast.error("Failed to load data");
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.back()}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">${pageTitle}</h1>
              <p className="text-gray-600">
                Principal ${section} management and oversight
              </p>
            </div>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={loadData}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add New
            </Button>
          </div>
        </div>

        {/* Content Area */}
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <Card>
            <CardHeader>
              <CardTitle>${pageTitle} Overview</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="text-center py-12">
                <div className="text-gray-500 mb-4">
                  <Eye className="h-12 w-12 mx-auto mb-4 opacity-50" />
                  <h3 className="text-lg font-medium mb-2">
                    ${pageTitle} Management
                  </h3>
                  <p className="text-sm">
                    This page is under development. Principal ${section} features will be available soon.
                  </p>
                </div>
                <div className="flex justify-center gap-2 mt-6">
                  <Button variant="outline" onClick={() => router.push("/principal/dashboard")}>
                    Return to Dashboard
                  </Button>
                  <Button onClick={() => toast.info("Feature coming soon!")}>
                    Learn More
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    </AppLayout>
  );
}`;
}

function formatTitle(route) {
  const parts = route.split('/').filter(part => part !== 'principal');
  return parts.map(part => 
    part.split('-').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join(' ')
  ).join(' - ');
}

function formatComponentName(route) {
  const parts = route.split('/').filter(part => part !== 'principal');
  const name = parts.map(part => 
    part.split('-').map(word => 
      word.charAt(0).toUpperCase() + word.slice(1)
    ).join('')
  ).join('');
  return `Principal${name}Page`;
}

console.log(`\n📋 Route Creation Summary:`);
console.log(`✅ Created: ${createdRoutes} routes`);
console.log(`📁 Total Principal Routes: ${allRoutes.length}`);
console.log(`🎯 Completion Rate: ${Math.round(((existingRoutes + createdRoutes) / allRoutes.length) * 100)}%`);

// Save report
const report = {
  timestamp: new Date().toISOString(),
  totalRoutes: allRoutes.length,
  existingRoutes,
  createdRoutes,
  missingRoutes: missingRoutes.length - createdRoutes,
  completionRate: Math.round(((existingRoutes + createdRoutes) / allRoutes.length) * 100),
  allRoutes,
  createdRoutesList: missingRoutes.slice(0, createdRoutes)
};

fs.writeFileSync('principal-routes-report.json', JSON.stringify(report, null, 2));
console.log(`\n📊 Detailed report saved to: principal-routes-report.json`);
