const fs = require('fs');
const path = require('path');

console.log("=== COMPREHENSIVE BUTTON FUNCTIONALITY TEST ===\n");

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  warnings: 0,
  details: []
};

// Function to check if a file contains specific button patterns
function checkButtonFunctionality(filePath, relativePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const results = {
      file: relativePath,
      buttons: [],
      issues: []
    };

    // Check for buttons without onClick handlers
    const buttonRegex = /<Button[^>]*>/g;
    const onClickRegex = /onClick\s*=\s*{[^}]*}/;
    const routerPushRegex = /router\.push\(/;
    const toastRegex = /toast\./;

    let match;
    let buttonCount = 0;
    let functionalButtons = 0;

    while ((match = buttonRegex.exec(content)) !== null) {
      buttonCount++;
      const buttonTag = match[0];
      const startIndex = match.index;
      
      // Get the full button element (including closing tag)
      const endTagIndex = content.indexOf('</Button>', startIndex);
      const selfClosingIndex = content.indexOf('/>', startIndex);
      
      let buttonElement;
      if (endTagIndex !== -1 && (selfClosingIndex === -1 || endTagIndex < selfClosingIndex)) {
        buttonElement = content.substring(startIndex, endTagIndex + 9);
      } else if (selfClosingIndex !== -1) {
        buttonElement = content.substring(startIndex, selfClosingIndex + 2);
      } else {
        buttonElement = buttonTag;
      }

      // Check if button has functionality
      const hasOnClick = onClickRegex.test(buttonElement);
      const hasRouterPush = routerPushRegex.test(buttonElement);
      const hasToast = toastRegex.test(buttonElement);
      const isFormSubmit = buttonElement.includes('type="submit"');
      const isDialogTrigger = buttonElement.includes('DialogTrigger') || content.substring(Math.max(0, startIndex - 100), startIndex).includes('DialogTrigger');

      if (hasOnClick || hasRouterPush || isFormSubmit || isDialogTrigger) {
        functionalButtons++;
        results.buttons.push({
          type: 'functional',
          element: buttonElement.substring(0, 100) + (buttonElement.length > 100 ? '...' : ''),
          functionality: [
            hasOnClick && 'onClick handler',
            hasRouterPush && 'router navigation',
            isFormSubmit && 'form submission',
            isDialogTrigger && 'dialog trigger'
          ].filter(Boolean).join(', ')
        });
      } else {
        results.issues.push({
          type: 'missing_functionality',
          element: buttonElement.substring(0, 100) + (buttonElement.length > 100 ? '...' : ''),
          line: content.substring(0, startIndex).split('\n').length
        });
      }
    }

    results.summary = {
      totalButtons: buttonCount,
      functionalButtons: functionalButtons,
      nonFunctionalButtons: buttonCount - functionalButtons,
      functionalityRate: buttonCount > 0 ? Math.round((functionalButtons / buttonCount) * 100) : 0
    };

    return results;
  } catch (error) {
    return {
      file: relativePath,
      error: error.message,
      summary: { totalButtons: 0, functionalButtons: 0, nonFunctionalButtons: 0, functionalityRate: 0 }
    };
  }
}

// Key pages to test
const pagesToTest = [
  'app/admin/dashboard/page.tsx',
  'app/admin/students/page.tsx',
  'app/admin/teachers/page.tsx',
  'app/admin/classes/page.tsx',
  'app/admin/finance/page.tsx',
  'app/admin/reports/page.tsx',
  'app/admin/users/page.tsx',
  'app/admin/settings/page.tsx',
  'components/navigation/sidebar.tsx',
  'components/navigation/user-menu.tsx',
  'app/teacher/dashboard/page.tsx',
  'app/student/dashboard/page.tsx',
  'app/parent/dashboard/page.tsx',
  'app/finance/dashboard/page.tsx',
  'app/library/dashboard/page.tsx',
  'app/transport/dashboard/page.tsx',
  'app/hostel/dashboard/page.tsx'
];

console.log("🔍 Testing Button Functionality Across Key Pages...\n");

let totalButtons = 0;
let totalFunctionalButtons = 0;
const pageResults = [];

pagesToTest.forEach(pagePath => {
  const fullPath = path.join(__dirname, pagePath);
  
  if (fs.existsSync(fullPath)) {
    const result = checkButtonFunctionality(fullPath, pagePath);
    pageResults.push(result);
    
    if (result.summary) {
      totalButtons += result.summary.totalButtons;
      totalFunctionalButtons += result.summary.functionalButtons;
      
      const status = result.summary.functionalityRate >= 80 ? '✅' : 
                    result.summary.functionalityRate >= 50 ? '⚠️' : '❌';
      
      console.log(`${status} ${pagePath}`);
      console.log(`   Buttons: ${result.summary.totalButtons} | Functional: ${result.summary.functionalButtons} | Rate: ${result.summary.functionalityRate}%`);
      
      if (result.issues && result.issues.length > 0) {
        console.log(`   Issues: ${result.issues.length} non-functional buttons`);
        result.issues.slice(0, 3).forEach(issue => {
          console.log(`     - Line ${issue.line}: ${issue.element.substring(0, 50)}...`);
        });
        if (result.issues.length > 3) {
          console.log(`     - ... and ${result.issues.length - 3} more`);
        }
      }
      console.log();
    } else if (result.error) {
      console.log(`❌ ${pagePath} - Error: ${result.error}\n`);
    }
  } else {
    console.log(`⚠️ ${pagePath} - File not found\n`);
  }
});

// Overall summary
const overallFunctionalityRate = totalButtons > 0 ? Math.round((totalFunctionalButtons / totalButtons) * 100) : 0;

console.log("=== OVERALL SUMMARY ===");
console.log(`Total Buttons Tested: ${totalButtons}`);
console.log(`Functional Buttons: ${totalFunctionalButtons}`);
console.log(`Non-Functional Buttons: ${totalButtons - totalFunctionalButtons}`);
console.log(`Overall Functionality Rate: ${overallFunctionalityRate}%`);

if (overallFunctionalityRate >= 80) {
  console.log("🎉 EXCELLENT: Most buttons have proper functionality!");
} else if (overallFunctionalityRate >= 60) {
  console.log("👍 GOOD: Majority of buttons are functional, some improvements needed.");
} else {
  console.log("⚠️ NEEDS IMPROVEMENT: Many buttons lack proper functionality.");
}

console.log("\n=== RECOMMENDATIONS ===");
if (totalButtons - totalFunctionalButtons > 0) {
  console.log("1. Add onClick handlers to non-functional buttons");
  console.log("2. Implement navigation using router.push() for action buttons");
  console.log("3. Add toast notifications for user feedback");
  console.log("4. Consider adding loading states for async operations");
}

// Export results for further analysis
const reportData = {
  timestamp: new Date().toISOString(),
  summary: {
    totalButtons,
    totalFunctionalButtons,
    overallFunctionalityRate,
    pagesTestedCount: pagesToTest.length,
    pagesFoundCount: pageResults.filter(r => !r.error).length
  },
  pageResults,
  recommendations: [
    "Add onClick handlers to buttons without functionality",
    "Implement proper navigation for action buttons",
    "Add user feedback with toast notifications",
    "Consider loading states for async operations"
  ]
};

fs.writeFileSync('button-functionality-report.json', JSON.stringify(reportData, null, 2));
console.log("\n📊 Detailed report saved to: button-functionality-report.json");
