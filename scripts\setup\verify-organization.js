const fs = require('fs');
const path = require('path');

console.log("=== PROJECT ORGANIZATION VERIFICATION ===\n");

// Define expected folder structure
const expectedStructure = {
  'docs/': {
    'implementation/': [
      'principal-login-system.md',
      'student-promotion-system.md',
      'academic-structure-corrections.md'
    ],
    'testing/': [
      'testing-guide.md'
    ],
    'reports/': [
      '*.json files (test reports)'
    ],
    'files': [
      'README.md'
    ]
  },
  'scripts/': {
    'testing/': [
      'test-principal-functionality.js',
      'test-all-principal-routes.js',
      'test-principal-add-functionality.js',
      'test-corrected-academic-structure.js',
      'test-promotion-system.js'
    ],
    'setup/': [
      'create-missing-principal-routes.js',
      'verify-organization.js'
    ]
  },
  'components/ui/': [
    'progress.tsx'
  ],
  'app/principal/': [
    'dashboard/page.tsx',
    'academic/page.tsx',
    'promotion/page.tsx',
    'promotion/workflow/page.tsx'
  ]
};

let results = {
  totalChecks: 0,
  passed: 0,
  failed: 0,
  details: []
};

function checkPath(filePath, description) {
  results.totalChecks++;
  if (fs.existsSync(filePath)) {
    results.passed++;
    console.log(`✅ ${description}`);
    results.details.push({ path: filePath, status: 'exists', description });
    return true;
  } else {
    results.failed++;
    console.log(`❌ ${description} - Missing: ${filePath}`);
    results.details.push({ path: filePath, status: 'missing', description });
    return false;
  }
}

function checkDirectory(dirPath, description) {
  results.totalChecks++;
  if (fs.existsSync(dirPath) && fs.statSync(dirPath).isDirectory()) {
    results.passed++;
    console.log(`✅ ${description}`);
    results.details.push({ path: dirPath, status: 'exists', description });
    return true;
  } else {
    results.failed++;
    console.log(`❌ ${description} - Missing: ${dirPath}`);
    results.details.push({ path: dirPath, status: 'missing', description });
    return false;
  }
}

// Check main directories
console.log("📁 Checking Main Directories:");
checkDirectory('docs', 'Documentation directory');
checkDirectory('docs/implementation', 'Implementation docs directory');
checkDirectory('docs/testing', 'Testing docs directory');
checkDirectory('docs/reports', 'Reports directory');
checkDirectory('scripts', 'Scripts directory');
checkDirectory('scripts/testing', 'Testing scripts directory');
checkDirectory('scripts/setup', 'Setup scripts directory');

console.log("\n📄 Checking Documentation Files:");
checkPath('docs/README.md', 'Main README documentation');
checkPath('docs/implementation/principal-login-system.md', 'Principal login system docs');
checkPath('docs/implementation/student-promotion-system.md', 'Student promotion system docs');
checkPath('docs/implementation/academic-structure-corrections.md', 'Academic structure docs');
checkPath('docs/testing/testing-guide.md', 'Testing guide documentation');

console.log("\n🧪 Checking Test Scripts:");
checkPath('scripts/testing/test-principal-functionality.js', 'Principal functionality test');
checkPath('scripts/testing/test-all-principal-routes.js', 'All routes test');
checkPath('scripts/testing/test-principal-add-functionality.js', 'Add functionality test');
checkPath('scripts/testing/test-corrected-academic-structure.js', 'Academic structure test');
checkPath('scripts/testing/test-promotion-system.js', 'Promotion system test');

console.log("\n🔧 Checking Setup Scripts:");
checkPath('scripts/setup/create-missing-principal-routes.js', 'Route creation script');
checkPath('scripts/setup/verify-organization.js', 'Organization verification script');

console.log("\n🎨 Checking UI Components:");
checkPath('components/ui/progress.tsx', 'Progress component (fixes error)');

console.log("\n📱 Checking Key Application Files:");
checkPath('app/principal/dashboard/page.tsx', 'Principal dashboard');
checkPath('app/principal/academic/page.tsx', 'Academic management page');
checkPath('app/principal/promotion/page.tsx', 'Student promotion page');
checkPath('app/principal/promotion/workflow/page.tsx', 'Promotion workflow page');

// Check for test reports
console.log("\n📊 Checking Test Reports:");
const reportsDir = 'docs/reports';
if (fs.existsSync(reportsDir)) {
  const reportFiles = fs.readdirSync(reportsDir).filter(file => file.endsWith('.json'));
  if (reportFiles.length > 0) {
    console.log(`✅ Found ${reportFiles.length} test report(s):`);
    reportFiles.forEach(file => {
      console.log(`   📄 ${file}`);
    });
    results.passed++;
  } else {
    console.log(`⚠️ Reports directory exists but no JSON reports found`);
    results.failed++;
  }
  results.totalChecks++;
} else {
  console.log(`❌ Reports directory not found`);
  results.failed++;
  results.totalChecks++;
}

// Check package.json for required dependencies
console.log("\n📦 Checking Dependencies:");
if (fs.existsSync('package.json')) {
  const packageJson = JSON.parse(fs.readFileSync('package.json', 'utf8'));
  const requiredDeps = [
    '@radix-ui/react-progress',
    'lucide-react',
    'sonner'
  ];
  
  requiredDeps.forEach(dep => {
    results.totalChecks++;
    if (packageJson.dependencies?.[dep] || packageJson.devDependencies?.[dep]) {
      results.passed++;
      console.log(`✅ ${dep} dependency installed`);
    } else {
      results.failed++;
      console.log(`❌ ${dep} dependency missing`);
    }
  });
} else {
  console.log(`❌ package.json not found`);
}

// Summary
console.log("\n📊 ORGANIZATION SUMMARY:");
console.log(`✅ Passed: ${results.passed}/${results.totalChecks} (${Math.round((results.passed/results.totalChecks)*100)}%)`);
console.log(`❌ Failed: ${results.failed}/${results.totalChecks} (${Math.round((results.failed/results.totalChecks)*100)}%)`);

const organizationScore = Math.round((results.passed/results.totalChecks)*100);

if (organizationScore >= 95) {
  console.log("\n🎉 EXCELLENT: Project is perfectly organized!");
} else if (organizationScore >= 85) {
  console.log("\n👍 VERY GOOD: Project is well organized with minor issues.");
} else if (organizationScore >= 75) {
  console.log("\n👌 GOOD: Project organization needs minor improvements.");
} else {
  console.log("\n⚠️ NEEDS WORK: Project organization requires attention.");
}

console.log("\n📁 FINAL PROJECT STRUCTURE:");
console.log(`
school-management-system/
├── docs/
│   ├── implementation/
│   │   ├── principal-login-system.md
│   │   ├── student-promotion-system.md
│   │   └── academic-structure-corrections.md
│   ├── testing/
│   │   └── testing-guide.md
│   ├── reports/
│   │   └── [test-reports].json
│   └── README.md
├── scripts/
│   ├── testing/
│   │   ├── test-principal-functionality.js
│   │   ├── test-all-principal-routes.js
│   │   ├── test-principal-add-functionality.js
│   │   ├── test-corrected-academic-structure.js
│   │   └── test-promotion-system.js
│   └── setup/
│       ├── create-missing-principal-routes.js
│       └── verify-organization.js
├── components/ui/
│   └── progress.tsx
├── app/principal/
│   ├── dashboard/page.tsx
│   ├── academic/page.tsx
│   ├── promotion/
│   │   ├── page.tsx
│   │   └── workflow/page.tsx
│   └── [26 other routes]
└── [other project files]
`);

console.log("\n🚀 QUICK ACCESS:");
console.log("📖 Documentation: docs/README.md");
console.log("🧪 Run Tests: node scripts/testing/test-all-principal-routes.js");
console.log("🔧 Setup: node scripts/setup/create-missing-principal-routes.js");
console.log("🌐 Login: http://localhost:3000/login (<EMAIL> / principal123)");

// Save organization report
const report = {
  timestamp: new Date().toISOString(),
  organizationScore,
  summary: {
    totalChecks: results.totalChecks,
    passed: results.passed,
    failed: results.failed
  },
  details: results.details,
  recommendations: results.failed > 0 ? [
    "Create missing directories and files",
    "Run setup scripts to generate missing components",
    "Install missing dependencies",
    "Verify all paths are correct"
  ] : [
    "Project is well organized",
    "All files and directories are in place",
    "Ready for development and testing"
  ]
};

fs.writeFileSync('docs/reports/organization-verification-report.json', JSON.stringify(report, null, 2));
console.log("\n📊 Organization report saved to: docs/reports/organization-verification-report.json");
