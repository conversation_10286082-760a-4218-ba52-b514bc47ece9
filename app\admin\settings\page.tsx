"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { useGetSystemSettings, useUpdateSystemSettings, useResetSystemSettings } from "@/features/api/use-system-settings";
import { useLogUserAction } from "@/features/api/use-audit-logs";
import { toast } from "sonner";
import {
  Settings,
  School,
  Users,
  Shield,
  Bell,
  Mail,
  Database,
  Palette,
  Globe,
  Lock,
  Calendar,
  Clock,
  Save,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Eye,
  EyeOff,
  Upload,
  Download,
} from "lucide-react";

export default function AdminSettings() {
  const [user, setUser] = useState<any>(null);
  const [activeTab, setActiveTab] = useState("general");
  const [showPassword, setShowPassword] = useState(false);
  const [settings, setSettings] = useState({
    general: {
      schoolName: "Greenwood International School",
      schoolCode: "GIS2024",
      address: "123 Education Street, Knowledge City, State 560001",
      phone: "+91 80 1234 5678",
      email: "<EMAIL>",
      website: "www.greenwood.edu.in",
      establishedYear: "1995",
      affiliation: "CBSE",
    },
    academic: {
      academicYear: "2024-25",
      sessionStart: "2024-04-01",
      sessionEnd: "2025-03-31",
      workingDays: "Monday to Saturday",
      schoolTiming: "8:00 AM - 3:30 PM",
      breakTime: "11:30 AM - 12:00 PM",
      lunchTime: "1:00 PM - 1:30 PM",
      gradingSystem: "10-point GPA",
    },
    notifications: {
      emailNotifications: true,
      smsNotifications: true,
      pushNotifications: true,
      parentNotifications: true,
      teacherNotifications: true,
      adminNotifications: true,
      feeReminders: true,
      attendanceAlerts: true,
    },
    security: {
      passwordPolicy: "Strong",
      sessionTimeout: "30 minutes",
      twoFactorAuth: false,
      loginAttempts: "5",
      accountLockout: "15 minutes",
      dataBackup: "Daily",
      auditLogs: true,
    },
    system: {
      timezone: "Asia/Kolkata",
      dateFormat: "DD/MM/YYYY",
      currency: "INR",
      language: "English",
      theme: "Light",
      autoBackup: true,
      maintenanceMode: false,
      debugMode: false,
    },
  });
  const router = useRouter();

  // API hooks
  const { data: systemSettingsData, isLoading: settingsLoading } = useGetSystemSettings();
  const updateSettingsMutation = useUpdateSystemSettings();
  const resetSettingsMutation = useResetSystemSettings();
  const logUserAction = useLogUserAction();

  // Load settings from API
  useEffect(() => {
    if (systemSettingsData?.data) {
      setSettings(prev => ({
        ...prev,
        ...systemSettingsData.data,
      }));
    }
  }, [systemSettingsData]);

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "super_admin" && parsedUser.role !== "admin") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  const handleSave = async () => {
    try {
      // Save each category
      for (const [category, categorySettings] of Object.entries(settings)) {
        await updateSettingsMutation.mutateAsync({
          category,
          settings: categorySettings,
        });
      }

      // Log the action
      logUserAction("update_system_settings", `Updated ${activeTab} settings`);
    } catch (error) {
      console.error("Error saving settings:", error);
    }
  };

  const handleReset = async () => {
    try {
      if (activeTab === "all") {
        await resetSettingsMutation.mutateAsync("all");
        logUserAction("reset_system_settings", "Reset all system settings");
      } else {
        await resetSettingsMutation.mutateAsync(activeTab);
        logUserAction("reset_system_settings", `Reset ${activeTab} settings`);
      }
    } catch (error) {
      console.error("Error resetting settings:", error);
    }
  };

  const tabs = [
    { key: "general", label: "General", icon: School },
    { key: "academic", label: "Academic", icon: Calendar },
    { key: "notifications", label: "Notifications", icon: Bell },
    { key: "security", label: "Security", icon: Shield },
    { key: "system", label: "System", icon: Settings },
  ];

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">System Settings</h1>
            <p className="text-gray-600">Configure and manage system preferences</p>
          </div>
          <div className="flex gap-2">
            <Button
              variant="outline"
              onClick={handleReset}
              disabled={resetSettingsMutation.isPending}
            >
              <RefreshCw className={`h-4 w-4 mr-2 ${resetSettingsMutation.isPending ? 'animate-spin' : ''}`} />
              Reset
            </Button>
            <Button
              onClick={handleSave}
              disabled={updateSettingsMutation.isPending || settingsLoading}
            >
              <Save className="h-4 w-4 mr-2" />
              {updateSettingsMutation.isPending ? 'Saving...' : 'Save Changes'}
            </Button>
          </div>
        </div>

        {/* Settings Navigation */}
        <Card>
          <CardContent className="p-0">
            <div className="flex border-b">
              {tabs.map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key)}
                  className={`flex items-center px-6 py-4 text-sm font-medium border-b-2 transition-colors ${
                    activeTab === tab.key
                      ? "border-blue-500 text-blue-600 bg-blue-50"
                      : "border-transparent text-gray-500 hover:text-gray-700 hover:bg-gray-50"
                  }`}
                >
                  <tab.icon className="h-4 w-4 mr-2" />
                  {tab.label}
                </button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Settings Content */}
        <Card>
          <CardContent className="p-6">
            {/* General Settings */}
            {activeTab === "general" && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">School Information</h3>
                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">School Name</label>
                      <Input
                        value={settings.general.schoolName}
                        onChange={(e) => setSettings({
                          ...settings,
                          general: { ...settings.general, schoolName: e.target.value }
                        })}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">School Code</label>
                      <Input
                        value={settings.general.schoolCode}
                        onChange={(e) => setSettings({
                          ...settings,
                          general: { ...settings.general, schoolCode: e.target.value }
                        })}
                      />
                    </div>
                    <div className="md:col-span-2">
                      <label className="block text-sm font-medium text-gray-700 mb-2">Address</label>
                      <Input
                        value={settings.general.address}
                        onChange={(e) => setSettings({
                          ...settings,
                          general: { ...settings.general, address: e.target.value }
                        })}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Phone</label>
                      <Input
                        value={settings.general.phone}
                        onChange={(e) => setSettings({
                          ...settings,
                          general: { ...settings.general, phone: e.target.value }
                        })}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Email</label>
                      <Input
                        type="email"
                        value={settings.general.email}
                        onChange={(e) => setSettings({
                          ...settings,
                          general: { ...settings.general, email: e.target.value }
                        })}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Website</label>
                      <Input
                        value={settings.general.website}
                        onChange={(e) => setSettings({
                          ...settings,
                          general: { ...settings.general, website: e.target.value }
                        })}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Established Year</label>
                      <Input
                        value={settings.general.establishedYear}
                        onChange={(e) => setSettings({
                          ...settings,
                          general: { ...settings.general, establishedYear: e.target.value }
                        })}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Affiliation</label>
                      <select
                        value={settings.general.affiliation}
                        onChange={(e) => setSettings({
                          ...settings,
                          general: { ...settings.general, affiliation: e.target.value }
                        })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="CBSE">CBSE</option>
                        <option value="ICSE">ICSE</option>
                        <option value="State Board">State Board</option>
                        <option value="IB">International Baccalaureate</option>
                      </select>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Academic Settings */}
            {activeTab === "academic" && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Academic Configuration</h3>
                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Academic Year</label>
                      <Input
                        value={settings.academic.academicYear}
                        onChange={(e) => setSettings({
                          ...settings,
                          academic: { ...settings.academic, academicYear: e.target.value }
                        })}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Grading System</label>
                      <select
                        value={settings.academic.gradingSystem}
                        onChange={(e) => setSettings({
                          ...settings,
                          academic: { ...settings.academic, gradingSystem: e.target.value }
                        })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="10-point GPA">10-point GPA</option>
                        <option value="4-point GPA">4-point GPA</option>
                        <option value="Percentage">Percentage</option>
                        <option value="Letter Grades">Letter Grades</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Session Start Date</label>
                      <Input
                        type="date"
                        value={settings.academic.sessionStart}
                        onChange={(e) => setSettings({
                          ...settings,
                          academic: { ...settings.academic, sessionStart: e.target.value }
                        })}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Session End Date</label>
                      <Input
                        type="date"
                        value={settings.academic.sessionEnd}
                        onChange={(e) => setSettings({
                          ...settings,
                          academic: { ...settings.academic, sessionEnd: e.target.value }
                        })}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Working Days</label>
                      <Input
                        value={settings.academic.workingDays}
                        onChange={(e) => setSettings({
                          ...settings,
                          academic: { ...settings.academic, workingDays: e.target.value }
                        })}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">School Timing</label>
                      <Input
                        value={settings.academic.schoolTiming}
                        onChange={(e) => setSettings({
                          ...settings,
                          academic: { ...settings.academic, schoolTiming: e.target.value }
                        })}
                      />
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Notification Settings */}
            {activeTab === "notifications" && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Notification Preferences</h3>
                  <div className="space-y-4">
                    {Object.entries(settings.notifications).map(([key, value]) => (
                      <div key={key} className="flex items-center justify-between p-4 border rounded-lg">
                        <div>
                          <div className="font-medium text-gray-900">
                            {key.replace(/([A-Z])/g, ' $1').replace(/^./, str => str.toUpperCase())}
                          </div>
                          <div className="text-sm text-gray-500">
                            {key === 'emailNotifications' && 'Send notifications via email'}
                            {key === 'smsNotifications' && 'Send notifications via SMS'}
                            {key === 'pushNotifications' && 'Send push notifications to mobile app'}
                            {key === 'parentNotifications' && 'Send notifications to parents'}
                            {key === 'teacherNotifications' && 'Send notifications to teachers'}
                            {key === 'adminNotifications' && 'Send notifications to administrators'}
                            {key === 'feeReminders' && 'Send automatic fee payment reminders'}
                            {key === 'attendanceAlerts' && 'Send attendance alerts to parents'}
                          </div>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={value}
                            onChange={(e) => setSettings({
                              ...settings,
                              notifications: { ...settings.notifications, [key]: e.target.checked }
                            })}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Security Settings */}
            {activeTab === "security" && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Security Configuration</h3>
                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Password Policy</label>
                      <select
                        value={settings.security.passwordPolicy}
                        onChange={(e) => setSettings({
                          ...settings,
                          security: { ...settings.security, passwordPolicy: e.target.value }
                        })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="Basic">Basic (8+ characters)</option>
                        <option value="Strong">Strong (8+ chars, mixed case, numbers)</option>
                        <option value="Very Strong">Very Strong (12+ chars, symbols)</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Session Timeout</label>
                      <select
                        value={settings.security.sessionTimeout}
                        onChange={(e) => setSettings({
                          ...settings,
                          security: { ...settings.security, sessionTimeout: e.target.value }
                        })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="15 minutes">15 minutes</option>
                        <option value="30 minutes">30 minutes</option>
                        <option value="1 hour">1 hour</option>
                        <option value="2 hours">2 hours</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Max Login Attempts</label>
                      <Input
                        type="number"
                        value={settings.security.loginAttempts}
                        onChange={(e) => setSettings({
                          ...settings,
                          security: { ...settings.security, loginAttempts: e.target.value }
                        })}
                      />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Account Lockout Duration</label>
                      <select
                        value={settings.security.accountLockout}
                        onChange={(e) => setSettings({
                          ...settings,
                          security: { ...settings.security, accountLockout: e.target.value }
                        })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="5 minutes">5 minutes</option>
                        <option value="15 minutes">15 minutes</option>
                        <option value="30 minutes">30 minutes</option>
                        <option value="1 hour">1 hour</option>
                      </select>
                    </div>
                    <div className="md:col-span-2">
                      <div className="flex items-center justify-between p-4 border rounded-lg">
                        <div>
                          <div className="font-medium text-gray-900">Two-Factor Authentication</div>
                          <div className="text-sm text-gray-500">Add an extra layer of security to user accounts</div>
                        </div>
                        <label className="relative inline-flex items-center cursor-pointer">
                          <input
                            type="checkbox"
                            checked={settings.security.twoFactorAuth}
                            onChange={(e) => setSettings({
                              ...settings,
                              security: { ...settings.security, twoFactorAuth: e.target.checked }
                            })}
                            className="sr-only peer"
                          />
                          <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                        </label>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* System Settings */}
            {activeTab === "system" && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">System Configuration</h3>
                  <div className="grid gap-4 md:grid-cols-2">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Timezone</label>
                      <select
                        value={settings.system.timezone}
                        onChange={(e) => setSettings({
                          ...settings,
                          system: { ...settings.system, timezone: e.target.value }
                        })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="Asia/Kolkata">Asia/Kolkata (IST)</option>
                        <option value="UTC">UTC</option>
                        <option value="America/New_York">America/New_York (EST)</option>
                        <option value="Europe/London">Europe/London (GMT)</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Date Format</label>
                      <select
                        value={settings.system.dateFormat}
                        onChange={(e) => setSettings({
                          ...settings,
                          system: { ...settings.system, dateFormat: e.target.value }
                        })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                        <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                        <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Currency</label>
                      <select
                        value={settings.system.currency}
                        onChange={(e) => setSettings({
                          ...settings,
                          system: { ...settings.system, currency: e.target.value }
                        })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="INR">Indian Rupee (₹)</option>
                        <option value="USD">US Dollar ($)</option>
                        <option value="EUR">Euro (€)</option>
                        <option value="GBP">British Pound (£)</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-2">Language</label>
                      <select
                        value={settings.system.language}
                        onChange={(e) => setSettings({
                          ...settings,
                          system: { ...settings.system, language: e.target.value }
                        })}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      >
                        <option value="English">English</option>
                        <option value="Hindi">Hindi</option>
                        <option value="Spanish">Spanish</option>
                        <option value="French">French</option>
                      </select>
                    </div>
                  </div>

                  <div className="space-y-4 mt-6">
                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <div className="font-medium text-gray-900">Auto Backup</div>
                        <div className="text-sm text-gray-500">Automatically backup system data daily</div>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={settings.system.autoBackup}
                          onChange={(e) => setSettings({
                            ...settings,
                            system: { ...settings.system, autoBackup: e.target.checked }
                          })}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </label>
                    </div>

                    <div className="flex items-center justify-between p-4 border rounded-lg">
                      <div>
                        <div className="font-medium text-gray-900">Maintenance Mode</div>
                        <div className="text-sm text-gray-500">Put system in maintenance mode for updates</div>
                      </div>
                      <label className="relative inline-flex items-center cursor-pointer">
                        <input
                          type="checkbox"
                          checked={settings.system.maintenanceMode}
                          onChange={(e) => setSettings({
                            ...settings,
                            system: { ...settings.system, maintenanceMode: e.target.checked }
                          })}
                          className="sr-only peer"
                        />
                        <div className="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                      </label>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* System Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Database className="h-5 w-5 mr-2" />
              System Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-4">
              <div className="flex items-center space-x-3">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <div>
                  <div className="font-medium text-gray-900">Database</div>
                  <div className="text-sm text-green-600">Connected</div>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <div>
                  <div className="font-medium text-gray-900">Email Service</div>
                  <div className="text-sm text-green-600">Active</div>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <AlertTriangle className="h-5 w-5 text-yellow-500" />
                <div>
                  <div className="font-medium text-gray-900">SMS Service</div>
                  <div className="text-sm text-yellow-600">Limited</div>
                </div>
              </div>
              <div className="flex items-center space-x-3">
                <CheckCircle className="h-5 w-5 text-green-500" />
                <div>
                  <div className="font-medium text-gray-900">Backup</div>
                  <div className="text-sm text-green-600">Up to date</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
