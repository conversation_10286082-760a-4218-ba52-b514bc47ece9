import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { client } from "@/lib/hono";
import { toast } from "sonner";

// Get all backups
export const useGetSystemBackups = (page: number = 1, limit: number = 10) => {
  return useQuery({
    queryKey: ["system-backups", page, limit],
    queryFn: async () => {
      const response = await client.api["system-backups"].$get({
        query: { page: page.toString(), limit: limit.toString() },
      });

      if (!response.ok) {
        const errorData = await response.json() as { error?: string };
        throw new Error(errorData.error || "Failed to fetch backups");
      }

      return await response.json();
    },
  });
};

// Create backup
export const useCreateSystemBackup = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: {
      name: string;
      description?: string;
      backupType: "full" | "incremental" | "manual";
      createdBy: string;
    }) => {
      const response = await client.api["system-backups"].$post({
        json: data,
      });

      if (!response.ok) {
        const errorData = await response.json() as { error?: string };
        throw new Error(errorData.error || "Failed to create backup");
      }

      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["system-backups"] });
      queryClient.invalidateQueries({ queryKey: ["backup-stats"] });
      toast.success("Backup started successfully!");
    },
    onError: (error: any) => {
      toast.error(`Failed to create backup: ${error.message}`);
    },
  });
};

// Get backup by ID
export const useGetSystemBackup = (id: string) => {
  return useQuery({
    queryKey: ["system-backups", id],
    queryFn: async () => {
      const response = await client.api["system-backups"][":id"].$get({
        param: { id },
      });

      if (!response.ok) {
        const errorData = await response.json() as { error?: string };
        throw new Error(errorData.error || "Failed to fetch backup");
      }

      return await response.json();
    },
    enabled: !!id,
  });
};

// Delete backup
export const useDeleteSystemBackup = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await client.api["system-backups"][":id"].$delete({
        param: { id },
      });

      if (!response.ok) {
        const errorData = await response.json() as { error?: string };
        throw new Error(errorData.error || "Failed to delete backup");
      }

      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["system-backups"] });
      queryClient.invalidateQueries({ queryKey: ["backup-stats"] });
      toast.success("Backup deleted successfully!");
    },
    onError: (error: any) => {
      toast.error(`Failed to delete backup: ${error.message}`);
    },
  });
};

// Download backup
export const useDownloadSystemBackup = () => {
  return useMutation({
    mutationFn: async (id: string) => {
      const response = await client.api["system-backups"][":id"].download.$get({
        param: { id },
      });

      if (!response.ok) {
        const errorData = await response.json() as { error?: string };
        throw new Error(errorData.error || "Failed to download backup");
      }

      return await response.json();
    },
    onSuccess: (data) => {
      // In a real implementation, you would trigger the actual download
      toast.success("Backup download started!");
      console.log("Download data:", data);
    },
    onError: (error: any) => {
      toast.error(`Failed to download backup: ${error.message}`);
    },
  });
};

// Get backup statistics
export const useGetBackupStats = () => {
  return useQuery({
    queryKey: ["backup-stats"],
    queryFn: async () => {
      const response = await client.api["system-backups"].stats.$get();

      if (!response.ok) {
        const errorData = await response.json() as { error?: string };
        throw new Error(errorData.error || "Failed to fetch backup statistics");
      }

      return await response.json();
    },
  });
};
