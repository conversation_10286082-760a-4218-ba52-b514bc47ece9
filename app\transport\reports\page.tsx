"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Bus,
  Users,
  Calendar,
  Download,
  Filter,
  Eye,
  FileText,
  PieChart,
  Target,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  IndianRupee,
  MapPin,
  Fuel,
  Wrench,
} from "lucide-react";

export default function TransportReports() {
  const [user, setUser] = useState<any>(null);
  const [selectedPeriod, setSelectedPeriod] = useState("month");
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "transport_manager") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  const transportMetrics = {
    totalVehicles: 12,
    totalStudents: 456,
    totalRoutes: 8,
    totalDrivers: 15,
    monthlyRevenue: 1596000,
    operationalCost: 425000,
    fuelCost: 185000,
    maintenanceCost: 125000,
    driverSalaries: 115000,
    routeEfficiency: 87.5,
    vehicleUtilization: 92.3,
    onTimePerformance: 94.2,
    studentSatisfaction: 4.6,
    safetyIncidents: 0,
    fuelEfficiency: 8.7,
  };

  const routePerformance = [
    { route: "Route A - Central Delhi", students: 115, efficiency: 92, onTime: 96, revenue: 402500 },
    { route: "Route B - South Delhi", students: 98, efficiency: 89, onTime: 94, revenue: 313600 },
    { route: "Route C - East Delhi", students: 87, efficiency: 85, onTime: 92, revenue: 287100 },
    { route: "Route D - West Delhi", students: 98, efficiency: 88, onTime: 95, revenue: 372400 },
    { route: "Route E - North Delhi", students: 58, efficiency: 83, onTime: 89, revenue: 220400 },
  ];

  const monthlyTrends = [
    { month: "Aug", revenue: 1450000, cost: 380000, students: 420, efficiency: 85 },
    { month: "Sep", revenue: 1520000, cost: 395000, students: 435, efficiency: 86 },
    { month: "Oct", revenue: 1580000, cost: 410000, students: 448, efficiency: 87 },
    { month: "Nov", revenue: 1596000, cost: 425000, students: 456, efficiency: 88 },
    { month: "Dec", revenue: 1612000, cost: 430000, students: 462, efficiency: 89 },
    { month: "Jan", revenue: 1596000, cost: 425000, students: 456, efficiency: 88 },
  ];

  const reportCategories = [
    {
      title: "Fleet Management Reports",
      description: "Vehicle performance, maintenance, and utilization reports",
      icon: Bus,
      reports: [
        { name: "Vehicle Utilization Report", description: "Track vehicle usage and efficiency" },
        { name: "Maintenance Schedule Report", description: "Upcoming and completed maintenance" },
        { name: "Fuel Consumption Report", description: "Fuel usage and cost analysis" },
        { name: "Driver Performance Report", description: "Driver efficiency and safety records" },
      ],
    },
    {
      title: "Route Analytics",
      description: "Route performance and optimization reports",
      icon: MapPin,
      reports: [
        { name: "Route Efficiency Report", description: "Route performance and optimization" },
        { name: "Student Distribution Report", description: "Student allocation across routes" },
        { name: "On-Time Performance Report", description: "Punctuality and schedule adherence" },
        { name: "Route Profitability Report", description: "Revenue and cost analysis by route" },
      ],
    },
    {
      title: "Financial Reports",
      description: "Revenue, costs, and financial analytics",
      icon: IndianRupee,
      reports: [
        { name: "Revenue Analysis Report", description: "Monthly and yearly revenue trends" },
        { name: "Cost Breakdown Report", description: "Operational cost analysis" },
        { name: "Fee Collection Report", description: "Student fee payment status" },
        { name: "Profit & Loss Report", description: "Transport department P&L statement" },
      ],
    },
    {
      title: "Safety & Compliance",
      description: "Safety records and regulatory compliance",
      icon: CheckCircle,
      reports: [
        { name: "Safety Incident Report", description: "Safety incidents and preventive measures" },
        { name: "Driver License Report", description: "Driver license status and renewals" },
        { name: "Vehicle Inspection Report", description: "Vehicle safety and compliance checks" },
        { name: "Insurance Status Report", description: "Vehicle and driver insurance status" },
      ],
    },
  ];

  const generateReport = (reportName: string) => {
    alert(`Generating ${reportName}...`);
  };

  const exportReport = (format: string) => {
    alert(`Exporting report in ${format} format...`);
  };

  const profitMargin = Math.round(((transportMetrics.monthlyRevenue - transportMetrics.operationalCost) / transportMetrics.monthlyRevenue) * 100);

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Transport Reports & Analytics</h1>
            <p className="text-gray-600">Comprehensive transport performance and analytics dashboard</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              Filter Period
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export All
            </Button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <IndianRupee className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    ₹{(transportMetrics.monthlyRevenue / 100000).toFixed(1)}L
                  </div>
                  <p className="text-sm text-gray-600">Monthly Revenue</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Target className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {profitMargin}%
                  </div>
                  <p className="text-sm text-gray-600">Profit Margin</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {transportMetrics.onTimePerformance}%
                  </div>
                  <p className="text-sm text-gray-600">On-Time Performance</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Fuel className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    {transportMetrics.fuelEfficiency}
                  </div>
                  <p className="text-sm text-gray-600">Avg Fuel Efficiency (km/l)</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Period Selection */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <h3 className="text-lg font-semibold">Report Period</h3>
              <div className="flex gap-2">
                {["week", "month", "quarter", "year"].map((period) => (
                  <Button
                    key={period}
                    variant={selectedPeriod === period ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedPeriod(period)}
                    className="capitalize"
                  >
                    {period}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Performance Overview */}
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Route Performance */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <MapPin className="h-5 w-5 mr-2" />
                Route Performance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {routePerformance.map((route, index) => (
                  <div key={route.route} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center text-sm font-medium text-blue-600">
                        {index + 1}
                      </div>
                      <div>
                        <div className="font-medium">{route.route}</div>
                        <div className="text-sm text-gray-500">{route.students} students</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-medium">₹{(route.revenue / 1000).toFixed(0)}K</div>
                      <div className="text-sm text-gray-500">{route.efficiency}% efficiency</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Monthly Trends */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="h-5 w-5 mr-2" />
                Monthly Trends
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {monthlyTrends.map((trend) => (
                  <div key={trend.month} className="flex items-center justify-between">
                    <div className="font-medium w-12">{trend.month}</div>
                    <div className="flex-1 mx-4">
                      <div className="flex justify-between text-sm mb-1">
                        <span>Revenue: ₹{(trend.revenue / 100000).toFixed(1)}L</span>
                        <span>Cost: ₹{(trend.cost / 100000).toFixed(1)}L</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-green-600 h-2 rounded-full"
                          style={{ width: `${(trend.revenue - trend.cost) / trend.revenue * 100}%` }}
                        />
                      </div>
                    </div>
                    <div className="text-sm text-gray-500 w-16 text-right">
                      {trend.students} students
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Report Categories */}
        <div className="grid gap-6 lg:grid-cols-2">
          {reportCategories.map((category) => (
            <Card key={category.title}>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <category.icon className="h-5 w-5 mr-2" />
                  {category.title}
                </CardTitle>
                <p className="text-sm text-gray-600">{category.description}</p>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {category.reports.map((report) => (
                    <div key={report.name} className="flex items-center justify-between p-3 border rounded-lg hover:bg-gray-50">
                      <div className="flex-1">
                        <div className="font-medium text-gray-900">{report.name}</div>
                        <div className="text-sm text-gray-500">{report.description}</div>
                      </div>
                      <div className="flex space-x-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => generateReport(report.name)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => exportReport("PDF")}
                        >
                          <Download className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Cost Breakdown */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <PieChart className="h-5 w-5 mr-2" />
              Cost Breakdown Analysis
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-4">
              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-red-600 mb-2">
                  ₹{(transportMetrics.fuelCost / 1000).toFixed(0)}K
                </div>
                <div className="text-sm text-gray-600">Fuel Costs</div>
                <div className="text-xs text-gray-500 mt-1">
                  {Math.round((transportMetrics.fuelCost / transportMetrics.operationalCost) * 100)}% of total
                </div>
              </div>

              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-yellow-600 mb-2">
                  ₹{(transportMetrics.maintenanceCost / 1000).toFixed(0)}K
                </div>
                <div className="text-sm text-gray-600">Maintenance</div>
                <div className="text-xs text-gray-500 mt-1">
                  {Math.round((transportMetrics.maintenanceCost / transportMetrics.operationalCost) * 100)}% of total
                </div>
              </div>

              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-blue-600 mb-2">
                  ₹{(transportMetrics.driverSalaries / 1000).toFixed(0)}K
                </div>
                <div className="text-sm text-gray-600">Driver Salaries</div>
                <div className="text-xs text-gray-500 mt-1">
                  {Math.round((transportMetrics.driverSalaries / transportMetrics.operationalCost) * 100)}% of total
                </div>
              </div>

              <div className="text-center p-4 border rounded-lg">
                <div className="text-2xl font-bold text-green-600 mb-2">
                  ₹{((transportMetrics.monthlyRevenue - transportMetrics.operationalCost) / 1000).toFixed(0)}K
                </div>
                <div className="text-sm text-gray-600">Net Profit</div>
                <div className="flex items-center justify-center mt-1 text-green-600">
                  <TrendingUp className="h-3 w-3 mr-1" />
                  <span className="text-xs">{profitMargin}% margin</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Export Options */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              Export Options
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-4">
              <Button
                variant="outline"
                className="h-20 flex flex-col items-center justify-center"
                onClick={() => exportReport("PDF")}
              >
                <FileText className="h-6 w-6 mb-2" />
                Export as PDF
              </Button>

              <Button
                variant="outline"
                className="h-20 flex flex-col items-center justify-center"
                onClick={() => exportReport("Excel")}
              >
                <BarChart3 className="h-6 w-6 mb-2" />
                Export as Excel
              </Button>

              <Button
                variant="outline"
                className="h-20 flex flex-col items-center justify-center"
                onClick={() => exportReport("CSV")}
              >
                <Download className="h-6 w-6 mb-2" />
                Export as CSV
              </Button>

              <Button
                variant="outline"
                className="h-20 flex flex-col items-center justify-center"
                onClick={() => exportReport("Print")}
              >
                <FileText className="h-6 w-6 mb-2" />
                Print Report
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}