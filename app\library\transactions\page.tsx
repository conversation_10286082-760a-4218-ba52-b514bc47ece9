"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  BookOpen,
  Search,
  Filter,
  Download,
  Calendar,
  User,
  Clock,
  CheckCircle,
  AlertTriangle,
  XCircle,
  RotateCcw,
  Plus,
  Eye,
  IndianRupee,
  Hash,
  Phone,
  Mail,
} from "lucide-react";

export default function LibraryTransactions() {
  const [user, setUser] = useState<any>(null);
  const [selectedTab, setSelectedTab] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedTransaction, setSelectedTransaction] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "library") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  const transactionStats = {
    totalTransactions: 8945,
    activeIssues: 2530,
    returnsToday: 45,
    overdueBooks: 187,
    finesCollected: 12450,
    renewalsToday: 23,
  };

  const transactions = [
    {
      id: "TXN001",
      type: "Issue",
      bookId: "BK001",
      bookTitle: "The Complete Guide to Physics",
      bookAuthor: "Dr. Rajesh Kumar",
      studentId: "STU001",
      studentName: "Arjun Sharma",
      studentClass: "Grade 12A",
      studentPhone: "+91 9876543210",
      studentEmail: "<EMAIL>",
      issueDate: "2024-01-15",
      dueDate: "2024-01-29",
      returnDate: null,
      status: "Issued",
      fine: 0,
      renewalCount: 0,
      librarian: "Ms. Priya Gupta",
      notes: "Book issued for physics project",
    },
    {
      id: "TXN002",
      type: "Return",
      bookId: "BK002",
      bookTitle: "Mathematics for Class XII",
      bookAuthor: "Prof. Sunita Sharma",
      studentId: "STU002",
      studentName: "Sneha Patel",
      studentClass: "Grade 12B",
      studentPhone: "+91 9876543211",
      studentEmail: "<EMAIL>",
      issueDate: "2024-01-08",
      dueDate: "2024-01-22",
      returnDate: "2024-01-20",
      status: "Returned",
      fine: 0,
      renewalCount: 1,
      librarian: "Ms. Priya Gupta",
      notes: "Returned in good condition",
    },
    {
      id: "TXN003",
      type: "Issue",
      bookId: "BK003",
      bookTitle: "Indian History and Culture",
      bookAuthor: "Dr. Anil Mehta",
      studentId: "STU003",
      studentName: "Rahul Kumar",
      studentClass: "Grade 11A",
      studentPhone: "+91 9876543212",
      studentEmail: "<EMAIL>",
      issueDate: "2024-01-10",
      dueDate: "2024-01-24",
      returnDate: null,
      status: "Overdue",
      fine: 50,
      renewalCount: 0,
      librarian: "Mr. Suresh Singh",
      notes: "Student notified about due date",
    },
    {
      id: "TXN004",
      type: "Renewal",
      bookId: "BK004",
      bookTitle: "English Literature Anthology",
      bookAuthor: "Ms. Priya Gupta",
      studentId: "STU004",
      studentName: "Ananya Singh",
      studentClass: "Grade 10A",
      studentPhone: "+91 9876543213",
      studentEmail: "<EMAIL>",
      issueDate: "2024-01-05",
      dueDate: "2024-01-26",
      returnDate: null,
      status: "Renewed",
      fine: 0,
      renewalCount: 1,
      librarian: "Ms. Priya Gupta",
      notes: "Renewed for additional 14 days",
    },
    {
      id: "TXN005",
      type: "Return",
      bookId: "BK005",
      bookTitle: "Computer Science Fundamentals",
      bookAuthor: "Er. Vikash Singh",
      studentId: "STU005",
      studentName: "Karan Gupta",
      studentClass: "Grade 11B",
      studentPhone: "+91 9876543214",
      studentEmail: "<EMAIL>",
      issueDate: "2023-12-20",
      dueDate: "2024-01-03",
      returnDate: "2024-01-18",
      status: "Returned Late",
      fine: 150,
      renewalCount: 0,
      librarian: "Mr. Suresh Singh",
      notes: "Returned 15 days late, fine collected",
    },
    {
      id: "TXN006",
      type: "Issue",
      bookId: "BK006",
      bookTitle: "Chemistry Lab Manual",
      bookAuthor: "Dr. Kavita Joshi",
      studentId: "STU006",
      studentName: "Priya Sharma",
      studentClass: "Grade 12A",
      studentPhone: "+91 9876543215",
      studentEmail: "<EMAIL>",
      issueDate: "2024-01-22",
      dueDate: "2024-02-05",
      returnDate: null,
      status: "Issued",
      fine: 0,
      renewalCount: 0,
      librarian: "Ms. Priya Gupta",
      notes: "Book issued for chemistry practicals",
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Issued": return "text-blue-600 bg-blue-100";
      case "Returned": return "text-green-600 bg-green-100";
      case "Overdue": return "text-red-600 bg-red-100";
      case "Renewed": return "text-purple-600 bg-purple-100";
      case "Returned Late": return "text-orange-600 bg-orange-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Issued": return <BookOpen className="h-4 w-4" />;
      case "Returned": return <CheckCircle className="h-4 w-4" />;
      case "Overdue": return <AlertTriangle className="h-4 w-4" />;
      case "Renewed": return <RotateCcw className="h-4 w-4" />;
      case "Returned Late": return <XCircle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const getTabTransactions = () => {
    let filtered = transactions;
    
    switch (selectedTab) {
      case "issued":
        filtered = transactions.filter(txn => txn.status === "Issued");
        break;
      case "returned":
        filtered = transactions.filter(txn => txn.status === "Returned" || txn.status === "Returned Late");
        break;
      case "overdue":
        filtered = transactions.filter(txn => txn.status === "Overdue");
        break;
      case "renewed":
        filtered = transactions.filter(txn => txn.status === "Renewed");
        break;
      default:
        filtered = transactions;
    }

    return filtered.filter(txn =>
      txn.bookTitle.toLowerCase().includes(searchTerm.toLowerCase()) ||
      txn.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      txn.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      txn.bookId.toLowerCase().includes(searchTerm.toLowerCase())
    );
  };

  const calculateDaysOverdue = (dueDate: string) => {
    const today = new Date();
    const due = new Date(dueDate);
    const diffTime = today.getTime() - due.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays > 0 ? diffDays : 0;
  };

  const processReturn = (transactionId: string) => {
    alert(`Processing return for transaction ${transactionId}`);
  };

  const renewBook = (transactionId: string) => {
    alert(`Renewing book for transaction ${transactionId}`);
  };

  const collectFine = (transactionId: string) => {
    alert(`Collecting fine for transaction ${transactionId}`);
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Library Transactions</h1>
            <p className="text-gray-600">Track book issues, returns, and renewals</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Transaction
            </Button>
          </div>
        </div>

        {/* Transaction Stats */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <BookOpen className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {transactionStats.totalTransactions.toLocaleString()}
                  </div>
                  <p className="text-sm text-gray-600">Total Transactions</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <User className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    {transactionStats.activeIssues.toLocaleString()}
                  </div>
                  <p className="text-sm text-gray-600">Active Issues</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {transactionStats.returnsToday}
                  </div>
                  <p className="text-sm text-gray-600">Returns Today</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <AlertTriangle className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-red-600">
                    {transactionStats.overdueBooks}
                  </div>
                  <p className="text-sm text-gray-600">Overdue Books</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <IndianRupee className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    ₹{transactionStats.finesCollected.toLocaleString()}
                  </div>
                  <p className="text-sm text-gray-600">Fines Collected</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <RotateCcw className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-yellow-600">
                    {transactionStats.renewalsToday}
                  </div>
                  <p className="text-sm text-gray-600">Renewals Today</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Transactions List */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
                  {[
                    { key: "all", label: "All" },
                    { key: "issued", label: "Issued" },
                    { key: "returned", label: "Returned" },
                    { key: "overdue", label: "Overdue" },
                    { key: "renewed", label: "Renewed" },
                  ].map((tab) => (
                    <button
                      key={tab.key}
                      onClick={() => setSelectedTab(tab.key)}
                      className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                        selectedTab === tab.key
                          ? "bg-white text-blue-600 shadow-sm"
                          : "text-gray-600 hover:text-gray-900"
                      }`}
                    >
                      {tab.label}
                    </button>
                  ))}
                </div>
                <div className="flex gap-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search transactions..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 w-64"
                    />
                  </div>
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {getTabTransactions().map((transaction) => (
                  <div
                    key={transaction.id}
                    onClick={() => setSelectedTransaction(transaction)}
                    className={`p-4 border rounded-lg cursor-pointer transition-colors hover:bg-gray-50 ${
                      selectedTransaction?.id === transaction.id ? 'bg-blue-50 border-blue-200' : ''
                    }`}
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-start space-x-4">
                        <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                          <BookOpen className="h-6 w-6 text-blue-600" />
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">{transaction.bookTitle}</h4>
                          <p className="text-sm text-gray-600">by {transaction.bookAuthor}</p>
                          <p className="text-sm text-gray-600">Student: {transaction.studentName} ({transaction.studentClass})</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(transaction.status)}`}>
                          {getStatusIcon(transaction.status)}
                          <span className="ml-1">{transaction.status}</span>
                        </span>
                        <div className="text-xs text-gray-500 mt-1">ID: {transaction.id}</div>
                      </div>
                    </div>

                    <div className="grid gap-2 md:grid-cols-3 text-sm mb-3">
                      <div className="flex items-center text-gray-600">
                        <Calendar className="h-3 w-3 mr-1" />
                        Issued: {transaction.issueDate}
                      </div>
                      <div className="flex items-center text-gray-600">
                        <Clock className="h-3 w-3 mr-1" />
                        Due: {transaction.dueDate}
                      </div>
                      {transaction.returnDate && (
                        <div className="flex items-center text-gray-600">
                          <CheckCircle className="h-3 w-3 mr-1" />
                          Returned: {transaction.returnDate}
                        </div>
                      )}
                    </div>

                    {transaction.status === "Overdue" && (
                      <div className="p-2 bg-red-50 border border-red-200 rounded-lg mb-3">
                        <div className="flex items-center text-red-700">
                          <AlertTriangle className="h-4 w-4 mr-2" />
                          <span className="text-sm font-medium">
                            {calculateDaysOverdue(transaction.dueDate)} days overdue
                          </span>
                        </div>
                      </div>
                    )}

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4 text-sm">
                        {transaction.fine > 0 && (
                          <span className="text-red-600 font-medium">Fine: ₹{transaction.fine}</span>
                        )}
                        {transaction.renewalCount > 0 && (
                          <span className="text-purple-600 font-medium">Renewed: {transaction.renewalCount}x</span>
                        )}
                        <span className="text-gray-600">Librarian: {transaction.librarian}</span>
                      </div>
                      <div className="flex gap-2">
                        {transaction.status === "Issued" && (
                          <>
                            <Button variant="outline" size="sm" onClick={() => processReturn(transaction.id)}>
                              Return
                            </Button>
                            <Button variant="outline" size="sm" onClick={() => renewBook(transaction.id)}>
                              Renew
                            </Button>
                          </>
                        )}
                        {transaction.status === "Overdue" && (
                          <>
                            <Button variant="outline" size="sm" onClick={() => processReturn(transaction.id)}>
                              Return
                            </Button>
                            {transaction.fine > 0 && (
                              <Button variant="outline" size="sm" onClick={() => collectFine(transaction.id)}>
                                Collect Fine
                              </Button>
                            )}
                          </>
                        )}
                        <Button variant="outline" size="sm">
                          <Eye className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Transaction Details */}
          <div className="space-y-6">
            {selectedTransaction ? (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>Transaction Details</span>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(selectedTransaction.status)}`}>
                      {selectedTransaction.status}
                    </span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Transaction Information */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Transaction Information</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Transaction ID:</span>
                          <span className="font-medium">{selectedTransaction.id}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Type:</span>
                          <span className="font-medium">{selectedTransaction.type}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Issue Date:</span>
                          <span className="font-medium">{selectedTransaction.issueDate}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Due Date:</span>
                          <span className="font-medium">{selectedTransaction.dueDate}</span>
                        </div>
                        {selectedTransaction.returnDate && (
                          <div className="flex justify-between">
                            <span className="text-gray-600">Return Date:</span>
                            <span className="font-medium">{selectedTransaction.returnDate}</span>
                          </div>
                        )}
                        <div className="flex justify-between">
                          <span className="text-gray-600">Librarian:</span>
                          <span className="font-medium">{selectedTransaction.librarian}</span>
                        </div>
                      </div>
                    </div>

                    {/* Book Information */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Book Information</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Book ID:</span>
                          <span className="font-medium">{selectedTransaction.bookId}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Title:</span>
                          <span className="font-medium">{selectedTransaction.bookTitle}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Author:</span>
                          <span className="font-medium">{selectedTransaction.bookAuthor}</span>
                        </div>
                      </div>
                    </div>

                    {/* Student Information */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Student Information</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Student ID:</span>
                          <span className="font-medium">{selectedTransaction.studentId}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Name:</span>
                          <span className="font-medium">{selectedTransaction.studentName}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Class:</span>
                          <span className="font-medium">{selectedTransaction.studentClass}</span>
                        </div>
                        <div className="flex items-center">
                          <Phone className="h-4 w-4 text-gray-400 mr-2" />
                          <span>{selectedTransaction.studentPhone}</span>
                        </div>
                        <div className="flex items-center">
                          <Mail className="h-4 w-4 text-gray-400 mr-2" />
                          <span>{selectedTransaction.studentEmail}</span>
                        </div>
                      </div>
                    </div>

                    {/* Additional Details */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Additional Details</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Renewal Count:</span>
                          <span className="font-medium">{selectedTransaction.renewalCount}</span>
                        </div>
                        {selectedTransaction.fine > 0 && (
                          <div className="flex justify-between">
                            <span className="text-gray-600">Fine Amount:</span>
                            <span className="font-medium text-red-600">₹{selectedTransaction.fine}</span>
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Notes */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Notes</h4>
                      <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
                        {selectedTransaction.notes}
                      </p>
                    </div>

                    {/* Actions */}
                    <div className="space-y-2">
                      {selectedTransaction.status === "Issued" && (
                        <>
                          <Button className="w-full" onClick={() => processReturn(selectedTransaction.id)}>
                            <CheckCircle className="h-4 w-4 mr-2" />
                            Process Return
                          </Button>
                          <Button variant="outline" className="w-full" onClick={() => renewBook(selectedTransaction.id)}>
                            <RotateCcw className="h-4 w-4 mr-2" />
                            Renew Book
                          </Button>
                        </>
                      )}
                      {selectedTransaction.status === "Overdue" && (
                        <>
                          <Button className="w-full" onClick={() => processReturn(selectedTransaction.id)}>
                            <CheckCircle className="h-4 w-4 mr-2" />
                            Process Return
                          </Button>
                          {selectedTransaction.fine > 0 && (
                            <Button variant="outline" className="w-full" onClick={() => collectFine(selectedTransaction.id)}>
                              <IndianRupee className="h-4 w-4 mr-2" />
                              Collect Fine (₹{selectedTransaction.fine})
                            </Button>
                          )}
                        </>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="p-6 text-center">
                  <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Select a Transaction</h3>
                  <p className="text-gray-600">Choose a transaction from the list to view details</p>
                </CardContent>
              </Card>
            )}

            {/* Quick Stats */}
            <Card>
              <CardHeader>
                <CardTitle>Today&apos;s Summary</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Books Issued:</span>
                    <span className="font-medium">28</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Books Returned:</span>
                    <span className="font-medium">45</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Books Renewed:</span>
                    <span className="font-medium">23</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Fines Collected:</span>
                    <span className="font-medium">₹850</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
