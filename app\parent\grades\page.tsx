"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Award,
  BookOpen,
  TrendingUp,
  TrendingDown,
  Target,
  BarChart3,
  Calendar,
  Download,
  Eye,
  User,
  Star,
  AlertTriangle,
  CheckCircle,
  FileText,
} from "lucide-react";

export default function ParentGrades() {
  const [user, setUser] = useState<any>(null);
  const [selectedChild, setSelectedChild] = useState("child1");
  const [selectedPeriod, setSelectedPeriod] = useState("current");
  const [selectedTab, setSelectedTab] = useState("overview");
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "parent") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock data for parent's children
  const children = [
    { id: "child1", name: "John Doe", class: "Grade 10A", rollNo: "10A001", currentGPA: 3.8 },
    { id: "child2", name: "Jane Doe", class: "Grade 8B", rollNo: "8B015", currentGPA: 4.0 },
  ];

  const selectedChildData = children.find(child => child.id === selectedChild) || children[0];

  const gradeOverview = {
    currentGPA: selectedChildData.currentGPA,
    classRank: 5,
    totalStudents: 45,
    subjectsCount: 6,
    averageScore: 85.2,
    improvement: "+0.3",
    attendanceRate: 94.5,
  };

  const subjectGrades = [
    { subject: "Mathematics", currentGrade: "A", score: 92, maxScore: 100, teacher: "Dr. Sarah Johnson", trend: "up", improvement: "+5%" },
    { subject: "Physics", currentGrade: "A-", score: 88, maxScore: 100, teacher: "Prof. Michael Smith", trend: "up", improvement: "+3%" },
    { subject: "Chemistry", currentGrade: "B+", score: 82, maxScore: 100, teacher: "Dr. Emily Brown", trend: "stable", improvement: "0%" },
    { subject: "English", currentGrade: "A+", score: 96, maxScore: 100, teacher: "Ms. Lisa Wilson", trend: "up", improvement: "+8%" },
    { subject: "Computer Science", currentGrade: "A", score: 90, maxScore: 100, teacher: "Mr. David Chen", trend: "up", improvement: "+2%" },
    { subject: "Biology", currentGrade: "B", score: 78, maxScore: 100, teacher: "Dr. Maria Garcia", trend: "down", improvement: "-2%" },
  ];

  const recentAssignments = [
    { subject: "Mathematics", title: "Quadratic Equations Test", score: 95, maxScore: 100, grade: "A+", date: "2024-01-20", type: "Test" },
    { subject: "Physics", title: "Motion and Force Lab", score: 88, maxScore: 100, grade: "A", date: "2024-01-18", type: "Lab Report" },
    { subject: "English", title: "Essay on Climate Change", score: 92, maxScore: 100, grade: "A", date: "2024-01-15", type: "Assignment" },
    { subject: "Chemistry", title: "Chemical Bonding Quiz", score: 85, maxScore: 100, grade: "B+", date: "2024-01-12", type: "Quiz" },
    { subject: "Computer Science", title: "Programming Project", score: 98, maxScore: 100, grade: "A+", date: "2024-01-10", type: "Project" },
  ];

  const examResults = [
    { exam: "Mid-term Examination", date: "2024-01-15", totalMarks: 600, obtainedMarks: 512, percentage: 85.3, grade: "A", rank: 5 },
    { exam: "Unit Test 2", date: "2024-01-05", totalMarks: 300, obtainedMarks: 268, percentage: 89.3, grade: "A+", rank: 3 },
    { exam: "Unit Test 1", date: "2023-12-15", totalMarks: 300, obtainedMarks: 245, percentage: 81.7, grade: "B+", rank: 8 },
  ];

  const performanceTrends = [
    { month: "Sep", gpa: 3.5, rank: 8 },
    { month: "Oct", gpa: 3.6, rank: 7 },
    { month: "Nov", gpa: 3.7, rank: 6 },
    { month: "Dec", gpa: 3.8, rank: 5 },
    { month: "Jan", gpa: 3.8, rank: 5 },
  ];

  const teacherComments = [
    { subject: "Mathematics", teacher: "Dr. Sarah Johnson", comment: "Excellent problem-solving skills. Shows great improvement in algebra.", date: "2024-01-20" },
    { subject: "English", teacher: "Ms. Lisa Wilson", comment: "Outstanding writing skills and creative thinking. Keep up the good work!", date: "2024-01-18" },
    { subject: "Physics", teacher: "Prof. Michael Smith", comment: "Good understanding of concepts. Needs to focus more on numerical problems.", date: "2024-01-15" },
  ];

  const getGradeColor = (grade: string) => {
    switch (grade) {
      case "A+": return "text-green-600 bg-green-100";
      case "A": return "text-blue-600 bg-blue-100";
      case "A-": return "text-blue-500 bg-blue-50";
      case "B+": return "text-yellow-600 bg-yellow-100";
      case "B": return "text-orange-600 bg-orange-100";
      case "C+": return "text-red-600 bg-red-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case "up": return <TrendingUp className="h-4 w-4 text-green-500" />;
      case "down": return <TrendingDown className="h-4 w-4 text-red-500" />;
      default: return <Target className="h-4 w-4 text-gray-500" />;
    }
  };

  const getTrendColor = (trend: string) => {
    switch (trend) {
      case "up": return "text-green-600";
      case "down": return "text-red-600";
      default: return "text-gray-600";
    }
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Academic Performance</h1>
            <p className="text-gray-600">Monitor your child&apos;s grades and academic progress</p>
          </div>
          <div className="flex gap-2">
            <select
              value={selectedChild}
              onChange={(e) => setSelectedChild(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {children.map((child) => (
                <option key={child.id} value={child.id}>
                  {child.name} - {child.class}
                </option>
              ))}
            </select>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Download Report
            </Button>
          </div>
        </div>

        {/* Student Overview */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-6">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                <User className="h-8 w-8 text-blue-600" />
              </div>
              <div className="flex-1">
                <h2 className="text-xl font-bold text-gray-900">{selectedChildData.name}</h2>
                <p className="text-gray-600">{selectedChildData.class} • Roll No: {selectedChildData.rollNo}</p>
              </div>
              <div className="grid gap-4 md:grid-cols-3 text-center">
                <div>
                  <div className="text-2xl font-bold text-blue-600">{gradeOverview.currentGPA}</div>
                  <div className="text-sm text-gray-500">Current GPA</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">{gradeOverview.classRank}</div>
                  <div className="text-sm text-gray-500">Class Rank</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-purple-600">{gradeOverview.averageScore}%</div>
                  <div className="text-sm text-gray-500">Average Score</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Performance Stats */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Award className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {gradeOverview.currentGPA}
                  </div>
                  <p className="text-sm text-gray-600">Current GPA</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600">{gradeOverview.improvement}</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Target className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {gradeOverview.classRank}/{gradeOverview.totalStudents}
                  </div>
                  <p className="text-sm text-gray-600">Class Rank</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <BarChart3 className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {gradeOverview.averageScore}%
                  </div>
                  <p className="text-sm text-gray-600">Average Score</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600">+2.5%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    {gradeOverview.attendanceRate}%
                  </div>
                  <p className="text-sm text-gray-600">Attendance</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Grade Tabs */}
        <Card>
          <CardHeader>
            <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
              {[
                { key: "overview", label: "Subject Grades" },
                { key: "assignments", label: "Recent Work" },
                { key: "exams", label: "Exam Results" },
                { key: "comments", label: "Teacher Comments" },
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setSelectedTab(tab.key)}
                  className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                    selectedTab === tab.key
                      ? "bg-white text-blue-600 shadow-sm"
                      : "text-gray-600 hover:text-gray-900"
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </div>
          </CardHeader>
          <CardContent>
            {/* Subject Grades */}
            {selectedTab === "overview" && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Subject-wise Performance</h3>
                <div className="grid gap-4 md:grid-cols-2">
                  {subjectGrades.map((subject, index) => (
                    <div key={index} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <BookOpen className="h-5 w-5 text-blue-500" />
                          <div>
                            <h4 className="font-medium text-gray-900">{subject.subject}</h4>
                            <p className="text-sm text-gray-600">{subject.teacher}</p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          {getTrendIcon(subject.trend)}
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getGradeColor(subject.currentGrade)}`}>
                            {subject.currentGrade}
                          </span>
                        </div>
                      </div>

                      <div className="grid gap-3 md:grid-cols-2 text-sm mb-3">
                        <div>
                          <div className="text-gray-500">Current Score</div>
                          <div className="font-bold text-gray-900">{subject.score}/{subject.maxScore}</div>
                        </div>
                        <div>
                          <div className="text-gray-500">Improvement</div>
                          <div className={`font-bold ${getTrendColor(subject.trend)}`}>{subject.improvement}</div>
                        </div>
                      </div>

                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${
                            subject.score >= 90 ? 'bg-green-500' :
                            subject.score >= 80 ? 'bg-blue-500' :
                            subject.score >= 70 ? 'bg-yellow-500' : 'bg-red-500'
                          }`}
                          style={{ width: `${(subject.score / subject.maxScore) * 100}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Recent Assignments */}
            {selectedTab === "assignments" && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Recent Assignments & Tests</h3>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Subject</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Assignment</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Score</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Grade</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Date</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Type</th>
                      </tr>
                    </thead>
                    <tbody>
                      {recentAssignments.map((assignment, index) => (
                        <tr key={index} className="border-b hover:bg-gray-50">
                          <td className="py-3 px-4 font-medium text-gray-900">{assignment.subject}</td>
                          <td className="py-3 px-4 text-gray-600">{assignment.title}</td>
                          <td className="py-3 px-4 font-medium text-gray-900">
                            {assignment.score}/{assignment.maxScore}
                          </td>
                          <td className="py-3 px-4">
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getGradeColor(assignment.grade)}`}>
                              {assignment.grade}
                            </span>
                          </td>
                          <td className="py-3 px-4 text-gray-600">{assignment.date}</td>
                          <td className="py-3 px-4">
                            <Badge variant="outline">{assignment.type}</Badge>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* Exam Results */}
            {selectedTab === "exams" && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Examination Results</h3>
                <div className="space-y-4">
                  {examResults.map((exam, index) => (
                    <div key={index} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-3">
                        <div>
                          <h4 className="font-medium text-gray-900">{exam.exam}</h4>
                          <p className="text-sm text-gray-600">{exam.date}</p>
                        </div>
                        <div className="text-right">
                          <div className="text-2xl font-bold text-blue-600">{exam.percentage}%</div>
                          <div className="text-sm text-gray-500">Rank: {exam.rank}</div>
                        </div>
                      </div>

                      <div className="grid gap-3 md:grid-cols-4 text-sm">
                        <div className="text-center p-3 bg-blue-50 rounded-lg">
                          <div className="font-bold text-blue-600">{exam.obtainedMarks}</div>
                          <div className="text-gray-500">Marks Obtained</div>
                        </div>
                        <div className="text-center p-3 bg-gray-50 rounded-lg">
                          <div className="font-bold text-gray-600">{exam.totalMarks}</div>
                          <div className="text-gray-500">Total Marks</div>
                        </div>
                        <div className="text-center p-3 bg-green-50 rounded-lg">
                          <div className={`font-bold ${getGradeColor(exam.grade).split(' ')[0]}`}>{exam.grade}</div>
                          <div className="text-gray-500">Grade</div>
                        </div>
                        <div className="text-center p-3 bg-purple-50 rounded-lg">
                          <div className="font-bold text-purple-600">{exam.rank}</div>
                          <div className="text-gray-500">Class Rank</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Teacher Comments */}
            {selectedTab === "comments" && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Teacher Feedback</h3>
                <div className="space-y-4">
                  {teacherComments.map((comment, index) => (
                    <div key={index} className="p-4 border rounded-lg">
                      <div className="flex items-start space-x-4">
                        <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                          <User className="h-6 w-6 text-blue-600" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center justify-between mb-2">
                            <div>
                              <h4 className="font-medium text-gray-900">{comment.subject}</h4>
                              <p className="text-sm text-gray-600">{comment.teacher}</p>
                            </div>
                            <span className="text-sm text-gray-500">{comment.date}</span>
                          </div>
                          <p className="text-gray-700 bg-gray-50 p-3 rounded-lg italic">
                            &quot;{comment.comment}&quot;
                          </p>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Performance Insights */}
        <Card>
          <CardHeader>
            <CardTitle>Performance Insights</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 lg:grid-cols-2">
              {/* Strengths */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                  <Star className="h-5 w-5 text-yellow-500 mr-2" />
                  Strengths
                </h4>
                <div className="space-y-2">
                  <div className="p-3 bg-green-50 rounded-lg">
                    <div className="font-medium text-green-800">Excellent in English</div>
                    <div className="text-sm text-green-600">Consistently scoring above 90% with creative writing skills</div>
                  </div>
                  <div className="p-3 bg-blue-50 rounded-lg">
                    <div className="font-medium text-blue-800">Strong Mathematical Skills</div>
                    <div className="text-sm text-blue-600">Good problem-solving abilities and logical thinking</div>
                  </div>
                </div>
              </div>

              {/* Areas for Improvement */}
              <div>
                <h4 className="font-medium text-gray-900 mb-3 flex items-center">
                  <AlertTriangle className="h-5 w-5 text-orange-500 mr-2" />
                  Areas for Improvement
                </h4>
                <div className="space-y-2">
                  <div className="p-3 bg-orange-50 rounded-lg">
                    <div className="font-medium text-orange-800">Biology Performance</div>
                    <div className="text-sm text-orange-600">Needs more focus on theoretical concepts and diagrams</div>
                  </div>
                  <div className="p-3 bg-yellow-50 rounded-lg">
                    <div className="font-medium text-yellow-800">Chemistry Lab Work</div>
                    <div className="text-sm text-yellow-600">Improve practical skills and observation recording</div>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
