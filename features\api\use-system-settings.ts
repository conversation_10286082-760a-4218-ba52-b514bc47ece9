import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { client } from "@/lib/hono";
import { toast } from "sonner";

// Get all settings or by category
export const useGetSystemSettings = (category?: string) => {
  return useQuery({
    queryKey: ["system-settings", category],
    queryFn: async () => {
      const response = await client.api["system-settings"].$get({
        query: category ? { category } : {},
      });

      if (!response.ok) {
        const errorData = await response.json() as { error?: string };
        throw new Error(errorData.error || "Failed to fetch settings");
      }

      return await response.json();
    },
  });
};

// Update settings
export const useUpdateSystemSettings = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: { category: string; settings: Record<string, any> }) => {
      const response = await client.api["system-settings"].$put({
        json: data,
      });

      if (!response.ok) {
        const errorData = await response.json() as { error?: string };
        throw new Error(errorData.error || "Failed to update settings");
      }

      return await response.json();
    },
    onSuccess: (data, variables) => {
      queryClient.invalidateQueries({ queryKey: ["system-settings"] });
      queryClient.invalidateQueries({ queryKey: ["system-settings", variables.category] });
      toast.success("Settings updated successfully!");
    },
    onError: (error: any) => {
      toast.error(`Failed to update settings: ${error.message}`);
    },
  });
};

// Get specific setting
export const useGetSystemSetting = (category: string, key: string) => {
  return useQuery({
    queryKey: ["system-settings", category, key],
    queryFn: async () => {
      const response = await client.api["system-settings"][":category"][":key"].$get({
        param: { category, key },
      });

      if (!response.ok) {
        const errorData = await response.json() as { error?: string };
        throw new Error(errorData.error || "Failed to fetch setting");
      }

      return await response.json();
    },
    enabled: !!category && !!key,
  });
};

// Reset settings
export const useResetSystemSettings = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (category?: string) => {
      const response = await client.api["system-settings"].reset.$post({
        query: category ? { category } : {},
      });

      if (!response.ok) {
        const errorData = await response.json() as { error?: string };
        throw new Error(errorData.error || "Failed to reset settings");
      }

      return await response.json();
    },
    onSuccess: (data, category) => {
      queryClient.invalidateQueries({ queryKey: ["system-settings"] });
      if (category) {
        queryClient.invalidateQueries({ queryKey: ["system-settings", category] });
      }
      toast.success("Settings reset successfully!");
    },
    onError: (error: any) => {
      toast.error(`Failed to reset settings: ${error.message}`);
    },
  });
};
