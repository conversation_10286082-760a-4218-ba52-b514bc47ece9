CREATE TYPE "public"."institution_type" AS ENUM('school', 'college');--> statement-breakpoint
CREATE TYPE "public"."stream_type" AS ENUM('science', 'commerce', 'arts', 'engineering', 'medical', 'management', 'other');--> statement-breakpoint
CREATE TABLE "academic_sections" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"program_id" uuid NOT NULL,
	"batch_id" uuid NOT NULL,
	"stream_id" uuid,
	"name" varchar(10) NOT NULL,
	"display_name" varchar(50) NOT NULL,
	"capacity" integer DEFAULT 30 NOT NULL,
	"occupied_seats" integer DEFAULT 0 NOT NULL,
	"available_seats" integer DEFAULT 30 NOT NULL,
	"class_teacher_id" uuid,
	"room" varchar(50),
	"academic_year" varchar(20) NOT NULL,
	"semester" "semester" DEFAULT '1',
	"is_active" boolean DEFAULT true NOT NULL,
	"status" "status" DEFAULT 'active' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "academic_streams" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(200) NOT NULL,
	"code" varchar(20) NOT NULL,
	"type" "stream_type" NOT NULL,
	"institution_type" "institution_type" NOT NULL,
	"description" text,
	"eligibility_criteria" text,
	"duration" integer DEFAULT 1 NOT NULL,
	"department" varchar(100),
	"is_active" boolean DEFAULT true NOT NULL,
	"display_order" integer DEFAULT 0,
	"status" "status" DEFAULT 'active' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "academic_streams_code_unique" UNIQUE("code")
);
--> statement-breakpoint
CREATE TABLE "institution_config" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"institution_type" "institution_type" DEFAULT 'school' NOT NULL,
	"institution_name" varchar(200) NOT NULL,
	"institution_code" varchar(20) NOT NULL,
	"address" text,
	"phone" varchar(20),
	"email" varchar(255),
	"website" varchar(255),
	"established_year" integer,
	"affiliation" varchar(100),
	"current_academic_year" varchar(20) NOT NULL,
	"session_start_month" integer DEFAULT 4 NOT NULL,
	"session_end_month" integer DEFAULT 3 NOT NULL,
	"grading_system" varchar(50) DEFAULT '10-point GPA',
	"currency" varchar(10) DEFAULT 'INR',
	"timezone" varchar(50) DEFAULT 'Asia/Kolkata',
	"date_format" varchar(20) DEFAULT 'DD/MM/YYYY',
	"language" varchar(20) DEFAULT 'English',
	"is_configured" boolean DEFAULT false NOT NULL,
	"configured_by" uuid,
	"configured_at" timestamp,
	"status" "status" DEFAULT 'active' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "institution_config_institution_code_unique" UNIQUE("institution_code")
);
--> statement-breakpoint
ALTER TABLE "students" ADD COLUMN "section_id" uuid;--> statement-breakpoint
ALTER TABLE "students" ADD COLUMN "stream_id" uuid;--> statement-breakpoint
ALTER TABLE "academic_sections" ADD CONSTRAINT "academic_sections_program_id_academic_programs_id_fk" FOREIGN KEY ("program_id") REFERENCES "public"."academic_programs"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "academic_sections" ADD CONSTRAINT "academic_sections_batch_id_academic_batches_id_fk" FOREIGN KEY ("batch_id") REFERENCES "public"."academic_batches"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "academic_sections" ADD CONSTRAINT "academic_sections_stream_id_academic_streams_id_fk" FOREIGN KEY ("stream_id") REFERENCES "public"."academic_streams"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "academic_sections" ADD CONSTRAINT "academic_sections_class_teacher_id_teachers_id_fk" FOREIGN KEY ("class_teacher_id") REFERENCES "public"."teachers"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "institution_config" ADD CONSTRAINT "institution_config_configured_by_users_id_fk" FOREIGN KEY ("configured_by") REFERENCES "public"."users"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "students" ADD CONSTRAINT "students_section_id_academic_sections_id_fk" FOREIGN KEY ("section_id") REFERENCES "public"."academic_sections"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "students" ADD CONSTRAINT "students_stream_id_academic_streams_id_fk" FOREIGN KEY ("stream_id") REFERENCES "public"."academic_streams"("id") ON DELETE no action ON UPDATE no action;