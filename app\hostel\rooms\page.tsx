"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Building,
  Search,
  Filter,
  Download,
  Plus,
  Eye,
  Edit,
  Trash2,
  Users,
  Bed,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Home,
  MapPin,
  Settings,
  IndianRupee,
  Calendar,
  Clock,
} from "lucide-react";

export default function HostelRooms() {
  const [user, setUser] = useState<any>(null);
  const [selectedTab, setSelectedTab] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedRoom, setSelectedRoom] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "hostel_manager") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  const roomStats = {
    totalRooms: 120,
    occupiedRooms: 98,
    availableRooms: 22,
    maintenanceRooms: 5,
    singleRooms: 40,
    doubleRooms: 60,
    tripleRooms: 20,
  };

  const mockRooms = [
    {
      id: "R001",
      roomNumber: "A-101",
      hostelBlock: "Block A",
      floor: 1,
      type: "Double",
      capacity: 2,
      occupied: 2,
      status: "Occupied",
      monthlyFee: 15000,
      students: ["Aarav Sharma", "Rahul Kumar"],
      amenities: ["AC", "WiFi", "Study Table", "Wardrobe"],
      lastMaintenance: "2024-01-10",
      condition: "Good",
    },
    {
      id: "R002",
      roomNumber: "A-102",
      hostelBlock: "Block A",
      floor: 1,
      type: "Single",
      capacity: 1,
      occupied: 0,
      status: "Available",
      monthlyFee: 20000,
      students: [],
      amenities: ["AC", "WiFi", "Study Table", "Wardrobe", "Balcony"],
      lastMaintenance: "2024-01-15",
      condition: "Excellent",
    },
    {
      id: "R003",
      roomNumber: "B-201",
      hostelBlock: "Block B",
      floor: 2,
      type: "Triple",
      capacity: 3,
      occupied: 3,
      status: "Occupied",
      monthlyFee: 12000,
      students: ["Priya Patel", "Sneha Reddy", "Anita Singh"],
      amenities: ["Fan", "WiFi", "Study Table", "Wardrobe"],
      lastMaintenance: "2024-01-05",
      condition: "Good",
    },
    {
      id: "R004",
      roomNumber: "B-202",
      hostelBlock: "Block B",
      floor: 2,
      type: "Double",
      capacity: 2,
      occupied: 0,
      status: "Maintenance",
      monthlyFee: 15000,
      students: [],
      amenities: ["AC", "WiFi", "Study Table", "Wardrobe"],
      lastMaintenance: "2024-01-20",
      condition: "Needs Repair",
    },
    {
      id: "R005",
      roomNumber: "C-301",
      hostelBlock: "Block C",
      floor: 3,
      type: "Single",
      capacity: 1,
      occupied: 1,
      status: "Occupied",
      monthlyFee: 20000,
      students: ["Vikram Singh"],
      amenities: ["AC", "WiFi", "Study Table", "Wardrobe", "Balcony"],
      lastMaintenance: "2024-01-12",
      condition: "Excellent",
    },
  ];

  const filteredRooms = mockRooms.filter((room) => {
    const matchesSearch = room.roomNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         room.hostelBlock.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         room.students.some(student => student.toLowerCase().includes(searchTerm.toLowerCase()));

    if (selectedTab === "all") return matchesSearch;
    if (selectedTab === "occupied") return matchesSearch && room.status === "Occupied";
    if (selectedTab === "available") return matchesSearch && room.status === "Available";
    if (selectedTab === "maintenance") return matchesSearch && room.status === "Maintenance";
    if (selectedTab === "single") return matchesSearch && room.type === "Single";
    if (selectedTab === "double") return matchesSearch && room.type === "Double";
    if (selectedTab === "triple") return matchesSearch && room.type === "Triple";

    return matchesSearch;
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Occupied":
        return <Badge className="bg-blue-100 text-blue-800">Occupied</Badge>;
      case "Available":
        return <Badge className="bg-green-100 text-green-800">Available</Badge>;
      case "Maintenance":
        return <Badge className="bg-yellow-100 text-yellow-800">Maintenance</Badge>;
      case "Reserved":
        return <Badge className="bg-purple-100 text-purple-800">Reserved</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }
  };

  const getConditionBadge = (condition: string) => {
    switch (condition) {
      case "Excellent":
        return <Badge variant="outline" className="text-green-600 border-green-200">Excellent</Badge>;
      case "Good":
        return <Badge variant="outline" className="text-blue-600 border-blue-200">Good</Badge>;
      case "Fair":
        return <Badge variant="outline" className="text-yellow-600 border-yellow-200">Fair</Badge>;
      case "Needs Repair":
        return <Badge variant="outline" className="text-red-600 border-red-200">Needs Repair</Badge>;
      default:
        return <Badge variant="outline">{condition}</Badge>;
    }
  };

  const viewRoomDetails = (room: any) => {
    setSelectedRoom(room);
  };

  const editRoom = (roomId: string) => {
    alert(`Editing room ${roomId}`);
  };

  const allocateRoom = (roomId: string) => {
    alert(`Allocating room ${roomId}`);
  };

  const scheduleMaintenanceRoom = (roomId: string) => {
    alert(`Scheduling maintenance for room ${roomId}`);
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Hostel Room Management</h1>
            <p className="text-gray-600">Manage hostel rooms, allocations, and occupancy</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Room Report
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Room
            </Button>
          </div>
        </div>

        {/* Room Statistics */}
        <div className="grid gap-4 md:grid-cols-4 lg:grid-cols-7">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Building className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {roomStats.totalRooms}
                  </div>
                  <p className="text-sm text-gray-600">Total Rooms</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {roomStats.occupiedRooms}
                  </div>
                  <p className="text-sm text-gray-600">Occupied</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {roomStats.availableRooms}
                  </div>
                  <p className="text-sm text-gray-600">Available</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <AlertTriangle className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-yellow-600">
                    {roomStats.maintenanceRooms}
                  </div>
                  <p className="text-sm text-gray-600">Maintenance</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Bed className="h-8 w-8 text-indigo-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-indigo-600">
                    {roomStats.singleRooms}
                  </div>
                  <p className="text-sm text-gray-600">Single</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Bed className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    {roomStats.doubleRooms}
                  </div>
                  <p className="text-sm text-gray-600">Double</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Bed className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-red-600">
                    {roomStats.tripleRooms}
                  </div>
                  <p className="text-sm text-gray-600">Triple</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filter */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search by room number, block, or student name..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </div>

            {/* Tabs */}
            <div className="flex flex-wrap gap-2 mt-4">
              {[
                { key: "all", label: "All Rooms", count: roomStats.totalRooms },
                { key: "occupied", label: "Occupied", count: roomStats.occupiedRooms },
                { key: "available", label: "Available", count: roomStats.availableRooms },
                { key: "maintenance", label: "Maintenance", count: roomStats.maintenanceRooms },
                { key: "single", label: "Single", count: roomStats.singleRooms },
                { key: "double", label: "Double", count: roomStats.doubleRooms },
                { key: "triple", label: "Triple", count: roomStats.tripleRooms },
              ].map((tab) => (
                <Button
                  key={tab.key}
                  variant={selectedTab === tab.key ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedTab(tab.key)}
                  className="text-xs"
                >
                  {tab.label} ({tab.count})
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Rooms Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Building className="h-5 w-5 mr-2" />
              Room Directory ({filteredRooms.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-4 font-medium text-gray-900">Room Details</th>
                    <th className="text-left p-4 font-medium text-gray-900">Occupancy</th>
                    <th className="text-left p-4 font-medium text-gray-900">Status</th>
                    <th className="text-left p-4 font-medium text-gray-900">Fee & Amenities</th>
                    <th className="text-left p-4 font-medium text-gray-900">Condition</th>
                    <th className="text-left p-4 font-medium text-gray-900">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredRooms.map((room) => (
                    <tr key={room.id} className="border-b hover:bg-gray-50">
                      <td className="p-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <Building className="h-6 w-6 text-blue-600" />
                          </div>
                          <div>
                            <div className="font-medium text-gray-900">{room.roomNumber}</div>
                            <div className="text-sm text-gray-500">{room.hostelBlock}</div>
                            <div className="text-sm text-gray-500">Floor {room.floor} • {room.type}</div>
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-1">
                          <div className="font-medium text-gray-900">
                            {room.occupied}/{room.capacity} occupied
                          </div>
                          <div className="text-sm text-gray-500">
                            {room.students.length > 0 ? (
                              room.students.map((student, index) => (
                                <div key={index}>{student}</div>
                              ))
                            ) : (
                              <span className="text-gray-400">No students</span>
                            )}
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-2">
                          {getStatusBadge(room.status)}
                          <div className="text-xs text-gray-500">
                            ID: {room.id}
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-1">
                          <div className="font-medium text-gray-900">
                            ₹{room.monthlyFee.toLocaleString()}/month
                          </div>
                          <div className="text-sm text-gray-500">
                            {room.amenities.slice(0, 2).join(", ")}
                            {room.amenities.length > 2 && ` +${room.amenities.length - 2} more`}
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-2">
                          {getConditionBadge(room.condition)}
                          <div className="text-xs text-gray-500">
                            Last: {new Date(room.lastMaintenance).toLocaleDateString()}
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => viewRoomDetails(room)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => editRoom(room.id)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          {room.status === "Available" && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => allocateRoom(room.id)}
                            >
                              <Users className="h-4 w-4" />
                            </Button>
                          )}
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => scheduleMaintenanceRoom(room.id)}
                          >
                            <Settings className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}