"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { UserForm } from "@/components/forms/user-form";
import { useCreateUser } from "@/features/api/use-users";
import { CreateUser } from "@/lib/schemas";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { 
  ArrowLeft, 
  UserPlus, 
  Shield, 
  AlertCircle,
  CheckCircle,
  Copy,
  Eye,
  EyeOff
} from "lucide-react";
import { toast } from "sonner";

export default function NewUserPage() {
  const [user, setUser] = useState<any>(null);
  const [createdUser, setCreatedUser] = useState<any>(null);
  const [showPassword, setShowPassword] = useState(false);
  const router = useRouter();
  const createUserMutation = useCreateUser();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "super_admin") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  const handleCreateUser = async (data: CreateUser) => {
    try {
      const result = await createUserMutation.mutateAsync(data);
      setCreatedUser(result.data);
      
      // Show success message with password if generated
      if (result.data.generatedPassword) {
        toast.success("User created successfully! Password has been generated.", {
          duration: 5000,
        });
      } else {
        toast.success("User created successfully!");
      }
    } catch (error) {
      console.error("Error creating user:", error);
    }
  };

  const handleCopyPassword = () => {
    if (createdUser?.generatedPassword) {
      navigator.clipboard.writeText(createdUser.generatedPassword);
      toast.success("Password copied to clipboard!");
    }
  };

  const handleCreateAnother = () => {
    setCreatedUser(null);
  };

  const handleGoToUsers = () => {
    router.push("/admin/users");
  };

  if (!user) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
            <p className="mt-2 text-gray-600">Loading...</p>
          </div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.back()}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
                <UserPlus className="h-6 w-6" />
                Create New User
              </h1>
              <p className="text-gray-600">Add a new user to the school management system</p>
            </div>
          </div>
          <Badge className="bg-red-100 text-red-800 border-red-200">
            <Shield className="h-3 w-3 mr-1" />
            Super Admin Only
          </Badge>
        </div>

        {/* Success State */}
        {createdUser && (
          <Card className="border-green-200 bg-green-50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-green-800">
                <CheckCircle className="h-5 w-5" />
                User Created Successfully!
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <p className="text-sm font-medium text-gray-700">Name</p>
                  <p className="text-gray-900">{createdUser.firstName} {createdUser.lastName}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-700">Email</p>
                  <p className="text-gray-900">{createdUser.email}</p>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-700">Role</p>
                  <Badge className="bg-blue-100 text-blue-800">
                    {createdUser.role.replace("_", " ").toUpperCase()}
                  </Badge>
                </div>
                <div>
                  <p className="text-sm font-medium text-gray-700">Status</p>
                  <Badge className={createdUser.status === "active" ? "bg-green-100 text-green-800" : "bg-red-100 text-red-800"}>
                    {createdUser.status.toUpperCase()}
                  </Badge>
                </div>
              </div>

              {createdUser.generatedPassword && (
                <div className="mt-4 p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                  <div className="flex items-center gap-2 mb-2">
                    <AlertCircle className="h-4 w-4 text-yellow-600" />
                    <p className="text-sm font-medium text-yellow-800">Generated Password</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className="flex-1 font-mono text-sm bg-white p-2 rounded border">
                      {showPassword ? createdUser.generatedPassword : "••••••••••••"}
                    </div>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => setShowPassword(!showPassword)}
                    >
                      {showPassword ? <EyeOff className="h-4 w-4" /> : <Eye className="h-4 w-4" />}
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={handleCopyPassword}
                    >
                      <Copy className="h-4 w-4" />
                    </Button>
                  </div>
                  <p className="text-xs text-yellow-700 mt-2">
                    Please share this password securely with the user. They should change it on first login.
                  </p>
                </div>
              )}

              <div className="flex gap-2 pt-4">
                <Button onClick={handleCreateAnother} variant="outline">
                  Create Another User
                </Button>
                <Button onClick={handleGoToUsers}>
                  Go to User Management
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* User Creation Form */}
        {!createdUser && (
          <div className="max-w-4xl">
            <UserForm
              onSubmit={handleCreateUser}
              isLoading={createUserMutation.isPending}
              mode="create"
            />
          </div>
        )}

        {/* Important Notes */}
        {!createdUser && (
          <Card className="border-blue-200 bg-blue-50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-blue-800">
                <AlertCircle className="h-5 w-5" />
                Important Notes
              </CardTitle>
            </CardHeader>
            <CardContent className="text-sm text-blue-700 space-y-2">
              <ul className="list-disc list-inside space-y-1">
                <li>All users will receive default permissions based on their role</li>
                <li>Generated passwords should be shared securely with users</li>
                <li>Users should be instructed to change their password on first login</li>
                <li>Student and parent accounts may require additional setup after creation</li>
                <li>Teacher accounts will need class and subject assignments</li>
                <li>Admin accounts inherit permissions from their role level</li>
              </ul>
            </CardContent>
          </Card>
        )}
      </div>
    </AppLayout>
  );
}
