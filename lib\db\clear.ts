import { config } from "dotenv";
import { db } from "./index";
import {
  users,
  books,
  academicPrograms,
  academicBatches,
  feeStructures,
  transportRoutes,
  vehicles,
  hostels,
  hostelRooms
} from "./schema";

config({ path: ".env.local" });

async function clearData() {
  console.log("🧹 Clearing database...");

  try {
    // Delete in order to avoid foreign key constraint issues
    await db.delete(users);
    await db.delete(books);
    await db.delete(academicBatches);
    await db.delete(academicPrograms);
    await db.delete(feeStructures);
    await db.delete(transportRoutes);
    await db.delete(vehicles);
    await db.delete(hostelRooms);
    await db.delete(hostels);
    console.log("✅ Database cleared successfully");
  } catch (error) {
    console.error("❌ Error clearing database:", error);
    process.exit(1);
  }
}

clearData();
