{"timestamp": "2025-06-08T09:41:36.386Z", "summary": {"totalButtons": 75, "totalFunctionalButtons": 48, "overallFunctionalityRate": 64, "pagesTestedCount": 17, "pagesFoundCount": 17}, "pageResults": [{"file": "app/admin/dashboard/page.tsx", "buttons": [{"type": "functional", "element": "<Button variant=\"outline\" onClick={() => router.push(\"/admin/reports\")}>\n              <FileText cla...", "functionality": "onClick handler, router navigation"}, {"type": "functional", "element": "<Button onClick={() => router.push(\"/admin/settings\")}>\n              <Settings className=\"h-4 w-4 m...", "functionality": "onClick handler, router navigation"}, {"type": "functional", "element": "<Button variant=\"outline\" className=\"w-full\" onClick={() => router.push(\"/admin/audit-logs\")}>\n     ...", "functionality": "onClick handler, router navigation"}], "issues": [], "summary": {"totalButtons": 3, "functionalButtons": 3, "nonFunctionalButtons": 0, "functionalityRate": 100}}, {"file": "app/admin/students/page.tsx", "buttons": [{"type": "functional", "element": "<Button variant=\"outline\" onClick={clearFilters}>\n              <RefreshCw className=\"h-4 w-4 mr-2\" ...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button variant=\"outline\" onClick={exportStudents} disabled={filteredStudents.length === 0}>\n       ...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button onClick={() => router.push(\"/admin/students/new\")}>\n              <Plus className=\"h-4 w-4 m...", "functionality": "onClick handler, router navigation"}, {"type": "functional", "element": "<Button\n                      variant=\"ghost\"\n                      size=\"sm\"\n                      ...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button\n                                variant=\"outline\"\n                                size=\"sm\"\n...", "functionality": "onClick handler, router navigation"}, {"type": "functional", "element": "<Button\n                                variant=\"outline\"\n                                size=\"sm\"\n...", "functionality": "onClick handler, router navigation"}, {"type": "functional", "element": "<Button\n                                variant=\"outline\"\n                                size=\"sm\"\n...", "functionality": "onClick handler"}], "issues": [], "summary": {"totalButtons": 7, "functionalButtons": 7, "nonFunctionalButtons": 0, "functionalityRate": 100}}, {"file": "app/admin/teachers/page.tsx", "buttons": [{"type": "functional", "element": "<Button\n              variant=\"outline\"\n              onClick={() => {\n                // TODO: Impl...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button\n              variant=\"outline\"\n              onClick={() => {\n                // Export tea...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button onClick={() => router.push(\"/admin/teachers/new\")}>\n              <Plus className=\"h-4 w-4 m...", "functionality": "onClick handler, router navigation"}, {"type": "functional", "element": "<Button\n                            variant=\"outline\"\n                            size=\"sm\"\n        ...", "functionality": "onClick handler, router navigation"}, {"type": "functional", "element": "<Button\n                            variant=\"outline\"\n                            size=\"sm\"\n        ...", "functionality": "onClick handler, router navigation"}, {"type": "functional", "element": "<Button\n                            variant=\"outline\"\n                            size=\"sm\"\n        ...", "functionality": "onClick handler"}], "issues": [{"type": "missing_functionality", "element": "<Button variant=\"outline\" size=\"sm\">\n                  <Filter className=\"h-4 w-4\" />", "line": 280}], "summary": {"totalButtons": 7, "functionalButtons": 6, "nonFunctionalButtons": 1, "functionalityRate": 86}}, {"file": "app/admin/classes/page.tsx", "buttons": [{"type": "functional", "element": "<Button onClick={resetForm}>\n                <Plus className=\"h-4 w-4 mr-2\" />", "functionality": "onClick handler, dialog trigger"}, {"type": "functional", "element": "<Button type=\"button\" variant=\"outline\" onClick={() => setIsDialogOpen(false)}>\n                    ...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button type=\"submit\">\n                    {editingClass ? \"Update Class\" : \"Create Class\"}\n        ...", "functionality": "form submission"}, {"type": "functional", "element": "<Button onClick={() => setIsDialogOpen(true)}>\n                  <Plus className=\"h-4 w-4 mr-2\" />", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button\n                        variant=\"ghost\"\n                        size=\"sm\"\n                  ...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button\n                        variant=\"ghost\"\n                        size=\"sm\"\n                  ...", "functionality": "onClick handler"}], "issues": [], "summary": {"totalButtons": 6, "functionalButtons": 6, "nonFunctionalButtons": 0, "functionalityRate": 100}}, {"file": "app/admin/finance/page.tsx", "buttons": [{"type": "functional", "element": "<Button\n              variant=\"outline\"\n              onClick={() => {\n                // Export fin...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button\n              variant=\"outline\"\n              onClick={() => router.push(\"/admin/settings\")}...", "functionality": "onClick handler, router navigation"}, {"type": "functional", "element": "<Button\n                  key={index}\n                  variant=\"outline\"\n                  classNam...", "functionality": "onClick handler, router navigation"}], "issues": [], "summary": {"totalButtons": 3, "functionalButtons": 3, "nonFunctionalButtons": 0, "functionalityRate": 100}}, {"file": "app/admin/reports/page.tsx", "buttons": [{"type": "functional", "element": "<Button\n              variant=\"outline\"\n              onClick={() => {\n                toast.success...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button\n              onClick={() => {\n                toast.success(\"Report generation started\");\n ...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button\n                  key={index}\n                  variant=\"outline\"\n                  classNam...", "functionality": "onClick handler"}], "issues": [{"type": "missing_functionality", "element": "<Button variant=\"outline\" size=\"sm\">\n                <Settings className=\"h-4 w-4 mr-2\" />", "line": 288}, {"type": "missing_functionality", "element": "<Button size=\"sm\" variant=\"outline\">\n                            <Eye className=\"h-4 w-4\" />", "line": 360}, {"type": "missing_functionality", "element": "<Button size=\"sm\" variant=\"outline\">\n                            <Download className=\"h-4 w-4\" />", "line": 363}, {"type": "missing_functionality", "element": "<Button variant=\"outline\">\n                  <Filter className=\"h-4 w-4 mr-2\" />", "line": 403}, {"type": "missing_functionality", "element": "<Button variant=\"outline\" size=\"sm\">\n                            <Eye className=\"h-3 w-3\" />", "line": 449}, {"type": "missing_functionality", "element": "<Button variant=\"outline\" size=\"sm\">\n                            <Download className=\"h-3 w-3\" />", "line": 452}, {"type": "missing_functionality", "element": "<Button variant=\"outline\" size=\"sm\">\n                            <Share className=\"h-3 w-3\" />", "line": 455}, {"type": "missing_functionality", "element": "<Button variant=\"outline\" size=\"sm\">\n                      Edit\n                    </Button>", "line": 490}], "summary": {"totalButtons": 11, "functionalButtons": 3, "nonFunctionalButtons": 8, "functionalityRate": 27}}, {"file": "app/admin/users/page.tsx", "buttons": [{"type": "functional", "element": "<Button\n                  variant=\"outline\"\n                  onClick={handleBulkPasswordReset}\n    ...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button\n                  variant=\"outline\"\n                  onClick={() => setSelectedUsers([])}\n ...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button variant=\"outline\" onClick={handleRefresh} disabled={isLoading}>\n              <RefreshCw cla...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button variant=\"outline\" onClick={handleExport}>\n              <Download className=\"h-4 w-4 mr-2\" /...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button onClick={() => router.push(\"/admin/users/new\")}>\n              <UserPlus className=\"h-4 w-4 ...", "functionality": "onClick handler, router navigation"}], "issues": [{"type": "missing_functionality", "element": "<Button variant=\"outline\" size=\"sm\">\n                                  <MoreHorizontal className=\"h-...", "line": 537}], "summary": {"totalButtons": 6, "functionalButtons": 5, "nonFunctionalButtons": 1, "functionalityRate": 83}}, {"file": "app/admin/settings/page.tsx", "buttons": [{"type": "functional", "element": "<Button\n              variant=\"outline\"\n              onClick={handleReset}\n              disabled={...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button\n              onClick={handleSave}\n              disabled={updateSettingsMutation.isPending ...", "functionality": "onClick handler"}], "issues": [], "summary": {"totalButtons": 2, "functionalButtons": 2, "nonFunctionalButtons": 0, "functionalityRate": 100}}, {"file": "components/navigation/sidebar.tsx", "buttons": [{"type": "functional", "element": "<Button\n          variant=\"outline\"\n          size=\"icon\"\n          onClick={() => setIsMobileMenuOp...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button\n              variant=\"ghost\"\n              size=\"sm\"\n              className=\"w-full justif...", "functionality": "onClick handler"}], "issues": [], "summary": {"totalButtons": 2, "functionalButtons": 2, "nonFunctionalButtons": 0, "functionalityRate": 100}}, {"file": "components/navigation/user-menu.tsx", "buttons": [{"type": "functional", "element": "<Button\n          variant=\"ghost\"\n          className={`flex items-center space-x-3 px-3 py-2 h-auto...", "functionality": "onClick handler"}], "issues": [], "summary": {"totalButtons": 1, "functionalButtons": 1, "nonFunctionalButtons": 0, "functionalityRate": 100}}, {"file": "app/teacher/dashboard/page.tsx", "buttons": [{"type": "functional", "element": "<Button\n                  variant=\"outline\"\n                  size=\"sm\"\n                  onClick={(...", "functionality": "onClick handler, router navigation"}, {"type": "functional", "element": "<Button\n                        variant=\"outline\"\n                        size=\"sm\"\n                ...", "functionality": "onClick handler, router navigation"}, {"type": "functional", "element": "<Button\n                        variant=\"outline\"\n                        size=\"sm\"\n                ...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button\n                className=\"w-full justify-start\"\n                variant=\"outline\"\n         ...", "functionality": "onClick handler, router navigation"}, {"type": "functional", "element": "<Button\n                className=\"w-full justify-start\"\n                variant=\"outline\"\n         ...", "functionality": "onClick handler, router navigation"}, {"type": "functional", "element": "<Button\n                className=\"w-full justify-start\"\n                variant=\"outline\"\n         ...", "functionality": "onClick handler, router navigation"}, {"type": "functional", "element": "<Button\n                className=\"w-full justify-start\"\n                variant=\"outline\"\n         ...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button\n                className=\"w-full justify-start\"\n                variant=\"outline\"\n         ...", "functionality": "onClick handler"}], "issues": [], "summary": {"totalButtons": 8, "functionalButtons": 8, "nonFunctionalButtons": 0, "functionalityRate": 100}}, {"file": "app/student/dashboard/page.tsx", "buttons": [], "issues": [{"type": "missing_functionality", "element": "<Button className=\"w-full justify-start\" variant=\"outline\">\n                <BookOpen className=\"h-4...", "line": 198}, {"type": "missing_functionality", "element": "<Button className=\"w-full justify-start\" variant=\"outline\">\n                <Award className=\"h-4 w-...", "line": 202}, {"type": "missing_functionality", "element": "<Button className=\"w-full justify-start\" variant=\"outline\">\n                <Calendar className=\"h-4...", "line": 206}, {"type": "missing_functionality", "element": "<Button className=\"w-full justify-start\" variant=\"outline\">\n                <FileText className=\"h-4...", "line": 210}], "summary": {"totalButtons": 4, "functionalButtons": 0, "nonFunctionalButtons": 4, "functionalityRate": 0}}, {"file": "app/parent/dashboard/page.tsx", "buttons": [], "issues": [{"type": "missing_functionality", "element": "<Button variant=\"outline\" size=\"sm\">\n                      Details\n                    </Button>", "line": 382}], "summary": {"totalButtons": 1, "functionalButtons": 0, "nonFunctionalButtons": 1, "functionalityRate": 0}}, {"file": "app/finance/dashboard/page.tsx", "buttons": [{"type": "functional", "element": "<Button\n                  key={index}\n                  variant=\"outline\"\n                  classNam...", "functionality": "onClick handler, router navigation"}], "issues": [], "summary": {"totalButtons": 1, "functionalButtons": 1, "nonFunctionalButtons": 0, "functionalityRate": 100}}, {"file": "app/library/dashboard/page.tsx", "buttons": [{"type": "functional", "element": "<Button\n                  key={index}\n                  variant=\"outline\"\n                  classNam...", "functionality": "onClick handler, router navigation"}], "issues": [], "summary": {"totalButtons": 1, "functionalButtons": 1, "nonFunctionalButtons": 0, "functionalityRate": 100}}, {"file": "app/transport/dashboard/page.tsx", "buttons": [], "issues": [{"type": "missing_functionality", "element": "<Button variant=\"outline\" size=\"sm\">\n                  View All\n                </Button>", "line": 239}, {"type": "missing_functionality", "element": "<Button className=\"w-full justify-start\" variant=\"outline\">\n                <Plus className=\"h-4 w-4...", "line": 282}, {"type": "missing_functionality", "element": "<Button className=\"w-full justify-start\" variant=\"outline\">\n                <Route className=\"h-4 w-...", "line": 286}, {"type": "missing_functionality", "element": "<Button className=\"w-full justify-start\" variant=\"outline\">\n                <Wrench className=\"h-4 w...", "line": 290}, {"type": "missing_functionality", "element": "<Button className=\"w-full justify-start\" variant=\"outline\">\n                <Fuel className=\"h-4 w-4...", "line": 294}, {"type": "missing_functionality", "element": "<Button className=\"w-full justify-start\" variant=\"outline\">\n                <MapPin className=\"h-4 w...", "line": 298}], "summary": {"totalButtons": 6, "functionalButtons": 0, "nonFunctionalButtons": 6, "functionalityRate": 0}}, {"file": "app/hostel/dashboard/page.tsx", "buttons": [], "issues": [{"type": "missing_functionality", "element": "<Button variant=\"outline\" size=\"sm\">\n                  View All\n                </Button>", "line": 266}, {"type": "missing_functionality", "element": "<Button className=\"w-full justify-start\" variant=\"outline\">\n                <Plus className=\"h-4 w-4...", "line": 314}, {"type": "missing_functionality", "element": "<Button className=\"w-full justify-start\" variant=\"outline\">\n                <User className=\"h-4 w-4...", "line": 318}, {"type": "missing_functionality", "element": "<Button className=\"w-full justify-start\" variant=\"outline\">\n                <AlertTriangle className...", "line": 322}, {"type": "missing_functionality", "element": "<Button className=\"w-full justify-start\" variant=\"outline\">\n                <IndianRupee className=\"...", "line": 326}, {"type": "missing_functionality", "element": "<Button className=\"w-full justify-start\" variant=\"outline\">\n                <Calendar className=\"h-4...", "line": 330}], "summary": {"totalButtons": 6, "functionalButtons": 0, "nonFunctionalButtons": 6, "functionalityRate": 0}}], "recommendations": ["Add onClick handlers to buttons without functionality", "Implement proper navigation for action buttons", "Add user feedback with toast notifications", "Consider loading states for async operations"]}