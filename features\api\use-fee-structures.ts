import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { client } from "@/lib/hono";
import { toast } from "sonner";

// Types
export interface CreateFeeStructureData {
  grade: string;
  academicYear: string;
  tuitionFee: string;
  admissionFee?: string;
  examFee?: string;
  libraryFee?: string;
  transportFee?: string;
  hostelFee?: string;
  miscellaneousFee?: string;
  totalFee: string;
  status?: "active" | "inactive";
  dueDate?: string;
}

// Get all fee structures with filtering and pagination
export const useGetFeeStructures = (params?: {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  academicYear?: string;
  grade?: string;
}) => {
  return useQuery({
    queryKey: ["fee-structures", params],
    queryFn: async () => {
      const searchParams = new URLSearchParams();

      if (params?.page) searchParams.append("page", params.page.toString());
      if (params?.limit) searchParams.append("limit", params.limit.toString());
      if (params?.search) searchParams.append("search", params.search);
      if (params?.status) searchParams.append("status", params.status);
      if (params?.academicYear) searchParams.append("academicYear", params.academicYear);
      if (params?.grade) searchParams.append("grade", params.grade);

      const response = await client.api["fee-structures"].$get({
        query: Object.fromEntries(searchParams),
      });

      if (!response.ok) {
        throw new Error("Failed to fetch fee structures");
      }

      return await response.json();
    },
  });
};

// Get fee structure by ID
export const useGetFeeStructure = (id: string) => {
  return useQuery({
    queryKey: ["fee-structures", id],
    queryFn: async () => {
      const response = await client.api["fee-structures"][":id"].$get({
        param: { id },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch fee structure");
      }

      return await response.json();
    },
    enabled: !!id,
  });
};

// Create fee structure mutation
export const useCreateFeeStructure = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (json: CreateFeeStructureData) => {
      const response = await client.api["fee-structures"].$post({ json });

      if (!response.ok) {
        const errorData = await response.json() as { error?: string };
        throw new Error(errorData.error || "Failed to create fee structure");
      }

      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["fee-structures"] });
      toast.success("Fee structure created successfully!");
    },
    onError: (error: any) => {
      toast.error(`Failed to create fee structure: ${error.message}`);
    },
  });
};

// Update fee structure mutation
export const useUpdateFeeStructure = (id: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (json: Partial<CreateFeeStructureData>) => {
      const response = await client.api["fee-structures"][":id"].$put({
        param: { id },
        json,
      });

      if (!response.ok) {
        const errorData = await response.json() as { error?: string };
        throw new Error(errorData.error || "Failed to update fee structure");
      }

      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["fee-structures"] });
      queryClient.invalidateQueries({ queryKey: ["fee-structures", id] });
      toast.success("Fee structure updated successfully!");
    },
    onError: (error: any) => {
      toast.error(`Failed to update fee structure: ${error.message}`);
    },
  });
};

// Delete fee structure mutation
export const useDeleteFeeStructure = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await client.api["fee-structures"][":id"].$delete({
        param: { id },
      });

      if (!response.ok) {
        const errorData = await response.json() as { error?: string };
        throw new Error(errorData.error || "Failed to delete fee structure");
      }

      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["fee-structures"] });
      toast.success("Fee structure deleted successfully!");
    },
    onError: (error: any) => {
      toast.error(`Failed to delete fee structure: ${error.message}`);
    },
  });
};

// Get fee structure by grade and academic year
export const useGetFeeStructureByGrade = (grade: string, academicYear: string) => {
  return useQuery({
    queryKey: ["fee-structures", "grade", grade, academicYear],
    queryFn: async () => {
      const response = await client.api["fee-structures"].$get({
        query: { grade, academicYear, limit: "1" },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch fee structure");
      }

      const data = await response.json();
      return data.data?.[0] || null;
    },
    enabled: !!grade && !!academicYear,
  });
};

// Get fee structure statistics
export const useGetFeeStructureStats = () => {
  return useQuery({
    queryKey: ["fee-structures", "stats"],
    queryFn: async () => {
      const response = await client.api["fee-structures"].$get({
        query: { limit: "1" }, // Just get count info
      });

      if (!response.ok) {
        throw new Error("Failed to fetch fee structure stats");
      }

      const data = await response.json();
      return { total: data.data?.length || 0, page: 1, limit: 10, totalPages: 1 };
    },
  });
};

// Get fee structures by academic year
export const useGetFeeStructuresByYear = (academicYear: string) => {
  return useQuery({
    queryKey: ["fee-structures", "year", academicYear],
    queryFn: async () => {
      const response = await client.api["fee-structures"].$get({
        query: { academicYear, limit: "1000" }, // Get all fee structures for this year
      });

      if (!response.ok) {
        throw new Error("Failed to fetch fee structures by year");
      }

      return await response.json();
    },
    enabled: !!academicYear,
  });
};

// Get active fee structures
export const useGetActiveFeeStructures = () => {
  return useQuery({
    queryKey: ["fee-structures", "active"],
    queryFn: async () => {
      const response = await client.api["fee-structures"].$get({
        query: { status: "active", limit: "1000" },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch active fee structures");
      }

      return await response.json();
    },
  });
};


