"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Bus,
  Search,
  Filter,
  Download,
  Plus,
  Eye,
  Edit,
  Trash2,
  Wrench,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Calendar,
  MapPin,
  Users,
  Fuel,
  Settings,
  Clock,
} from "lucide-react";

export default function TransportVehicles() {
  const [user, setUser] = useState<any>(null);
  const [selectedTab, setSelectedTab] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedVehicle, setSelectedVehicle] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "transport_manager") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  const vehicleStats = {
    totalVehicles: 12,
    activeVehicles: 10,
    maintenanceVehicles: 2,
    availableVehicles: 8,
    busesCount: 8,
    vansCount: 4,
  };

  const mockVehicles = [
    {
      id: "VH001",
      vehicleNumber: "DL-1CA-1234",
      type: "Bus",
      model: "Tata LP 909",
      capacity: 45,
      driver: "Rajesh Kumar",
      route: "Route A - Central Delhi",
      status: "Active",
      lastMaintenance: "2024-01-10",
      nextMaintenance: "2024-02-10",
      fuelEfficiency: "8.5 km/l",
      totalKm: 45678,
      monthlyKm: 2340,
      condition: "Good",
    },
    {
      id: "VH002",
      vehicleNumber: "DL-1CB-5678",
      type: "Van",
      model: "Mahindra Bolero",
      capacity: 12,
      driver: "Suresh Singh",
      route: "Route B - South Delhi",
      status: "Active",
      lastMaintenance: "2024-01-15",
      nextMaintenance: "2024-02-15",
      fuelEfficiency: "12.2 km/l",
      totalKm: 32145,
      monthlyKm: 1890,
      condition: "Excellent",
    },
    {
      id: "VH003",
      vehicleNumber: "DL-1CC-9012",
      type: "Bus",
      model: "Ashok Leyland",
      capacity: 50,
      driver: "Unassigned",
      route: "Not Assigned",
      status: "Maintenance",
      lastMaintenance: "2024-01-20",
      nextMaintenance: "2024-01-25",
      fuelEfficiency: "7.8 km/l",
      totalKm: 67890,
      monthlyKm: 0,
      condition: "Needs Repair",
    },
    {
      id: "VH004",
      vehicleNumber: "DL-1CD-3456",
      type: "Van",
      model: "Force Traveller",
      capacity: 15,
      driver: "Amit Sharma",
      route: "Route C - East Delhi",
      status: "Active",
      lastMaintenance: "2024-01-05",
      nextMaintenance: "2024-02-05",
      fuelEfficiency: "10.5 km/l",
      totalKm: 28765,
      monthlyKm: 2100,
      condition: "Good",
    },
    {
      id: "VH005",
      vehicleNumber: "DL-1CE-7890",
      type: "Bus",
      model: "Tata LP 1109",
      capacity: 55,
      driver: "Vikram Yadav",
      route: "Route D - West Delhi",
      status: "Maintenance",
      lastMaintenance: "2024-01-18",
      nextMaintenance: "2024-01-28",
      fuelEfficiency: "8.0 km/l",
      totalKm: 54321,
      monthlyKm: 0,
      condition: "Fair",
    },
  ];

  const filteredVehicles = mockVehicles.filter((vehicle) => {
    const matchesSearch = vehicle.vehicleNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         vehicle.model.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         vehicle.driver.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         vehicle.route.toLowerCase().includes(searchTerm.toLowerCase());

    if (selectedTab === "all") return matchesSearch;
    if (selectedTab === "active") return matchesSearch && vehicle.status === "Active";
    if (selectedTab === "maintenance") return matchesSearch && vehicle.status === "Maintenance";
    if (selectedTab === "buses") return matchesSearch && vehicle.type === "Bus";
    if (selectedTab === "vans") return matchesSearch && vehicle.type === "Van";

    return matchesSearch;
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Active":
        return <Badge className="bg-green-100 text-green-800">Active</Badge>;
      case "Maintenance":
        return <Badge className="bg-yellow-100 text-yellow-800">Maintenance</Badge>;
      case "Out of Service":
        return <Badge className="bg-red-100 text-red-800">Out of Service</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }
  };

  const getConditionBadge = (condition: string) => {
    switch (condition) {
      case "Excellent":
        return <Badge variant="outline" className="text-green-600 border-green-200">Excellent</Badge>;
      case "Good":
        return <Badge variant="outline" className="text-blue-600 border-blue-200">Good</Badge>;
      case "Fair":
        return <Badge variant="outline" className="text-yellow-600 border-yellow-200">Fair</Badge>;
      case "Needs Repair":
        return <Badge variant="outline" className="text-red-600 border-red-200">Needs Repair</Badge>;
      default:
        return <Badge variant="outline">{condition}</Badge>;
    }
  };

  const viewVehicleDetails = (vehicle: any) => {
    setSelectedVehicle(vehicle);
  };

  const editVehicle = (vehicleId: string) => {
    alert(`Editing vehicle ${vehicleId}`);
  };

  const scheduleMaintenanceVehicle = (vehicleId: string) => {
    alert(`Scheduling maintenance for vehicle ${vehicleId}`);
  };

  const assignDriver = (vehicleId: string) => {
    alert(`Assigning driver to vehicle ${vehicleId}`);
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Vehicle Fleet Management</h1>
            <p className="text-gray-600">Manage school transport vehicles and fleet operations</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Fleet Report
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Vehicle
            </Button>
          </div>
        </div>

        {/* Vehicle Statistics */}
        <div className="grid gap-4 md:grid-cols-3 lg:grid-cols-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Bus className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {vehicleStats.totalVehicles}
                  </div>
                  <p className="text-sm text-gray-600">Total Vehicles</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {vehicleStats.activeVehicles}
                  </div>
                  <p className="text-sm text-gray-600">Active</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Wrench className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-yellow-600">
                    {vehicleStats.maintenanceVehicles}
                  </div>
                  <p className="text-sm text-gray-600">Maintenance</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {vehicleStats.availableVehicles}
                  </div>
                  <p className="text-sm text-gray-600">Available</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Bus className="h-8 w-8 text-indigo-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-indigo-600">
                    {vehicleStats.busesCount}
                  </div>
                  <p className="text-sm text-gray-600">Buses</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Bus className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    {vehicleStats.vansCount}
                  </div>
                  <p className="text-sm text-gray-600">Vans</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filter */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search by vehicle number, model, driver, or route..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </div>

            {/* Tabs */}
            <div className="flex flex-wrap gap-2 mt-4">
              {[
                { key: "all", label: "All Vehicles", count: vehicleStats.totalVehicles },
                { key: "active", label: "Active", count: vehicleStats.activeVehicles },
                { key: "maintenance", label: "Maintenance", count: vehicleStats.maintenanceVehicles },
                { key: "buses", label: "Buses", count: vehicleStats.busesCount },
                { key: "vans", label: "Vans", count: vehicleStats.vansCount },
              ].map((tab) => (
                <Button
                  key={tab.key}
                  variant={selectedTab === tab.key ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedTab(tab.key)}
                  className="text-xs"
                >
                  {tab.label} ({tab.count})
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Vehicles Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Bus className="h-5 w-5 mr-2" />
              Vehicle Fleet ({filteredVehicles.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-4 font-medium text-gray-900">Vehicle Details</th>
                    <th className="text-left p-4 font-medium text-gray-900">Driver & Route</th>
                    <th className="text-left p-4 font-medium text-gray-900">Status</th>
                    <th className="text-left p-4 font-medium text-gray-900">Maintenance</th>
                    <th className="text-left p-4 font-medium text-gray-900">Performance</th>
                    <th className="text-left p-4 font-medium text-gray-900">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredVehicles.map((vehicle) => (
                    <tr key={vehicle.id} className="border-b hover:bg-gray-50">
                      <td className="p-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <Bus className="h-6 w-6 text-blue-600" />
                          </div>
                          <div>
                            <div className="font-medium text-gray-900">{vehicle.vehicleNumber}</div>
                            <div className="text-sm text-gray-500">{vehicle.model}</div>
                            <div className="text-sm text-gray-500">Capacity: {vehicle.capacity} seats</div>
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-1">
                          <div className="font-medium text-gray-900">{vehicle.driver}</div>
                          <div className="text-sm text-gray-500">{vehicle.route}</div>
                          <div className="text-xs text-gray-400">ID: {vehicle.id}</div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-2">
                          {getStatusBadge(vehicle.status)}
                          {getConditionBadge(vehicle.condition)}
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-1">
                          <div className="text-sm">
                            <span className="font-medium">Last:</span>
                            <div className="text-gray-500">{new Date(vehicle.lastMaintenance).toLocaleDateString()}</div>
                          </div>
                          <div className="text-sm">
                            <span className="font-medium">Next:</span>
                            <div className="text-gray-500">{new Date(vehicle.nextMaintenance).toLocaleDateString()}</div>
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-1">
                          <div className="text-sm">
                            <span className="font-medium">Fuel:</span>
                            <div className="text-gray-500">{vehicle.fuelEfficiency}</div>
                          </div>
                          <div className="text-sm">
                            <span className="font-medium">Total KM:</span>
                            <div className="text-gray-500">{vehicle.totalKm.toLocaleString()}</div>
                          </div>
                          <div className="text-sm">
                            <span className="font-medium">Monthly:</span>
                            <div className="text-gray-500">{vehicle.monthlyKm.toLocaleString()} km</div>
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => viewVehicleDetails(vehicle)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => editVehicle(vehicle.id)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => scheduleMaintenanceVehicle(vehicle.id)}
                          >
                            <Wrench className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}