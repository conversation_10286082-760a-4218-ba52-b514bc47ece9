"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Users,
  Search,
  Filter,
  Download,
  Plus,
  Eye,
  Edit,
  Trash2,
  Phone,
  Mail,
  Home,
  Calendar,
  CheckCircle,
  XCircle,
  AlertTriangle,
  User,
  GraduationCap,
  Building,
  MapPin,
  IndianRupee,
  Clock,
  Bed,
} from "lucide-react";

export default function HostelStudents() {
  const [user, setUser] = useState<any>(null);
  const [selectedTab, setSelectedTab] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStudent, setSelectedStudent] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "hostel_manager") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  const studentStats = {
    totalStudents: 185,
    maleStudents: 98,
    femaleStudents: 87,
    activeStudents: 178,
    inactiveStudents: 7,
    blockAStudents: 65,
    blockBStudents: 72,
    blockCStudents: 48,
  };

  const mockStudents = [
    {
      id: "HS001",
      studentName: "Aarav Sharma",
      studentId: "ST001",
      class: "Class 10-A",
      rollNumber: "10A001",
      gender: "Male",
      dateOfBirth: "2008-05-15",
      parentName: "Rajesh Sharma",
      parentPhone: "+91 98765 43210",
      parentEmail: "<EMAIL>",
      address: "123 Main Street, Delhi",
      roomNumber: "A-101",
      hostelBlock: "Block A",
      roomType: "Double",
      admissionDate: "2023-04-15",
      status: "Active",
      monthlyFee: 15000,
      feeStatus: "Paid",
      emergencyContact: "+91 98765 43211",
      medicalInfo: "No known allergies",
      guardianName: "Sunita Sharma",
      guardianRelation: "Mother",
    },
    {
      id: "HS002",
      studentName: "Priya Patel",
      studentId: "ST002",
      class: "Class 12-B",
      rollNumber: "12B015",
      gender: "Female",
      dateOfBirth: "2006-08-22",
      parentName: "Suresh Patel",
      parentPhone: "+91 98765 43212",
      parentEmail: "<EMAIL>",
      address: "456 Park Avenue, Mumbai",
      roomNumber: "B-201",
      hostelBlock: "Block B",
      roomType: "Triple",
      admissionDate: "2022-06-20",
      status: "Active",
      monthlyFee: 12000,
      feeStatus: "Pending",
      emergencyContact: "+91 98765 43213",
      medicalInfo: "Asthma - requires inhaler",
      guardianName: "Meera Patel",
      guardianRelation: "Mother",
    },
    {
      id: "HS003",
      studentName: "Rahul Kumar",
      studentId: "ST003",
      class: "Class 11-C",
      rollNumber: "11C008",
      gender: "Male",
      dateOfBirth: "2007-12-10",
      parentName: "Amit Kumar",
      parentPhone: "+91 98765 43214",
      parentEmail: "<EMAIL>",
      address: "789 Garden Road, Bangalore",
      roomNumber: "A-102",
      hostelBlock: "Block A",
      roomType: "Single",
      admissionDate: "2023-01-10",
      status: "Inactive",
      monthlyFee: 20000,
      feeStatus: "Overdue",
      emergencyContact: "+91 98765 43215",
      medicalInfo: "Diabetic - requires special diet",
      guardianName: "Priya Kumar",
      guardianRelation: "Mother",
    },
    {
      id: "HS004",
      studentName: "Sneha Reddy",
      studentId: "ST004",
      class: "Class 9-A",
      rollNumber: "09A022",
      gender: "Female",
      dateOfBirth: "2009-03-18",
      parentName: "Vikram Reddy",
      parentPhone: "+91 98765 43216",
      parentEmail: "<EMAIL>",
      address: "321 Hill View, Hyderabad",
      roomNumber: "C-301",
      hostelBlock: "Block C",
      roomType: "Single",
      admissionDate: "2023-08-05",
      status: "Active",
      monthlyFee: 18000,
      feeStatus: "Paid",
      emergencyContact: "+91 98765 43217",
      medicalInfo: "No known medical issues",
      guardianName: "Lakshmi Reddy",
      guardianRelation: "Mother",
    },
    {
      id: "HS005",
      studentName: "Vikram Singh",
      studentId: "ST005",
      class: "Class 8-B",
      rollNumber: "08B012",
      gender: "Male",
      dateOfBirth: "2010-07-25",
      parentName: "Ravi Singh",
      parentPhone: "+91 98765 43218",
      parentEmail: "<EMAIL>",
      address: "654 Valley Street, Pune",
      roomNumber: "B-202",
      hostelBlock: "Block B",
      roomType: "Double",
      admissionDate: "2023-11-12",
      status: "Active",
      monthlyFee: 15000,
      feeStatus: "Paid",
      emergencyContact: "+91 98765 43219",
      medicalInfo: "Lactose intolerant",
      guardianName: "Kavita Singh",
      guardianRelation: "Mother",
    },
  ];

  const filteredStudents = mockStudents.filter((student) => {
    const matchesSearch = student.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         student.studentId.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         student.class.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         student.roomNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         student.hostelBlock.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         student.parentName.toLowerCase().includes(searchTerm.toLowerCase());

    if (selectedTab === "all") return matchesSearch;
    if (selectedTab === "active") return matchesSearch && student.status === "Active";
    if (selectedTab === "inactive") return matchesSearch && student.status === "Inactive";
    if (selectedTab === "male") return matchesSearch && student.gender === "Male";
    if (selectedTab === "female") return matchesSearch && student.gender === "Female";
    if (selectedTab === "block_a") return matchesSearch && student.hostelBlock === "Block A";
    if (selectedTab === "block_b") return matchesSearch && student.hostelBlock === "Block B";
    if (selectedTab === "block_c") return matchesSearch && student.hostelBlock === "Block C";

    return matchesSearch;
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Active":
        return <Badge className="bg-green-100 text-green-800">Active</Badge>;
      case "Inactive":
        return <Badge className="bg-red-100 text-red-800">Inactive</Badge>;
      case "Suspended":
        return <Badge className="bg-yellow-100 text-yellow-800">Suspended</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }
  };

  const getFeeStatusBadge = (status: string) => {
    switch (status) {
      case "Paid":
        return <Badge variant="outline" className="text-green-600 border-green-200">Paid</Badge>;
      case "Pending":
        return <Badge variant="outline" className="text-yellow-600 border-yellow-200">Pending</Badge>;
      case "Overdue":
        return <Badge variant="outline" className="text-red-600 border-red-200">Overdue</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const getGenderBadge = (gender: string) => {
    switch (gender) {
      case "Male":
        return <Badge variant="outline" className="text-blue-600 border-blue-200">Male</Badge>;
      case "Female":
        return <Badge variant="outline" className="text-pink-600 border-pink-200">Female</Badge>;
      default:
        return <Badge variant="outline">{gender}</Badge>;
    }
  };

  const viewStudentDetails = (student: any) => {
    setSelectedStudent(student);
  };

  const editStudent = (studentId: string) => {
    alert(`Editing student ${studentId}`);
  };

  const changeRoom = (studentId: string) => {
    alert(`Changing room for student ${studentId}`);
  };

  const suspendStudent = (studentId: string) => {
    alert(`Suspending student ${studentId}`);
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Hostel Students</h1>
            <p className="text-gray-600">Manage hostel student records and accommodations</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Student List
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Student
            </Button>
          </div>
        </div>

        {/* Student Statistics */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {studentStats.totalStudents}
                  </div>
                  <p className="text-sm text-gray-600">Total Students</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {studentStats.activeStudents}
                  </div>
                  <p className="text-sm text-gray-600">Active</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <User className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {studentStats.maleStudents}
                  </div>
                  <p className="text-sm text-gray-600">Male Students</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <User className="h-8 w-8 text-pink-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-pink-600">
                    {studentStats.femaleStudents}
                  </div>
                  <p className="text-sm text-gray-600">Female Students</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filter */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search by name, ID, class, room, block, or parent..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </div>

            {/* Tabs */}
            <div className="flex flex-wrap gap-2 mt-4">
              {[
                { key: "all", label: "All Students", count: studentStats.totalStudents },
                { key: "active", label: "Active", count: studentStats.activeStudents },
                { key: "inactive", label: "Inactive", count: studentStats.inactiveStudents },
                { key: "male", label: "Male", count: studentStats.maleStudents },
                { key: "female", label: "Female", count: studentStats.femaleStudents },
                { key: "block_a", label: "Block A", count: studentStats.blockAStudents },
                { key: "block_b", label: "Block B", count: studentStats.blockBStudents },
                { key: "block_c", label: "Block C", count: studentStats.blockCStudents },
              ].map((tab) => (
                <Button
                  key={tab.key}
                  variant={selectedTab === tab.key ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedTab(tab.key)}
                  className="text-xs"
                >
                  {tab.label} ({tab.count})
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Students Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Hostel Students ({filteredStudents.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-4 font-medium text-gray-900">Student Details</th>
                    <th className="text-left p-4 font-medium text-gray-900">Parent Contact</th>
                    <th className="text-left p-4 font-medium text-gray-900">Room & Block</th>
                    <th className="text-left p-4 font-medium text-gray-900">Fee & Status</th>
                    <th className="text-left p-4 font-medium text-gray-900">Medical Info</th>
                    <th className="text-left p-4 font-medium text-gray-900">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredStudents.map((student) => (
                    <tr key={student.id} className="border-b hover:bg-gray-50">
                      <td className="p-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <User className="h-5 w-5 text-blue-600" />
                          </div>
                          <div>
                            <div className="font-medium text-gray-900">{student.studentName}</div>
                            <div className="text-sm text-gray-500">{student.studentId}</div>
                            <div className="text-sm text-gray-500">{student.class} • {student.rollNumber}</div>
                            <div className="flex items-center space-x-2 mt-1">
                              {getGenderBadge(student.gender)}
                              <span className="text-xs text-gray-400">
                                Age: {new Date().getFullYear() - new Date(student.dateOfBirth).getFullYear()}
                              </span>
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-1">
                          <div className="font-medium text-gray-900">{student.parentName}</div>
                          <div className="text-sm text-gray-500 flex items-center">
                            <Phone className="h-3 w-3 mr-1" />
                            {student.parentPhone}
                          </div>
                          <div className="text-sm text-gray-500 flex items-center">
                            <Mail className="h-3 w-3 mr-1" />
                            {student.parentEmail}
                          </div>
                          <div className="text-xs text-gray-400">
                            Guardian: {student.guardianName} ({student.guardianRelation})
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-1">
                          <div className="font-medium text-gray-900">{student.roomNumber}</div>
                          <div className="text-sm text-gray-500">{student.hostelBlock}</div>
                          <div className="text-sm text-gray-500">{student.roomType} Room</div>
                          <div className="text-xs text-gray-400">
                            Since: {new Date(student.admissionDate).toLocaleDateString()}
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-2">
                          <div className="font-medium text-gray-900">
                            ₹{student.monthlyFee.toLocaleString()}/month
                          </div>
                          {getFeeStatusBadge(student.feeStatus)}
                          {getStatusBadge(student.status)}
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="text-sm text-gray-500">
                          {student.medicalInfo}
                        </div>
                        <div className="text-xs text-gray-400 mt-1">
                          Emergency: {student.emergencyContact}
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => viewStudentDetails(student)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => editStudent(student.id)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => changeRoom(student.id)}
                          >
                            <Bed className="h-4 w-4" />
                          </Button>
                          {student.status === "Active" && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => suspendStudent(student.id)}
                            >
                              <XCircle className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}