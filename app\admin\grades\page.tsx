"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Award,
  BookOpen,
  Users,
  TrendingUp,
  TrendingDown,
  Search,
  Filter,
  Download,
  Upload,
  Edit,
  Eye,
  Plus,
  BarChart3,
  Target,
  Calendar,
  FileText,
  CheckCircle,
  AlertTriangle,
  Star,
} from "lucide-react";

export default function AdminGrades() {
  const [user, setUser] = useState<any>(null);
  const [selectedClass, setSelectedClass] = useState("all");
  const [selectedSubject, setSelectedSubject] = useState("all");
  const [selectedExam, setSelectedExam] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "super_admin" && parsedUser.role !== "admin") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock grades data
  const gradeStats = {
    totalStudents: 1234,
    averageGPA: 3.7,
    passRate: 94.2,
    excellentGrades: 185,
    improvementNeeded: 67,
    topPerformers: 45,
    gradeDistribution: {
      "A+": 15.0,
      "A": 20.0,
      "B+": 30.0,
      "B": 25.0,
      "C+": 10.0,
    },
  };

  const subjectPerformance = [
    { subject: "Mathematics", avgGrade: 3.8, passRate: 92.5, topScore: 98, students: 1234 },
    { subject: "Physics", avgGrade: 3.6, passRate: 89.2, topScore: 96, students: 856 },
    { subject: "Chemistry", avgGrade: 3.7, passRate: 91.8, topScore: 97, students: 856 },
    { subject: "English", avgGrade: 3.9, passRate: 96.1, topScore: 99, students: 1234 },
    { subject: "Computer Science", avgGrade: 4.0, passRate: 97.3, topScore: 100, students: 456 },
    { subject: "Biology", avgGrade: 3.5, passRate: 87.4, topScore: 95, students: 378 },
  ];

  const classPerformance = [
    { class: "Grade 10A", students: 45, avgGPA: 3.8, passRate: 95.6, topStudent: "John Doe" },
    { class: "Grade 10B", students: 44, avgGPA: 3.6, passRate: 93.2, topStudent: "Alice Smith" },
    { class: "Grade 11A", students: 43, avgGPA: 3.7, passRate: 94.2, topStudent: "Michael Johnson" },
    { class: "Grade 11B", students: 42, avgGPA: 3.5, passRate: 92.9, topStudent: "Emma Davis" },
    { class: "Grade 12A", students: 41, avgGPA: 3.9, passRate: 97.6, topStudent: "Robert Wilson" },
    { class: "Grade 12B", students: 40, avgGPA: 3.8, passRate: 95.0, topStudent: "Sarah Brown" },
  ];

  const recentGrades = [
    { id: "1", student: "John Doe", class: "Grade 10A", subject: "Mathematics", exam: "Mid-term", grade: "A+", score: 95, date: "2024-01-20" },
    { id: "2", student: "Alice Smith", class: "Grade 10A", subject: "Physics", exam: "Mid-term", grade: "A", score: 88, date: "2024-01-20" },
    { id: "3", student: "Michael Johnson", class: "Grade 11B", subject: "Chemistry", exam: "Mid-term", grade: "B+", score: 82, date: "2024-01-19" },
    { id: "4", student: "Emma Davis", class: "Grade 12A", subject: "English", exam: "Mid-term", grade: "A+", score: 96, date: "2024-01-19" },
    { id: "5", student: "Robert Wilson", class: "Grade 10B", subject: "Computer Science", exam: "Mid-term", grade: "A", score: 91, date: "2024-01-18" },
  ];

  const topPerformers = [
    { rank: 1, name: "Emma Davis", class: "Grade 12A", gpa: 4.0, subjects: 6, avgScore: 96.5 },
    { rank: 2, name: "John Doe", class: "Grade 10A", gpa: 3.95, subjects: 5, avgScore: 94.8 },
    { rank: 3, name: "Sarah Brown", class: "Grade 12B", gpa: 3.92, subjects: 6, avgScore: 93.2 },
    { rank: 4, name: "Michael Johnson", class: "Grade 11B", gpa: 3.88, subjects: 5, avgScore: 92.1 },
    { rank: 5, name: "Alice Smith", class: "Grade 10A", gpa: 3.85, subjects: 5, avgScore: 91.5 },
  ];

  const improvementNeeded = [
    { name: "David Lee", class: "Grade 10B", gpa: 2.1, subjects: 5, avgScore: 65.2, weakSubject: "Mathematics" },
    { name: "Lisa Wang", class: "Grade 11A", gpa: 2.3, subjects: 5, avgScore: 67.8, weakSubject: "Physics" },
    { name: "James Miller", class: "Grade 12B", gpa: 2.5, subjects: 6, avgScore: 69.1, weakSubject: "Chemistry" },
    { name: "Maria Garcia", class: "Grade 10A", gpa: 2.2, subjects: 5, avgScore: 66.5, weakSubject: "English" },
  ];

  const getGradeColor = (grade: string) => {
    switch (grade) {
      case "A+": return "text-green-600 bg-green-100";
      case "A": return "text-blue-600 bg-blue-100";
      case "B+": return "text-yellow-600 bg-yellow-100";
      case "B": return "text-orange-600 bg-orange-100";
      case "C+": return "text-red-600 bg-red-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const getPerformanceColor = (value: number, type: string) => {
    if (type === "gpa") {
      return value >= 3.5 ? "text-green-600" : value >= 3.0 ? "text-blue-600" : value >= 2.5 ? "text-yellow-600" : "text-red-600";
    }
    if (type === "passRate") {
      return value >= 95 ? "text-green-600" : value >= 90 ? "text-blue-600" : value >= 85 ? "text-yellow-600" : "text-red-600";
    }
    return "text-gray-600";
  };

  const filteredGrades = recentGrades.filter(grade => {
    const matchesSearch = grade.student.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         grade.subject.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesClass = selectedClass === "all" || grade.class === selectedClass;
    const matchesSubject = selectedSubject === "all" || grade.subject === selectedSubject;
    const matchesExam = selectedExam === "all" || grade.exam === selectedExam;
    return matchesSearch && matchesClass && matchesSubject && matchesExam;
  });

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Grades Management</h1>
            <p className="text-gray-600">Monitor and manage student academic performance</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Upload className="h-4 w-4 mr-2" />
              Import Grades
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Grade
            </Button>
          </div>
        </div>

        {/* Grade Overview Stats */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {gradeStats.totalStudents}
                  </div>
                  <p className="text-sm text-gray-600">Total Students</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Award className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {gradeStats.averageGPA}
                  </div>
                  <p className="text-sm text-gray-600">Average GPA</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600">+0.2</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Target className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {gradeStats.passRate}%
                  </div>
                  <p className="text-sm text-gray-600">Pass Rate</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600">****%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Star className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-yellow-600">
                    {gradeStats.excellentGrades}
                  </div>
                  <p className="text-sm text-gray-600">Excellent Grades</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600">****%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search by student name or subject..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <select
                  value={selectedClass}
                  onChange={(e) => setSelectedClass(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Classes</option>
                  <option value="Grade 10A">Grade 10A</option>
                  <option value="Grade 10B">Grade 10B</option>
                  <option value="Grade 11A">Grade 11A</option>
                  <option value="Grade 11B">Grade 11B</option>
                  <option value="Grade 12A">Grade 12A</option>
                  <option value="Grade 12B">Grade 12B</option>
                </select>
                <select
                  value={selectedSubject}
                  onChange={(e) => setSelectedSubject(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Subjects</option>
                  <option value="Mathematics">Mathematics</option>
                  <option value="Physics">Physics</option>
                  <option value="Chemistry">Chemistry</option>
                  <option value="English">English</option>
                  <option value="Computer Science">Computer Science</option>
                  <option value="Biology">Biology</option>
                </select>
                <select
                  value={selectedExam}
                  onChange={(e) => setSelectedExam(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Exams</option>
                  <option value="Mid-term">Mid-term</option>
                  <option value="Final">Final</option>
                  <option value="Quiz">Quiz</option>
                  <option value="Assignment">Assignment</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Main Content Grid */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Subject Performance */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center">
                <BookOpen className="h-5 w-5 mr-2" />
                Subject Performance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {subjectPerformance.map((subject, index) => (
                  <div key={index} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="font-semibold text-gray-900">{subject.subject}</h3>
                      <span className="text-sm text-gray-500">{subject.students} students</span>
                    </div>
                    
                    <div className="grid gap-3 md:grid-cols-4 text-sm mb-3">
                      <div className="text-center">
                        <div className={`text-lg font-bold ${getPerformanceColor(subject.avgGrade, 'gpa')}`}>
                          {subject.avgGrade}
                        </div>
                        <div className="text-gray-500">Avg GPA</div>
                      </div>
                      <div className="text-center">
                        <div className={`text-lg font-bold ${getPerformanceColor(subject.passRate, 'passRate')}`}>
                          {subject.passRate}%
                        </div>
                        <div className="text-gray-500">Pass Rate</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-purple-600">{subject.topScore}</div>
                        <div className="text-gray-500">Top Score</div>
                      </div>
                      <div className="text-center">
                        <Button size="sm" variant="outline">
                          <Eye className="h-3 w-3 mr-1" />
                          View Details
                        </Button>
                      </div>
                    </div>

                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${
                          subject.passRate >= 95 ? 'bg-green-500' :
                          subject.passRate >= 90 ? 'bg-blue-500' :
                          subject.passRate >= 85 ? 'bg-yellow-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${subject.passRate}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Grade Distribution */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="h-5 w-5 mr-2" />
                Grade Distribution
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {Object.entries(gradeStats.gradeDistribution).map(([grade, percentage]) => (
                  <div key={grade} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getGradeColor(grade)}`}>
                        {grade}
                      </span>
                      <span className="text-sm text-gray-600">Grade {grade}</span>
                    </div>
                    <div className="flex items-center space-x-2">
                      <div className="w-16 bg-gray-200 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${
                            grade === 'A+' ? 'bg-green-500' :
                            grade === 'A' ? 'bg-blue-500' :
                            grade === 'B+' ? 'bg-yellow-500' :
                            grade === 'B' ? 'bg-orange-500' : 'bg-red-500'
                          }`}
                          style={{ width: `${percentage * 2}%` }}
                        />
                      </div>
                      <span className="text-sm font-medium w-8">{percentage}%</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Grades */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              Recent Grades ({filteredGrades.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Student</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Class</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Subject</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Exam</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Grade</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Score</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Date</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredGrades.map((grade) => (
                    <tr key={grade.id} className="border-b hover:bg-gray-50">
                      <td className="py-3 px-4 font-medium text-gray-900">{grade.student}</td>
                      <td className="py-3 px-4 text-gray-600">{grade.class}</td>
                      <td className="py-3 px-4 text-gray-600">{grade.subject}</td>
                      <td className="py-3 px-4 text-gray-600">{grade.exam}</td>
                      <td className="py-3 px-4">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getGradeColor(grade.grade)}`}>
                          {grade.grade}
                        </span>
                      </td>
                      <td className="py-3 px-4 font-medium text-gray-900">{grade.score}%</td>
                      <td className="py-3 px-4 text-gray-600">{grade.date}</td>
                      <td className="py-3 px-4">
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm">
                            <Eye className="h-3 w-3" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <Edit className="h-3 w-3" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Performance Insights */}
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Top Performers */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Star className="h-5 w-5 mr-2 text-yellow-500" />
                Top Performers
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {topPerformers.map((student) => (
                  <div key={student.rank} className="p-4 border border-green-200 rounded-lg bg-green-50">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-3">
                        <div className="w-8 h-8 bg-yellow-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                          {student.rank}
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">{student.name}</div>
                          <div className="text-sm text-gray-600">{student.class}</div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold text-green-600">{student.gpa}</div>
                        <div className="text-sm text-gray-500">GPA</div>
                      </div>
                    </div>
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Subjects: {student.subjects}</span>
                      <span className="text-gray-600">Avg Score: {student.avgScore}%</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Students Needing Improvement */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertTriangle className="h-5 w-5 mr-2 text-red-500" />
                Students Needing Improvement
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {improvementNeeded.map((student, index) => (
                  <div key={index} className="p-4 border border-red-200 rounded-lg bg-red-50">
                    <div className="flex items-center justify-between mb-2">
                      <div>
                        <div className="font-medium text-gray-900">{student.name}</div>
                        <div className="text-sm text-gray-600">{student.class}</div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold text-red-600">{student.gpa}</div>
                        <div className="text-sm text-gray-500">GPA</div>
                      </div>
                    </div>
                    <div className="flex items-center justify-between text-sm mb-2">
                      <span className="text-gray-600">Avg Score: {student.avgScore}%</span>
                      <span className="text-gray-600">Weak: {student.weakSubject}</span>
                    </div>
                    <Button size="sm" variant="outline" className="text-red-600 border-red-300">
                      Create Improvement Plan
                    </Button>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  );
}
