"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Clock,
  Calendar,
  BookOpen,
  Users,
  MapPin,
  Download,
  Printer,
  Filter,
  ChevronLeft,
  ChevronRight,
  Plus,
  Edit,
  AlertCircle,
} from "lucide-react";

export default function TeacherTimetable() {
  const [user, setUser] = useState<any>(null);
  const [selectedWeek, setSelectedWeek] = useState(0); // 0 = current week
  const [selectedView, setSelectedView] = useState("week"); // week, day
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "teacher") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock timetable data
  const teacherInfo = {
    name: "Dr. Sarah Johnson",
    employeeId: "EMP001",
    department: "Mathematics",
    totalClasses: 18,
    weeklyHours: 24,
    subjects: ["Mathematics", "Statistics", "Algebra"],
  };

  const timeSlots = [
    "08:00 - 08:45",
    "08:45 - 09:30",
    "09:30 - 10:15",
    "10:15 - 10:30", // Break
    "10:30 - 11:15",
    "11:15 - 12:00",
    "12:00 - 12:45",
    "12:45 - 13:30", // Lunch
    "13:30 - 14:15",
    "14:15 - 15:00",
    "15:00 - 15:45",
  ];

  const weekDays = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];

  const timetableData = {
    Monday: [
      { time: "08:00 - 08:45", subject: "Mathematics", class: "Grade 10A", room: "Room 101", type: "regular" },
      { time: "08:45 - 09:30", subject: "Mathematics", class: "Grade 10B", room: "Room 101", type: "regular" },
      { time: "09:30 - 10:15", subject: "Statistics", class: "Grade 12A", room: "Room 102", type: "regular" },
      { time: "10:15 - 10:30", subject: "Break", class: "", room: "", type: "break" },
      { time: "10:30 - 11:15", subject: "Algebra", class: "Grade 11A", room: "Room 101", type: "regular" },
      { time: "11:15 - 12:00", subject: "Mathematics", class: "Grade 10A", room: "Room 101", type: "regular" },
      { time: "12:00 - 12:45", subject: "Free Period", class: "", room: "", type: "free" },
      { time: "12:45 - 13:30", subject: "Lunch Break", class: "", room: "", type: "break" },
      { time: "13:30 - 14:15", subject: "Statistics", class: "Grade 12B", room: "Room 102", type: "regular" },
      { time: "14:15 - 15:00", subject: "Mathematics", class: "Grade 11B", room: "Room 101", type: "regular" },
      { time: "15:00 - 15:45", subject: "Free Period", class: "", room: "", type: "free" },
    ],
    Tuesday: [
      { time: "08:00 - 08:45", subject: "Mathematics", class: "Grade 11A", room: "Room 101", type: "regular" },
      { time: "08:45 - 09:30", subject: "Statistics", class: "Grade 12A", room: "Room 102", type: "regular" },
      { time: "09:30 - 10:15", subject: "Mathematics", class: "Grade 10B", room: "Room 101", type: "regular" },
      { time: "10:15 - 10:30", subject: "Break", class: "", room: "", type: "break" },
      { time: "10:30 - 11:15", subject: "Algebra", class: "Grade 11B", room: "Room 101", type: "regular" },
      { time: "11:15 - 12:00", subject: "Mathematics", class: "Grade 10A", room: "Room 101", type: "regular" },
      { time: "12:00 - 12:45", subject: "Statistics", class: "Grade 12B", room: "Room 102", type: "regular" },
      { time: "12:45 - 13:30", subject: "Lunch Break", class: "", room: "", type: "break" },
      { time: "13:30 - 14:15", subject: "Free Period", class: "", room: "", type: "free" },
      { time: "14:15 - 15:00", subject: "Mathematics", class: "Grade 11A", room: "Room 101", type: "regular" },
      { time: "15:00 - 15:45", subject: "Faculty Meeting", class: "", room: "Conference Room", type: "meeting" },
    ],
    Wednesday: [
      { time: "08:00 - 08:45", subject: "Statistics", class: "Grade 12A", room: "Room 102", type: "regular" },
      { time: "08:45 - 09:30", subject: "Mathematics", class: "Grade 10A", room: "Room 101", type: "regular" },
      { time: "09:30 - 10:15", subject: "Algebra", class: "Grade 11A", room: "Room 101", type: "regular" },
      { time: "10:15 - 10:30", subject: "Break", class: "", room: "", type: "break" },
      { time: "10:30 - 11:15", subject: "Mathematics", class: "Grade 10B", room: "Room 101", type: "regular" },
      { time: "11:15 - 12:00", subject: "Statistics", class: "Grade 12B", room: "Room 102", type: "regular" },
      { time: "12:00 - 12:45", subject: "Mathematics", class: "Grade 11B", room: "Room 101", type: "regular" },
      { time: "12:45 - 13:30", subject: "Lunch Break", class: "", room: "", type: "break" },
      { time: "13:30 - 14:15", subject: "Algebra", class: "Grade 11B", room: "Room 101", type: "regular" },
      { time: "14:15 - 15:00", subject: "Free Period", class: "", room: "", type: "free" },
      { time: "15:00 - 15:45", subject: "Parent Meeting", class: "", room: "Room 101", type: "meeting" },
    ],
    Thursday: [
      { time: "08:00 - 08:45", subject: "Mathematics", class: "Grade 10A", room: "Room 101", type: "regular" },
      { time: "08:45 - 09:30", subject: "Algebra", class: "Grade 11A", room: "Room 101", type: "regular" },
      { time: "09:30 - 10:15", subject: "Statistics", class: "Grade 12A", room: "Room 102", type: "regular" },
      { time: "10:15 - 10:30", subject: "Break", class: "", room: "", type: "break" },
      { time: "10:30 - 11:15", subject: "Mathematics", class: "Grade 10B", room: "Room 101", type: "regular" },
      { time: "11:15 - 12:00", subject: "Mathematics", class: "Grade 11B", room: "Room 101", type: "regular" },
      { time: "12:00 - 12:45", subject: "Statistics", class: "Grade 12B", room: "Room 102", type: "regular" },
      { time: "12:45 - 13:30", subject: "Lunch Break", class: "", room: "", type: "break" },
      { time: "13:30 - 14:15", subject: "Algebra", class: "Grade 11B", room: "Room 101", type: "regular" },
      { time: "14:15 - 15:00", subject: "Free Period", class: "", room: "", type: "free" },
      { time: "15:00 - 15:45", subject: "Free Period", class: "", room: "", type: "free" },
    ],
    Friday: [
      { time: "08:00 - 08:45", subject: "Statistics", class: "Grade 12B", room: "Room 102", type: "regular" },
      { time: "08:45 - 09:30", subject: "Mathematics", class: "Grade 10A", room: "Room 101", type: "regular" },
      { time: "09:30 - 10:15", subject: "Mathematics", class: "Grade 11A", room: "Room 101", type: "regular" },
      { time: "10:15 - 10:30", subject: "Break", class: "", room: "", type: "break" },
      { time: "10:30 - 11:15", subject: "Algebra", class: "Grade 11B", room: "Room 101", type: "regular" },
      { time: "11:15 - 12:00", subject: "Statistics", class: "Grade 12A", room: "Room 102", type: "regular" },
      { time: "12:00 - 12:45", subject: "Mathematics", class: "Grade 10B", room: "Room 101", type: "regular" },
      { time: "12:45 - 13:30", subject: "Lunch Break", class: "", room: "", type: "break" },
      { time: "13:30 - 14:15", subject: "Free Period", class: "", room: "", type: "free" },
      { time: "14:15 - 15:00", subject: "Mathematics", class: "Grade 11B", room: "Room 101", type: "regular" },
      { time: "15:00 - 15:45", subject: "Department Meeting", class: "", room: "Math Dept", type: "meeting" },
    ],
    Saturday: [
      { time: "08:00 - 08:45", subject: "Mathematics", class: "Grade 10A", room: "Room 101", type: "regular" },
      { time: "08:45 - 09:30", subject: "Statistics", class: "Grade 12A", room: "Room 102", type: "regular" },
      { time: "09:30 - 10:15", subject: "Free Period", class: "", room: "", type: "free" },
      { time: "10:15 - 10:30", subject: "Break", class: "", room: "", type: "break" },
      { time: "10:30 - 11:15", subject: "Algebra", class: "Grade 11A", room: "Room 101", type: "regular" },
      { time: "11:15 - 12:00", subject: "Mathematics", class: "Grade 10B", room: "Room 101", type: "regular" },
      { time: "12:00 - 12:45", subject: "Free Period", class: "", room: "", type: "free" },
      { time: "12:45 - 13:30", subject: "Lunch Break", class: "", room: "", type: "break" },
      { time: "13:30 - 14:15", subject: "Free Period", class: "", room: "", type: "free" },
      { time: "14:15 - 15:00", subject: "Free Period", class: "", room: "", type: "free" },
      { time: "15:00 - 15:45", subject: "Free Period", class: "", room: "", type: "free" },
    ],
  };

  const upcomingClasses = [
    { time: "08:00 AM", subject: "Mathematics", class: "Grade 10A", room: "Room 101", day: "Tomorrow" },
    { time: "08:45 AM", subject: "Statistics", class: "Grade 12A", room: "Room 102", day: "Tomorrow" },
    { time: "09:30 AM", subject: "Algebra", class: "Grade 11A", room: "Room 101", day: "Tomorrow" },
  ];

  const getClassTypeColor = (type: string) => {
    switch (type) {
      case "regular": return "bg-blue-100 text-blue-800 border-blue-200";
      case "break": return "bg-gray-100 text-gray-600 border-gray-200";
      case "free": return "bg-green-100 text-green-700 border-green-200";
      case "meeting": return "bg-purple-100 text-purple-700 border-purple-200";
      default: return "bg-gray-100 text-gray-600 border-gray-200";
    }
  };

  const getClassTypeIcon = (type: string) => {
    switch (type) {
      case "regular": return <BookOpen className="h-3 w-3" />;
      case "break": return <Clock className="h-3 w-3" />;
      case "free": return <Users className="h-3 w-3" />;
      case "meeting": return <AlertCircle className="h-3 w-3" />;
      default: return <Clock className="h-3 w-3" />;
    }
  };

  const getCurrentWeekDates = () => {
    const today = new Date();
    const currentDay = today.getDay();
    const monday = new Date(today);
    monday.setDate(today.getDate() - currentDay + 1 + (selectedWeek * 7));

    return weekDays.map((_, index) => {
      const date = new Date(monday);
      date.setDate(monday.getDate() + index);
      return date.toLocaleDateString('en-IN', { day: '2-digit', month: 'short' });
    });
  };

  const weekDates = getCurrentWeekDates();

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">My Timetable</h1>
            <p className="text-gray-600">Your teaching schedule and class assignments</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Printer className="h-4 w-4 mr-2" />
              Print
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Event
            </Button>
          </div>
        </div>

        {/* Teacher Info & Stats */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {teacherInfo.totalClasses}
                  </div>
                  <p className="text-sm text-gray-600">Total Classes</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {teacherInfo.weeklyHours}
                  </div>
                  <p className="text-sm text-gray-600">Weekly Hours</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <BookOpen className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {teacherInfo.subjects.length}
                  </div>
                  <p className="text-sm text-gray-600">Subjects</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <MapPin className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-lg font-bold text-orange-600">
                    {teacherInfo.department}
                  </div>
                  <p className="text-sm text-gray-600">Department</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Week Navigation */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSelectedWeek(selectedWeek - 1)}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <div className="text-lg font-semibold">
                  {selectedWeek === 0 ? "Current Week" :
                   selectedWeek > 0 ? `${selectedWeek} Week${selectedWeek > 1 ? 's' : ''} Ahead` :
                   `${Math.abs(selectedWeek)} Week${Math.abs(selectedWeek) > 1 ? 's' : ''} Ago`}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSelectedWeek(selectedWeek + 1)}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex gap-2">
                <Button
                  variant={selectedView === "week" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedView("week")}
                >
                  Week View
                </Button>
                <Button
                  variant={selectedView === "day" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedView("day")}
                >
                  Day View
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Main Timetable */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              Weekly Timetable
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr>
                    <th className="border border-gray-300 p-3 bg-gray-50 text-left font-medium text-gray-900 min-w-[120px]">
                      Time
                    </th>
                    {weekDays.map((day, index) => (
                      <th key={day} className="border border-gray-300 p-3 bg-gray-50 text-center font-medium text-gray-900 min-w-[150px]">
                        <div>{day}</div>
                        <div className="text-sm font-normal text-gray-500">{weekDates[index]}</div>
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {timeSlots.map((timeSlot, timeIndex) => (
                    <tr key={timeSlot}>
                      <td className="border border-gray-300 p-3 bg-gray-50 font-medium text-gray-700">
                        {timeSlot}
                      </td>
                      {weekDays.map((day) => {
                        const classData = timetableData[day as keyof typeof timetableData]?.[timeIndex];
                        return (
                          <td key={`${day}-${timeIndex}`} className="border border-gray-300 p-2">
                            {classData && classData.subject !== "Break" && classData.subject !== "Lunch Break" ? (
                              <div className={`p-2 rounded-lg border ${getClassTypeColor(classData.type)}`}>
                                <div className="flex items-center space-x-1 mb-1">
                                  {getClassTypeIcon(classData.type)}
                                  <span className="font-medium text-sm">{classData.subject}</span>
                                </div>
                                {classData.class && (
                                  <div className="text-xs opacity-80">{classData.class}</div>
                                )}
                                {classData.room && (
                                  <div className="text-xs opacity-70 flex items-center">
                                    <MapPin className="h-3 w-3 mr-1" />
                                    {classData.room}
                                  </div>
                                )}
                              </div>
                            ) : classData ? (
                              <div className={`p-2 rounded-lg border text-center ${getClassTypeColor(classData.type)}`}>
                                <div className="flex items-center justify-center space-x-1">
                                  {getClassTypeIcon(classData.type)}
                                  <span className="font-medium text-sm">{classData.subject}</span>
                                </div>
                              </div>
                            ) : (
                              <div className="p-2 text-center text-gray-400 text-sm">-</div>
                            )}
                          </td>
                        );
                      })}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Upcoming Classes */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Clock className="h-5 w-5 mr-2" />
              Upcoming Classes
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {upcomingClasses.map((classItem, index) => (
                <div key={index} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                  <div className="flex items-center space-x-4">
                    <div className="text-center">
                      <div className="font-bold text-blue-600">{classItem.time}</div>
                      <div className="text-xs text-gray-500">{classItem.day}</div>
                    </div>
                    <div>
                      <div className="font-medium text-gray-900">{classItem.subject}</div>
                      <div className="text-sm text-gray-600">{classItem.class}</div>
                    </div>
                  </div>
                  <div className="flex items-center space-x-2">
                    <div className="text-sm text-gray-500 flex items-center">
                      <MapPin className="h-3 w-3 mr-1" />
                      {classItem.room}
                    </div>
                    <Button variant="outline" size="sm">
                      <Edit className="h-3 w-3" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Subject Summary */}
        <Card>
          <CardHeader>
            <CardTitle>Subject Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              {teacherInfo.subjects.map((subject, index) => (
                <div key={index} className="p-4 border rounded-lg">
                  <div className="font-medium text-gray-900 mb-2">{subject}</div>
                  <div className="text-sm text-gray-600">
                    Classes per week: {subject === "Mathematics" ? "8" : subject === "Statistics" ? "6" : "4"}
                  </div>
                  <div className="text-sm text-gray-600">
                    Total students: {subject === "Mathematics" ? "180" : subject === "Statistics" ? "85" : "90"}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
