"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Library,
  BookOpen,
  Search,
  Filter,
  Clock,
  Calendar,
  Star,
  Heart,
  Download,
  Eye,
  Bookmark,
  AlertTriangle,
  CheckCircle,
  RefreshCw,
  Plus,
  TrendingUp,
} from "lucide-react";

export default function StudentLibrary() {
  const [user, setUser] = useState<any>(null);
  const [activeTab, setActiveTab] = useState("issued");
  const [searchTerm, setSearchTerm] = useState("");
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "student") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock library data
  const libraryStats = {
    booksIssued: 3,
    booksReserved: 1,
    overdueBooks: 1,
    maxAllowed: 5,
    totalFines: 25,
    readingHours: 45,
  };

  const issuedBooks = [
    {
      id: "BOOK001",
      title: "Introduction to Physics",
      author: "Dr. Richard Feynman",
      isbn: "978-0-123456-78-9",
      issueDate: "2024-01-10",
      dueDate: "2024-01-24",
      status: "issued",
      category: "Science",
      location: "Section A, Shelf 12",
      renewals: 0,
      maxRenewals: 2,
    },
    {
      id: "BOOK002",
      title: "Advanced Mathematics",
      author: "Prof. John Smith",
      isbn: "978-0-987654-32-1",
      issueDate: "2024-01-05",
      dueDate: "2024-01-19",
      status: "overdue",
      category: "Mathematics",
      location: "Section B, Shelf 8",
      renewals: 1,
      maxRenewals: 2,
      fine: 25,
    },
    {
      id: "BOOK003",
      title: "Computer Science Fundamentals",
      author: "Dr. Alan Turing",
      isbn: "978-0-456789-12-3",
      issueDate: "2024-01-15",
      dueDate: "2024-01-29",
      status: "issued",
      category: "Computer Science",
      location: "Section C, Shelf 5",
      renewals: 0,
      maxRenewals: 2,
    },
  ];

  const reservedBooks = [
    {
      id: "BOOK004",
      title: "Organic Chemistry",
      author: "Dr. Marie Curie",
      isbn: "978-0-789123-45-6",
      reservedDate: "2024-01-20",
      expectedDate: "2024-01-25",
      position: 2,
      category: "Chemistry",
      location: "Section A, Shelf 15",
    },
  ];

  const recommendedBooks = [
    {
      id: "BOOK005",
      title: "Quantum Physics for Beginners",
      author: "Dr. Stephen Hawking",
      isbn: "978-0-321654-98-7",
      category: "Science",
      rating: 4.8,
      available: true,
      location: "Section A, Shelf 10",
      description: "An accessible introduction to quantum physics concepts.",
    },
    {
      id: "BOOK006",
      title: "Data Structures and Algorithms",
      author: "Prof. Donald Knuth",
      isbn: "978-0-654321-87-0",
      category: "Computer Science",
      rating: 4.9,
      available: false,
      location: "Section C, Shelf 3",
      description: "Comprehensive guide to data structures and algorithmic thinking.",
    },
    {
      id: "BOOK007",
      title: "English Literature Classics",
      author: "Various Authors",
      isbn: "978-0-147258-36-9",
      category: "Literature",
      rating: 4.6,
      available: true,
      location: "Section D, Shelf 20",
      description: "Collection of timeless English literature masterpieces.",
    },
  ];

  const readingHistory = [
    { title: "Basic Chemistry", returnDate: "2024-01-08", rating: 4 },
    { title: "Calculus Made Easy", returnDate: "2024-01-03", rating: 5 },
    { title: "Programming Basics", returnDate: "2023-12-28", rating: 4 },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "issued": return "bg-blue-100 text-blue-700";
      case "overdue": return "bg-red-100 text-red-700";
      case "reserved": return "bg-yellow-100 text-yellow-700";
      case "returned": return "bg-green-100 text-green-700";
      default: return "bg-gray-100 text-gray-700";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "issued": return <BookOpen className="h-4 w-4" />;
      case "overdue": return <AlertTriangle className="h-4 w-4" />;
      case "reserved": return <Bookmark className="h-4 w-4" />;
      case "returned": return <CheckCircle className="h-4 w-4" />;
      default: return <BookOpen className="h-4 w-4" />;
    }
  };

  const getDaysUntilDue = (dueDate: string) => {
    const due = new Date(dueDate);
    const today = new Date();
    const diffTime = due.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const renderStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, index) => (
      <Star
        key={index}
        className={`h-4 w-4 ${
          index < rating ? "text-yellow-400 fill-current" : "text-gray-300"
        }`}
      />
    ));
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Library</h1>
            <p className="text-gray-600">Manage your books and explore the library collection</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Search className="h-4 w-4 mr-2" />
              Search Catalog
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Reading Report
            </Button>
          </div>
        </div>

        {/* Library Statistics */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <BookOpen className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {libraryStats.booksIssued}
                  </div>
                  <p className="text-sm text-gray-600">Books Issued</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Bookmark className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-yellow-600">
                    {libraryStats.booksReserved}
                  </div>
                  <p className="text-sm text-gray-600">Reserved</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <AlertTriangle className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-red-600">
                    {libraryStats.overdueBooks}
                  </div>
                  <p className="text-sm text-gray-600">Overdue</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {libraryStats.readingHours}h
                  </div>
                  <p className="text-sm text-gray-600">Reading Time</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Book Limit Progress */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Book Limit Usage</h3>
              <div className="text-sm text-gray-500">
                {libraryStats.booksIssued} of {libraryStats.maxAllowed} books
              </div>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-3 mb-2">
              <div 
                className="bg-blue-500 h-3 rounded-full"
                style={{ width: `${(libraryStats.booksIssued / libraryStats.maxAllowed) * 100}%` }}
              />
            </div>
            <div className="text-sm text-gray-600">
              You can issue {libraryStats.maxAllowed - libraryStats.booksIssued} more books
            </div>
          </CardContent>
        </Card>

        {/* Main Content Tabs */}
        <Card>
          <CardHeader>
            <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
              {[
                { key: "issued", label: "Issued Books", count: libraryStats.booksIssued },
                { key: "reserved", label: "Reserved", count: libraryStats.booksReserved },
                { key: "recommended", label: "Recommended", count: recommendedBooks.length },
                { key: "history", label: "History", count: readingHistory.length },
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key)}
                  className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                    activeTab === tab.key
                      ? "bg-white text-blue-600 shadow-sm"
                      : "text-gray-600 hover:text-gray-900"
                  }`}
                >
                  {tab.label} ({tab.count})
                </button>
              ))}
            </div>
          </CardHeader>
          <CardContent>
            {/* Issued Books Tab */}
            {activeTab === "issued" && (
              <div className="space-y-4">
                {issuedBooks.map((book) => (
                  <div key={book.id} className="border rounded-lg p-6 hover:shadow-md transition-shadow">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900 mb-1">{book.title}</h3>
                        <p className="text-gray-600 mb-2">by {book.author}</p>
                        <div className="flex items-center space-x-4 text-sm text-gray-500 mb-3">
                          <span>ISBN: {book.isbn}</span>
                          <span>Category: {book.category}</span>
                          <span>Location: {book.location}</span>
                        </div>
                      </div>
                      <Badge className={getStatusColor(book.status)}>
                        {getStatusIcon(book.status)}
                        <span className="ml-1 capitalize">{book.status}</span>
                      </Badge>
                    </div>

                    <div className="grid gap-4 md:grid-cols-3 mb-4">
                      <div>
                        <div className="text-sm text-gray-600">Issue Date</div>
                        <div className="font-medium">{book.issueDate}</div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-600">Due Date</div>
                        <div className={`font-medium ${
                          book.status === "overdue" ? "text-red-600" : 
                          getDaysUntilDue(book.dueDate) <= 3 ? "text-orange-600" : "text-gray-900"
                        }`}>
                          {book.dueDate}
                        </div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-600">Days Left</div>
                        <div className={`font-medium ${
                          book.status === "overdue" ? "text-red-600" : 
                          getDaysUntilDue(book.dueDate) <= 3 ? "text-orange-600" : "text-green-600"
                        }`}>
                          {book.status === "overdue" 
                            ? `${Math.abs(getDaysUntilDue(book.dueDate))} days overdue`
                            : `${getDaysUntilDue(book.dueDate)} days left`
                          }
                        </div>
                      </div>
                    </div>

                    {book.fine && (
                      <div className="bg-red-50 border border-red-200 rounded-lg p-3 mb-4">
                        <div className="flex items-center">
                          <AlertTriangle className="h-4 w-4 text-red-600 mr-2" />
                          <span className="text-red-700 font-medium">Fine: ₹{book.fine}</span>
                        </div>
                      </div>
                    )}

                    <div className="flex items-center justify-between pt-4 border-t">
                      <div className="text-sm text-gray-500">
                        Renewals: {book.renewals}/{book.maxRenewals}
                      </div>
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-1" />
                          Details
                        </Button>
                        {book.renewals < book.maxRenewals && book.status !== "overdue" && (
                          <Button size="sm">
                            <RefreshCw className="h-4 w-4 mr-1" />
                            Renew
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Reserved Books Tab */}
            {activeTab === "reserved" && (
              <div className="space-y-4">
                {reservedBooks.map((book) => (
                  <div key={book.id} className="border rounded-lg p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900 mb-1">{book.title}</h3>
                        <p className="text-gray-600 mb-2">by {book.author}</p>
                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                          <span>Category: {book.category}</span>
                          <span>Location: {book.location}</span>
                        </div>
                      </div>
                      <Badge className="bg-yellow-100 text-yellow-700">
                        <Bookmark className="h-4 w-4 mr-1" />
                        Reserved
                      </Badge>
                    </div>

                    <div className="grid gap-4 md:grid-cols-3 mb-4">
                      <div>
                        <div className="text-sm text-gray-600">Reserved Date</div>
                        <div className="font-medium">{book.reservedDate}</div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-600">Expected Available</div>
                        <div className="font-medium">{book.expectedDate}</div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-600">Queue Position</div>
                        <div className="font-medium">#{book.position}</div>
                      </div>
                    </div>

                    <div className="flex justify-end">
                      <Button variant="outline" size="sm">
                        Cancel Reservation
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Recommended Books Tab */}
            {activeTab === "recommended" && (
              <div className="space-y-4">
                {recommendedBooks.map((book) => (
                  <div key={book.id} className="border rounded-lg p-6">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900 mb-1">{book.title}</h3>
                        <p className="text-gray-600 mb-2">by {book.author}</p>
                        <p className="text-sm text-gray-600 mb-3">{book.description}</p>
                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                          <span>Category: {book.category}</span>
                          <span>Location: {book.location}</span>
                          <div className="flex items-center">
                            {renderStars(Math.floor(book.rating))}
                            <span className="ml-1">{book.rating}</span>
                          </div>
                        </div>
                      </div>
                      <Badge className={book.available ? "bg-green-100 text-green-700" : "bg-red-100 text-red-700"}>
                        {book.available ? "Available" : "Not Available"}
                      </Badge>
                    </div>

                    <div className="flex justify-end space-x-2">
                      <Button variant="outline" size="sm">
                        <Heart className="h-4 w-4 mr-1" />
                        Add to Wishlist
                      </Button>
                      {book.available ? (
                        <Button size="sm">
                          <Plus className="h-4 w-4 mr-1" />
                          Issue Book
                        </Button>
                      ) : (
                        <Button size="sm">
                          <Bookmark className="h-4 w-4 mr-1" />
                          Reserve
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Reading History Tab */}
            {activeTab === "history" && (
              <div className="space-y-4">
                {readingHistory.map((book, index) => (
                  <div key={index} className="border rounded-lg p-6">
                    <div className="flex items-center justify-between">
                      <div>
                        <h3 className="text-lg font-semibold text-gray-900">{book.title}</h3>
                        <p className="text-sm text-gray-500">Returned on {book.returnDate}</p>
                      </div>
                      <div className="flex items-center space-x-2">
                        <div className="flex items-center">
                          {renderStars(book.rating)}
                        </div>
                        <Button variant="outline" size="sm">
                          <RefreshCw className="h-4 w-4 mr-1" />
                          Issue Again
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
