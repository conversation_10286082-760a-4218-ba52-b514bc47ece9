"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Calendar,
  Clock,
  BookOpen,
  MapPin,
  User,
  Download,
  Printer,
  ChevronLeft,
  ChevronRight,
  Bell,
  AlertCircle,
  CheckCircle,
} from "lucide-react";

export default function ParentTimetable() {
  const [user, setUser] = useState<any>(null);
  const [selectedChild, setSelectedChild] = useState("child1");
  const [selectedWeek, setSelectedWeek] = useState(0);
  const [selectedView, setSelectedView] = useState("week");
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "parent") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock data for parent's children
  const children = [
    { id: "child1", name: "John Doe", class: "Grade 10A", rollNo: "10A001", section: "A" },
    { id: "child2", name: "Jane Doe", class: "Grade 8B", rollNo: "8B015", section: "B" },
  ];

  const selectedChildData = children.find(child => child.id === selectedChild) || children[0];

  const timeSlots = [
    "08:00 - 08:45",
    "08:45 - 09:30",
    "09:30 - 10:15",
    "10:15 - 10:30", // Break
    "10:30 - 11:15",
    "11:15 - 12:00",
    "12:00 - 12:45",
    "12:45 - 13:30", // Lunch
    "13:30 - 14:15",
    "14:15 - 15:00",
    "15:00 - 15:45",
  ];

  const weekDays = ["Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];

  const timetableData = {
    Monday: [
      { time: "08:00 - 08:45", subject: "Mathematics", teacher: "Dr. Sarah Johnson", room: "Room 101", type: "regular" },
      { time: "08:45 - 09:30", subject: "Physics", teacher: "Prof. Michael Smith", room: "Physics Lab", type: "regular" },
      { time: "09:30 - 10:15", subject: "Chemistry", teacher: "Dr. Emily Brown", room: "Chemistry Lab", type: "regular" },
      { time: "10:15 - 10:30", subject: "Break", teacher: "", room: "", type: "break" },
      { time: "10:30 - 11:15", subject: "English", teacher: "Ms. Lisa Wilson", room: "Room 205", type: "regular" },
      { time: "11:15 - 12:00", subject: "Computer Science", teacher: "Mr. David Chen", room: "Computer Lab", type: "regular" },
      { time: "12:00 - 12:45", subject: "Biology", teacher: "Dr. Maria Garcia", room: "Biology Lab", type: "regular" },
      { time: "12:45 - 13:30", subject: "Lunch Break", teacher: "", room: "", type: "break" },
      { time: "13:30 - 14:15", subject: "Physical Education", teacher: "Mr. James Wilson", room: "Sports Ground", type: "regular" },
      { time: "14:15 - 15:00", subject: "Art", teacher: "Ms. Anna Smith", room: "Art Room", type: "regular" },
      { time: "15:00 - 15:45", subject: "Study Hall", teacher: "Class Teacher", room: "Room 101", type: "study" },
    ],
    Tuesday: [
      { time: "08:00 - 08:45", subject: "English", teacher: "Ms. Lisa Wilson", room: "Room 205", type: "regular" },
      { time: "08:45 - 09:30", subject: "Mathematics", teacher: "Dr. Sarah Johnson", room: "Room 101", type: "regular" },
      { time: "09:30 - 10:15", subject: "Physics", teacher: "Prof. Michael Smith", room: "Physics Lab", type: "regular" },
      { time: "10:15 - 10:30", subject: "Break", teacher: "", room: "", type: "break" },
      { time: "10:30 - 11:15", subject: "Chemistry", teacher: "Dr. Emily Brown", room: "Chemistry Lab", type: "regular" },
      { time: "11:15 - 12:00", subject: "Biology", teacher: "Dr. Maria Garcia", room: "Biology Lab", type: "regular" },
      { time: "12:00 - 12:45", subject: "Computer Science", teacher: "Mr. David Chen", room: "Computer Lab", type: "regular" },
      { time: "12:45 - 13:30", subject: "Lunch Break", teacher: "", room: "", type: "break" },
      { time: "13:30 - 14:15", subject: "History", teacher: "Dr. Robert Brown", room: "Room 301", type: "regular" },
      { time: "14:15 - 15:00", subject: "Geography", teacher: "Ms. Sarah Davis", room: "Room 302", type: "regular" },
      { time: "15:00 - 15:45", subject: "Library Period", teacher: "Librarian", room: "Library", type: "study" },
    ],
    Wednesday: [
      { time: "08:00 - 08:45", subject: "Chemistry", teacher: "Dr. Emily Brown", room: "Chemistry Lab", type: "regular" },
      { time: "08:45 - 09:30", subject: "Biology", teacher: "Dr. Maria Garcia", room: "Biology Lab", type: "regular" },
      { time: "09:30 - 10:15", subject: "Mathematics", teacher: "Dr. Sarah Johnson", room: "Room 101", type: "regular" },
      { time: "10:15 - 10:30", subject: "Break", teacher: "", room: "", type: "break" },
      { time: "10:30 - 11:15", subject: "Physics", teacher: "Prof. Michael Smith", room: "Physics Lab", type: "regular" },
      { time: "11:15 - 12:00", subject: "English", teacher: "Ms. Lisa Wilson", room: "Room 205", type: "regular" },
      { time: "12:00 - 12:45", subject: "History", teacher: "Dr. Robert Brown", room: "Room 301", type: "regular" },
      { time: "12:45 - 13:30", subject: "Lunch Break", teacher: "", room: "", type: "break" },
      { time: "13:30 - 14:15", subject: "Computer Science", teacher: "Mr. David Chen", room: "Computer Lab", type: "regular" },
      { time: "14:15 - 15:00", subject: "Music", teacher: "Ms. Jennifer Lee", room: "Music Room", type: "regular" },
      { time: "15:00 - 15:45", subject: "Free Period", teacher: "", room: "", type: "free" },
    ],
    Thursday: [
      { time: "08:00 - 08:45", subject: "Mathematics", teacher: "Dr. Sarah Johnson", room: "Room 101", type: "regular" },
      { time: "08:45 - 09:30", subject: "English", teacher: "Ms. Lisa Wilson", room: "Room 205", type: "regular" },
      { time: "09:30 - 10:15", subject: "Biology", teacher: "Dr. Maria Garcia", room: "Biology Lab", type: "regular" },
      { time: "10:15 - 10:30", subject: "Break", teacher: "", room: "", type: "break" },
      { time: "10:30 - 11:15", subject: "Chemistry", teacher: "Dr. Emily Brown", room: "Chemistry Lab", type: "regular" },
      { time: "11:15 - 12:00", subject: "Physics", teacher: "Prof. Michael Smith", room: "Physics Lab", type: "regular" },
      { time: "12:00 - 12:45", subject: "Geography", teacher: "Ms. Sarah Davis", room: "Room 302", type: "regular" },
      { time: "12:45 - 13:30", subject: "Lunch Break", teacher: "", room: "", type: "break" },
      { time: "13:30 - 14:15", subject: "Computer Science", teacher: "Mr. David Chen", room: "Computer Lab", type: "regular" },
      { time: "14:15 - 15:00", subject: "Physical Education", teacher: "Mr. James Wilson", room: "Sports Ground", type: "regular" },
      { time: "15:00 - 15:45", subject: "Study Hall", teacher: "Class Teacher", room: "Room 101", type: "study" },
    ],
    Friday: [
      { time: "08:00 - 08:45", subject: "Physics", teacher: "Prof. Michael Smith", room: "Physics Lab", type: "regular" },
      { time: "08:45 - 09:30", subject: "Chemistry", teacher: "Dr. Emily Brown", room: "Chemistry Lab", type: "regular" },
      { time: "09:30 - 10:15", subject: "English", teacher: "Ms. Lisa Wilson", room: "Room 205", type: "regular" },
      { time: "10:15 - 10:30", subject: "Break", teacher: "", room: "", type: "break" },
      { time: "10:30 - 11:15", subject: "Mathematics", teacher: "Dr. Sarah Johnson", room: "Room 101", type: "regular" },
      { time: "11:15 - 12:00", subject: "Computer Science", teacher: "Mr. David Chen", room: "Computer Lab", type: "regular" },
      { time: "12:00 - 12:45", subject: "Biology", teacher: "Dr. Maria Garcia", room: "Biology Lab", type: "regular" },
      { time: "12:45 - 13:30", subject: "Lunch Break", teacher: "", room: "", type: "break" },
      { time: "13:30 - 14:15", subject: "History", teacher: "Dr. Robert Brown", room: "Room 301", type: "regular" },
      { time: "14:15 - 15:00", subject: "Art", teacher: "Ms. Anna Smith", room: "Art Room", type: "regular" },
      { time: "15:00 - 15:45", subject: "Assembly", teacher: "Principal", room: "Main Hall", type: "assembly" },
    ],
    Saturday: [
      { time: "08:00 - 08:45", subject: "Mathematics", teacher: "Dr. Sarah Johnson", room: "Room 101", type: "regular" },
      { time: "08:45 - 09:30", subject: "Science Project", teacher: "Science Teachers", room: "Labs", type: "project" },
      { time: "09:30 - 10:15", subject: "English", teacher: "Ms. Lisa Wilson", room: "Room 205", type: "regular" },
      { time: "10:15 - 10:30", subject: "Break", teacher: "", room: "", type: "break" },
      { time: "10:30 - 11:15", subject: "Sports", teacher: "Mr. James Wilson", room: "Sports Ground", type: "regular" },
      { time: "11:15 - 12:00", subject: "Club Activities", teacher: "Club Mentors", room: "Various", type: "activity" },
      { time: "12:00 - 12:45", subject: "Free Period", teacher: "", room: "", type: "free" },
      { time: "12:45 - 13:30", subject: "Lunch Break", teacher: "", room: "", type: "break" },
      { time: "13:30 - 14:15", subject: "Free Period", teacher: "", room: "", type: "free" },
      { time: "14:15 - 15:00", subject: "Free Period", teacher: "", room: "", type: "free" },
      { time: "15:00 - 15:45", subject: "Free Period", teacher: "", room: "", type: "free" },
    ],
  };

  const upcomingClasses = [
    { time: "08:00 AM", subject: "Mathematics", teacher: "Dr. Sarah Johnson", room: "Room 101", day: "Tomorrow" },
    { time: "08:45 AM", subject: "Physics", teacher: "Prof. Michael Smith", room: "Physics Lab", day: "Tomorrow" },
    { time: "09:30 AM", subject: "Chemistry", teacher: "Dr. Emily Brown", room: "Chemistry Lab", day: "Tomorrow" },
  ];

  const importantEvents = [
    { date: "2024-01-25", event: "Mathematics Test", type: "exam", time: "09:00 AM" },
    { date: "2024-01-28", event: "Science Fair", type: "event", time: "10:00 AM" },
    { date: "2024-02-01", event: "Parent-Teacher Meeting", type: "meeting", time: "02:00 PM" },
  ];

  const getClassTypeColor = (type: string) => {
    switch (type) {
      case "regular": return "bg-blue-100 text-blue-800 border-blue-200";
      case "break": return "bg-gray-100 text-gray-600 border-gray-200";
      case "free": return "bg-green-100 text-green-700 border-green-200";
      case "study": return "bg-purple-100 text-purple-700 border-purple-200";
      case "project": return "bg-orange-100 text-orange-700 border-orange-200";
      case "activity": return "bg-pink-100 text-pink-700 border-pink-200";
      case "assembly": return "bg-yellow-100 text-yellow-700 border-yellow-200";
      default: return "bg-gray-100 text-gray-600 border-gray-200";
    }
  };

  const getClassTypeIcon = (type: string) => {
    switch (type) {
      case "regular": return <BookOpen className="h-3 w-3" />;
      case "break": return <Clock className="h-3 w-3" />;
      case "free": return <CheckCircle className="h-3 w-3" />;
      case "study": return <BookOpen className="h-3 w-3" />;
      case "project": return <AlertCircle className="h-3 w-3" />;
      case "activity": return <User className="h-3 w-3" />;
      case "assembly": return <Bell className="h-3 w-3" />;
      default: return <Clock className="h-3 w-3" />;
    }
  };

  const getEventTypeColor = (type: string) => {
    switch (type) {
      case "exam": return "text-red-600 bg-red-100";
      case "event": return "text-blue-600 bg-blue-100";
      case "meeting": return "text-purple-600 bg-purple-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const getCurrentWeekDates = () => {
    const today = new Date();
    const currentDay = today.getDay();
    const monday = new Date(today);
    monday.setDate(today.getDate() - currentDay + 1 + (selectedWeek * 7));

    return weekDays.map((_, index) => {
      const date = new Date(monday);
      date.setDate(monday.getDate() + index);
      return date.toLocaleDateString('en-IN', { day: '2-digit', month: 'short' });
    });
  };

  const weekDates = getCurrentWeekDates();

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Class Timetable</h1>
            <p className="text-gray-600">View your child&apos;s class schedule and upcoming events</p>
          </div>
          <div className="flex gap-2">
            <select
              value={selectedChild}
              onChange={(e) => setSelectedChild(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {children.map((child) => (
                <option key={child.id} value={child.id}>
                  {child.name} - {child.class}
                </option>
              ))}
            </select>
            <Button variant="outline">
              <Printer className="h-4 w-4 mr-2" />
              Print
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Download
            </Button>
          </div>
        </div>

        {/* Student Overview */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-6">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                <User className="h-8 w-8 text-blue-600" />
              </div>
              <div className="flex-1">
                <h2 className="text-xl font-bold text-gray-900">{selectedChildData.name}</h2>
                <p className="text-gray-600">{selectedChildData.class} • Section {selectedChildData.section} • Roll No: {selectedChildData.rollNo}</p>
              </div>
              <div className="grid gap-4 md:grid-cols-3 text-center">
                <div>
                  <div className="text-2xl font-bold text-blue-600">6</div>
                  <div className="text-sm text-gray-500">Subjects Today</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-green-600">8:00 AM</div>
                  <div className="text-sm text-gray-500">First Class</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-purple-600">3:45 PM</div>
                  <div className="text-sm text-gray-500">Last Class</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Week Navigation */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSelectedWeek(selectedWeek - 1)}
                >
                  <ChevronLeft className="h-4 w-4" />
                </Button>
                <div className="text-lg font-semibold">
                  {selectedWeek === 0 ? "Current Week" :
                   selectedWeek > 0 ? `${selectedWeek} Week${selectedWeek > 1 ? 's' : ''} Ahead` :
                   `${Math.abs(selectedWeek)} Week${Math.abs(selectedWeek) > 1 ? 's' : ''} Ago`}
                </div>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSelectedWeek(selectedWeek + 1)}
                >
                  <ChevronRight className="h-4 w-4" />
                </Button>
              </div>
              <div className="flex gap-2">
                <Button
                  variant={selectedView === "week" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedView("week")}
                >
                  Week View
                </Button>
                <Button
                  variant={selectedView === "day" ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedView("day")}
                >
                  Day View
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Main Timetable */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              Weekly Timetable - {selectedChildData.class}
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr>
                    <th className="border border-gray-300 p-3 bg-gray-50 text-left font-medium text-gray-900 min-w-[120px]">
                      Time
                    </th>
                    {weekDays.map((day, index) => (
                      <th key={day} className="border border-gray-300 p-3 bg-gray-50 text-center font-medium text-gray-900 min-w-[150px]">
                        <div>{day}</div>
                        <div className="text-sm font-normal text-gray-500">{weekDates[index]}</div>
                      </th>
                    ))}
                  </tr>
                </thead>
                <tbody>
                  {timeSlots.map((timeSlot, timeIndex) => (
                    <tr key={timeSlot}>
                      <td className="border border-gray-300 p-3 bg-gray-50 font-medium text-gray-700">
                        {timeSlot}
                      </td>
                      {weekDays.map((day) => {
                        const classData = timetableData[day as keyof typeof timetableData]?.[timeIndex];
                        return (
                          <td key={`${day}-${timeIndex}`} className="border border-gray-300 p-2">
                            {classData && classData.subject !== "Break" && classData.subject !== "Lunch Break" ? (
                              <div className={`p-2 rounded-lg border ${getClassTypeColor(classData.type)}`}>
                                <div className="flex items-center space-x-1 mb-1">
                                  {getClassTypeIcon(classData.type)}
                                  <span className="font-medium text-sm">{classData.subject}</span>
                                </div>
                                {classData.teacher && (
                                  <div className="text-xs opacity-80 flex items-center">
                                    <User className="h-3 w-3 mr-1" />
                                    {classData.teacher}
                                  </div>
                                )}
                                {classData.room && (
                                  <div className="text-xs opacity-70 flex items-center">
                                    <MapPin className="h-3 w-3 mr-1" />
                                    {classData.room}
                                  </div>
                                )}
                              </div>
                            ) : classData ? (
                              <div className={`p-2 rounded-lg border text-center ${getClassTypeColor(classData.type)}`}>
                                <div className="flex items-center justify-center space-x-1">
                                  {getClassTypeIcon(classData.type)}
                                  <span className="font-medium text-sm">{classData.subject}</span>
                                </div>
                              </div>
                            ) : (
                              <div className="p-2 text-center text-gray-400 text-sm">-</div>
                            )}
                          </td>
                        );
                      })}
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Sidebar Information */}
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Upcoming Classes */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Clock className="h-5 w-5 mr-2" />
                Upcoming Classes
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {upcomingClasses.map((classItem, index) => (
                  <div key={index} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                    <div className="flex items-center space-x-4">
                      <div className="text-center">
                        <div className="font-bold text-blue-600">{classItem.time}</div>
                        <div className="text-xs text-gray-500">{classItem.day}</div>
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">{classItem.subject}</div>
                        <div className="text-sm text-gray-600">{classItem.teacher}</div>
                      </div>
                    </div>
                    <div className="text-sm text-gray-500 flex items-center">
                      <MapPin className="h-3 w-3 mr-1" />
                      {classItem.room}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Important Events */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Bell className="h-5 w-5 mr-2" />
                Important Events
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {importantEvents.map((event, index) => (
                  <div key={index} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-gray-900">{event.event}</h4>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getEventTypeColor(event.type)}`}>
                        {event.type}
                      </span>
                    </div>
                    <div className="flex items-center justify-between text-sm text-gray-600">
                      <span className="flex items-center">
                        <Calendar className="h-3 w-3 mr-1" />
                        {event.date}
                      </span>
                      <span className="flex items-center">
                        <Clock className="h-3 w-3 mr-1" />
                        {event.time}
                      </span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Class Summary */}
        <Card>
          <CardHeader>
            <CardTitle>Weekly Summary</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3 lg:grid-cols-6">
              {[
                { subject: "Mathematics", classes: 5, teacher: "Dr. Sarah Johnson" },
                { subject: "Physics", classes: 4, teacher: "Prof. Michael Smith" },
                { subject: "Chemistry", classes: 4, teacher: "Dr. Emily Brown" },
                { subject: "English", classes: 4, teacher: "Ms. Lisa Wilson" },
                { subject: "Computer Science", classes: 3, teacher: "Mr. David Chen" },
                { subject: "Biology", classes: 3, teacher: "Dr. Maria Garcia" },
              ].map((subject, index) => (
                <div key={index} className="p-4 border rounded-lg text-center">
                  <div className="font-medium text-gray-900 mb-1">{subject.subject}</div>
                  <div className="text-2xl font-bold text-blue-600 mb-1">{subject.classes}</div>
                  <div className="text-xs text-gray-500">classes/week</div>
                  <div className="text-xs text-gray-600 mt-2">{subject.teacher}</div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
