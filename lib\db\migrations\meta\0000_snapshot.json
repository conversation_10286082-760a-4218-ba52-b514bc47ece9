{"id": "345750f0-f6cb-49fb-b210-59c73919096d", "prevId": "00000000-0000-0000-0000-000000000000", "version": "7", "dialect": "postgresql", "tables": {"public.assignment_submissions": {"name": "assignment_submissions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "assignment_id": {"name": "assignment_id", "type": "uuid", "primaryKey": false, "notNull": true}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": true}, "submission_text": {"name": "submission_text", "type": "text", "primaryKey": false, "notNull": false}, "attachments": {"name": "attachments", "type": "text", "primaryKey": false, "notNull": false}, "submitted_at": {"name": "submitted_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "obtained_marks": {"name": "obtained_marks", "type": "integer", "primaryKey": false, "notNull": false}, "feedback": {"name": "feedback", "type": "text", "primaryKey": false, "notNull": false}, "graded_by": {"name": "graded_by", "type": "uuid", "primaryKey": false, "notNull": false}, "graded_at": {"name": "graded_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'submitted'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"assignment_submissions_assignment_id_assignments_id_fk": {"name": "assignment_submissions_assignment_id_assignments_id_fk", "tableFrom": "assignment_submissions", "tableTo": "assignments", "columnsFrom": ["assignment_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "assignment_submissions_student_id_students_id_fk": {"name": "assignment_submissions_student_id_students_id_fk", "tableFrom": "assignment_submissions", "tableTo": "students", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "assignment_submissions_graded_by_teachers_id_fk": {"name": "assignment_submissions_graded_by_teachers_id_fk", "tableFrom": "assignment_submissions", "tableTo": "teachers", "columnsFrom": ["graded_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.assignments": {"name": "assignments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "class_id": {"name": "class_id", "type": "uuid", "primaryKey": false, "notNull": true}, "teacher_id": {"name": "teacher_id", "type": "uuid", "primaryKey": false, "notNull": true}, "subject": {"name": "subject", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "total_marks": {"name": "total_marks", "type": "integer", "primaryKey": false, "notNull": true}, "due_date": {"name": "due_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "assigned_date": {"name": "assigned_date", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "instructions": {"name": "instructions", "type": "text", "primaryKey": false, "notNull": false}, "attachments": {"name": "attachments", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"assignments_class_id_classes_id_fk": {"name": "assignments_class_id_classes_id_fk", "tableFrom": "assignments", "tableTo": "classes", "columnsFrom": ["class_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "assignments_teacher_id_teachers_id_fk": {"name": "assignments_teacher_id_teachers_id_fk", "tableFrom": "assignments", "tableTo": "teachers", "columnsFrom": ["teacher_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.attendance": {"name": "attendance", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": true}, "class_id": {"name": "class_id", "type": "uuid", "primaryKey": false, "notNull": true}, "date": {"name": "date", "type": "date", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "attendance_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "remarks": {"name": "remarks", "type": "text", "primaryKey": false, "notNull": false}, "marked_by": {"name": "marked_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"attendance_student_id_students_id_fk": {"name": "attendance_student_id_students_id_fk", "tableFrom": "attendance", "tableTo": "students", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "attendance_class_id_classes_id_fk": {"name": "attendance_class_id_classes_id_fk", "tableFrom": "attendance", "tableTo": "classes", "columnsFrom": ["class_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "attendance_marked_by_users_id_fk": {"name": "attendance_marked_by_users_id_fk", "tableFrom": "attendance", "tableTo": "users", "columnsFrom": ["marked_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.book_issues": {"name": "book_issues", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "book_id": {"name": "book_id", "type": "uuid", "primaryKey": false, "notNull": true}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": false}, "teacher_id": {"name": "teacher_id", "type": "uuid", "primaryKey": false, "notNull": false}, "issue_date": {"name": "issue_date", "type": "date", "primaryKey": false, "notNull": true}, "due_date": {"name": "due_date", "type": "date", "primaryKey": false, "notNull": true}, "return_date": {"name": "return_date", "type": "date", "primaryKey": false, "notNull": false}, "fine_amount": {"name": "fine_amount", "type": "numeric(8, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "status": {"name": "status", "type": "issue_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'issued'"}, "issued_by": {"name": "issued_by", "type": "uuid", "primaryKey": false, "notNull": false}, "returned_by": {"name": "returned_by", "type": "uuid", "primaryKey": false, "notNull": false}, "remarks": {"name": "remarks", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"book_issues_book_id_books_id_fk": {"name": "book_issues_book_id_books_id_fk", "tableFrom": "book_issues", "tableTo": "books", "columnsFrom": ["book_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "book_issues_student_id_students_id_fk": {"name": "book_issues_student_id_students_id_fk", "tableFrom": "book_issues", "tableTo": "students", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "book_issues_teacher_id_teachers_id_fk": {"name": "book_issues_teacher_id_teachers_id_fk", "tableFrom": "book_issues", "tableTo": "teachers", "columnsFrom": ["teacher_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "book_issues_issued_by_users_id_fk": {"name": "book_issues_issued_by_users_id_fk", "tableFrom": "book_issues", "tableTo": "users", "columnsFrom": ["issued_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "book_issues_returned_by_users_id_fk": {"name": "book_issues_returned_by_users_id_fk", "tableFrom": "book_issues", "tableTo": "users", "columnsFrom": ["returned_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.books": {"name": "books", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(300)", "primaryKey": false, "notNull": true}, "author": {"name": "author", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "isbn": {"name": "isbn", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "publisher": {"name": "publisher", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": false}, "publication_year": {"name": "publication_year", "type": "integer", "primaryKey": false, "notNull": false}, "total_copies": {"name": "total_copies", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "available_copies": {"name": "available_copies", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "location": {"name": "location", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "price": {"name": "price", "type": "numeric(8, 2)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "book_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'available'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"books_isbn_unique": {"name": "books_isbn_unique", "nullsNotDistinct": false, "columns": ["isbn"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.class_enrollments": {"name": "class_enrollments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": true}, "class_id": {"name": "class_id", "type": "uuid", "primaryKey": false, "notNull": true}, "enrollment_date": {"name": "enrollment_date", "type": "date", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"class_enrollments_student_id_students_id_fk": {"name": "class_enrollments_student_id_students_id_fk", "tableFrom": "class_enrollments", "tableTo": "students", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "class_enrollments_class_id_classes_id_fk": {"name": "class_enrollments_class_id_classes_id_fk", "tableFrom": "class_enrollments", "tableTo": "classes", "columnsFrom": ["class_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.classes": {"name": "classes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "grade": {"name": "grade", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "section": {"name": "section", "type": "<PERSON><PERSON><PERSON>(5)", "primaryKey": false, "notNull": false}, "subject": {"name": "subject", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "teacher_id": {"name": "teacher_id", "type": "uuid", "primaryKey": false, "notNull": false}, "capacity": {"name": "capacity", "type": "integer", "primaryKey": false, "notNull": true, "default": 30}, "enrolled_students": {"name": "enrolled_students", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "academic_year": {"name": "academic_year", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "semester": {"name": "semester", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"classes_teacher_id_teachers_id_fk": {"name": "classes_teacher_id_teachers_id_fk", "tableFrom": "classes", "tableTo": "teachers", "columnsFrom": ["teacher_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.exams": {"name": "exams", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "exam_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "grade": {"name": "grade", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "subject": {"name": "subject", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "date": {"name": "date", "type": "date", "primaryKey": false, "notNull": true}, "start_time": {"name": "start_time", "type": "time", "primaryKey": false, "notNull": true}, "end_time": {"name": "end_time", "type": "time", "primaryKey": false, "notNull": true}, "total_marks": {"name": "total_marks", "type": "integer", "primaryKey": false, "notNull": true}, "passing_marks": {"name": "passing_marks", "type": "integer", "primaryKey": false, "notNull": true}, "room": {"name": "room", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "invigilator": {"name": "invigilator", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "instructions": {"name": "instructions", "type": "text", "primaryKey": false, "notNull": false}, "academic_year": {"name": "academic_year", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "semester": {"name": "semester", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "exam_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'scheduled'"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"exams_created_by_users_id_fk": {"name": "exams_created_by_users_id_fk", "tableFrom": "exams", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.fee_payments": {"name": "fee_payments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": true}, "fee_structure_id": {"name": "fee_structure_id", "type": "uuid", "primaryKey": false, "notNull": true}, "receipt_number": {"name": "receipt_number", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "amount_paid": {"name": "amount_paid", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "payment_method": {"name": "payment_method", "type": "payment_method", "typeSchema": "public", "primaryKey": false, "notNull": true}, "payment_date": {"name": "payment_date", "type": "date", "primaryKey": false, "notNull": true}, "transaction_id": {"name": "transaction_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "remarks": {"name": "remarks", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "payment_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'completed'"}, "processed_by": {"name": "processed_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"fee_payments_student_id_students_id_fk": {"name": "fee_payments_student_id_students_id_fk", "tableFrom": "fee_payments", "tableTo": "students", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "fee_payments_fee_structure_id_fee_structures_id_fk": {"name": "fee_payments_fee_structure_id_fee_structures_id_fk", "tableFrom": "fee_payments", "tableTo": "fee_structures", "columnsFrom": ["fee_structure_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "fee_payments_processed_by_users_id_fk": {"name": "fee_payments_processed_by_users_id_fk", "tableFrom": "fee_payments", "tableTo": "users", "columnsFrom": ["processed_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"fee_payments_receipt_number_unique": {"name": "fee_payments_receipt_number_unique", "nullsNotDistinct": false, "columns": ["receipt_number"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.fee_structures": {"name": "fee_structures", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "grade": {"name": "grade", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "academic_year": {"name": "academic_year", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "tuition_fee": {"name": "tuition_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "admission_fee": {"name": "admission_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "exam_fee": {"name": "exam_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "library_fee": {"name": "library_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "transport_fee": {"name": "transport_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "hostel_fee": {"name": "hostel_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "miscellaneous_fee": {"name": "miscellaneous_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "total_fee": {"name": "total_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "due_date": {"name": "due_date", "type": "date", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.grades": {"name": "grades", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": true}, "class_id": {"name": "class_id", "type": "uuid", "primaryKey": false, "notNull": true}, "exam_type": {"name": "exam_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "subject": {"name": "subject", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "total_marks": {"name": "total_marks", "type": "integer", "primaryKey": false, "notNull": true}, "obtained_marks": {"name": "obtained_marks", "type": "integer", "primaryKey": false, "notNull": true}, "grade": {"name": "grade", "type": "grade", "typeSchema": "public", "primaryKey": false, "notNull": false}, "percentage": {"name": "percentage", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "exam_date": {"name": "exam_date", "type": "date", "primaryKey": false, "notNull": false}, "graded_by": {"name": "graded_by", "type": "uuid", "primaryKey": false, "notNull": false}, "remarks": {"name": "remarks", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"grades_student_id_students_id_fk": {"name": "grades_student_id_students_id_fk", "tableFrom": "grades", "tableTo": "students", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "grades_class_id_classes_id_fk": {"name": "grades_class_id_classes_id_fk", "tableFrom": "grades", "tableTo": "classes", "columnsFrom": ["class_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "grades_graded_by_teachers_id_fk": {"name": "grades_graded_by_teachers_id_fk", "tableFrom": "grades", "tableTo": "teachers", "columnsFrom": ["graded_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.hostel_allocations": {"name": "hostel_allocations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": true}, "room_id": {"name": "room_id", "type": "uuid", "primaryKey": false, "notNull": true}, "allocation_date": {"name": "allocation_date", "type": "date", "primaryKey": false, "notNull": true}, "vacation_date": {"name": "vacation_date", "type": "date", "primaryKey": false, "notNull": false}, "monthly_fee": {"name": "monthly_fee", "type": "numeric(8, 2)", "primaryKey": false, "notNull": true}, "security_deposit": {"name": "security_deposit", "type": "numeric(8, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "allocated_by": {"name": "allocated_by", "type": "uuid", "primaryKey": false, "notNull": false}, "remarks": {"name": "remarks", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"hostel_allocations_student_id_students_id_fk": {"name": "hostel_allocations_student_id_students_id_fk", "tableFrom": "hostel_allocations", "tableTo": "students", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "hostel_allocations_room_id_hostel_rooms_id_fk": {"name": "hostel_allocations_room_id_hostel_rooms_id_fk", "tableFrom": "hostel_allocations", "tableTo": "hostel_rooms", "columnsFrom": ["room_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "hostel_allocations_allocated_by_users_id_fk": {"name": "hostel_allocations_allocated_by_users_id_fk", "tableFrom": "hostel_allocations", "tableTo": "users", "columnsFrom": ["allocated_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.hostel_rooms": {"name": "hostel_rooms", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "hostel_id": {"name": "hostel_id", "type": "uuid", "primaryKey": false, "notNull": true}, "room_number": {"name": "room_number", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "room_type": {"name": "room_type", "type": "room_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "floor": {"name": "floor", "type": "integer", "primaryKey": false, "notNull": false}, "capacity": {"name": "capacity", "type": "integer", "primaryKey": false, "notNull": true}, "occupied_beds": {"name": "occupied_beds", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "monthly_rent": {"name": "monthly_rent", "type": "numeric(8, 2)", "primaryKey": false, "notNull": true}, "facilities": {"name": "facilities", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "room_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'available'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"hostel_rooms_hostel_id_hostels_id_fk": {"name": "hostel_rooms_hostel_id_hostels_id_fk", "tableFrom": "hostel_rooms", "tableTo": "hostels", "columnsFrom": ["hostel_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.hostels": {"name": "hostels", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "hostel_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": true}, "warden_name": {"name": "warden_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "warden_phone": {"name": "warden_phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "warden_email": {"name": "warden_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "total_rooms": {"name": "total_rooms", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "total_beds": {"name": "total_beds", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "facilities": {"name": "facilities", "type": "text", "primaryKey": false, "notNull": false}, "monthly_fee": {"name": "monthly_fee", "type": "numeric(8, 2)", "primaryKey": false, "notNull": true}, "security_deposit": {"name": "security_deposit", "type": "numeric(8, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.student_transport": {"name": "student_transport", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": true}, "route_id": {"name": "route_id", "type": "uuid", "primaryKey": false, "notNull": true}, "pickup_point": {"name": "pickup_point", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": false}, "monthly_fee": {"name": "monthly_fee", "type": "numeric(8, 2)", "primaryKey": false, "notNull": true}, "start_date": {"name": "start_date", "type": "date", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "date", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"student_transport_student_id_students_id_fk": {"name": "student_transport_student_id_students_id_fk", "tableFrom": "student_transport", "tableTo": "students", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "student_transport_route_id_transport_routes_id_fk": {"name": "student_transport_route_id_transport_routes_id_fk", "tableFrom": "student_transport", "tableTo": "transport_routes", "columnsFrom": ["route_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.students": {"name": "students", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "roll_number": {"name": "roll_number", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "grade": {"name": "grade", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "section": {"name": "section", "type": "<PERSON><PERSON><PERSON>(5)", "primaryKey": false, "notNull": false}, "date_of_birth": {"name": "date_of_birth", "type": "date", "primaryKey": false, "notNull": true}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "parent_name": {"name": "parent_name", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": false}, "parent_phone": {"name": "parent_phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "parent_email": {"name": "parent_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "admission_date": {"name": "admission_date", "type": "date", "primaryKey": false, "notNull": true}, "blood_group": {"name": "blood_group", "type": "<PERSON><PERSON><PERSON>(5)", "primaryKey": false, "notNull": false}, "emergency_contact": {"name": "emergency_contact", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "medical_info": {"name": "medical_info", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"students_user_id_users_id_fk": {"name": "students_user_id_users_id_fk", "tableFrom": "students", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"students_roll_number_unique": {"name": "students_roll_number_unique", "nullsNotDistinct": false, "columns": ["roll_number"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.teachers": {"name": "teachers", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "employee_id": {"name": "employee_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "subject": {"name": "subject", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "qualification": {"name": "qualification", "type": "text", "primaryKey": false, "notNull": false}, "experience": {"name": "experience", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "joining_date": {"name": "joining_date", "type": "date", "primaryKey": false, "notNull": true}, "salary": {"name": "salary", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "department": {"name": "department", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"teachers_user_id_users_id_fk": {"name": "teachers_user_id_users_id_fk", "tableFrom": "teachers", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"teachers_employee_id_unique": {"name": "teachers_employee_id_unique", "nullsNotDistinct": false, "columns": ["employee_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.timetables": {"name": "timetables", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "class_id": {"name": "class_id", "type": "uuid", "primaryKey": false, "notNull": true}, "teacher_id": {"name": "teacher_id", "type": "uuid", "primaryKey": false, "notNull": true}, "subject": {"name": "subject", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "day": {"name": "day", "type": "day", "typeSchema": "public", "primaryKey": false, "notNull": true}, "start_time": {"name": "start_time", "type": "time", "primaryKey": false, "notNull": true}, "end_time": {"name": "end_time", "type": "time", "primaryKey": false, "notNull": true}, "room": {"name": "room", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "academic_year": {"name": "academic_year", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "semester": {"name": "semester", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"timetables_class_id_classes_id_fk": {"name": "timetables_class_id_classes_id_fk", "tableFrom": "timetables", "tableTo": "classes", "columnsFrom": ["class_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "timetables_teacher_id_teachers_id_fk": {"name": "timetables_teacher_id_teachers_id_fk", "tableFrom": "timetables", "tableTo": "teachers", "columnsFrom": ["teacher_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.transport_routes": {"name": "transport_routes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "route_name": {"name": "route_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "route_code": {"name": "route_code", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "start_point": {"name": "start_point", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "end_point": {"name": "end_point", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "stops": {"name": "stops", "type": "text", "primaryKey": false, "notNull": false}, "distance": {"name": "distance", "type": "numeric(6, 2)", "primaryKey": false, "notNull": false}, "estimated_time": {"name": "estimated_time", "type": "integer", "primaryKey": false, "notNull": false}, "fee": {"name": "fee", "type": "numeric(8, 2)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"transport_routes_route_code_unique": {"name": "transport_routes_route_code_unique", "nullsNotDistinct": false, "columns": ["route_code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "user_role", "typeSchema": "public", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "last_login": {"name": "last_login", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.vehicles": {"name": "vehicles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "vehicle_number": {"name": "vehicle_number", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "vehicle_type": {"name": "vehicle_type", "type": "vehicle_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "capacity": {"name": "capacity", "type": "integer", "primaryKey": false, "notNull": true}, "route_id": {"name": "route_id", "type": "uuid", "primaryKey": false, "notNull": false}, "driver_name": {"name": "driver_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "driver_phone": {"name": "driver_phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "driver_license": {"name": "driver_license", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "insurance_number": {"name": "insurance_number", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "insurance_expiry": {"name": "insurance_expiry", "type": "date", "primaryKey": false, "notNull": false}, "permit_number": {"name": "permit_number", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "permit_expiry": {"name": "permit_expiry", "type": "date", "primaryKey": false, "notNull": false}, "last_maintenance": {"name": "last_maintenance", "type": "date", "primaryKey": false, "notNull": false}, "next_maintenance": {"name": "next_maintenance", "type": "date", "primaryKey": false, "notNull": false}, "fuel_type": {"name": "fuel_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "vehicle_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"vehicles_route_id_transport_routes_id_fk": {"name": "vehicles_route_id_transport_routes_id_fk", "tableFrom": "vehicles", "tableTo": "transport_routes", "columnsFrom": ["route_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"vehicles_vehicle_number_unique": {"name": "vehicles_vehicle_number_unique", "nullsNotDistinct": false, "columns": ["vehicle_number"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.attendance_status": {"name": "attendance_status", "schema": "public", "values": ["present", "absent", "late", "excused"]}, "public.book_status": {"name": "book_status", "schema": "public", "values": ["available", "issued", "maintenance", "lost"]}, "public.day": {"name": "day", "schema": "public", "values": ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]}, "public.exam_status": {"name": "exam_status", "schema": "public", "values": ["scheduled", "ongoing", "completed", "cancelled"]}, "public.exam_type": {"name": "exam_type", "schema": "public", "values": ["midterm", "final", "unit_test", "practical", "oral"]}, "public.grade": {"name": "grade", "schema": "public", "values": ["A+", "A", "A-", "B+", "B", "B-", "C+", "C", "C-", "D+", "D", "F"]}, "public.hostel_type": {"name": "hostel_type", "schema": "public", "values": ["boys", "girls", "mixed"]}, "public.issue_status": {"name": "issue_status", "schema": "public", "values": ["issued", "returned", "overdue"]}, "public.payment_method": {"name": "payment_method", "schema": "public", "values": ["cash", "card", "bank_transfer", "online", "cheque"]}, "public.payment_status": {"name": "payment_status", "schema": "public", "values": ["pending", "completed", "failed", "refunded"]}, "public.room_status": {"name": "room_status", "schema": "public", "values": ["available", "occupied", "maintenance"]}, "public.room_type": {"name": "room_type", "schema": "public", "values": ["single", "double", "triple", "dormitory"]}, "public.status": {"name": "status", "schema": "public", "values": ["active", "inactive", "suspended"]}, "public.user_role": {"name": "user_role", "schema": "public", "values": ["super_admin", "admin", "teacher", "student", "parent", "admission_officer", "finance_manager", "librarian", "transport_manager", "hostel_manager"]}, "public.vehicle_status": {"name": "vehicle_status", "schema": "public", "values": ["active", "maintenance", "inactive"]}, "public.vehicle_type": {"name": "vehicle_type", "schema": "public", "values": ["bus", "van", "car"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}