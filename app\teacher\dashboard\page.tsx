"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  BookOpen,
  Users,
  Calendar,
  Clock,
  CheckCircle,
  AlertTriangle,
  FileText,
  Award,
  TrendingUp,
  Plus
} from "lucide-react";

export default function TeacherDashboard() {
  const [user, setUser] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "teacher") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock teacher data
  const teacherData = {
    totalClasses: 5,
    totalStudents: 156,
    pendingGrades: 12,
    upcomingClasses: 3,
    attendanceRate: 94,
    averageGrade: 85,
  };

  const todaySchedule = [
    { time: "09:00 - 10:00", subject: "Mathematics", class: "10A", room: "Room 101", students: 32 },
    { time: "10:00 - 11:00", subject: "Mathematics", class: "10B", room: "Room 101", students: 28 },
    { time: "14:00 - 15:00", subject: "Advanced Math", class: "11A", room: "Room 101", students: 25 },
  ];

  const recentActivities = [
    {
      type: "grade",
      message: "Graded Mathematics test for Class 10A",
      time: "2 hours ago",
      icon: Award,
      color: "text-green-600",
    },
    {
      type: "attendance",
      message: "Marked attendance for Class 10B",
      time: "4 hours ago",
      icon: CheckCircle,
      color: "text-blue-600",
    },
    {
      type: "assignment",
      message: "Created new assignment for Class 11A",
      time: "1 day ago",
      icon: FileText,
      color: "text-purple-600",
    },
    {
      type: "class",
      message: "Completed Physics practical session",
      time: "2 days ago",
      icon: BookOpen,
      color: "text-orange-600",
    },
  ];

  const pendingTasks = [
    {
      task: "Grade Chemistry test papers",
      class: "Class 10A",
      dueDate: "Today",
      priority: "high",
    },
    {
      task: "Prepare lesson plan for next week",
      class: "Class 10B",
      dueDate: "Tomorrow",
      priority: "medium",
    },
    {
      task: "Submit monthly progress report",
      class: "All Classes",
      dueDate: "Jan 25",
      priority: "low",
    },
  ];

  const classPerformance = [
    { class: "10A", averageGrade: 87, attendance: 95, totalStudents: 32 },
    { class: "10B", averageGrade: 82, attendance: 92, totalStudents: 28 },
    { class: "11A", averageGrade: 89, attendance: 96, totalStudents: 25 },
  ];

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Welcome Header */}
        <div className="bg-gradient-to-r from-green-500 to-blue-600 rounded-lg p-6 text-white">
          <h1 className="text-2xl font-bold mb-2">
            Welcome back, {user.firstName}!
          </h1>
          <p className="text-green-100">
            Ready to inspire and educate your students today?
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <BookOpen className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {teacherData.totalClasses}
                  </div>
                  <p className="text-sm text-gray-600">My Classes</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {teacherData.totalStudents}
                  </div>
                  <p className="text-sm text-gray-600">Total Students</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <AlertTriangle className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    {teacherData.pendingGrades}
                  </div>
                  <p className="text-sm text-gray-600">Pending Grades</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <TrendingUp className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {teacherData.averageGrade}%
                  </div>
                  <p className="text-sm text-gray-600">Avg. Grade</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Grid */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Today's Schedule */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center">
                  <Clock className="h-5 w-5 mr-2" />
                  Today&apos;s Schedule
                </CardTitle>
                <Button variant="outline" size="sm">
                  View Full Timetable
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {todaySchedule.map((class_, index) => (
                  <div key={index} className="flex items-center p-4 bg-gray-50 rounded-lg">
                    <div className="flex-shrink-0 w-28 text-sm font-medium text-gray-600">
                      {class_.time}
                    </div>
                    <div className="ml-4 flex-1">
                      <div className="font-medium text-gray-900">{class_.subject}</div>
                      <div className="text-sm text-gray-500">
                        {class_.class} • {class_.room} • {class_.students} students
                      </div>
                    </div>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        Attendance
                      </Button>
                      <Button variant="outline" size="sm">
                        Materials
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button className="w-full justify-start" variant="outline">
                <CheckCircle className="h-4 w-4 mr-2" />
                Mark Attendance
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <Award className="h-4 w-4 mr-2" />
                Enter Grades
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <Plus className="h-4 w-4 mr-2" />
                Create Assignment
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <FileText className="h-4 w-4 mr-2" />
                Lesson Plans
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <Calendar className="h-4 w-4 mr-2" />
                Schedule Meeting
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Class Performance and Pending Tasks */}
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Class Performance */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <TrendingUp className="h-5 w-5 mr-2" />
                Class Performance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {classPerformance.map((classData, index) => (
                  <div key={index} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <div>
                        <h3 className="font-medium text-gray-900">Class {classData.class}</h3>
                        <p className="text-sm text-gray-500">{classData.totalStudents} students</p>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold text-blue-600">
                          {classData.averageGrade}%
                        </div>
                        <div className="text-sm text-gray-500">Avg. Grade</div>
                      </div>
                    </div>

                    <div className="space-y-2">
                      <div className="flex items-center justify-between text-sm">
                        <span>Academic Performance</span>
                        <span className="font-medium">{classData.averageGrade}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: `${classData.averageGrade}%` }}
                        />
                      </div>

                      <div className="flex items-center justify-between text-sm">
                        <span>Attendance Rate</span>
                        <span className="font-medium">{classData.attendance}%</span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-green-600 h-2 rounded-full"
                          style={{ width: `${classData.attendance}%` }}
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Pending Tasks */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertTriangle className="h-5 w-5 mr-2" />
                Pending Tasks
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {pendingTasks.map((task, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div className="font-medium text-gray-900">{task.task}</div>
                      <div className="text-sm text-gray-500">{task.class}</div>
                    </div>
                    <div className="text-right">
                      <div className={`text-sm font-medium ${
                        task.priority === "high" ? "text-red-600" :
                        task.priority === "medium" ? "text-yellow-600" : "text-green-600"
                      }`}>
                        Due: {task.dueDate}
                      </div>
                      <div className={`text-xs px-2 py-1 rounded-full ${
                        task.priority === "high" ? "bg-red-100 text-red-600" :
                        task.priority === "medium" ? "bg-yellow-100 text-yellow-600" : "bg-green-100 text-green-600"
                      }`}>
                        {task.priority.toUpperCase()}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activities */}
        <Card>
          <CardHeader>
            <CardTitle>Recent Activities</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {recentActivities.map((activity, index) => (
                <div key={index} className="flex items-center p-3 hover:bg-gray-50 rounded-lg transition-colors">
                  <div className={`p-2 rounded-full bg-gray-100 ${activity.color}`}>
                    <activity.icon className="h-4 w-4" />
                  </div>
                  <div className="ml-4 flex-1">
                    <div className="text-sm font-medium text-gray-900">
                      {activity.message}
                    </div>
                    <div className="text-xs text-gray-500">{activity.time}</div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Teaching Statistics */}
        <Card>
          <CardHeader>
            <CardTitle>Teaching Statistics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 md:grid-cols-3">
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">
                  {teacherData.attendanceRate}%
                </div>
                <div className="text-sm text-gray-600">Class Attendance Rate</div>
                <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-600 h-2 rounded-full w-[94%]" />
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 mb-2">
                  {teacherData.averageGrade}%
                </div>
                <div className="text-sm text-gray-600">Average Class Grade</div>
                <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-green-600 h-2 rounded-full w-[85%]" />
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600 mb-2">
                  {teacherData.totalClasses}
                </div>
                <div className="text-sm text-gray-600">Active Classes</div>
                <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-purple-600 h-2 rounded-full w-[100%]" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
