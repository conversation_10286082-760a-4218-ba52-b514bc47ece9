"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  IndianRupee,
  Calendar,
  CreditCard,
  Download,
  Eye,
  AlertTriangle,
  CheckCircle,
  Clock,
  Receipt,
  FileText,
  TrendingUp,
  Target,
  Banknote,
  History,
} from "lucide-react";

export default function StudentFees() {
  const [user, setUser] = useState<any>(null);
  const [selectedTab, setSelectedTab] = useState("overview");
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "student") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock student data
  const studentInfo = {
    name: "John Doe",
    rollNo: "10A001",
    class: "Grade 10A",
    academicYear: "2024-25",
    admissionNumber: "**********",
  };

  const feeOverview = {
    totalFees: 125000,
    paidAmount: 75000,
    pendingAmount: 50000,
    overdueAmount: 15000,
    nextDueDate: "2024-02-15",
    paymentStatus: "Partially Paid",
  };

  const feeStructure = [
    { category: "Tuition Fee", amount: 60000, paid: 40000, pending: 20000, dueDate: "2024-02-15", status: "Partial" },
    { category: "Development Fee", amount: 15000, paid: 15000, pending: 0, dueDate: "2024-04-15", status: "Paid" },
    { category: "Laboratory Fee", amount: 12000, paid: 8000, pending: 4000, dueDate: "2024-02-15", status: "Partial" },
    { category: "Library Fee", amount: 5000, paid: 5000, pending: 0, dueDate: "2024-04-15", status: "Paid" },
    { category: "Sports Fee", amount: 8000, paid: 0, pending: 8000, dueDate: "2024-01-30", status: "Overdue" },
    { category: "Transport Fee", amount: 18000, paid: 6000, pending: 12000, dueDate: "2024-02-15", status: "Partial" },
    { category: "Examination Fee", amount: 7000, paid: 1000, pending: 6000, dueDate: "2024-03-01", status: "Pending" },
  ];

  const paymentHistory = [
    { id: "PAY001", date: "2024-01-15", amount: 25000, method: "Online Banking", category: "Tuition Fee", status: "Success", receipt: "RCP001" },
    { id: "PAY002", date: "2024-01-10", amount: 15000, method: "UPI", category: "Development Fee", status: "Success", receipt: "RCP002" },
    { id: "PAY003", date: "2024-01-05", amount: 8000, method: "Credit Card", category: "Laboratory Fee", status: "Success", receipt: "RCP003" },
    { id: "PAY004", date: "2023-12-20", amount: 5000, method: "Cash", category: "Library Fee", status: "Success", receipt: "RCP004" },
    { id: "PAY005", date: "2023-12-15", amount: 15000, method: "Bank Transfer", category: "Tuition Fee", status: "Success", receipt: "RCP005" },
    { id: "PAY006", date: "2023-12-10", amount: 6000, method: "Online Banking", category: "Transport Fee", status: "Success", receipt: "RCP006" },
  ];

  const upcomingDues = [
    { category: "Sports Fee", amount: 8000, dueDate: "2024-01-30", daysLeft: -10, status: "Overdue" },
    { category: "Tuition Fee", amount: 20000, dueDate: "2024-02-15", daysLeft: 5, status: "Due Soon" },
    { category: "Laboratory Fee", amount: 4000, dueDate: "2024-02-15", daysLeft: 5, status: "Due Soon" },
    { category: "Transport Fee", amount: 12000, dueDate: "2024-02-15", daysLeft: 5, status: "Due Soon" },
  ];

  const paymentMethods = [
    { method: "Online Banking", icon: Banknote, description: "Pay through net banking" },
    { method: "UPI Payment", icon: CreditCard, description: "Pay using UPI apps" },
    { method: "Credit/Debit Card", icon: CreditCard, description: "Pay using cards" },
    { method: "Bank Transfer", icon: Banknote, description: "Direct bank transfer" },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Paid": return "text-green-600 bg-green-100";
      case "Partial": return "text-yellow-600 bg-yellow-100";
      case "Pending": return "text-blue-600 bg-blue-100";
      case "Overdue": return "text-red-600 bg-red-100";
      case "Success": return "text-green-600 bg-green-100";
      case "Failed": return "text-red-600 bg-red-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Paid": return <CheckCircle className="h-4 w-4" />;
      case "Success": return <CheckCircle className="h-4 w-4" />;
      case "Overdue": return <AlertTriangle className="h-4 w-4" />;
      case "Failed": return <AlertTriangle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const getDaysLeftColor = (daysLeft: number) => {
    if (daysLeft < 0) return "text-red-600";
    if (daysLeft <= 7) return "text-orange-600";
    return "text-green-600";
  };

  const calculatePercentagePaid = (paid: number, total: number) => {
    return ((paid / total) * 100).toFixed(1);
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Fee Management</h1>
            <p className="text-gray-600">View fee structure, payment history, and make payments</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Download Receipt
            </Button>
            <Button>
              <CreditCard className="h-4 w-4 mr-2" />
              Make Payment
            </Button>
          </div>
        </div>

        {/* Student Info & Fee Overview */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <IndianRupee className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    ₹{(feeOverview.totalFees / 1000).toFixed(0)}K
                  </div>
                  <p className="text-sm text-gray-600">Total Fees</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    ₹{(feeOverview.paidAmount / 1000).toFixed(0)}K
                  </div>
                  <p className="text-sm text-gray-600">Amount Paid</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600">
                      {calculatePercentagePaid(feeOverview.paidAmount, feeOverview.totalFees)}%
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    ₹{(feeOverview.pendingAmount / 1000).toFixed(0)}K
                  </div>
                  <p className="text-sm text-gray-600">Pending Amount</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <AlertTriangle className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-red-600">
                    ₹{(feeOverview.overdueAmount / 1000).toFixed(0)}K
                  </div>
                  <p className="text-sm text-gray-600">Overdue Amount</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Student Information */}
        <Card>
          <CardHeader>
            <CardTitle>Student Information</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <div>
                <div className="text-sm text-gray-500">Student Name</div>
                <div className="font-medium text-gray-900">{studentInfo.name}</div>
              </div>
              <div>
                <div className="text-sm text-gray-500">Roll Number</div>
                <div className="font-medium text-gray-900">{studentInfo.rollNo}</div>
              </div>
              <div>
                <div className="text-sm text-gray-500">Class</div>
                <div className="font-medium text-gray-900">{studentInfo.class}</div>
              </div>
              <div>
                <div className="text-sm text-gray-500">Academic Year</div>
                <div className="font-medium text-gray-900">{studentInfo.academicYear}</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Fee Tabs */}
        <Card>
          <CardHeader>
            <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
              {[
                { key: "overview", label: "Fee Structure" },
                { key: "history", label: "Payment History" },
                { key: "upcoming", label: "Upcoming Dues" },
                { key: "methods", label: "Payment Methods" },
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setSelectedTab(tab.key)}
                  className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                    selectedTab === tab.key
                      ? "bg-white text-blue-600 shadow-sm"
                      : "text-gray-600 hover:text-gray-900"
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </div>
          </CardHeader>
          <CardContent>
            {/* Fee Structure */}
            {selectedTab === "overview" && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Fee Structure - {studentInfo.academicYear}</h3>
                <div className="space-y-4">
                  {feeStructure.map((fee, index) => (
                    <div key={index} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <h4 className="font-medium text-gray-900">{fee.category}</h4>
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(fee.status)}`}>
                            {getStatusIcon(fee.status)}
                            <span className="ml-1">{fee.status}</span>
                          </span>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-bold text-blue-600">₹{fee.amount.toLocaleString()}</div>
                          <div className="text-sm text-gray-500">Due: {fee.dueDate}</div>
                        </div>
                      </div>
                      
                      <div className="grid gap-3 md:grid-cols-3 text-sm mb-3">
                        <div className="text-center p-3 bg-green-50 rounded-lg">
                          <div className="font-bold text-green-600">₹{fee.paid.toLocaleString()}</div>
                          <div className="text-gray-500">Paid</div>
                        </div>
                        <div className="text-center p-3 bg-orange-50 rounded-lg">
                          <div className="font-bold text-orange-600">₹{fee.pending.toLocaleString()}</div>
                          <div className="text-gray-500">Pending</div>
                        </div>
                        <div className="text-center p-3 bg-blue-50 rounded-lg">
                          <div className="font-bold text-blue-600">{calculatePercentagePaid(fee.paid, fee.amount)}%</div>
                          <div className="text-gray-500">Completed</div>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="w-full bg-gray-200 rounded-full h-2 mr-4">
                          <div 
                            className={`h-2 rounded-full ${
                              fee.status === 'Paid' ? 'bg-green-500' :
                              fee.status === 'Partial' ? 'bg-yellow-500' :
                              fee.status === 'Overdue' ? 'bg-red-500' : 'bg-blue-500'
                            }`}
                            style={{ width: `${calculatePercentagePaid(fee.paid, fee.amount)}%` }}
                          />
                        </div>
                        {fee.pending > 0 && (
                          <Button size="sm">
                            <CreditCard className="h-3 w-3 mr-1" />
                            Pay Now
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Payment History */}
            {selectedTab === "history" && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Payment History</h3>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Date</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Amount</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Category</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Method</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Receipt</th>
                      </tr>
                    </thead>
                    <tbody>
                      {paymentHistory.map((payment) => (
                        <tr key={payment.id} className="border-b hover:bg-gray-50">
                          <td className="py-3 px-4 text-gray-900">{payment.date}</td>
                          <td className="py-3 px-4 font-medium text-gray-900">₹{payment.amount.toLocaleString()}</td>
                          <td className="py-3 px-4 text-gray-600">{payment.category}</td>
                          <td className="py-3 px-4 text-gray-600">{payment.method}</td>
                          <td className="py-3 px-4">
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(payment.status)}`}>
                              {getStatusIcon(payment.status)}
                              <span className="ml-1">{payment.status}</span>
                            </span>
                          </td>
                          <td className="py-3 px-4">
                            <Button variant="outline" size="sm">
                              <Download className="h-3 w-3 mr-1" />
                              {payment.receipt}
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* Upcoming Dues */}
            {selectedTab === "upcoming" && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Upcoming Dues</h3>
                <div className="space-y-3">
                  {upcomingDues.map((due, index) => (
                    <div key={index} className={`p-4 border rounded-lg ${
                      due.status === 'Overdue' ? 'border-red-200 bg-red-50' : 
                      due.status === 'Due Soon' ? 'border-orange-200 bg-orange-50' : 'border-gray-200'
                    }`}>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div>
                            <div className="font-medium text-gray-900">{due.category}</div>
                            <div className="text-sm text-gray-600">Due: {due.dueDate}</div>
                          </div>
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(due.status)}`}>
                            {due.status}
                          </span>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-bold text-gray-900">₹{due.amount.toLocaleString()}</div>
                          <div className={`text-sm font-medium ${getDaysLeftColor(due.daysLeft)}`}>
                            {due.daysLeft < 0 ? `${Math.abs(due.daysLeft)} days overdue` : 
                             due.daysLeft === 0 ? 'Due today' : 
                             `${due.daysLeft} days left`}
                          </div>
                        </div>
                      </div>
                      <div className="mt-3 flex justify-end">
                        <Button size="sm" className={due.status === 'Overdue' ? 'bg-red-600 hover:bg-red-700' : ''}>
                          <CreditCard className="h-3 w-3 mr-1" />
                          Pay Now
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Payment Methods */}
            {selectedTab === "methods" && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Available Payment Methods</h3>
                <div className="grid gap-4 md:grid-cols-2">
                  {paymentMethods.map((method, index) => (
                    <div key={index} className="p-4 border rounded-lg hover:bg-gray-50 cursor-pointer">
                      <div className="flex items-center space-x-4">
                        <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                          <method.icon className="h-6 w-6 text-blue-600" />
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">{method.method}</div>
                          <div className="text-sm text-gray-600">{method.description}</div>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
                
                <div className="p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-medium text-blue-900 mb-2">Payment Instructions</h4>
                  <ul className="text-sm text-blue-700 space-y-1">
                    <li>• All payments are processed securely through our payment gateway</li>
                    <li>• Payment receipts will be generated automatically upon successful transaction</li>
                    <li>• For any payment issues, contact the finance office immediately</li>
                    <li>• Late payment charges may apply for overdue amounts</li>
                  </ul>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
