import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { client } from "@/lib/hono";
import { toast } from "sonner";
import { CreateUser, UpdateUser, BulkCreateUsers } from "@/lib/schemas";

// Get all users with filtering and pagination
export const useGetUsers = (params?: {
  page?: number;
  limit?: number;
  search?: string;
  role?: string;
  status?: string;
}) => {
  return useQuery({
    queryKey: ["users", params],
    queryFn: async () => {
      const searchParams = new URLSearchParams();
      
      if (params?.page) searchParams.append("page", params.page.toString());
      if (params?.limit) searchParams.append("limit", params.limit.toString());
      if (params?.search) searchParams.append("search", params.search);
      if (params?.role) searchParams.append("role", params.role);
      if (params?.status) searchParams.append("status", params.status);

      const response = await client.api.users.$get({
        query: Object.fromEntries(searchParams),
      });

      if (!response.ok) {
        throw new Error("Failed to fetch users");
      }

      return await response.json();
    },
  });
};

// Get user by ID
export const useGetUser = (id: string) => {
  return useQuery({
    queryKey: ["users", id],
    queryFn: async () => {
      const response = await client.api.users[":id"].$get({
        param: { id },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch user");
      }

      return await response.json();
    },
    enabled: !!id,
  });
};

// Create user mutation
export const useCreateUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (json: CreateUser) => {
      const response = await client.api.users.$post({ json });

      if (!response.ok) {
        const errorData = await response.json() as { error?: string };
        throw new Error(errorData.error || "Failed to create user");
      }

      return await response.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
      toast.success("User created successfully!");
      
      // Show generated password if applicable
      if (data.data.generatedPassword) {
        toast.info(`Generated password: ${data.data.generatedPassword}`, {
          duration: 10000,
        });
      }
    },
    onError: (error: any) => {
      toast.error(`Failed to create user: ${error.message}`);
    },
  });
};

// Update user mutation
export const useUpdateUser = (id: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (json: Partial<UpdateUser>) => {
      const response = await client.api.users[":id"].$put({
        param: { id },
        json,
      });

      if (!response.ok) {
        const errorData = await response.json() as { error?: string };
        throw new Error(errorData.error || "Failed to update user");
      }

      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
      queryClient.invalidateQueries({ queryKey: ["users", id] });
      toast.success("User updated successfully!");
    },
    onError: (error: any) => {
      toast.error(`Failed to update user: ${error.message}`);
    },
  });
};

// Delete (deactivate) user mutation
export const useDeleteUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await client.api.users[":id"].$delete({
        param: { id },
      });

      if (!response.ok) {
        const errorData = await response.json() as { error?: string };
        throw new Error(errorData.error || "Failed to deactivate user");
      }

      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
      toast.success("User deactivated successfully!");
    },
    onError: (error: any) => {
      toast.error(`Failed to deactivate user: ${error.message}`);
    },
  });
};

// Bulk create users mutation
export const useBulkCreateUsers = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (json: BulkCreateUsers) => {
      const response = await client.api.users.bulk.$post({ json });

      if (!response.ok) {
        const errorData = await response.json() as { error?: string };
        throw new Error(errorData.error || "Failed to bulk create users");
      }

      return await response.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
      
      const { summary } = data.data;
      toast.success(
        `Bulk operation completed: ${summary.successful} successful, ${summary.failed} failed`
      );
      
      // Show generated passwords if any
      if (data.data.results.generatedPasswords.length > 0) {
        console.log("Generated passwords:", data.data.results.generatedPasswords);
        toast.info(
          `${data.data.results.generatedPasswords.length} passwords generated. Check console for details.`,
          { duration: 10000 }
        );
      }
    },
    onError: (error: any) => {
      toast.error(`Failed to bulk create users: ${error.message}`);
    },
  });
};

// Get user statistics
export const useGetUserStats = () => {
  return useQuery({
    queryKey: ["users", "stats"],
    queryFn: async () => {
      const response = await client.api.users.$get({
        query: { limit: "1" }, // Just get count info
      });

      if (!response.ok) {
        throw new Error("Failed to fetch user stats");
      }

      const data = await response.json();
      return data.pagination;
    },
  });
};

// Get users by role
export const useGetUsersByRole = (role: string) => {
  return useQuery({
    queryKey: ["users", "role", role],
    queryFn: async () => {
      const response = await client.api.users.$get({
        query: { role, limit: "1000" }, // Get all users of this role
      });

      if (!response.ok) {
        throw new Error("Failed to fetch users by role");
      }

      return await response.json();
    },
    enabled: !!role,
  });
};

// Reset user password mutation
export const useResetUserPassword = (userId: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (json: { newPassword?: string; generatePassword?: boolean; sendNotification?: boolean }) => {
      const response = await client.api.users[":id"]["reset-password"].$post({
        param: { id: userId },
        json,
      });

      if (!response.ok) {
        const errorData = await response.json() as { error?: string };
        throw new Error(errorData.error || "Failed to reset password");
      }

      return await response.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
      queryClient.invalidateQueries({ queryKey: ["users", userId] });

      if (data.data.generatedPassword) {
        toast.success("Password reset successfully! New password generated.", {
          duration: 5000,
        });
        toast.info(`Generated password: ${data.data.generatedPassword}`, {
          duration: 10000,
        });
      } else {
        toast.success("Password reset successfully!");
      }
    },
    onError: (error: any) => {
      toast.error(`Failed to reset password: ${error.message}`);
    },
  });
};

// Bulk password reset mutation
export const useBulkPasswordReset = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (json: { userIds: string[]; generatePasswords?: boolean; sendNotifications?: boolean }) => {
      const response = await client.api.users["bulk-reset-password"].$post({ json });

      if (!response.ok) {
        const errorData = await response.json() as { error?: string };
        throw new Error(errorData.error || "Failed to bulk reset passwords");
      }

      return await response.json();
    },
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ["users"] });

      const results = data.data;
      toast.success(
        `Bulk password reset completed: ${results.successful?.length || 0} successful, ${results.failed?.length || 0} failed`
      );

      // Show generated passwords if any
      if (results.generatedPasswords?.length > 0) {
        console.log("Generated passwords:", results.generatedPasswords);
        toast.info(
          `${results.generatedPasswords.length} passwords generated. Check console for details.`,
          { duration: 10000 }
        );
      }
    },
    onError: (error: any) => {
      toast.error(`Failed to bulk reset passwords: ${error.message}`);
    },
  });
};
