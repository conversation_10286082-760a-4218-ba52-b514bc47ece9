"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  PieChart,
  BarChart3,
  TrendingUp,
  TrendingDown,
  Download,
  Filter,
  Calendar,
  IndianRupee,
  Users,
  CreditCard,
  FileText,
  Target,
  AlertTriangle,
  CheckCircle,
  Clock,
} from "lucide-react";

export default function FinanceReports() {
  const [user, setUser] = useState<any>(null);
  const [selectedPeriod, setSelectedPeriod] = useState("monthly");
  const [selectedReport, setSelectedReport] = useState("overview");
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "finance_manager" && parsedUser.role !== "admin" && parsedUser.role !== "super_admin") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock financial data
  const financialOverview = {
    totalRevenue: 2850000,
    totalExpenses: 1200000,
    netProfit: 1650000,
    collectionRate: 94.5,
    pendingAmount: 150000,
    overdueAmount: 75000,
    monthlyTarget: 2500000,
    yearlyTarget: 30000000,
  };

  const monthlyRevenue = [
    { month: "Aug", revenue: 2400000, expenses: 1100000, collections: 2280000, target: 2500000 },
    { month: "Sep", revenue: 2600000, expenses: 1150000, collections: 2470000, target: 2500000 },
    { month: "Oct", revenue: 2300000, expenses: 1080000, collections: 2185000, target: 2500000 },
    { month: "Nov", revenue: 2700000, expenses: 1200000, collections: 2565000, target: 2500000 },
    { month: "Dec", revenue: 2900000, expenses: 1250000, collections: 2755000, target: 2500000 },
    { month: "Jan", revenue: 2850000, expenses: 1200000, collections: 2693000, target: 2500000 },
  ];

  const paymentMethodBreakdown = [
    { method: "Online Banking", amount: 1200000, percentage: 42.1, transactions: 156, color: "bg-blue-500" },
    { method: "UPI", amount: 850000, percentage: 29.8, transactions: 234, color: "bg-green-500" },
    { method: "Cash", amount: 450000, percentage: 15.8, transactions: 89, color: "bg-yellow-500" },
    { method: "Credit Card", amount: 200000, percentage: 7.0, transactions: 45, color: "bg-purple-500" },
    { method: "Bank Transfer", amount: 150000, percentage: 5.3, transactions: 23, color: "bg-indigo-500" },
  ];

  const feeTypeBreakdown = [
    { type: "Tuition Fee", amount: 1800000, percentage: 63.2, students: 270 },
    { type: "Lab Fee", amount: 450000, percentage: 15.8, students: 180 },
    { type: "Transport Fee", amount: 300000, percentage: 10.5, students: 120 },
    { type: "Library Fee", amount: 150000, percentage: 5.3, students: 270 },
    { type: "Sports Fee", amount: 100000, percentage: 3.5, students: 80 },
    { type: "Activity Fee", amount: 50000, percentage: 1.7, students: 50 },
  ];

  const classWiseCollection = [
    { class: "Grade 10A", students: 32, totalFees: 800000, collected: 760000, pending: 40000, rate: 95.0 },
    { class: "Grade 10B", students: 28, totalFees: 700000, collected: 665000, pending: 35000, rate: 95.0 },
    { class: "Grade 11A", students: 25, totalFees: 625000, collected: 575000, pending: 50000, rate: 92.0 },
    { class: "Grade 11B", students: 22, totalFees: 550000, collected: 495000, pending: 55000, rate: 90.0 },
    { class: "Grade 12A", students: 20, totalFees: 600000, collected: 540000, pending: 60000, rate: 90.0 },
  ];

  const expenseCategories = [
    { category: "Salaries", amount: 800000, percentage: 66.7, budget: 850000 },
    { category: "Infrastructure", amount: 200000, percentage: 16.7, budget: 180000 },
    { category: "Utilities", amount: 100000, percentage: 8.3, budget: 120000 },
    { category: "Supplies", amount: 60000, percentage: 5.0, budget: 80000 },
    { category: "Maintenance", amount: 40000, percentage: 3.3, budget: 50000 },
  ];

  const outstandingAnalysis = [
    { range: "0-30 days", amount: 50000, count: 15, percentage: 33.3 },
    { range: "31-60 days", amount: 40000, count: 12, percentage: 26.7 },
    { range: "61-90 days", amount: 35000, count: 8, percentage: 23.3 },
    { range: "90+ days", amount: 25000, count: 5, percentage: 16.7 },
  ];

  const getCollectionRateColor = (rate: number) => {
    if (rate >= 95) return "text-green-600";
    if (rate >= 90) return "text-blue-600";
    if (rate >= 85) return "text-yellow-600";
    return "text-red-600";
  };

  const getBudgetVarianceColor = (actual: number, budget: number) => {
    const variance = ((actual - budget) / budget) * 100;
    if (variance <= 0) return "text-green-600";
    if (variance <= 10) return "text-yellow-600";
    return "text-red-600";
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Financial Reports</h1>
            <p className="text-gray-600">Comprehensive financial analytics and insights</p>
          </div>
          <div className="flex gap-2">
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="daily">Daily</option>
              <option value="weekly">Weekly</option>
              <option value="monthly">Monthly</option>
              <option value="quarterly">Quarterly</option>
              <option value="yearly">Yearly</option>
            </select>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
          </div>
        </div>

        {/* Financial Overview */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <IndianRupee className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    ₹{(financialOverview.totalRevenue / 100000).toFixed(1)}L
                  </div>
                  <p className="text-sm text-gray-600">Total Revenue</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600">+12.5%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Target className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {financialOverview.collectionRate}%
                  </div>
                  <p className="text-sm text-gray-600">Collection Rate</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600">+2.1%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <AlertTriangle className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    ₹{(financialOverview.pendingAmount / 1000).toFixed(0)}K
                  </div>
                  <p className="text-sm text-gray-600">Pending Amount</p>
                  <div className="flex items-center mt-1">
                    <TrendingDown className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600">-8.3%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <TrendingUp className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    ₹{(financialOverview.netProfit / 100000).toFixed(1)}L
                  </div>
                  <p className="text-sm text-gray-600">Net Profit</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600">+15.2%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Report Tabs */}
        <Card>
          <CardHeader>
            <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
              {[
                { key: "overview", label: "Overview" },
                { key: "collections", label: "Collections" },
                { key: "expenses", label: "Expenses" },
                { key: "outstanding", label: "Outstanding" },
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setSelectedReport(tab.key)}
                  className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                    selectedReport === tab.key
                      ? "bg-white text-blue-600 shadow-sm"
                      : "text-gray-600 hover:text-gray-900"
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </div>
          </CardHeader>
          <CardContent>
            {/* Overview Tab */}
            {selectedReport === "overview" && (
              <div className="space-y-6">
                {/* Monthly Revenue Trend */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Monthly Revenue Trend</h3>
                  <div className="grid gap-6 md:grid-cols-6">
                    {monthlyRevenue.map((month, index) => (
                      <div key={index} className="text-center">
                        <div className="text-sm font-medium text-gray-900 mb-2">{month.month}</div>
                        <div className="space-y-2">
                          <div className="w-full bg-gray-200 rounded h-24 flex flex-col justify-end">
                            <div
                              className="bg-green-500 rounded-b"
                              style={{ height: `${(month.revenue / month.target) * 100}%` }}
                            />
                          </div>
                          <div className="text-xs text-green-600">
                            ₹{(month.revenue / 100000).toFixed(1)}L
                          </div>
                          <div className="text-xs text-gray-500">
                            Target: ₹{(month.target / 100000).toFixed(1)}L
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Payment Methods */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Payment Method Distribution</h3>
                  <div className="grid gap-4 md:grid-cols-2">
                    {paymentMethodBreakdown.map((method, index) => (
                      <div key={index} className="flex items-center justify-between p-4 bg-gray-50 rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className={`w-4 h-4 ${method.color} rounded`} />
                          <div>
                            <div className="font-medium text-gray-900">{method.method}</div>
                            <div className="text-sm text-gray-500">{method.transactions} transactions</div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-bold text-gray-900">
                            ₹{(method.amount / 100000).toFixed(1)}L
                          </div>
                          <div className="text-sm text-gray-500">{method.percentage}%</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Collections Tab */}
            {selectedReport === "collections" && (
              <div className="space-y-6">
                {/* Fee Type Breakdown */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Fee Type Collection</h3>
                  <div className="space-y-3">
                    {feeTypeBreakdown.map((fee, index) => (
                      <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex-1">
                          <div className="font-medium text-gray-900">{fee.type}</div>
                          <div className="text-sm text-gray-500">{fee.students} students</div>
                        </div>
                        <div className="flex-1 mx-4">
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div
                              className="bg-blue-500 h-2 rounded-full"
                              style={{ width: `${fee.percentage}%` }}
                            />
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-bold text-gray-900">
                            ₹{(fee.amount / 100000).toFixed(1)}L
                          </div>
                          <div className="text-sm text-gray-500">{fee.percentage}%</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Class-wise Collection */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Class-wise Collection Rate</h3>
                  <div className="space-y-3">
                    {classWiseCollection.map((cls, index) => (
                      <div key={index} className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between mb-3">
                          <div>
                            <div className="font-medium text-gray-900">{cls.class}</div>
                            <div className="text-sm text-gray-500">{cls.students} students</div>
                          </div>
                          <div className={`text-lg font-bold ${getCollectionRateColor(cls.rate)}`}>
                            {cls.rate}%
                          </div>
                        </div>
                        <div className="grid gap-3 md:grid-cols-3 text-sm">
                          <div>
                            <span className="text-gray-600">Total: </span>
                            <span className="font-medium">₹{(cls.totalFees / 1000).toFixed(0)}K</span>
                          </div>
                          <div>
                            <span className="text-gray-600">Collected: </span>
                            <span className="font-medium text-green-600">₹{(cls.collected / 1000).toFixed(0)}K</span>
                          </div>
                          <div>
                            <span className="text-gray-600">Pending: </span>
                            <span className="font-medium text-red-600">₹{(cls.pending / 1000).toFixed(0)}K</span>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Expenses Tab */}
            {selectedReport === "expenses" && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Expense Categories</h3>
                  <div className="space-y-3">
                    {expenseCategories.map((expense, index) => (
                      <div key={index} className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between mb-3">
                          <div className="font-medium text-gray-900">{expense.category}</div>
                          <div className="text-right">
                            <div className="font-bold text-gray-900">
                              ₹{(expense.amount / 1000).toFixed(0)}K
                            </div>
                            <div className="text-sm text-gray-500">{expense.percentage}%</div>
                          </div>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <div className="w-full bg-gray-200 rounded-full h-2 mr-4">
                            <div
                              className="bg-red-500 h-2 rounded-full"
                              style={{ width: `${(expense.amount / expense.budget) * 100}%` }}
                            />
                          </div>
                          <div className={`font-medium ${getBudgetVarianceColor(expense.amount, expense.budget)}`}>
                            Budget: ₹{(expense.budget / 1000).toFixed(0)}K
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Outstanding Tab */}
            {selectedReport === "outstanding" && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Outstanding Amount Analysis</h3>
                  <div className="grid gap-4 md:grid-cols-2">
                    {outstandingAnalysis.map((range, index) => (
                      <div key={index} className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between mb-3">
                          <div className="font-medium text-gray-900">{range.range}</div>
                          <div className="text-lg font-bold text-red-600">
                            ₹{(range.amount / 1000).toFixed(0)}K
                          </div>
                        </div>
                        <div className="flex items-center justify-between text-sm">
                          <span className="text-gray-600">{range.count} invoices</span>
                          <span className="text-gray-600">{range.percentage}% of total</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                          <div
                            className="bg-red-500 h-2 rounded-full"
                            style={{ width: `${range.percentage}%` }}
                          />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
