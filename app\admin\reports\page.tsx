"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  BarChart3,
  FileText,
  Download,
  Calendar,
  Users,
  GraduationCap,
  IndianRupee,
  TrendingUp,
  TrendingDown,
  Award,
  CheckCircle,
  AlertTriangle,
  Clock,
  Target,
  PieChart,
  Filter,
  Search,
  RefreshCw,
  Eye,
  Share,
  Printer,
  Settings,
} from "lucide-react";
import { toast } from "sonner";

export default function AdminReports() {
  const [user, setUser] = useState<any>(null);
  const [selectedPeriod, setSelectedPeriod] = useState("monthly");
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "super_admin" && parsedUser.role !== "admin") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock reports data
  const reportStats = {
    totalReports: 156,
    generatedThisMonth: 23,
    scheduledReports: 8,
    downloadedReports: 89,
    popularReport: "Student Performance Report",
    lastGenerated: "2024-01-20 14:30",
  };

  const reportCategories = [
    {
      category: "Academic Reports",
      count: 45,
      icon: GraduationCap,
      color: "bg-blue-500",
      reports: [
        { name: "Student Performance Report", frequency: "Monthly", lastRun: "2024-01-20", downloads: 156 },
        { name: "Grade Distribution Analysis", frequency: "Quarterly", lastRun: "2024-01-15", downloads: 89 },
        { name: "Subject-wise Performance", frequency: "Monthly", lastRun: "2024-01-18", downloads: 134 },
        { name: "Class Comparison Report", frequency: "Weekly", lastRun: "2024-01-19", downloads: 67 },
      ]
    },
    {
      category: "Financial Reports",
      count: 32,
      icon: IndianRupee,
      color: "bg-green-500",
      reports: [
        { name: "Fee Collection Report", frequency: "Monthly", lastRun: "2024-01-20", downloads: 198 },
        { name: "Revenue Analysis", frequency: "Quarterly", lastRun: "2024-01-10", downloads: 145 },
        { name: "Expense Breakdown", frequency: "Monthly", lastRun: "2024-01-15", downloads: 112 },
        { name: "Budget vs Actual Report", frequency: "Monthly", lastRun: "2024-01-18", downloads: 87 },
      ]
    },
    {
      category: "Attendance Reports",
      count: 28,
      icon: CheckCircle,
      color: "bg-purple-500",
      reports: [
        { name: "Daily Attendance Summary", frequency: "Daily", lastRun: "2024-01-20", downloads: 234 },
        { name: "Monthly Attendance Report", frequency: "Monthly", lastRun: "2024-01-01", downloads: 167 },
        { name: "Low Attendance Alert", frequency: "Weekly", lastRun: "2024-01-19", downloads: 89 },
        { name: "Class-wise Attendance", frequency: "Weekly", lastRun: "2024-01-19", downloads: 123 },
      ]
    },
    {
      category: "Administrative Reports",
      count: 51,
      icon: Users,
      color: "bg-orange-500",
      reports: [
        { name: "Staff Performance Report", frequency: "Quarterly", lastRun: "2024-01-05", downloads: 78 },
        { name: "Student Enrollment Report", frequency: "Monthly", lastRun: "2024-01-15", downloads: 145 },
        { name: "Infrastructure Utilization", frequency: "Monthly", lastRun: "2024-01-12", downloads: 56 },
        { name: "Parent Feedback Summary", frequency: "Quarterly", lastRun: "2024-01-08", downloads: 92 },
      ]
    }
  ];

  const quickReports = [
    { name: "Today's Attendance", icon: CheckCircle, color: "bg-green-500", description: "Current day attendance summary" },
    { name: "Fee Collection Status", icon: IndianRupee, color: "bg-blue-500", description: "Outstanding fees and collections" },
    { name: "Grade Summary", icon: Award, color: "bg-purple-500", description: "Latest grade distribution" },
    { name: "Staff Overview", icon: Users, color: "bg-orange-500", description: "Staff attendance and performance" },
    { name: "Student Count", icon: GraduationCap, color: "bg-indigo-500", description: "Current enrollment numbers" },
    { name: "Monthly Summary", icon: BarChart3, color: "bg-pink-500", description: "Comprehensive monthly report" },
  ];

  const scheduledReports = [
    { name: "Weekly Attendance Report", schedule: "Every Monday 9:00 AM", nextRun: "2024-01-22 09:00", status: "Active" },
    { name: "Monthly Fee Collection", schedule: "1st of every month", nextRun: "2024-02-01 08:00", status: "Active" },
    { name: "Quarterly Performance Review", schedule: "End of quarter", nextRun: "2024-03-31 17:00", status: "Active" },
    { name: "Daily Attendance Summary", schedule: "Every day 6:00 PM", nextRun: "2024-01-20 18:00", status: "Active" },
  ];

  const recentReports = [
    { name: "Student Performance Report", category: "Academic", generatedBy: "Admin", date: "2024-01-20", size: "2.3 MB", downloads: 15 },
    { name: "Fee Collection Report", category: "Financial", generatedBy: "Finance Manager", date: "2024-01-20", size: "1.8 MB", downloads: 23 },
    { name: "Daily Attendance Summary", category: "Attendance", generatedBy: "System", date: "2024-01-20", size: "856 KB", downloads: 8 },
    { name: "Grade Distribution Analysis", category: "Academic", generatedBy: "Admin", date: "2024-01-19", size: "3.1 MB", downloads: 12 },
    { name: "Monthly Expense Report", category: "Financial", generatedBy: "Admin", date: "2024-01-19", size: "1.2 MB", downloads: 7 },
  ];

  const getCategoryColor = (category: string) => {
    switch (category) {
      case "Academic": return "text-blue-600 bg-blue-100";
      case "Financial": return "text-green-600 bg-green-100";
      case "Attendance": return "text-purple-600 bg-purple-100";
      case "Administrative": return "text-orange-600 bg-orange-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Active": return "text-green-600 bg-green-100";
      case "Paused": return "text-yellow-600 bg-yellow-100";
      case "Inactive": return "text-red-600 bg-red-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const filteredReports = recentReports.filter(report => {
    const matchesSearch = report.name.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesCategory = selectedCategory === "all" || report.category === selectedCategory;
    return matchesSearch && matchesCategory;
  });

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Reports & Analytics</h1>
            <p className="text-gray-600">Generate and manage comprehensive reports</p>
          </div>
          <div className="flex gap-2">
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="daily">Daily</option>
              <option value="weekly">Weekly</option>
              <option value="monthly">Monthly</option>
              <option value="quarterly">Quarterly</option>
              <option value="yearly">Yearly</option>
            </select>
            <Button
              variant="outline"
              onClick={() => {
                toast.success("Reports refreshed successfully");
                // TODO: Implement actual refresh functionality
              }}
            >
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button
              onClick={() => {
                toast.success("Report generation started");
                // TODO: Implement report generation
              }}
            >
              <FileText className="h-4 w-4 mr-2" />
              Generate Report
            </Button>
          </div>
        </div>

        {/* Report Overview Stats */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <FileText className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {reportStats.totalReports}
                  </div>
                  <p className="text-sm text-gray-600">Total Reports</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <TrendingUp className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {reportStats.generatedThisMonth}
                  </div>
                  <p className="text-sm text-gray-600">Generated This Month</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600">+15.2%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {reportStats.scheduledReports}
                  </div>
                  <p className="text-sm text-gray-600">Scheduled Reports</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Download className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    {reportStats.downloadedReports}
                  </div>
                  <p className="text-sm text-gray-600">Downloads</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600">+8.7%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Report Generation */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Quick Reports</span>
              <Button variant="outline" size="sm">
                <Settings className="h-4 w-4 mr-2" />
                Customize
              </Button>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
              {quickReports.map((report, index) => (
                <Button
                  key={index}
                  variant="outline"
                  className="h-auto p-4 flex items-center space-x-3 justify-start"
                  onClick={() => {
                    toast.success(`Generating ${report.name}...`);
                    // TODO: Implement actual report generation
                  }}
                >
                  <div className={`w-10 h-10 ${report.color} rounded-lg flex items-center justify-center flex-shrink-0`}>
                    <report.icon className="h-5 w-5 text-white" />
                  </div>
                  <div className="text-left">
                    <div className="font-medium text-sm">{report.name}</div>
                    <div className="text-xs text-gray-500">{report.description}</div>
                  </div>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Tabbed Report Categories */}
        <Card>
          <CardHeader>
            <CardTitle>Report Categories</CardTitle>
            <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg mt-4">
              {reportCategories.map((category, index) => (
                <button
                  key={index}
                  onClick={() => setSelectedCategory(category.category)}
                  className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                    selectedCategory === category.category || (selectedCategory === "all" && index === 0)
                      ? "bg-white text-blue-600 shadow-sm"
                      : "text-gray-600 hover:text-gray-900"
                  }`}
                >
                  <category.icon className="h-4 w-4 inline mr-2" />
                  {category.category.replace(" Reports", "")}
                  <Badge className="ml-2" variant="outline">{category.count}</Badge>
                </button>
              ))}
            </div>
          </CardHeader>
          <CardContent>
            {reportCategories.map((category, index) => (
              (selectedCategory === category.category || (selectedCategory === "all" && index === 0)) && (
                <div key={index} className="space-y-3">
                  {category.reports.map((report, reportIndex) => (
                    <div key={reportIndex} className="flex items-center justify-between p-4 border rounded-lg hover:shadow-sm transition-shadow">
                      <div className="flex-1">
                        <div className="font-medium text-gray-900">{report.name}</div>
                        <div className="text-sm text-gray-500 mt-1">
                          <Clock className="h-4 w-4 inline mr-1" />
                          {report.frequency} • Last run: {report.lastRun}
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        <div className="text-right">
                          <div className="text-sm font-medium text-gray-900">{report.downloads}</div>
                          <div className="text-xs text-gray-500">downloads</div>
                        </div>
                        <div className="flex space-x-2">
                          <Button size="sm" variant="outline">
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button size="sm" variant="outline">
                            <Download className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              )
            ))}
          </CardContent>
        </Card>

        {/* Search and Filter */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search reports..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Categories</option>
                  <option value="Academic">Academic</option>
                  <option value="Financial">Financial</option>
                  <option value="Attendance">Attendance</option>
                  <option value="Administrative">Administrative</option>
                </select>
                <Button variant="outline">
                  <Filter className="h-4 w-4 mr-2" />
                  Filter
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Recent Reports */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              Recent Reports ({filteredReports.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Report Name</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Category</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Generated By</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Date</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Size</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Downloads</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredReports.map((report, index) => (
                    <tr key={index} className="border-b hover:bg-gray-50">
                      <td className="py-3 px-4 font-medium text-gray-900">{report.name}</td>
                      <td className="py-3 px-4">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(report.category)}`}>
                          {report.category}
                        </span>
                      </td>
                      <td className="py-3 px-4 text-gray-600">{report.generatedBy}</td>
                      <td className="py-3 px-4 text-gray-600">{report.date}</td>
                      <td className="py-3 px-4 text-gray-600">{report.size}</td>
                      <td className="py-3 px-4 text-gray-600">{report.downloads}</td>
                      <td className="py-3 px-4">
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm">
                            <Eye className="h-3 w-3" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <Download className="h-3 w-3" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <Share className="h-3 w-3" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Scheduled Reports */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Clock className="h-5 w-5 mr-2" />
              Scheduled Reports
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {scheduledReports.map((report, index) => (
                <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                  <div>
                    <div className="font-medium text-gray-900">{report.name}</div>
                    <div className="text-sm text-gray-500">
                      {report.schedule} • Next run: {report.nextRun}
                    </div>
                  </div>
                  <div className="flex items-center space-x-3">
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(report.status)}`}>
                      {report.status}
                    </span>
                    <Button variant="outline" size="sm">
                      Edit
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
