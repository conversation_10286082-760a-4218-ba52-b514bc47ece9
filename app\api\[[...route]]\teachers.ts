import { Hono } from "hono";
import { zValidator } from "@hono/zod-validator";
import { insertTeacherSchema } from "@/lib/schemas";
import { mockTeachers } from "@/lib/mock-db";
import { generateId } from "@/lib/utils";

const app = new Hono()
  .get("/", async (c) => {
    const page = parseInt(c.req.query("page") || "1");
    const limit = parseInt(c.req.query("limit") || "10");
    const search = c.req.query("search");
    const department = c.req.query("department");
    const status = c.req.query("status");

    let filteredTeachers = [...mockTeachers];

    if (search) {
      const searchLower = search.toLowerCase();
      filteredTeachers = filteredTeachers.filter(
        (teacher) =>
          teacher.firstName.toLowerCase().includes(searchLower) ||
          teacher.lastName.toLowerCase().includes(searchLower) ||
          teacher.email.toLowerCase().includes(searchLower) ||
          teacher.employeeId.toLowerCase().includes(searchLower) ||
          teacher.subject.toLowerCase().includes(searchLower)
      );
    }

    if (department) {
      filteredTeachers = filteredTeachers.filter(
        (teacher) => teacher.department === department
      );
    }

    if (status) {
      filteredTeachers = filteredTeachers.filter(
        (teacher) => teacher.status === status
      );
    }

    const offset = (page - 1) * limit;
    const paginatedTeachers = filteredTeachers.slice(offset, offset + limit);

    const meta = {
      page,
      limit,
      total: filteredTeachers.length,
      totalPages: Math.ceil(filteredTeachers.length / limit),
      hasNext: page < Math.ceil(filteredTeachers.length / limit),
      hasPrev: page > 1,
    };

    return c.json({ data: paginatedTeachers, meta });
  })
  .get("/:id", async (c) => {
    const id = c.req.param("id");
    const teacher = mockTeachers.find((t) => t.id === id);

    if (!teacher) {
      return c.json({ error: "Teacher not found" }, 404);
    }

    return c.json({ data: teacher });
  })
  .post(
    "/",
    zValidator("json", insertTeacherSchema),
    async (c) => {
      const values = c.req.valid("json");

      const newTeacher = {
        id: generateId(),
        ...values,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      mockTeachers.push(newTeacher);

      return c.json({ data: newTeacher }, 201);
    }
  )
  .put(
    "/:id",
    zValidator("json", insertTeacherSchema),
    async (c) => {
      const id = c.req.param("id");
      const values = c.req.valid("json");

      const teacherIndex = mockTeachers.findIndex((t) => t.id === id);
      if (teacherIndex === -1) {
        return c.json({ error: "Teacher not found" }, 404);
      }

      const updatedTeacher = {
        ...mockTeachers[teacherIndex],
        ...values,
        updatedAt: new Date().toISOString(),
      };

      mockTeachers[teacherIndex] = updatedTeacher;

      return c.json({ data: updatedTeacher });
    }
  )
  .delete("/:id", async (c) => {
    const id = c.req.param("id");
    const teacherIndex = mockTeachers.findIndex((t) => t.id === id);

    if (teacherIndex === -1) {
      return c.json({ error: "Teacher not found" }, 404);
    }

    const deletedTeacher = mockTeachers.splice(teacherIndex, 1)[0];
    return c.json({ data: deletedTeacher });
  });

export default app;
