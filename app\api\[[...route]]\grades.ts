import { Hono } from "hono";
import { zValidator } from "@hono/zod-validator";
import { insertGradeSchema } from "@/lib/schemas";
import { mockGrades } from "@/lib/mock-db";
import { generateId } from "@/lib/utils";

const app = new Hono()
  .get("/", async (c) => {
    const page = parseInt(c.req.query("page") || "1");
    const limit = parseInt(c.req.query("limit") || "10");
    const studentId = c.req.query("studentId");
    const classId = c.req.query("classId");
    const type = c.req.query("type");

    let filteredGrades = [...mockGrades];

    if (studentId) {
      filteredGrades = filteredGrades.filter(
        (grade) => grade.studentId === studentId
      );
    }

    if (classId) {
      filteredGrades = filteredGrades.filter(
        (grade) => grade.classId === classId
      );
    }

    if (type) {
      filteredGrades = filteredGrades.filter((grade) => grade.type === type);
    }

    const offset = (page - 1) * limit;
    const paginatedGrades = filteredGrades.slice(offset, offset + limit);

    const meta = {
      page,
      limit,
      total: filteredGrades.length,
      totalPages: Math.ceil(filteredGrades.length / limit),
      hasNext: page < Math.ceil(filteredGrades.length / limit),
      hasPrev: page > 1,
    };

    return c.json({ data: paginatedGrades, meta });
  })
  .get("/:id", async (c) => {
    const id = c.req.param("id");
    const grade = mockGrades.find((g) => g.id === id);

    if (!grade) {
      return c.json({ error: "Grade not found" }, 404);
    }

    return c.json({ data: grade });
  })
  .post(
    "/",
    zValidator("json", insertGradeSchema),
    async (c) => {
      const values = c.req.valid("json");

      const newGrade = {
        id: generateId(),
        ...values,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      mockGrades.push(newGrade);

      return c.json({ data: newGrade }, 201);
    }
  )
  .put(
    "/:id",
    zValidator("json", insertGradeSchema),
    async (c) => {
      const id = c.req.param("id");
      const values = c.req.valid("json");

      const gradeIndex = mockGrades.findIndex((g) => g.id === id);
      if (gradeIndex === -1) {
        return c.json({ error: "Grade not found" }, 404);
      }

      const updatedGrade = {
        ...mockGrades[gradeIndex],
        ...values,
        updatedAt: new Date().toISOString(),
      };

      mockGrades[gradeIndex] = updatedGrade;

      return c.json({ data: updatedGrade });
    }
  )
  .delete("/:id", async (c) => {
    const id = c.req.param("id");
    const gradeIndex = mockGrades.findIndex((g) => g.id === id);

    if (gradeIndex === -1) {
      return c.json({ error: "Grade not found" }, 404);
    }

    const deletedGrade = mockGrades.splice(gradeIndex, 1)[0];
    return c.json({ data: deletedGrade });
  });

export default app;
