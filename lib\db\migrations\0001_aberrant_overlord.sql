CREATE TYPE "public"."admission_status" AS ENUM('open', 'closed', 'waitlist');--> statement-breakpoint
CREATE TYPE "public"."program_type" AS ENUM('school', 'undergraduate', 'postgraduate', 'diploma', 'certificate');--> statement-breakpoint
CREATE TYPE "public"."semester" AS ENUM('1', '2', '3', '4', '5', '6', '7', '8');--> statement-breakpoint
CREATE TYPE "public"."student_type" AS ENUM('regular', 'lateral', 'distance');--> statement-breakpoint
CREATE TYPE "public"."teacher_type" AS ENUM('permanent', 'contract', 'visiting', 'guest');--> statement-breakpoint
CREATE TABLE "academic_batches" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"program_id" uuid NOT NULL,
	"batch_name" varchar(100) NOT NULL,
	"start_year" integer NOT NULL,
	"end_year" integer NOT NULL,
	"current_semester" "semester" DEFAULT '1',
	"total_seats" integer NOT NULL,
	"occupied_seats" integer DEFAULT 0 NOT NULL,
	"available_seats" integer NOT NULL,
	"admission_status" "admission_status" DEFAULT 'open' NOT NULL,
	"status" "status" DEFAULT 'active' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL
);
--> statement-breakpoint
CREATE TABLE "academic_programs" (
	"id" uuid PRIMARY KEY DEFAULT gen_random_uuid() NOT NULL,
	"name" varchar(200) NOT NULL,
	"code" varchar(20) NOT NULL,
	"type" "program_type" NOT NULL,
	"duration" integer NOT NULL,
	"total_semesters" integer DEFAULT 2,
	"description" text,
	"eligibility_criteria" text,
	"total_seats" integer DEFAULT 30 NOT NULL,
	"department" varchar(100),
	"admission_status" "admission_status" DEFAULT 'open' NOT NULL,
	"status" "status" DEFAULT 'active' NOT NULL,
	"created_at" timestamp DEFAULT now() NOT NULL,
	"updated_at" timestamp DEFAULT now() NOT NULL,
	CONSTRAINT "academic_programs_code_unique" UNIQUE("code")
);
--> statement-breakpoint
ALTER TABLE "classes" ALTER COLUMN "section" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "classes" ALTER COLUMN "subject" SET NOT NULL;--> statement-breakpoint
ALTER TABLE "classes" ALTER COLUMN "semester" SET DEFAULT '1'::"public"."semester";--> statement-breakpoint
ALTER TABLE "classes" ALTER COLUMN "semester" SET DATA TYPE "public"."semester" USING "semester"::"public"."semester";--> statement-breakpoint
ALTER TABLE "classes" ADD COLUMN "program_id" uuid NOT NULL;--> statement-breakpoint
ALTER TABLE "classes" ADD COLUMN "batch_id" uuid NOT NULL;--> statement-breakpoint
ALTER TABLE "classes" ADD COLUMN "assistant_teacher_id" uuid;--> statement-breakpoint
ALTER TABLE "classes" ADD COLUMN "class_type" varchar(20) DEFAULT 'theory';--> statement-breakpoint
ALTER TABLE "classes" ADD COLUMN "room" varchar(50);--> statement-breakpoint
ALTER TABLE "classes" ADD COLUMN "available_seats" integer DEFAULT 30 NOT NULL;--> statement-breakpoint
ALTER TABLE "classes" ADD COLUMN "credits" integer DEFAULT 3;--> statement-breakpoint
ALTER TABLE "classes" ADD COLUMN "is_elective" boolean DEFAULT false;--> statement-breakpoint
ALTER TABLE "classes" ADD COLUMN "prerequisites" text;--> statement-breakpoint
ALTER TABLE "students" ADD COLUMN "program_id" uuid NOT NULL;--> statement-breakpoint
ALTER TABLE "students" ADD COLUMN "batch_id" uuid NOT NULL;--> statement-breakpoint
ALTER TABLE "students" ADD COLUMN "current_semester" "semester" DEFAULT '1';--> statement-breakpoint
ALTER TABLE "students" ADD COLUMN "student_type" "student_type" DEFAULT 'regular' NOT NULL;--> statement-breakpoint
ALTER TABLE "students" ADD COLUMN "is_lateral_entry" boolean DEFAULT false NOT NULL;--> statement-breakpoint
ALTER TABLE "students" ADD COLUMN "previous_education" text;--> statement-breakpoint
ALTER TABLE "students" ADD COLUMN "transfer_certificate" varchar(255);--> statement-breakpoint
ALTER TABLE "teachers" ADD COLUMN "teacher_type" "teacher_type" DEFAULT 'permanent' NOT NULL;--> statement-breakpoint
ALTER TABLE "teachers" ADD COLUMN "subjects" text;--> statement-breakpoint
ALTER TABLE "teachers" ADD COLUMN "primary_subject" varchar(100) NOT NULL;--> statement-breakpoint
ALTER TABLE "teachers" ADD COLUMN "contract_end_date" date;--> statement-breakpoint
ALTER TABLE "teachers" ADD COLUMN "max_classes_per_week" integer DEFAULT 20;--> statement-breakpoint
ALTER TABLE "teachers" ADD COLUMN "current_class_load" integer DEFAULT 0;--> statement-breakpoint
ALTER TABLE "teachers" ADD COLUMN "specialization" text;--> statement-breakpoint
ALTER TABLE "teachers" ADD COLUMN "certifications" text;--> statement-breakpoint
ALTER TABLE "academic_batches" ADD CONSTRAINT "academic_batches_program_id_academic_programs_id_fk" FOREIGN KEY ("program_id") REFERENCES "public"."academic_programs"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "classes" ADD CONSTRAINT "classes_program_id_academic_programs_id_fk" FOREIGN KEY ("program_id") REFERENCES "public"."academic_programs"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "classes" ADD CONSTRAINT "classes_batch_id_academic_batches_id_fk" FOREIGN KEY ("batch_id") REFERENCES "public"."academic_batches"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "classes" ADD CONSTRAINT "classes_assistant_teacher_id_teachers_id_fk" FOREIGN KEY ("assistant_teacher_id") REFERENCES "public"."teachers"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "students" ADD CONSTRAINT "students_program_id_academic_programs_id_fk" FOREIGN KEY ("program_id") REFERENCES "public"."academic_programs"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "students" ADD CONSTRAINT "students_batch_id_academic_batches_id_fk" FOREIGN KEY ("batch_id") REFERENCES "public"."academic_batches"("id") ON DELETE no action ON UPDATE no action;--> statement-breakpoint
ALTER TABLE "teachers" DROP COLUMN "subject";