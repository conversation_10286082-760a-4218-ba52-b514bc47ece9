"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Calendar,
  Clock,
  Users,
  MapPin,
  Phone,
  Mail,
  User,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Plus,
  Edit,
  Eye,
  Search,
  Filter,
  Download,
  Video,
  MessageSquare,
  Star,
  FileText,
  ChevronLeft,
  ChevronRight,
} from "lucide-react";

export default function AdmissionInterviews() {
  const [user, setUser] = useState<any>(null);
  const [selectedTab, setSelectedTab] = useState("scheduled");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedInterview, setSelectedInterview] = useState<any>(null);
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "admission") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  const interviewStats = {
    totalInterviews: 85,
    scheduledToday: 12,
    completed: 58,
    pending: 27,
    averageRating: 4.2,
  };

  const interviews = [
    {
      id: "INT001",
      applicationId: "APP001",
      studentName: "Arjun Sharma",
      parentName: "Rajesh Sharma",
      appliedClass: "Grade 6",
      appliedProgram: "Regular",
      date: "2024-01-25",
      time: "10:00 AM",
      duration: "30 minutes",
      mode: "In-person",
      interviewer: "Dr. Priya Mehta",
      room: "Interview Room 1",
      status: "Scheduled",
      phone: "+91 9876543210",
      email: "<EMAIL>",
      notes: "Student shows good academic potential",
      rating: null,
      recommendation: null,
      previousSchool: "ABC Primary School",
      academicScore: 92,
    },
    {
      id: "INT002",
      applicationId: "APP002",
      studentName: "Sneha Patel",
      parentName: "Amit Patel",
      appliedClass: "Grade 7",
      appliedProgram: "Regular",
      date: "2024-01-25",
      time: "11:00 AM",
      duration: "30 minutes",
      mode: "Online",
      interviewer: "Prof. Suresh Kumar",
      room: "Virtual Room A",
      status: "Scheduled",
      phone: "+91 9876543211",
      email: "<EMAIL>",
      notes: "Strong extracurricular background",
      rating: null,
      recommendation: null,
      previousSchool: "XYZ Public School",
      academicScore: 88,
    },
    {
      id: "INT003",
      applicationId: "APP003",
      studentName: "Rahul Kumar",
      parentName: "Suresh Kumar",
      appliedClass: "Grade 8",
      appliedProgram: "Science Stream",
      date: "2024-01-24",
      time: "02:00 PM",
      duration: "45 minutes",
      mode: "In-person",
      interviewer: "Dr. Anjali Singh",
      room: "Interview Room 2",
      status: "Completed",
      phone: "+91 9876543212",
      email: "<EMAIL>",
      notes: "Excellent performance in entrance test",
      rating: 4.8,
      recommendation: "Strongly Recommended",
      previousSchool: "PQR International School",
      academicScore: 95,
    },
    {
      id: "INT004",
      applicationId: "APP004",
      studentName: "Ananya Singh",
      parentName: "Vikram Singh",
      appliedClass: "Grade 5",
      appliedProgram: "Regular",
      date: "2024-01-24",
      time: "03:30 PM",
      duration: "30 minutes",
      mode: "In-person",
      interviewer: "Ms. Kavita Sharma",
      room: "Interview Room 1",
      status: "Completed",
      phone: "+91 9876543213",
      email: "<EMAIL>",
      notes: "Needs improvement in communication skills",
      rating: 3.2,
      recommendation: "Not Recommended",
      previousSchool: "LMN Convent School",
      academicScore: 75,
    },
    {
      id: "INT005",
      applicationId: "APP005",
      studentName: "Karan Gupta",
      parentName: "Ashok Gupta",
      appliedClass: "Grade 7",
      appliedProgram: "Sports Quota",
      date: "2024-01-26",
      time: "09:00 AM",
      duration: "45 minutes",
      mode: "In-person",
      interviewer: "Coach Ramesh Yadav",
      room: "Sports Office",
      status: "Scheduled",
      phone: "+91 9876543214",
      email: "<EMAIL>",
      notes: "State level cricket player",
      rating: null,
      recommendation: null,
      previousSchool: "Sports Academy School",
      academicScore: 82,
    },
  ];

  const interviewers = [
    { name: "Dr. Priya Mehta", role: "Principal", specialization: "General Admission" },
    { name: "Prof. Suresh Kumar", role: "Academic Head", specialization: "Science Programs" },
    { name: "Dr. Anjali Singh", role: "Vice Principal", specialization: "Senior Classes" },
    { name: "Ms. Kavita Sharma", role: "Primary Coordinator", specialization: "Primary Classes" },
    { name: "Coach Ramesh Yadav", role: "Sports Director", specialization: "Sports Quota" },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Scheduled": return "text-blue-600 bg-blue-100";
      case "Completed": return "text-green-600 bg-green-100";
      case "Cancelled": return "text-red-600 bg-red-100";
      case "Rescheduled": return "text-yellow-600 bg-yellow-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Scheduled": return <Clock className="h-4 w-4" />;
      case "Completed": return <CheckCircle className="h-4 w-4" />;
      case "Cancelled": return <XCircle className="h-4 w-4" />;
      case "Rescheduled": return <AlertTriangle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const getModeIcon = (mode: string) => {
    return mode === "Online" ? <Video className="h-4 w-4" /> : <MapPin className="h-4 w-4" />;
  };

  const getRecommendationColor = (recommendation: string) => {
    switch (recommendation) {
      case "Strongly Recommended": return "text-green-600 bg-green-100";
      case "Recommended": return "text-blue-600 bg-blue-100";
      case "Conditional": return "text-yellow-600 bg-yellow-100";
      case "Not Recommended": return "text-red-600 bg-red-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const getRatingStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${i < Math.floor(rating) ? 'text-yellow-500 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  const getTabInterviews = () => {
    let filtered = interviews;
    
    switch (selectedTab) {
      case "scheduled":
        filtered = interviews.filter(interview => interview.status === "Scheduled");
        break;
      case "completed":
        filtered = interviews.filter(interview => interview.status === "Completed");
        break;
      case "today":
        filtered = interviews.filter(interview => interview.date === new Date().toISOString().split('T')[0]);
        break;
      default:
        filtered = interviews;
    }

    return filtered.filter(interview =>
      interview.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      interview.applicationId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      interview.interviewer.toLowerCase().includes(searchTerm.toLowerCase())
    );
  };

  const updateInterviewStatus = (interviewId: string, newStatus: string) => {
    alert(`Interview ${interviewId} status updated to: ${newStatus}`);
  };

  const scheduleInterview = () => {
    alert("Interview scheduling form would open here");
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Interview Management</h1>
            <p className="text-gray-600">Schedule and manage admission interviews</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Schedule
            </Button>
            <Button onClick={scheduleInterview}>
              <Plus className="h-4 w-4 mr-2" />
              Schedule Interview
            </Button>
          </div>
        </div>

        {/* Interview Stats */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Calendar className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {interviewStats.totalInterviews}
                  </div>
                  <p className="text-sm text-gray-600">Total Interviews</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    {interviewStats.scheduledToday}
                  </div>
                  <p className="text-sm text-gray-600">Today&apos;s Interviews</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {interviewStats.completed}
                  </div>
                  <p className="text-sm text-gray-600">Completed</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <AlertTriangle className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-yellow-600">
                    {interviewStats.pending}
                  </div>
                  <p className="text-sm text-gray-600">Pending</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Star className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {interviewStats.averageRating}
                  </div>
                  <p className="text-sm text-gray-600">Avg Rating</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Interviews List */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
                  {[
                    { key: "all", label: "All" },
                    { key: "scheduled", label: "Scheduled" },
                    { key: "today", label: "Today" },
                    { key: "completed", label: "Completed" },
                  ].map((tab) => (
                    <button
                      key={tab.key}
                      onClick={() => setSelectedTab(tab.key)}
                      className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                        selectedTab === tab.key
                          ? "bg-white text-blue-600 shadow-sm"
                          : "text-gray-600 hover:text-gray-900"
                      }`}
                    >
                      {tab.label}
                    </button>
                  ))}
                </div>
                <div className="flex gap-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search interviews..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 w-64"
                    />
                  </div>
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {getTabInterviews().map((interview) => (
                  <div
                    key={interview.id}
                    onClick={() => setSelectedInterview(interview)}
                    className={`p-4 border rounded-lg cursor-pointer transition-colors hover:bg-gray-50 ${
                      selectedInterview?.id === interview.id ? 'bg-blue-50 border-blue-200' : ''
                    }`}
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-start space-x-4">
                        <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                          <User className="h-6 w-6 text-blue-600" />
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">{interview.studentName}</h4>
                          <p className="text-sm text-gray-600">ID: {interview.applicationId}</p>
                          <p className="text-sm text-gray-600">{interview.appliedClass} - {interview.appliedProgram}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(interview.status)}`}>
                          {getStatusIcon(interview.status)}
                          <span className="ml-1">{interview.status}</span>
                        </span>
                        <div className="text-xs text-gray-500 mt-1">{interview.date}</div>
                      </div>
                    </div>

                    <div className="grid gap-2 md:grid-cols-3 text-sm mb-3">
                      <div className="flex items-center text-gray-600">
                        <Clock className="h-3 w-3 mr-1" />
                        {interview.time}
                      </div>
                      <div className="flex items-center text-gray-600">
                        {getModeIcon(interview.mode)}
                        <span className="ml-1">{interview.mode}</span>
                      </div>
                      <div className="flex items-center text-gray-600">
                        <User className="h-3 w-3 mr-1" />
                        {interview.interviewer}
                      </div>
                    </div>

                    {interview.status === "Completed" && interview.rating && (
                      <div className="flex items-center justify-between pt-3 border-t">
                        <div className="flex items-center space-x-2">
                          <div className="flex">
                            {getRatingStars(interview.rating)}
                          </div>
                          <span className="text-sm text-gray-600">({interview.rating})</span>
                        </div>
                        {interview.recommendation && (
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getRecommendationColor(interview.recommendation)}`}>
                            {interview.recommendation}
                          </span>
                        )}
                      </div>
                    )}

                    {interview.status === "Scheduled" && (
                      <div className="flex items-center justify-between pt-3 border-t">
                        <span className="text-sm text-gray-600">{interview.room}</span>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm">
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <MessageSquare className="h-3 w-3" />
                          </Button>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Interview Details */}
          <div className="space-y-6">
            {selectedInterview ? (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>Interview Details</span>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(selectedInterview.status)}`}>
                      {selectedInterview.status}
                    </span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Student Information */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Student Information</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Name:</span>
                          <span className="font-medium">{selectedInterview.studentName}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Parent:</span>
                          <span className="font-medium">{selectedInterview.parentName}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Applied Class:</span>
                          <span className="font-medium">{selectedInterview.appliedClass}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Program:</span>
                          <span className="font-medium">{selectedInterview.appliedProgram}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Academic Score:</span>
                          <span className="font-medium">{selectedInterview.academicScore}%</span>
                        </div>
                      </div>
                    </div>

                    {/* Interview Schedule */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Interview Schedule</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Date:</span>
                          <span className="font-medium">{selectedInterview.date}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Time:</span>
                          <span className="font-medium">{selectedInterview.time}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Duration:</span>
                          <span className="font-medium">{selectedInterview.duration}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Mode:</span>
                          <span className="font-medium">{selectedInterview.mode}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Location:</span>
                          <span className="font-medium">{selectedInterview.room}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Interviewer:</span>
                          <span className="font-medium">{selectedInterview.interviewer}</span>
                        </div>
                      </div>
                    </div>

                    {/* Contact Information */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Contact Information</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex items-center">
                          <Phone className="h-4 w-4 text-gray-400 mr-2" />
                          <span>{selectedInterview.phone}</span>
                        </div>
                        <div className="flex items-center">
                          <Mail className="h-4 w-4 text-gray-400 mr-2" />
                          <span>{selectedInterview.email}</span>
                        </div>
                      </div>
                    </div>

                    {/* Notes */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Notes</h4>
                      <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
                        {selectedInterview.notes}
                      </p>
                    </div>

                    {/* Interview Results (if completed) */}
                    {selectedInterview.status === "Completed" && (
                      <div>
                        <h4 className="font-medium text-gray-900 mb-2">Interview Results</h4>
                        <div className="space-y-3">
                          <div className="flex items-center justify-between">
                            <span className="text-gray-600">Rating:</span>
                            <div className="flex items-center space-x-2">
                              <div className="flex">
                                {getRatingStars(selectedInterview.rating)}
                              </div>
                              <span className="font-medium">({selectedInterview.rating})</span>
                            </div>
                          </div>
                          <div className="flex items-center justify-between">
                            <span className="text-gray-600">Recommendation:</span>
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getRecommendationColor(selectedInterview.recommendation)}`}>
                              {selectedInterview.recommendation}
                            </span>
                          </div>
                        </div>
                      </div>
                    )}

                    {/* Actions */}
                    <div className="space-y-2">
                      {selectedInterview.status === "Scheduled" && (
                        <>
                          <Button className="w-full">
                            <CheckCircle className="h-4 w-4 mr-2" />
                            Mark as Completed
                          </Button>
                          <div className="grid grid-cols-2 gap-2">
                            <Button variant="outline">
                              <Edit className="h-4 w-4 mr-2" />
                              Reschedule
                            </Button>
                            <Button variant="outline">
                              <XCircle className="h-4 w-4 mr-2" />
                              Cancel
                            </Button>
                          </div>
                        </>
                      )}
                      {selectedInterview.status === "Completed" && (
                        <Button variant="outline" className="w-full">
                          <FileText className="h-4 w-4 mr-2" />
                          View Full Report
                        </Button>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="p-6 text-center">
                  <Calendar className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Select an Interview</h3>
                  <p className="text-gray-600">Choose an interview from the list to view details</p>
                </CardContent>
              </Card>
            )}

            {/* Today&apos;s Schedule */}
            <Card>
              <CardHeader>
                <CardTitle>Today&apos;s Schedule</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {interviews
                    .filter(interview => interview.date === new Date().toISOString().split('T')[0])
                    .slice(0, 4)
                    .map((interview) => (
                      <div key={interview.id} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                        <div>
                          <div className="font-medium text-gray-900">{interview.time}</div>
                          <div className="text-sm text-gray-600">{interview.studentName}</div>
                        </div>
                        <div className="text-right">
                          <div className="text-sm font-medium text-gray-900">{interview.room}</div>
                          <div className="text-xs text-gray-500">{interview.interviewer}</div>
                        </div>
                      </div>
                    ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
