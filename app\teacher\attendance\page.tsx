"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Calendar,
  Users,
  CheckCircle,
  XCircle,
  Clock,
  Search,
  Filter,
  Download,
  Save,
  RotateCcw,
  UserCheck,
  UserX,
  AlertTriangle,
  TrendingUp,
  BarChart3,
} from "lucide-react";

export default function TeacherAttendance() {
  const [user, setUser] = useState<any>(null);
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [selectedClass, setSelectedClass] = useState("Grade 10A");
  const [searchTerm, setSearchTerm] = useState("");
  const [attendanceData, setAttendanceData] = useState<any>({});
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "teacher") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock data for teacher's classes
  const teacherClasses = [
    { id: "10A", name: "Grade 10A", subject: "Mathematics", students: 45 },
    { id: "10B", name: "Grade 10B", subject: "Mathematics", students: 44 },
    { id: "11A", name: "Grade 11A", subject: "Algebra", students: 43 },
    { id: "11B", name: "Grade 11B", subject: "Mathematics", students: 42 },
    { id: "12A", name: "Grade 12A", subject: "Statistics", students: 41 },
    { id: "12B", name: "Grade 12B", subject: "Statistics", students: 40 },
  ];

  const students = [
    { id: "1", rollNo: "10A001", name: "John Doe", status: "present" },
    { id: "2", rollNo: "10A002", name: "Alice Smith", status: "present" },
    { id: "3", rollNo: "10A003", name: "Michael Johnson", status: "absent" },
    { id: "4", rollNo: "10A004", name: "Emma Davis", status: "present" },
    { id: "5", rollNo: "10A005", name: "Robert Wilson", status: "late" },
    { id: "6", rollNo: "10A006", name: "Sarah Brown", status: "present" },
    { id: "7", rollNo: "10A007", name: "David Lee", status: "present" },
    { id: "8", rollNo: "10A008", name: "Lisa Wang", status: "absent" },
    { id: "9", rollNo: "10A009", name: "James Miller", status: "present" },
    { id: "10", rollNo: "10A010", name: "Maria Garcia", status: "present" },
  ];

  const attendanceStats = {
    totalStudents: 45,
    present: 38,
    absent: 5,
    late: 2,
    attendanceRate: 84.4,
    weeklyAverage: 87.2,
    monthlyAverage: 85.8,
  };

  const recentAttendance = [
    { date: "2024-01-19", class: "Grade 10A", present: 42, absent: 3, rate: 93.3 },
    { date: "2024-01-18", class: "Grade 10A", present: 40, absent: 5, rate: 88.9 },
    { date: "2024-01-17", class: "Grade 10A", present: 43, absent: 2, rate: 95.6 },
    { date: "2024-01-16", class: "Grade 10A", present: 41, absent: 4, rate: 91.1 },
    { date: "2024-01-15", class: "Grade 10A", present: 39, absent: 6, rate: 86.7 },
  ];

  const handleAttendanceChange = (studentId: string, status: string) => {
    setAttendanceData((prev: any) => ({
      ...prev,
      [studentId]: status
    }));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "present": return "text-green-600 bg-green-100 border-green-200";
      case "absent": return "text-red-600 bg-red-100 border-red-200";
      case "late": return "text-yellow-600 bg-yellow-100 border-yellow-200";
      default: return "text-gray-600 bg-gray-100 border-gray-200";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "present": return <CheckCircle className="h-4 w-4" />;
      case "absent": return <XCircle className="h-4 w-4" />;
      case "late": return <Clock className="h-4 w-4" />;
      default: return <AlertTriangle className="h-4 w-4" />;
    }
  };

  const filteredStudents = students.filter(student =>
    student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.rollNo.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const saveAttendance = () => {
    // Save attendance logic here
    alert("Attendance saved successfully!");
  };

  const markAllPresent = () => {
    const newAttendanceData: any = {};
    students.forEach(student => {
      newAttendanceData[student.id] = "present";
    });
    setAttendanceData(newAttendanceData);
  };

  const resetAttendance = () => {
    setAttendanceData({});
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Mark Attendance</h1>
            <p className="text-gray-600">Record student attendance for your classes</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={resetAttendance}>
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button onClick={saveAttendance}>
              <Save className="h-4 w-4 mr-2" />
              Save Attendance
            </Button>
          </div>
        </div>

        {/* Attendance Stats */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {attendanceStats.totalStudents}
                  </div>
                  <p className="text-sm text-gray-600">Total Students</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {attendanceStats.present}
                  </div>
                  <p className="text-sm text-gray-600">Present Today</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <XCircle className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-red-600">
                    {attendanceStats.absent}
                  </div>
                  <p className="text-sm text-gray-600">Absent Today</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <BarChart3 className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {attendanceStats.attendanceRate}%
                  </div>
                  <p className="text-sm text-gray-600">Attendance Rate</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600">+2.1%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Controls */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search students by name or roll number..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <input
                  type="date"
                  value={selectedDate}
                  onChange={(e) => setSelectedDate(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <select
                  value={selectedClass}
                  onChange={(e) => setSelectedClass(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {teacherClasses.map((cls) => (
                    <option key={cls.id} value={cls.name}>
                      {cls.name} - {cls.subject}
                    </option>
                  ))}
                </select>
                <Button variant="outline" onClick={markAllPresent}>
                  <UserCheck className="h-4 w-4 mr-2" />
                  Mark All Present
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Main Content Grid */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Student Attendance List */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="h-5 w-5 mr-2" />
                Student Attendance - {selectedClass}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {filteredStudents.map((student) => (
                  <div key={student.id} className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="font-medium text-blue-600">
                          {student.name.split(' ').map(n => n[0]).join('')}
                        </span>
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">{student.name}</div>
                        <div className="text-sm text-gray-500">Roll No: {student.rollNo}</div>
                      </div>
                    </div>

                    <div className="flex items-center space-x-2">
                      <Button
                        variant={attendanceData[student.id] === "present" || (!attendanceData[student.id] && student.status === "present") ? "default" : "outline"}
                        size="sm"
                        onClick={() => handleAttendanceChange(student.id, "present")}
                        className="text-green-600 border-green-300 hover:bg-green-50"
                      >
                        <CheckCircle className="h-4 w-4 mr-1" />
                        Present
                      </Button>
                      <Button
                        variant={attendanceData[student.id] === "absent" || (!attendanceData[student.id] && student.status === "absent") ? "default" : "outline"}
                        size="sm"
                        onClick={() => handleAttendanceChange(student.id, "absent")}
                        className="text-red-600 border-red-300 hover:bg-red-50"
                      >
                        <XCircle className="h-4 w-4 mr-1" />
                        Absent
                      </Button>
                      <Button
                        variant={attendanceData[student.id] === "late" || (!attendanceData[student.id] && student.status === "late") ? "default" : "outline"}
                        size="sm"
                        onClick={() => handleAttendanceChange(student.id, "late")}
                        className="text-yellow-600 border-yellow-300 hover:bg-yellow-50"
                      >
                        <Clock className="h-4 w-4 mr-1" />
                        Late
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Quick Stats */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Stats</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Weekly Average:</span>
                    <span className="font-medium">{attendanceStats.weeklyAverage}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Monthly Average:</span>
                    <span className="font-medium">{attendanceStats.monthlyAverage}%</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div
                      className="bg-blue-500 h-2 rounded-full"
                      style={{ width: `${attendanceStats.attendanceRate}%` }}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Recent Attendance */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Attendance</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {recentAttendance.map((record, index) => (
                    <div key={index} className="p-3 border rounded-lg">
                      <div className="flex justify-between items-center mb-2">
                        <span className="font-medium text-gray-900">{record.date}</span>
                        <span className={`text-sm font-medium ${
                          record.rate >= 95 ? 'text-green-600' :
                          record.rate >= 90 ? 'text-blue-600' :
                          record.rate >= 85 ? 'text-yellow-600' : 'text-red-600'
                        }`}>
                          {record.rate}%
                        </span>
                      </div>
                      <div className="text-sm text-gray-600">
                        Present: {record.present} | Absent: {record.absent}
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-1 mt-2">
                        <div
                          className={`h-1 rounded-full ${
                            record.rate >= 95 ? 'bg-green-500' :
                            record.rate >= 90 ? 'bg-blue-500' :
                            record.rate >= 85 ? 'bg-yellow-500' : 'bg-red-500'
                          }`}
                          style={{ width: `${record.rate}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* My Classes */}
            <Card>
              <CardHeader>
                <CardTitle>My Classes</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  {teacherClasses.map((cls) => (
                    <div
                      key={cls.id}
                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                        selectedClass === cls.name ? 'bg-blue-50 border-blue-200' : 'hover:bg-gray-50'
                      }`}
                      onClick={() => setSelectedClass(cls.name)}
                    >
                      <div className="font-medium text-gray-900">{cls.name}</div>
                      <div className="text-sm text-gray-600">{cls.subject}</div>
                      <div className="text-xs text-gray-500">{cls.students} students</div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
