import { Hono } from "hono";
import { db } from "@/lib/db";
import { users, students, teachers, classes } from "@/lib/db/schema";
import { count } from "drizzle-orm";

const app = new Hono()
  .get("/stats", async (c) => {
    try {
      // Get database statistics
      const [userCount] = await db.select({ count: count() }).from(users);
      const [studentCount] = await db.select({ count: count() }).from(students);
      const [teacherCount] = await db.select({ count: count() }).from(teachers);
      const [classCount] = await db.select({ count: count() }).from(classes);

      return c.json({
        data: {
          users: userCount.count,
          students: studentCount.count,
          teachers: teacherCount.count,
          classes: classCount.count,
        },
      });
    } catch (error) {
      console.error("Database error:", error);
      return c.json({ error: "Failed to fetch database stats" }, 500);
    }
  })
  .get("/health", async (c) => {
    try {
      // Simple health check - try to query the database
      await db.select({ count: count() }).from(users).limit(1);
      
      return c.json({
        status: "healthy",
        database: "connected",
        timestamp: new Date().toISOString(),
      });
    } catch (error) {
      console.error("Database health check failed:", error);
      return c.json({
        status: "unhealthy",
        database: "disconnected",
        error: error instanceof Error ? error.message : "Unknown error",
        timestamp: new Date().toISOString(),
      }, 500);
    }
  });

export default app;
