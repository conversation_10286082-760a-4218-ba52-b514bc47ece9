"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  IndianRupee,
  Calendar,
  CreditCard,
  Download,
  Eye,
  AlertTriangle,
  CheckCircle,
  Clock,
  Receipt,
  FileText,
  TrendingUp,
  Target,
  Banknote,
  History,
  User,
  Bell,
} from "lucide-react";

export default function ParentFees() {
  const [user, setUser] = useState<any>(null);
  const [selectedChild, setSelectedChild] = useState("child1");
  const [selectedTab, setSelectedTab] = useState("overview");
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "parent") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock data for parent's children
  const children = [
    { 
      id: "child1", 
      name: "John Doe", 
      class: "Grade 10A", 
      rollNo: "10A001",
      totalFees: 125000,
      paidAmount: 75000,
      pendingAmount: 50000,
      overdueAmount: 15000,
    },
    { 
      id: "child2", 
      name: "Jane Doe", 
      class: "Grade 8B", 
      rollNo: "8B015",
      totalFees: 110000,
      paidAmount: 85000,
      pendingAmount: 25000,
      overdueAmount: 0,
    },
  ];

  const selectedChildData = children.find(child => child.id === selectedChild) || children[0];

  const feeStructure = [
    { category: "Tuition Fee", amount: 60000, paid: 40000, pending: 20000, dueDate: "2024-02-15", status: "Partial" },
    { category: "Development Fee", amount: 15000, paid: 15000, pending: 0, dueDate: "2024-04-15", status: "Paid" },
    { category: "Laboratory Fee", amount: 12000, paid: 8000, pending: 4000, dueDate: "2024-02-15", status: "Partial" },
    { category: "Library Fee", amount: 5000, paid: 5000, pending: 0, dueDate: "2024-04-15", status: "Paid" },
    { category: "Sports Fee", amount: 8000, paid: 0, pending: 8000, dueDate: "2024-01-30", status: "Overdue" },
    { category: "Transport Fee", amount: 18000, paid: 6000, pending: 12000, dueDate: "2024-02-15", status: "Partial" },
    { category: "Examination Fee", amount: 7000, paid: 1000, pending: 6000, dueDate: "2024-03-01", status: "Pending" },
  ];

  const paymentHistory = [
    { id: "PAY001", date: "2024-01-15", amount: 25000, method: "Online Banking", category: "Tuition Fee", status: "Success", receipt: "RCP001", child: "John Doe" },
    { id: "PAY002", date: "2024-01-10", amount: 15000, method: "UPI", category: "Development Fee", status: "Success", receipt: "RCP002", child: "John Doe" },
    { id: "PAY003", date: "2024-01-08", amount: 20000, method: "Credit Card", category: "Tuition Fee", status: "Success", receipt: "RCP003", child: "Jane Doe" },
    { id: "PAY004", date: "2023-12-20", amount: 5000, method: "Cash", category: "Library Fee", status: "Success", receipt: "RCP004", child: "John Doe" },
    { id: "PAY005", date: "2023-12-15", amount: 15000, method: "Bank Transfer", category: "Development Fee", status: "Success", receipt: "RCP005", child: "Jane Doe" },
  ];

  const upcomingDues = [
    { child: "John Doe", category: "Sports Fee", amount: 8000, dueDate: "2024-01-30", daysLeft: -10, status: "Overdue" },
    { child: "John Doe", category: "Tuition Fee", amount: 20000, dueDate: "2024-02-15", daysLeft: 5, status: "Due Soon" },
    { child: "Jane Doe", category: "Transport Fee", amount: 12000, dueDate: "2024-02-15", daysLeft: 5, status: "Due Soon" },
    { child: "John Doe", category: "Laboratory Fee", amount: 4000, dueDate: "2024-02-15", daysLeft: 5, status: "Due Soon" },
  ];

  const feeAlerts = [
    { child: "John Doe", type: "Overdue Payment", message: "Sports Fee payment is 10 days overdue", severity: "error" },
    { child: "John Doe", type: "Due Soon", message: "Multiple fees due in 5 days", severity: "warning" },
    { child: "Jane Doe", type: "Payment Reminder", message: "Transport fee due in 5 days", severity: "info" },
  ];

  const totalFamilyFees = children.reduce((total, child) => total + child.totalFees, 0);
  const totalPaidAmount = children.reduce((total, child) => total + child.paidAmount, 0);
  const totalPendingAmount = children.reduce((total, child) => total + child.pendingAmount, 0);
  const totalOverdueAmount = children.reduce((total, child) => total + child.overdueAmount, 0);

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Paid": return "text-green-600 bg-green-100";
      case "Partial": return "text-yellow-600 bg-yellow-100";
      case "Pending": return "text-blue-600 bg-blue-100";
      case "Overdue": return "text-red-600 bg-red-100";
      case "Success": return "text-green-600 bg-green-100";
      case "Failed": return "text-red-600 bg-red-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Paid": return <CheckCircle className="h-4 w-4" />;
      case "Success": return <CheckCircle className="h-4 w-4" />;
      case "Overdue": return <AlertTriangle className="h-4 w-4" />;
      case "Failed": return <AlertTriangle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const getAlertColor = (severity: string) => {
    switch (severity) {
      case "error": return "border-red-200 bg-red-50 text-red-800";
      case "warning": return "border-yellow-200 bg-yellow-50 text-yellow-800";
      case "info": return "border-blue-200 bg-blue-50 text-blue-800";
      default: return "border-gray-200 bg-gray-50 text-gray-800";
    }
  };

  const getDaysLeftColor = (daysLeft: number) => {
    if (daysLeft < 0) return "text-red-600";
    if (daysLeft <= 7) return "text-orange-600";
    return "text-green-600";
  };

  const calculatePercentagePaid = (paid: number, total: number) => {
    return ((paid / total) * 100).toFixed(1);
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Fee Management</h1>
            <p className="text-gray-600">Manage fees and payments for all your children</p>
          </div>
          <div className="flex gap-2">
            <select
              value={selectedChild}
              onChange={(e) => setSelectedChild(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Children</option>
              {children.map((child) => (
                <option key={child.id} value={child.id}>
                  {child.name} - {child.class}
                </option>
              ))}
            </select>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Download Report
            </Button>
            <Button>
              <CreditCard className="h-4 w-4 mr-2" />
              Make Payment
            </Button>
          </div>
        </div>

        {/* Family Fee Overview */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <IndianRupee className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    ₹{(totalFamilyFees / 100000).toFixed(1)}L
                  </div>
                  <p className="text-sm text-gray-600">Total Family Fees</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    ₹{(totalPaidAmount / 100000).toFixed(1)}L
                  </div>
                  <p className="text-sm text-gray-600">Total Paid</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600">
                      {calculatePercentagePaid(totalPaidAmount, totalFamilyFees)}%
                    </span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    ₹{(totalPendingAmount / 1000).toFixed(0)}K
                  </div>
                  <p className="text-sm text-gray-600">Total Pending</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <AlertTriangle className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-red-600">
                    ₹{(totalOverdueAmount / 1000).toFixed(0)}K
                  </div>
                  <p className="text-sm text-gray-600">Total Overdue</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Children Overview */}
        <Card>
          <CardHeader>
            <CardTitle>Children Fee Status</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              {children.map((child) => (
                <div key={child.id} className="p-4 border rounded-lg">
                  <div className="flex items-center space-x-4 mb-4">
                    <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                      <User className="h-6 w-6 text-blue-600" />
                    </div>
                    <div>
                      <h3 className="font-semibold text-gray-900">{child.name}</h3>
                      <p className="text-sm text-gray-600">{child.class} • {child.rollNo}</p>
                    </div>
                  </div>
                  
                  <div className="grid gap-3 md:grid-cols-3 text-sm mb-4">
                    <div className="text-center p-3 bg-blue-50 rounded-lg">
                      <div className="font-bold text-blue-600">₹{(child.totalFees / 1000).toFixed(0)}K</div>
                      <div className="text-gray-500">Total Fees</div>
                    </div>
                    <div className="text-center p-3 bg-green-50 rounded-lg">
                      <div className="font-bold text-green-600">₹{(child.paidAmount / 1000).toFixed(0)}K</div>
                      <div className="text-gray-500">Paid</div>
                    </div>
                    <div className="text-center p-3 bg-orange-50 rounded-lg">
                      <div className="font-bold text-orange-600">₹{(child.pendingAmount / 1000).toFixed(0)}K</div>
                      <div className="text-gray-500">Pending</div>
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="w-full bg-gray-200 rounded-full h-2 mr-4">
                      <div 
                        className="bg-blue-500 h-2 rounded-full"
                        style={{ width: `${calculatePercentagePaid(child.paidAmount, child.totalFees)}%` }}
                      />
                    </div>
                    <span className="text-sm font-medium text-gray-600">
                      {calculatePercentagePaid(child.paidAmount, child.totalFees)}%
                    </span>
                  </div>

                  {child.overdueAmount > 0 && (
                    <div className="mt-3 p-2 bg-red-50 border border-red-200 rounded-lg">
                      <div className="flex items-center text-red-700">
                        <AlertTriangle className="h-4 w-4 mr-2" />
                        <span className="text-sm font-medium">₹{child.overdueAmount.toLocaleString()} overdue</span>
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Fee Alerts */}
        {feeAlerts.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Bell className="h-5 w-5 mr-2" />
                Fee Alerts & Notifications
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {feeAlerts.map((alert, index) => (
                  <div key={index} className={`p-4 border rounded-lg ${getAlertColor(alert.severity)}`}>
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium">{alert.type}</div>
                        <div className="text-sm">{alert.message}</div>
                      </div>
                      <div className="text-sm font-medium">{alert.child}</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Fee Tabs */}
        <Card>
          <CardHeader>
            <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
              {[
                { key: "overview", label: "Fee Structure" },
                { key: "history", label: "Payment History" },
                { key: "upcoming", label: "Upcoming Dues" },
                { key: "summary", label: "Annual Summary" },
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setSelectedTab(tab.key)}
                  className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                    selectedTab === tab.key
                      ? "bg-white text-blue-600 shadow-sm"
                      : "text-gray-600 hover:text-gray-900"
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </div>
          </CardHeader>
          <CardContent>
            {/* Fee Structure */}
            {selectedTab === "overview" && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">
                  Fee Structure - {selectedChild === "all" ? "All Children" : selectedChildData.name}
                </h3>
                <div className="space-y-4">
                  {feeStructure.map((fee, index) => (
                    <div key={index} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <h4 className="font-medium text-gray-900">{fee.category}</h4>
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(fee.status)}`}>
                            {getStatusIcon(fee.status)}
                            <span className="ml-1">{fee.status}</span>
                          </span>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-bold text-blue-600">₹{fee.amount.toLocaleString()}</div>
                          <div className="text-sm text-gray-500">Due: {fee.dueDate}</div>
                        </div>
                      </div>
                      
                      <div className="grid gap-3 md:grid-cols-3 text-sm mb-3">
                        <div className="text-center p-3 bg-green-50 rounded-lg">
                          <div className="font-bold text-green-600">₹{fee.paid.toLocaleString()}</div>
                          <div className="text-gray-500">Paid</div>
                        </div>
                        <div className="text-center p-3 bg-orange-50 rounded-lg">
                          <div className="font-bold text-orange-600">₹{fee.pending.toLocaleString()}</div>
                          <div className="text-gray-500">Pending</div>
                        </div>
                        <div className="text-center p-3 bg-blue-50 rounded-lg">
                          <div className="font-bold text-blue-600">{calculatePercentagePaid(fee.paid, fee.amount)}%</div>
                          <div className="text-gray-500">Completed</div>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="w-full bg-gray-200 rounded-full h-2 mr-4">
                          <div 
                            className={`h-2 rounded-full ${
                              fee.status === 'Paid' ? 'bg-green-500' :
                              fee.status === 'Partial' ? 'bg-yellow-500' :
                              fee.status === 'Overdue' ? 'bg-red-500' : 'bg-blue-500'
                            }`}
                            style={{ width: `${calculatePercentagePaid(fee.paid, fee.amount)}%` }}
                          />
                        </div>
                        {fee.pending > 0 && (
                          <Button size="sm">
                            <CreditCard className="h-3 w-3 mr-1" />
                            Pay Now
                          </Button>
                        )}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Payment History */}
            {selectedTab === "history" && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Payment History</h3>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Date</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Child</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Amount</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Category</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Method</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Receipt</th>
                      </tr>
                    </thead>
                    <tbody>
                      {paymentHistory.map((payment) => (
                        <tr key={payment.id} className="border-b hover:bg-gray-50">
                          <td className="py-3 px-4 text-gray-900">{payment.date}</td>
                          <td className="py-3 px-4 text-gray-600">{payment.child}</td>
                          <td className="py-3 px-4 font-medium text-gray-900">₹{payment.amount.toLocaleString()}</td>
                          <td className="py-3 px-4 text-gray-600">{payment.category}</td>
                          <td className="py-3 px-4 text-gray-600">{payment.method}</td>
                          <td className="py-3 px-4">
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(payment.status)}`}>
                              {getStatusIcon(payment.status)}
                              <span className="ml-1">{payment.status}</span>
                            </span>
                          </td>
                          <td className="py-3 px-4">
                            <Button variant="outline" size="sm">
                              <Download className="h-3 w-3 mr-1" />
                              {payment.receipt}
                            </Button>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* Upcoming Dues */}
            {selectedTab === "upcoming" && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Upcoming Dues</h3>
                <div className="space-y-3">
                  {upcomingDues.map((due, index) => (
                    <div key={index} className={`p-4 border rounded-lg ${
                      due.status === 'Overdue' ? 'border-red-200 bg-red-50' : 
                      due.status === 'Due Soon' ? 'border-orange-200 bg-orange-50' : 'border-gray-200'
                    }`}>
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div>
                            <div className="font-medium text-gray-900">{due.category}</div>
                            <div className="text-sm text-gray-600">{due.child} • Due: {due.dueDate}</div>
                          </div>
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(due.status)}`}>
                            {due.status}
                          </span>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-bold text-gray-900">₹{due.amount.toLocaleString()}</div>
                          <div className={`text-sm font-medium ${getDaysLeftColor(due.daysLeft)}`}>
                            {due.daysLeft < 0 ? `${Math.abs(due.daysLeft)} days overdue` : 
                             due.daysLeft === 0 ? 'Due today' : 
                             `${due.daysLeft} days left`}
                          </div>
                        </div>
                      </div>
                      <div className="mt-3 flex justify-end">
                        <Button size="sm" className={due.status === 'Overdue' ? 'bg-red-600 hover:bg-red-700' : ''}>
                          <CreditCard className="h-3 w-3 mr-1" />
                          Pay Now
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Annual Summary */}
            {selectedTab === "summary" && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900">Annual Fee Summary</h3>
                
                <div className="grid gap-6 lg:grid-cols-2">
                  {/* Payment Summary */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-4">Payment Summary</h4>
                    <div className="space-y-3">
                      <div className="p-4 bg-blue-50 rounded-lg">
                        <div className="flex justify-between items-center">
                          <span className="text-blue-700">Total Fees (All Children)</span>
                          <span className="font-bold text-blue-800">₹{totalFamilyFees.toLocaleString()}</span>
                        </div>
                      </div>
                      <div className="p-4 bg-green-50 rounded-lg">
                        <div className="flex justify-between items-center">
                          <span className="text-green-700">Amount Paid</span>
                          <span className="font-bold text-green-800">₹{totalPaidAmount.toLocaleString()}</span>
                        </div>
                      </div>
                      <div className="p-4 bg-orange-50 rounded-lg">
                        <div className="flex justify-between items-center">
                          <span className="text-orange-700">Remaining Balance</span>
                          <span className="font-bold text-orange-800">₹{totalPendingAmount.toLocaleString()}</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  {/* Payment Progress */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-4">Payment Progress</h4>
                    <div className="space-y-4">
                      {children.map((child) => (
                        <div key={child.id} className="p-4 border rounded-lg">
                          <div className="flex justify-between items-center mb-2">
                            <span className="font-medium text-gray-900">{child.name}</span>
                            <span className="text-sm text-gray-600">
                              {calculatePercentagePaid(child.paidAmount, child.totalFees)}% paid
                            </span>
                          </div>
                          <div className="w-full bg-gray-200 rounded-full h-3">
                            <div 
                              className="bg-blue-500 h-3 rounded-full"
                              style={{ width: `${calculatePercentagePaid(child.paidAmount, child.totalFees)}%` }}
                            />
                          </div>
                          <div className="flex justify-between text-sm text-gray-600 mt-1">
                            <span>₹{child.paidAmount.toLocaleString()} paid</span>
                            <span>₹{child.totalFees.toLocaleString()} total</span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
