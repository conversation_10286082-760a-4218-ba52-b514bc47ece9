"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Users,
  Search,
  Filter,
  Download,
  Plus,
  Eye,
  Edit,
  Trash2,
  Mail,
  Phone,
  Calendar,
  BookOpen,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  User,
  GraduationCap,
  Building,
  MapPin,
  CreditCard,
  History,
} from "lucide-react";

export default function LibraryMembers() {
  const [user, setUser] = useState<any>(null);
  const [selectedTab, setSelectedTab] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedMember, setSelectedMember] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "library") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  const memberStats = {
    totalMembers: 1234,
    activeMembers: 892,
    inactiveMembers: 342,
    newMembersThisMonth: 23,
    studentsMembers: 756,
    teachersMembers: 89,
    staffMembers: 47,
  };

  const mockMembers = [
    {
      id: "LM001",
      name: "Aarav Sharma",
      email: "<EMAIL>",
      phone: "+91 98765 43210",
      type: "Student",
      class: "Class 10-A",
      membershipDate: "2024-01-15",
      status: "Active",
      booksIssued: 2,
      maxAllowed: 3,
      fineAmount: 0,
      lastActivity: "2024-01-20",
    },
    {
      id: "LM002",
      name: "Priya Patel",
      email: "<EMAIL>",
      phone: "+91 98765 43211",
      type: "Teacher",
      department: "Mathematics",
      membershipDate: "2023-08-10",
      status: "Active",
      booksIssued: 5,
      maxAllowed: 10,
      fineAmount: 25,
      lastActivity: "2024-01-19",
    },
    {
      id: "LM003",
      name: "Rahul Kumar",
      email: "<EMAIL>",
      phone: "+91 98765 43212",
      type: "Student",
      class: "Class 12-B",
      membershipDate: "2023-06-20",
      status: "Inactive",
      booksIssued: 0,
      maxAllowed: 3,
      fineAmount: 50,
      lastActivity: "2023-12-15",
    },
    {
      id: "LM004",
      name: "Sneha Reddy",
      email: "<EMAIL>",
      phone: "+91 98765 43213",
      type: "Staff",
      department: "Administration",
      membershipDate: "2023-09-05",
      status: "Active",
      booksIssued: 1,
      maxAllowed: 5,
      fineAmount: 0,
      lastActivity: "2024-01-18",
    },
    {
      id: "LM005",
      name: "Vikram Singh",
      email: "<EMAIL>",
      phone: "+91 98765 43214",
      type: "Student",
      class: "Class 11-C",
      membershipDate: "2023-11-12",
      status: "Active",
      booksIssued: 3,
      maxAllowed: 3,
      fineAmount: 15,
      lastActivity: "2024-01-21",
    },
  ];

  const filteredMembers = mockMembers.filter((member) => {
    const matchesSearch = member.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         member.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         member.id.toLowerCase().includes(searchTerm.toLowerCase());

    if (selectedTab === "all") return matchesSearch;
    if (selectedTab === "active") return matchesSearch && member.status === "Active";
    if (selectedTab === "inactive") return matchesSearch && member.status === "Inactive";
    if (selectedTab === "students") return matchesSearch && member.type === "Student";
    if (selectedTab === "teachers") return matchesSearch && member.type === "Teacher";
    if (selectedTab === "staff") return matchesSearch && member.type === "Staff";

    return matchesSearch;
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Active":
        return <Badge className="bg-green-100 text-green-800">Active</Badge>;
      case "Inactive":
        return <Badge className="bg-red-100 text-red-800">Inactive</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }
  };

  const getTypeBadge = (type: string) => {
    switch (type) {
      case "Student":
        return <Badge variant="outline" className="text-blue-600 border-blue-200">Student</Badge>;
      case "Teacher":
        return <Badge variant="outline" className="text-purple-600 border-purple-200">Teacher</Badge>;
      case "Staff":
        return <Badge variant="outline" className="text-orange-600 border-orange-200">Staff</Badge>;
      default:
        return <Badge variant="outline">{type}</Badge>;
    }
  };

  const viewMemberDetails = (member: any) => {
    setSelectedMember(member);
  };

  const editMember = (memberId: string) => {
    alert(`Editing member ${memberId}`);
  };

  const deleteMember = (memberId: string) => {
    alert(`Deleting member ${memberId}`);
  };

  const suspendMember = (memberId: string) => {
    alert(`Suspending member ${memberId}`);
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Library Members</h1>
            <p className="text-gray-600">Manage library membership and member information</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Members
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Member
            </Button>
          </div>
        </div>

        {/* Member Statistics */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {memberStats.totalMembers}
                  </div>
                  <p className="text-sm text-gray-600">Total Members</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {memberStats.activeMembers}
                  </div>
                  <p className="text-sm text-gray-600">Active Members</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <XCircle className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-red-600">
                    {memberStats.inactiveMembers}
                  </div>
                  <p className="text-sm text-gray-600">Inactive Members</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Plus className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {memberStats.newMembersThisMonth}
                  </div>
                  <p className="text-sm text-gray-600">New This Month</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filter */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search members by name, email, or ID..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </div>

            {/* Tabs */}
            <div className="flex flex-wrap gap-2 mt-4">
              {[
                { key: "all", label: "All Members", count: memberStats.totalMembers },
                { key: "active", label: "Active", count: memberStats.activeMembers },
                { key: "inactive", label: "Inactive", count: memberStats.inactiveMembers },
                { key: "students", label: "Students", count: memberStats.studentsMembers },
                { key: "teachers", label: "Teachers", count: memberStats.teachersMembers },
                { key: "staff", label: "Staff", count: memberStats.staffMembers },
              ].map((tab) => (
                <Button
                  key={tab.key}
                  variant={selectedTab === tab.key ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedTab(tab.key)}
                  className="text-xs"
                >
                  {tab.label} ({tab.count})
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Members Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Members List ({filteredMembers.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-4 font-medium text-gray-900">Member</th>
                    <th className="text-left p-4 font-medium text-gray-900">Type</th>
                    <th className="text-left p-4 font-medium text-gray-900">Status</th>
                    <th className="text-left p-4 font-medium text-gray-900">Books</th>
                    <th className="text-left p-4 font-medium text-gray-900">Fine</th>
                    <th className="text-left p-4 font-medium text-gray-900">Last Activity</th>
                    <th className="text-left p-4 font-medium text-gray-900">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredMembers.map((member) => (
                    <tr key={member.id} className="border-b hover:bg-gray-50">
                      <td className="p-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <User className="h-5 w-5 text-blue-600" />
                          </div>
                          <div>
                            <div className="font-medium text-gray-900">{member.name}</div>
                            <div className="text-sm text-gray-500">{member.id}</div>
                            <div className="text-sm text-gray-500">{member.email}</div>
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-1">
                          {getTypeBadge(member.type)}
                          <div className="text-sm text-gray-500">
                            {member.type === "Student" ? member.class : member.department}
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        {getStatusBadge(member.status)}
                      </td>
                      <td className="p-4">
                        <div className="text-sm">
                          <div className="font-medium">{member.booksIssued}/{member.maxAllowed}</div>
                          <div className="text-gray-500">Books Issued</div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="text-sm">
                          {member.fineAmount > 0 ? (
                            <div className="font-medium text-red-600">₹{member.fineAmount}</div>
                          ) : (
                            <div className="font-medium text-green-600">₹0</div>
                          )}
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="text-sm text-gray-500">
                          {new Date(member.lastActivity).toLocaleDateString()}
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => viewMemberDetails(member)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => editMember(member.id)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => deleteMember(member.id)}
                          >
                            <Trash2 className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Member Details Modal */}
        {selectedMember && (
          <Card className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white rounded-lg p-6 max-w-2xl w-full mx-4 max-h-[90vh] overflow-y-auto">
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-semibold">Member Details</h3>
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setSelectedMember(null)}
                >
                  ×
                </Button>
              </div>

              <div className="grid gap-4 md:grid-cols-2">
                <div>
                  <label className="text-sm font-medium text-gray-700">Name</label>
                  <p className="text-gray-900">{selectedMember.name}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Member ID</label>
                  <p className="text-gray-900">{selectedMember.id}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Email</label>
                  <p className="text-gray-900">{selectedMember.email}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Phone</label>
                  <p className="text-gray-900">{selectedMember.phone}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Type</label>
                  <p className="text-gray-900">{selectedMember.type}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Status</label>
                  <p className="text-gray-900">{selectedMember.status}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Books Issued</label>
                  <p className="text-gray-900">{selectedMember.booksIssued}/{selectedMember.maxAllowed}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Fine Amount</label>
                  <p className="text-gray-900">₹{selectedMember.fineAmount}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Membership Date</label>
                  <p className="text-gray-900">{new Date(selectedMember.membershipDate).toLocaleDateString()}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-gray-700">Last Activity</label>
                  <p className="text-gray-900">{new Date(selectedMember.lastActivity).toLocaleDateString()}</p>
                </div>
              </div>

              <div className="flex gap-2 mt-6">
                <Button onClick={() => editMember(selectedMember.id)}>
                  <Edit className="h-4 w-4 mr-2" />
                  Edit Member
                </Button>
                <Button variant="outline" onClick={() => suspendMember(selectedMember.id)}>
                  <AlertTriangle className="h-4 w-4 mr-2" />
                  Suspend
                </Button>
              </div>
            </div>
          </Card>
        )}
      </div>
    </AppLayout>
  );
}