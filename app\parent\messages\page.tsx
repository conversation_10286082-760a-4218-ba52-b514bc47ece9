"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  MessageCircle,
  Send,
  Inbox,
  Archive,
  Star,
  Search,
  Filter,
  User,
  Calendar,
  Clock,
  Paperclip,
  Reply,
  Forward,
  Trash2,
  Plus,
  Bell,
  CheckCircle,
  AlertTriangle,
} from "lucide-react";

export default function ParentMessages() {
  const [user, setUser] = useState<any>(null);
  const [selectedChild, setSelectedChild] = useState("child1");
  const [selectedTab, setSelectedTab] = useState("inbox");
  const [selectedMessage, setSelectedMessage] = useState<any>(null);
  const [searchTerm, setSearchTerm] = useState("");
  const [newMessage, setNewMessage] = useState("");
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "parent") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock data for parent's children
  const children = [
    { id: "child1", name: "John Doe", class: "Grade 10A", rollNo: "10A001" },
    { id: "child2", name: "Jane Doe", class: "Grade 8B", rollNo: "8B015" },
  ];

  const selectedChildData = children.find(child => child.id === selectedChild) || children[0];

  const messageStats = {
    totalMessages: 45,
    unreadMessages: 8,
    importantMessages: 12,
    archivedMessages: 15,
  };

  const messages = [
    {
      id: "1",
      from: "Dr. Sarah Johnson",
      role: "Mathematics Teacher",
      subject: "John&apos;s Performance in Mathematics",
      preview: "I wanted to discuss John&apos;s recent improvement in algebra. He has shown excellent progress...",
      content: "Dear Mr. and Mrs. Doe,\n\nI wanted to discuss John's recent improvement in algebra. He has shown excellent progress in understanding quadratic equations and has been actively participating in class discussions. His recent test score of 92% reflects his dedication and hard work.\n\nI would like to suggest some additional practice problems to further strengthen his concepts. Please let me know if you would like to schedule a meeting to discuss his academic progress in detail.\n\nBest regards,\nDr. Sarah Johnson\nMathematics Teacher",
      date: "2024-01-20",
      time: "10:30 AM",
      isRead: false,
      isImportant: true,
      hasAttachment: false,
      child: "John Doe",
      type: "academic",
    },
    {
      id: "2",
      from: "Principal Office",
      role: "Principal",
      subject: "Parent-Teacher Meeting Schedule",
      preview: "The upcoming parent-teacher meeting is scheduled for February 5th, 2024...",
      content: "Dear Parents,\n\nThe upcoming parent-teacher meeting is scheduled for February 5th, 2024, from 2:00 PM to 6:00 PM. This is an excellent opportunity to discuss your child's academic progress, behavior, and any concerns you may have.\n\nPlease confirm your attendance by replying to this message. Time slots will be allocated on a first-come, first-served basis.\n\nLooking forward to meeting you.\n\nBest regards,\nPrincipal\nSt. Mary's School",
      date: "2024-01-19",
      time: "2:15 PM",
      isRead: true,
      isImportant: true,
      hasAttachment: true,
      child: "Both Children",
      type: "meeting",
    },
    {
      id: "3",
      from: "Finance Office",
      role: "Finance Manager",
      subject: "Fee Payment Reminder - John Doe",
      preview: "This is a friendly reminder that the sports fee payment is due...",
      content: "Dear Mr. and Mrs. Doe,\n\nThis is a friendly reminder that the sports fee payment for John Doe (Roll No: 10A001) is due on January 30th, 2024.\n\nAmount Due: ₹8,000\nDue Date: January 30th, 2024\n\nYou can make the payment through our online portal or visit the finance office during working hours.\n\nThank you for your cooperation.\n\nBest regards,\nFinance Office",
      date: "2024-01-18",
      time: "11:45 AM",
      isRead: false,
      isImportant: false,
      hasAttachment: false,
      child: "John Doe",
      type: "finance",
    },
    {
      id: "4",
      from: "Ms. Lisa Wilson",
      role: "English Teacher",
      subject: "Jane&apos;s Creative Writing Achievement",
      preview: "I am pleased to inform you that Jane has won the inter-class essay competition...",
      content: "Dear Mr. and Mrs. Doe,\n\nI am pleased to inform you that Jane has won the inter-class essay competition with her outstanding piece on 'Climate Change and Our Future'. Her creativity and writing skills are truly remarkable.\n\nShe will be representing our school in the district-level competition next month. I believe she has great potential and would benefit from additional creative writing workshops.\n\nCongratulations to Jane and to you as proud parents!\n\nBest regards,\nMs. Lisa Wilson\nEnglish Teacher",
      date: "2024-01-17",
      time: "4:20 PM",
      isRead: true,
      isImportant: true,
      hasAttachment: false,
      child: "Jane Doe",
      type: "achievement",
    },
    {
      id: "5",
      from: "School Nurse",
      role: "Health Department",
      subject: "Health Checkup Report - Jane Doe",
      preview: "The annual health checkup for Jane has been completed...",
      content: "Dear Parents,\n\nThe annual health checkup for Jane Doe has been completed. Overall, she is in excellent health. However, we recommend a dental checkup as a routine preventive measure.\n\nThe detailed report is attached to this message. Please review it and contact us if you have any questions.\n\nBest regards,\nSchool Health Department",
      date: "2024-01-16",
      time: "1:30 PM",
      isRead: true,
      isImportant: false,
      hasAttachment: true,
      child: "Jane Doe",
      type: "health",
    },
  ];

  const sentMessages = [
    {
      id: "s1",
      to: "Dr. Sarah Johnson",
      subject: "Re: John&apos;s Performance in Mathematics",
      preview: "Thank you for the update. We would like to schedule a meeting...",
      date: "2024-01-20",
      time: "2:45 PM",
      child: "John Doe",
    },
    {
      id: "s2",
      to: "Principal Office",
      subject: "Absence Request - Jane Doe",
      preview: "Jane will be absent on January 25th due to a family function...",
      date: "2024-01-19",
      time: "9:15 AM",
      child: "Jane Doe",
    },
  ];

  const getMessageTypeColor = (type: string) => {
    switch (type) {
      case "academic": return "text-blue-600 bg-blue-100";
      case "meeting": return "text-purple-600 bg-purple-100";
      case "finance": return "text-orange-600 bg-orange-100";
      case "achievement": return "text-green-600 bg-green-100";
      case "health": return "text-red-600 bg-red-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const getMessageTypeIcon = (type: string) => {
    switch (type) {
      case "academic": return "📚";
      case "meeting": return "🤝";
      case "finance": return "💰";
      case "achievement": return "🏆";
      case "health": return "🏥";
      default: return "📧";
    }
  };

  const filteredMessages = messages.filter(message =>
    message.subject.toLowerCase().includes(searchTerm.toLowerCase()) ||
    message.from.toLowerCase().includes(searchTerm.toLowerCase()) ||
    message.preview.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getTabMessages = () => {
    switch (selectedTab) {
      case "inbox":
        return filteredMessages;
      case "sent":
        return sentMessages;
      case "important":
        return filteredMessages.filter(msg => msg.isImportant);
      case "unread":
        return filteredMessages.filter(msg => !msg.isRead);
      default:
        return filteredMessages;
    }
  };

  const sendMessage = () => {
    if (newMessage.trim()) {
      // Send message logic here
      alert("Message sent successfully!");
      setNewMessage("");
    }
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Messages</h1>
            <p className="text-gray-600">Communicate with teachers and school administration</p>
          </div>
          <div className="flex gap-2">
            <select
              value={selectedChild}
              onChange={(e) => setSelectedChild(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Children</option>
              {children.map((child) => (
                <option key={child.id} value={child.id}>
                  {child.name} - {child.class}
                </option>
              ))}
            </select>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Message
            </Button>
          </div>
        </div>

        {/* Message Stats */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Inbox className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {messageStats.totalMessages}
                  </div>
                  <p className="text-sm text-gray-600">Total Messages</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Bell className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    {messageStats.unreadMessages}
                  </div>
                  <p className="text-sm text-gray-600">Unread Messages</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Star className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-yellow-600">
                    {messageStats.importantMessages}
                  </div>
                  <p className="text-sm text-gray-600">Important</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Archive className="h-8 w-8 text-gray-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-gray-600">
                    {messageStats.archivedMessages}
                  </div>
                  <p className="text-sm text-gray-600">Archived</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Messages Interface */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Message List */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
                  {[
                    { key: "inbox", label: "Inbox", icon: Inbox },
                    { key: "sent", label: "Sent", icon: Send },
                    { key: "important", label: "Important", icon: Star },
                    { key: "unread", label: "Unread", icon: Bell },
                  ].map((tab) => (
                    <button
                      key={tab.key}
                      onClick={() => setSelectedTab(tab.key)}
                      className={`flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                        selectedTab === tab.key
                          ? "bg-white text-blue-600 shadow-sm"
                          : "text-gray-600 hover:text-gray-900"
                      }`}
                    >
                      <tab.icon className="h-4 w-4 mr-1" />
                      {tab.label}
                    </button>
                  ))}
                </div>
                <div className="flex gap-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search messages..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 w-64"
                    />
                  </div>
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-2">
                {getTabMessages().map((message) => (
                  <div
                    key={message.id}
                    onClick={() => setSelectedMessage(message)}
                    className={`p-4 border rounded-lg cursor-pointer transition-colors hover:bg-gray-50 ${
                      selectedMessage?.id === message.id ? 'bg-blue-50 border-blue-200' : ''
                    } ${!(message as any).isRead && selectedTab === 'inbox' ? 'bg-blue-25 border-l-4 border-l-blue-500' : ''}`}
                  >
                    <div className="flex items-start justify-between mb-2">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                          <User className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">{(message as any).from || (message as any).to}</div>
                          <div className="text-sm text-gray-500">{(message as any).role}</div>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        {(message as any).isImportant && <Star className="h-4 w-4 text-yellow-500" />}
                        {(message as any).hasAttachment && <Paperclip className="h-4 w-4 text-gray-400" />}
                        <span className="text-xs text-gray-500">{(message as any).time}</span>
                      </div>
                    </div>

                    <div className="mb-2">
                      <h4 className="font-medium text-gray-900 mb-1">{message.subject}</h4>
                      <p className="text-sm text-gray-600 line-clamp-2">{message.preview}</p>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getMessageTypeColor((message as any).type)}`}>
                          {getMessageTypeIcon((message as any).type)} {(message as any).type}
                        </span>
                        <span className="text-xs text-gray-500">{(message as any).child}</span>
                      </div>
                      <span className="text-xs text-gray-500">{message.date}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Message Detail / Compose */}
          <div className="space-y-6">
            {selectedMessage ? (
              <Card>
                <CardHeader>
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{selectedMessage.subject}</CardTitle>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        <Reply className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Forward className="h-4 w-4" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                  <div className="flex items-center space-x-4 text-sm text-gray-600">
                    <div className="flex items-center">
                      <User className="h-4 w-4 mr-1" />
                      {selectedMessage.from}
                    </div>
                    <div className="flex items-center">
                      <Calendar className="h-4 w-4 mr-1" />
                      {selectedMessage.date}
                    </div>
                    <div className="flex items-center">
                      <Clock className="h-4 w-4 mr-1" />
                      {selectedMessage.time}
                    </div>
                  </div>
                </CardHeader>
                <CardContent>
                  <div className="prose prose-sm max-w-none">
                    <p className="whitespace-pre-line">{selectedMessage.content}</p>
                  </div>
                  {selectedMessage.hasAttachment && (
                    <div className="mt-4 p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center">
                        <Paperclip className="h-4 w-4 text-gray-400 mr-2" />
                        <span className="text-sm text-gray-600">Attachment: meeting_schedule.pdf</span>
                        <Button variant="link" size="sm" className="ml-auto">
                          Download
                        </Button>
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardHeader>
                  <CardTitle>Compose New Message</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">To</label>
                      <select className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent">
                        <option>Select recipient...</option>
                        <option>Dr. Sarah Johnson - Mathematics Teacher</option>
                        <option>Ms. Lisa Wilson - English Teacher</option>
                        <option>Principal Office</option>
                        <option>Finance Office</option>
                      </select>
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Subject</label>
                      <Input placeholder="Enter subject..." />
                    </div>
                    <div>
                      <label className="block text-sm font-medium text-gray-700 mb-1">Message</label>
                      <textarea
                        value={newMessage}
                        onChange={(e) => setNewMessage(e.target.value)}
                        placeholder="Type your message here..."
                        rows={8}
                        className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                      />
                    </div>
                    <div className="flex gap-2">
                      <Button onClick={sendMessage} className="flex-1">
                        <Send className="h-4 w-4 mr-2" />
                        Send Message
                      </Button>
                      <Button variant="outline">
                        <Paperclip className="h-4 w-4" />
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            {/* Quick Actions */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Actions</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <Button variant="outline" className="w-full justify-start">
                    <MessageCircle className="h-4 w-4 mr-2" />
                    Contact Class Teacher
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <Calendar className="h-4 w-4 mr-2" />
                    Schedule Meeting
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <AlertTriangle className="h-4 w-4 mr-2" />
                    Report Issue
                  </Button>
                  <Button variant="outline" className="w-full justify-start">
                    <CheckCircle className="h-4 w-4 mr-2" />
                    Request Leave
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
