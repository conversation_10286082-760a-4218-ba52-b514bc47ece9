import { config } from "dotenv";
import { db } from "./index";
import {
  users,
  students,
  teachers,
  classes,
  feeStructures,
  books,
  transportRoutes,
  vehicles,
  hostels,
  hostelRooms,
  academicPrograms,
  academicBatches,
  classEnrollments
} from "./schema";

config({ path: ".env.local" });

async function seed() {
  console.log("🌱 Seeding database...");

  try {
    // Check if data already exists
    const existingUsers = await db.select().from(users).limit(1);
    if (existingUsers.length > 0) {
      console.log("📊 Database already seeded, skipping...");
      return;
    }

    // Create admin users
    console.log("👤 Creating users...");
    const adminUsers = await db.insert(users).values([
      {
        email: "<EMAIL>",
        password: "admin123", // In production, this should be hashed
        firstName: "Super",
        lastName: "Admin",
        phone: "1234567890",
        role: "super_admin",
      },
      {
        email: "<EMAIL>",
        password: "principal123",
        firstName: "<PERSON>",
        lastName: "Principal",
        phone: "1234567891",
        role: "principal",
      },
      {
        email: "<EMAIL>",
        password: "admin123",
        firstName: "Sarah",
        lastName: "Admin",
        phone: "1234567892",
        role: "admin",
      },
      {
        email: "<EMAIL>",
        password: "admission123",
        firstName: "Michael",
        lastName: "Johnson",
        phone: "1234567893",
        role: "admission_officer",
      },
      {
        email: "<EMAIL>",
        password: "finance123",
        firstName: "David",
        lastName: "Finance",
        phone: "1234567894",
        role: "finance_manager",
      },
      {
        email: "<EMAIL>",
        password: "admin123",
        firstName: "Emily",
        lastName: "Books",
        phone: "1234567894",
        role: "librarian",
      },
      {
        email: "<EMAIL>",
        password: "admin123",
        firstName: "David",
        lastName: "Transport",
        phone: "1234567895",
        role: "transport_manager",
      },
      {
        email: "<EMAIL>",
        password: "admin123",
        firstName: "Lisa",
        lastName: "Hostel",
        phone: "1234567896",
        role: "hostel_manager",
      },
    ]).returning();

    // Create teacher users
    const teacherUsers = await db.insert(users).values([
      {
        email: "<EMAIL>",
        password: "teacher123",
        firstName: "Emily",
        lastName: "Wilson",
        phone: "2234567890",
        role: "teacher",
      },
      {
        email: "<EMAIL>",
        password: "teacher123",
        firstName: "David",
        lastName: "Brown",
        phone: "2234567891",
        role: "teacher",
      },
      {
        email: "<EMAIL>",
        password: "teacher123",
        firstName: "Sarah",
        lastName: "Johnson",
        phone: "2234567892",
        role: "teacher",
      },
    ]).returning();

    // Create student users
    const studentUsers = await db.insert(users).values([
      {
        email: "<EMAIL>",
        password: "student123",
        firstName: "John",
        lastName: "Doe",
        phone: "3234567890",
        role: "student",
      },
      {
        email: "<EMAIL>",
        password: "student123",
        firstName: "Alice",
        lastName: "Smith",
        phone: "3234567891",
        role: "student",
      },
      {
        email: "<EMAIL>",
        password: "student123",
        firstName: "Michael",
        lastName: "Johnson",
        phone: "3234567892",
        role: "student",
      },
    ]).returning();

    console.log(`✅ Created ${adminUsers.length + teacherUsers.length + studentUsers.length} users`);

    // Removed academic streams - school-only mode doesn't use streams

    // Removed academic branches - school-only mode doesn't use branches

    // Create academic programs (school classes only)
    console.log("🎓 Creating academic programs...");
    const programRecords = await db.insert(academicPrograms).values([
      {
        name: "Class 10",
        code: "CLS10",
        type: "school",
        duration: 1,
        description: "Secondary education class 10",
        eligibilityCriteria: "Completion of Class 9",
        totalSeats: 120,
        admissionStatus: "open",
      },
      {
        name: "Class 11",
        code: "CLS11",
        type: "school",
        duration: 1,
        description: "Higher secondary education class 11",
        eligibilityCriteria: "Completion of Class 10",
        totalSeats: 100,
        admissionStatus: "open",
      },
      {
        name: "Class 12",
        code: "CLS12",
        type: "school",
        duration: 1,
        description: "Higher secondary education class 12",
        eligibilityCriteria: "Completion of Class 11",
        totalSeats: 100,
        admissionStatus: "open",
      },
    ]).returning();

    console.log(`✅ Created ${programRecords.length} academic programs`);

    // Create academic batches (school sessions only)
    console.log("📅 Creating academic batches...");
    const batchRecords = await db.insert(academicBatches).values([
      {
        programId: programRecords[0].id, // Class 10
        batchName: "2023-2024",
        startYear: 2023,
        endYear: 2024,
        totalSeats: 120,
        occupiedSeats: 85,
        availableSeats: 35,
        admissionStatus: "open",
      },
      {
        programId: programRecords[0].id, // Class 10
        batchName: "2024-2025",
        startYear: 2024,
        endYear: 2025,
        totalSeats: 120,
        occupiedSeats: 60,
        availableSeats: 60,
        admissionStatus: "open",
      },
      {
        programId: programRecords[1].id, // Class 11
        batchName: "2023-2024",
        startYear: 2023,
        endYear: 2024,
        totalSeats: 100,
        occupiedSeats: 75,
        availableSeats: 25,
        admissionStatus: "open",
      },
      {
        programId: programRecords[2].id, // Class 12
        batchName: "2023-2024",
        startYear: 2023,
        endYear: 2024,
        totalSeats: 100,
        occupiedSeats: 80,
        availableSeats: 20,
        admissionStatus: "open",
      },
    ]).returning();

    console.log(`✅ Created ${batchRecords.length} academic batches`);

    // Create teachers
    console.log("👨‍🏫 Creating teachers...");
    const teacherRecords = await db.insert(teachers).values([
      {
        userId: teacherUsers[0].id,
        employeeId: "EMP001",
        teacherType: "permanent",
        subjects: JSON.stringify(["Mathematics", "Statistics"]),
        primarySubject: "Mathematics",
        qualification: "M.Sc Mathematics, B.Ed",
        experience: 8,
        joiningDate: "2020-06-15",
        salary: "75000",
        department: "Science",
        maxClassesPerWeek: 20,
        currentClassLoad: 15,
        specialization: "Advanced Mathematics and Statistics",
        certifications: JSON.stringify(["B.Ed", "Mathematics Olympiad Trainer"]),
      },
      {
        userId: teacherUsers[1].id,
        employeeId: "EMP002",
        teacherType: "permanent",
        subjects: JSON.stringify(["Physics", "Applied Physics"]),
        primarySubject: "Physics",
        qualification: "M.Sc Physics, B.Ed",
        experience: 12,
        joiningDate: "2018-04-01",
        salary: "85000",
        department: "Science",
        maxClassesPerWeek: 18,
        currentClassLoad: 16,
        specialization: "Quantum Physics and Electronics",
        certifications: JSON.stringify(["B.Ed", "Physics Research Certificate"]),
      },
      {
        userId: teacherUsers[2].id,
        employeeId: "EMP003",
        teacherType: "permanent",
        subjects: JSON.stringify(["Chemistry", "Organic Chemistry", "Inorganic Chemistry"]),
        primarySubject: "Chemistry",
        qualification: "M.Sc Chemistry, Ph.D",
        experience: 15,
        joiningDate: "2015-08-20",
        salary: "95000",
        department: "Science",
        maxClassesPerWeek: 16,
        currentClassLoad: 14,
        specialization: "Organic Chemistry and Research",
        certifications: JSON.stringify(["Ph.D", "Research Publications", "Lab Safety Certification"]),
      },
    ]).returning();

    console.log(`✅ Created ${teacherRecords.length} teachers`);

    // Create students (school students only)
    console.log("👨‍🎓 Creating students...");
    const studentRecords = await db.insert(students).values([
      {
        userId: studentUsers[0].id,
        rollNumber: "STU001",
        programId: programRecords[0].id, // Class 10
        batchId: batchRecords[0].id, // 2023-2024
        grade: "10",
        section: "A",
        studentType: "regular",
        isLateralEntry: false,
        dateOfBirth: "2008-05-15",
        address: "123 Main St, City",
        parentName: "Jane Doe",
        parentPhone: "9876543210",
        parentEmail: "<EMAIL>",
        admissionDate: "2023-04-01",
        bloodGroup: "O+",
        emergencyContact: "9876543210",
      },
      {
        userId: studentUsers[1].id,
        rollNumber: "STU002",
        programId: programRecords[0].id, // Class 10
        batchId: batchRecords[0].id, // 2023-2024
        grade: "10",
        section: "A",
        studentType: "regular",
        isLateralEntry: false,
        dateOfBirth: "2008-03-22",
        address: "456 Oak Ave, City",
        parentName: "Bob Smith",
        parentPhone: "9876543211",
        parentEmail: "<EMAIL>",
        admissionDate: "2023-04-01",
        bloodGroup: "A+",
        emergencyContact: "9876543211",
      },
      {
        userId: studentUsers[2].id,
        rollNumber: "STU003",
        programId: programRecords[1].id, // Class 11
        batchId: batchRecords[2].id, // Class 11 batch
        grade: "11",
        section: "B",
        studentType: "regular",
        isLateralEntry: false,
        dateOfBirth: "2007-08-10",
        address: "789 Pine St, City",
        parentName: "Sarah Johnson",
        parentPhone: "9876543212",
        parentEmail: "<EMAIL>",
        admissionDate: "2023-04-01",
        bloodGroup: "B+",
        emergencyContact: "9876543212",
      },
    ]).returning();

    console.log(`✅ Created ${studentRecords.length} students`);

    // Create classes (school classes only)
    console.log("🏫 Creating classes...");
    const classRecords = await db.insert(classes).values([
      {
        name: "Mathematics 10A",
        programId: programRecords[0].id, // Class 10
        batchId: batchRecords[0].id, // 2023-2024
        grade: "10",
        section: "A",
        subject: "Mathematics",
        teacherId: teacherRecords[0].id,
        classType: "theory",
        room: "Room 101",
        capacity: 30,
        enrolledStudents: 25,
        availableSeats: 5,
        academicYear: "2023-24",
      },
      {
        name: "Physics 10A",
        programId: programRecords[0].id, // Class 10
        batchId: batchRecords[0].id, // 2023-2024
        grade: "10",
        section: "A",
        subject: "Physics",
        teacherId: teacherRecords[1].id,
        classType: "theory",
        room: "Room 102",
        capacity: 30,
        enrolledStudents: 25,
        availableSeats: 5,
        academicYear: "2023-24",
      },
      {
        name: "Chemistry 11B",
        programId: programRecords[1].id, // Class 11
        batchId: batchRecords[2].id, // Class 11 batch
        grade: "11",
        section: "B",
        subject: "Chemistry",
        teacherId: teacherRecords[2].id,
        classType: "theory",
        room: "Room 201",
        capacity: 30,
        enrolledStudents: 28,
        availableSeats: 2,
        academicYear: "2023-24",
      },
    ]).returning();

    console.log(`✅ Created ${classRecords.length} classes`);

    // Create fee structures
    console.log("💰 Creating fee structures...");
    await db.insert(feeStructures).values([
      {
        grade: "10",
        academicYear: "2023-24",
        tuitionFee: "50000",
        admissionFee: "5000",
        examFee: "2000",
        libraryFee: "1000",
        transportFee: "8000",
        hostelFee: "15000",
        miscellaneousFee: "2000",
        totalFee: "83000",
        dueDate: "2024-04-30",
      },
      {
        grade: "11",
        academicYear: "2023-24",
        tuitionFee: "55000",
        admissionFee: "0",
        examFee: "2500",
        libraryFee: "1000",
        transportFee: "8000",
        hostelFee: "15000",
        miscellaneousFee: "2000",
        totalFee: "83500",
        dueDate: "2024-04-30",
      },
      {
        grade: "12",
        academicYear: "2023-24",
        tuitionFee: "60000",
        admissionFee: "0",
        examFee: "3000",
        libraryFee: "1000",
        transportFee: "8000",
        hostelFee: "15000",
        miscellaneousFee: "2000",
        totalFee: "89000",
        dueDate: "2024-04-30",
      },
    ]);

    console.log("✅ Created fee structures");

    // Create books
    console.log("📚 Creating books...");
    await db.insert(books).values([
      {
        title: "Advanced Mathematics for Class 10",
        author: "Dr. R.S. Aggarwal",
        isbn: "978-8177091892",
        category: "Mathematics",
        publisher: "S. Chand Publishing",
        publicationYear: 2020,
        totalCopies: 50,
        availableCopies: 45,
        location: "A-101",
        price: "450",
      },
      {
        title: "Physics for Class 11",
        author: "H.C. Verma",
        isbn: "978-8177091915",
        category: "Physics",
        publisher: "Bharati Bhawan",
        publicationYear: 2019,
        totalCopies: 40,
        availableCopies: 38,
        location: "B-201",
        price: "650",
      },
      {
        title: "Organic Chemistry",
        author: "Morrison & Boyd",
        isbn: "978-8131704815",
        category: "Chemistry",
        publisher: "Pearson",
        publicationYear: 2021,
        totalCopies: 30,
        availableCopies: 28,
        location: "C-301",
        price: "850",
      },
    ]);

    console.log("✅ Created books");

    console.log("🌱 Database seeded successfully!");

  } catch (error) {
    console.error("❌ Error seeding database:", error);
    throw error;
  }
}

// Run the seed function
if (require.main === module) {
  seed()
    .then(() => {
      console.log("✅ Seeding completed");
      process.exit(0);
    })
    .catch((error) => {
      console.error("❌ Seeding failed:", error);
      process.exit(1);
    });
}

export { seed };
