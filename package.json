{"name": "school-management-system", "version": "0.1.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@hono/node-server": "^1.14.4", "@hono/zod-validator": "^0.7.0", "@hookform/resolvers": "^3.10.0", "@neondatabase/serverless": "0.10.0", "@radix-ui/react-alert-dialog": "^1.1.4", "@radix-ui/react-checkbox": "^1.1.4", "@radix-ui/react-dialog": "^1.1.4", "@radix-ui/react-dropdown-menu": "^2.1.4", "@radix-ui/react-label": "^2.1.1", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-select": "^2.1.4", "@radix-ui/react-slot": "^1.1.1", "@radix-ui/react-tabs": "^1.1.12", "@tanstack/react-query": "^5.80.6", "@types/node": "^22.10.2", "@types/react": "^19.0.2", "@types/react-dom": "^19.0.2", "autoprefixer": "^10.4.21", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "dotenv": "^16.5.0", "drizzle-orm": "0.36.4", "hono": "^4.7.11", "lucide-react": "^0.468.0", "next": "^15.3.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-hook-form": "^7.57.0", "sonner": "^1.7.4", "tailwind-merge": "^2.6.0", "tailwindcss-animate": "^1.0.7", "typescript": "^5.7.2", "zod": "^3.25.56"}, "devDependencies": {"@eslint/eslintrc": "^3.2.0", "@types/eslint": "^9.6.1", "autoprefixer": "^10.4.20", "drizzle-kit": "0.28.1", "eslint": "^9.17.0", "eslint-config-next": "15.3.2", "postcss": "^8.5.1", "tailwindcss": "^3.4.17"}}