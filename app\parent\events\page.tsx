"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Calendar,
  Clock,
  MapPin,
  Users,
  Star,
  Bell,
  Download,
  Filter,
  Search,
  ChevronLeft,
  ChevronRight,
  User,
  Trophy,
  BookOpen,
  Music,
  Palette,
  Gamepad2,
  GraduationCap,
  Heart,
  Camera,
} from "lucide-react";

export default function ParentEvents() {
  const [user, setUser] = useState<any>(null);
  const [selectedChild, setSelectedChild] = useState("child1");
  const [selectedTab, setSelectedTab] = useState("upcoming");
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth());
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "parent") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock data for parent's children
  const children = [
    { id: "child1", name: "John Doe", class: "Grade 10A", rollNo: "10A001" },
    { id: "child2", name: "Jane Doe", class: "Grade 8B", rollNo: "8B015" },
  ];

  const selectedChildData = children.find(child => child.id === selectedChild) || children[0];

  const eventStats = {
    totalEvents: 24,
    upcomingEvents: 8,
    registeredEvents: 5,
    completedEvents: 16,
  };

  const upcomingEvents = [
    {
      id: "1",
      title: "Annual Science Fair",
      description: "Students will showcase their innovative science projects and experiments",
      date: "2024-02-15",
      time: "09:00 AM - 05:00 PM",
      location: "School Auditorium",
      category: "Academic",
      type: "Competition",
      participants: 150,
      isRegistered: true,
      isImportant: true,
      eligibleClasses: ["Grade 8", "Grade 9", "Grade 10"],
      organizer: "Science Department",
      registrationDeadline: "2024-02-10",
      icon: BookOpen,
      color: "blue",
    },
    {
      id: "2",
      title: "Parent-Teacher Meeting",
      description: "Quarterly meeting to discuss student progress and academic performance",
      date: "2024-02-05",
      time: "02:00 PM - 06:00 PM",
      location: "Respective Classrooms",
      category: "Meeting",
      type: "Mandatory",
      participants: 200,
      isRegistered: false,
      isImportant: true,
      eligibleClasses: ["All Classes"],
      organizer: "Academic Office",
      registrationDeadline: "2024-02-01",
      icon: Users,
      color: "purple",
    },
    {
      id: "3",
      title: "Inter-House Sports Competition",
      description: "Annual sports competition between different houses of the school",
      date: "2024-02-20",
      time: "08:00 AM - 04:00 PM",
      location: "School Sports Ground",
      category: "Sports",
      type: "Competition",
      participants: 300,
      isRegistered: true,
      isImportant: false,
      eligibleClasses: ["Grade 6", "Grade 7", "Grade 8", "Grade 9", "Grade 10"],
      organizer: "Sports Department",
      registrationDeadline: "2024-02-15",
      icon: Trophy,
      color: "green",
    },
    {
      id: "4",
      title: "Cultural Festival",
      description: "Celebration of diverse cultures with performances, food, and exhibitions",
      date: "2024-03-01",
      time: "10:00 AM - 08:00 PM",
      location: "School Campus",
      category: "Cultural",
      type: "Festival",
      participants: 500,
      isRegistered: false,
      isImportant: false,
      eligibleClasses: ["All Classes"],
      organizer: "Cultural Committee",
      registrationDeadline: "2024-02-25",
      icon: Music,
      color: "pink",
    },
    {
      id: "5",
      title: "Art Exhibition",
      description: "Display of student artwork and creative projects",
      date: "2024-02-28",
      time: "11:00 AM - 05:00 PM",
      location: "Art Gallery",
      category: "Arts",
      type: "Exhibition",
      participants: 80,
      isRegistered: true,
      isImportant: false,
      eligibleClasses: ["Grade 8", "Grade 9", "Grade 10"],
      organizer: "Art Department",
      registrationDeadline: "2024-02-20",
      icon: Palette,
      color: "orange",
    },
  ];

  const pastEvents = [
    {
      id: "p1",
      title: "Annual Day Celebration",
      date: "2024-01-15",
      category: "Cultural",
      participants: 400,
      photos: 25,
      videos: 3,
      icon: GraduationCap,
      color: "indigo",
    },
    {
      id: "p2",
      title: "Mathematics Olympiad",
      date: "2024-01-10",
      category: "Academic",
      participants: 60,
      photos: 15,
      videos: 1,
      icon: BookOpen,
      color: "blue",
    },
    {
      id: "p3",
      title: "Health Awareness Workshop",
      date: "2024-01-05",
      category: "Health",
      participants: 120,
      photos: 20,
      videos: 2,
      icon: Heart,
      color: "red",
    },
  ];

  const monthNames = [
    "January", "February", "March", "April", "May", "June",
    "July", "August", "September", "October", "November", "December"
  ];

  const getCategoryColor = (category: string) => {
    switch (category.toLowerCase()) {
      case "academic": return "text-blue-600 bg-blue-100";
      case "sports": return "text-green-600 bg-green-100";
      case "cultural": return "text-purple-600 bg-purple-100";
      case "arts": return "text-orange-600 bg-orange-100";
      case "meeting": return "text-gray-600 bg-gray-100";
      case "health": return "text-red-600 bg-red-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const getTypeColor = (type: string) => {
    switch (type.toLowerCase()) {
      case "mandatory": return "text-red-600 bg-red-100";
      case "competition": return "text-blue-600 bg-blue-100";
      case "festival": return "text-purple-600 bg-purple-100";
      case "exhibition": return "text-orange-600 bg-orange-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const getIconColor = (color: string) => {
    switch (color) {
      case "blue": return "text-blue-600 bg-blue-100";
      case "green": return "text-green-600 bg-green-100";
      case "purple": return "text-purple-600 bg-purple-100";
      case "pink": return "text-pink-600 bg-pink-100";
      case "orange": return "text-orange-600 bg-orange-100";
      case "red": return "text-red-600 bg-red-100";
      case "indigo": return "text-indigo-600 bg-indigo-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const getDaysUntilEvent = (eventDate: string) => {
    const today = new Date();
    const event = new Date(eventDate);
    const diffTime = event.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  const registerForEvent = (eventId: string) => {
    alert(`Registered for event ${eventId} successfully!`);
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">School Events</h1>
            <p className="text-gray-600">Stay updated with school activities and events</p>
          </div>
          <div className="flex gap-2">
            <select
              value={selectedChild}
              onChange={(e) => setSelectedChild(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="all">All Children</option>
              {children.map((child) => (
                <option key={child.id} value={child.id}>
                  {child.name} - {child.class}
                </option>
              ))}
            </select>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Calendar
            </Button>
          </div>
        </div>

        {/* Event Stats */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Calendar className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {eventStats.totalEvents}
                  </div>
                  <p className="text-sm text-gray-600">Total Events</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Bell className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    {eventStats.upcomingEvents}
                  </div>
                  <p className="text-sm text-gray-600">Upcoming Events</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Star className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {eventStats.registeredEvents}
                  </div>
                  <p className="text-sm text-gray-600">Registered</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Trophy className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {eventStats.completedEvents}
                  </div>
                  <p className="text-sm text-gray-600">Completed</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Event Tabs */}
        <Card>
          <CardHeader>
            <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
              {[
                { key: "upcoming", label: "Upcoming Events" },
                { key: "calendar", label: "Calendar View" },
                { key: "past", label: "Past Events" },
                { key: "registered", label: "My Registrations" },
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setSelectedTab(tab.key)}
                  className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                    selectedTab === tab.key
                      ? "bg-white text-blue-600 shadow-sm"
                      : "text-gray-600 hover:text-gray-900"
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </div>
          </CardHeader>
          <CardContent>
            {/* Upcoming Events */}
            {selectedTab === "upcoming" && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Upcoming Events</h3>
                <div className="space-y-4">
                  {upcomingEvents.map((event) => {
                    const daysLeft = getDaysUntilEvent(event.date);
                    const IconComponent = event.icon;
                    
                    return (
                      <div key={event.id} className="p-6 border rounded-lg hover:bg-gray-50">
                        <div className="flex items-start justify-between mb-4">
                          <div className="flex items-start space-x-4">
                            <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${getIconColor(event.color)}`}>
                              <IconComponent className="h-6 w-6" />
                            </div>
                            <div className="flex-1">
                              <div className="flex items-center space-x-3 mb-2">
                                <h4 className="font-semibold text-gray-900">{event.title}</h4>
                                {event.isImportant && <Star className="h-4 w-4 text-yellow-500" />}
                                {event.isRegistered && (
                                  <Badge className="bg-green-100 text-green-700">Registered</Badge>
                                )}
                              </div>
                              <p className="text-gray-600 mb-3">{event.description}</p>
                              <div className="flex flex-wrap gap-2 mb-3">
                                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(event.category)}`}>
                                  {event.category}
                                </span>
                                <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(event.type)}`}>
                                  {event.type}
                                </span>
                              </div>
                            </div>
                          </div>
                          <div className="text-right">
                            <div className="text-lg font-bold text-blue-600">{event.date}</div>
                            <div className="text-sm text-gray-500">
                              {daysLeft === 0 ? "Today" : 
                               daysLeft === 1 ? "Tomorrow" : 
                               daysLeft > 0 ? `${daysLeft} days left` : 
                               `${Math.abs(daysLeft)} days ago`}
                            </div>
                          </div>
                        </div>

                        <div className="grid gap-3 md:grid-cols-3 text-sm mb-4">
                          <div className="flex items-center text-gray-600">
                            <Clock className="h-4 w-4 mr-2" />
                            {event.time}
                          </div>
                          <div className="flex items-center text-gray-600">
                            <MapPin className="h-4 w-4 mr-2" />
                            {event.location}
                          </div>
                          <div className="flex items-center text-gray-600">
                            <Users className="h-4 w-4 mr-2" />
                            {event.participants} participants
                          </div>
                        </div>

                        <div className="flex items-center justify-between pt-4 border-t">
                          <div className="text-sm text-gray-600">
                            <span className="font-medium">Organizer:</span> {event.organizer} • 
                            <span className="font-medium"> Registration Deadline:</span> {event.registrationDeadline}
                          </div>
                          <div className="flex gap-2">
                            <Button variant="outline" size="sm">
                              <Calendar className="h-3 w-3 mr-1" />
                              View Details
                            </Button>
                            {!event.isRegistered && (
                              <Button 
                                size="sm"
                                onClick={() => registerForEvent(event.id)}
                                className={daysLeft <= 3 ? 'bg-orange-600 hover:bg-orange-700' : ''}
                              >
                                <Star className="h-3 w-3 mr-1" />
                                Register
                              </Button>
                            )}
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* Calendar View */}
            {selectedTab === "calendar" && (
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <h3 className="text-lg font-semibold text-gray-900">
                    {monthNames[selectedMonth]} {selectedYear}
                  </h3>
                  <div className="flex items-center gap-2">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        if (selectedMonth === 0) {
                          setSelectedMonth(11);
                          setSelectedYear(selectedYear - 1);
                        } else {
                          setSelectedMonth(selectedMonth - 1);
                        }
                      }}
                    >
                      <ChevronLeft className="h-4 w-4" />
                    </Button>
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={() => {
                        if (selectedMonth === 11) {
                          setSelectedMonth(0);
                          setSelectedYear(selectedYear + 1);
                        } else {
                          setSelectedMonth(selectedMonth + 1);
                        }
                      }}
                    >
                      <ChevronRight className="h-4 w-4" />
                    </Button>
                  </div>
                </div>
                
                <div className="grid grid-cols-7 gap-2 text-center">
                  {["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"].map((day) => (
                    <div key={day} className="p-2 font-medium text-gray-500 text-sm">
                      {day}
                    </div>
                  ))}
                  {Array.from({ length: 35 }, (_, i) => (
                    <div key={i} className="p-2 h-20 border border-gray-200 rounded">
                      <div className="text-sm text-gray-600">{i + 1}</div>
                      {/* Event indicators would go here */}
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Past Events */}
            {selectedTab === "past" && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Past Events</h3>
                <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
                  {pastEvents.map((event) => {
                    const IconComponent = event.icon;
                    
                    return (
                      <div key={event.id} className="p-4 border rounded-lg">
                        <div className="flex items-center space-x-3 mb-3">
                          <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${getIconColor(event.color)}`}>
                            <IconComponent className="h-5 w-5" />
                          </div>
                          <div>
                            <h4 className="font-medium text-gray-900">{event.title}</h4>
                            <p className="text-sm text-gray-600">{event.date}</p>
                          </div>
                        </div>
                        
                        <div className="space-y-2 text-sm">
                          <div className="flex justify-between">
                            <span className="text-gray-600">Category:</span>
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(event.category)}`}>
                              {event.category}
                            </span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Participants:</span>
                            <span className="font-medium">{event.participants}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Photos:</span>
                            <span className="font-medium">{event.photos}</span>
                          </div>
                          <div className="flex justify-between">
                            <span className="text-gray-600">Videos:</span>
                            <span className="font-medium">{event.videos}</span>
                          </div>
                        </div>
                        
                        <div className="mt-4 flex gap-2">
                          <Button variant="outline" size="sm" className="flex-1">
                            <Camera className="h-3 w-3 mr-1" />
                            View Gallery
                          </Button>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}

            {/* My Registrations */}
            {selectedTab === "registered" && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">My Registrations</h3>
                <div className="space-y-3">
                  {upcomingEvents.filter(event => event.isRegistered).map((event) => {
                    const IconComponent = event.icon;
                    
                    return (
                      <div key={event.id} className="p-4 border rounded-lg bg-green-50 border-green-200">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center space-x-4">
                            <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${getIconColor(event.color)}`}>
                              <IconComponent className="h-5 w-5" />
                            </div>
                            <div>
                              <h4 className="font-medium text-gray-900">{event.title}</h4>
                              <div className="flex items-center space-x-4 text-sm text-gray-600">
                                <span className="flex items-center">
                                  <Calendar className="h-3 w-3 mr-1" />
                                  {event.date}
                                </span>
                                <span className="flex items-center">
                                  <Clock className="h-3 w-3 mr-1" />
                                  {event.time}
                                </span>
                                <span className="flex items-center">
                                  <MapPin className="h-3 w-3 mr-1" />
                                  {event.location}
                                </span>
                              </div>
                            </div>
                          </div>
                          <div className="flex items-center space-x-2">
                            <Badge className="bg-green-100 text-green-700">Registered</Badge>
                            <Button variant="outline" size="sm">
                              View Details
                            </Button>
                          </div>
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
