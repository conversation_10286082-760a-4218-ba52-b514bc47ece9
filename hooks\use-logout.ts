"use client";

import { useRouter } from "next/navigation";
import { toast } from "sonner";

export function useLogout() {
  const router = useRouter();

  const logout = async (showConfirmation = true) => {
    try {
      // Show confirmation dialog if requested
      if (showConfirmation) {
        const confirmed = window.confirm(
          "Are you sure you want to logout? You will need to sign in again to access your account."
        );
        if (!confirmed) {
          return false;
        }
      }

      // Get token for API call
      const token = localStorage.getItem("token");

      // Call logout API if token exists
      if (token) {
        try {
          await fetch("/api/auth/logout", {
            method: "POST",
            headers: {
              "Authorization": `Bearer ${token}`,
              "Content-Type": "application/json",
            },
          });
        } catch (error) {
          // Continue with logout even if API call fails
          console.warn("Logout API call failed:", error);
        }
      }

      // Clear all authentication data from localStorage
      localStorage.removeItem("user");
      localStorage.removeItem("token");

      // Clear any other app-specific data
      localStorage.removeItem("preferences");
      localStorage.removeItem("theme");

      // Clear sessionStorage as well
      sessionStorage.clear();

      // Clear the token cookie
      document.cookie = "token=; path=/; expires=Thu, 01 Jan 1970 00:00:00 GMT; SameSite=Lax";

      // Show success message
      toast.success("Logged out successfully!");

      // Redirect to login page
      router.push("/login");

      return true;
    } catch (error) {
      console.error("Logout error:", error);
      toast.error("An error occurred during logout. Please try again.");
      return false;
    }
  };

  const logoutWithoutConfirmation = () => logout(false);

  return {
    logout,
    logoutWithoutConfirmation,
  };
}
