"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  FileText,
  Plus,
  Search,
  Filter,
  Calendar,
  Clock,
  Users,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Edit,
  Eye,
  Download,
  Upload,
  BookOpen,
  Target,
  TrendingUp,
  Send,
  MessageSquare,
} from "lucide-react";

export default function TeacherAssignments() {
  const [user, setUser] = useState<any>(null);
  const [selectedClass, setSelectedClass] = useState("all");
  const [selectedSubject, setSelectedSubject] = useState("all");
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "teacher") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock assignments data
  const assignmentStats = {
    totalAssignments: 24,
    activeAssignments: 8,
    completedAssignments: 16,
    pendingReview: 12,
    averageSubmissionRate: 87.5,
    onTimeSubmissions: 156,
    lateSubmissions: 23,
    totalStudents: 180,
  };

  const assignments = [
    {
      id: "1",
      title: "Quadratic Equations Practice",
      subject: "Mathematics",
      class: "Grade 10A",
      dueDate: "2024-01-25",
      assignedDate: "2024-01-15",
      totalStudents: 45,
      submitted: 38,
      pending: 7,
      status: "Active",
      type: "Homework",
      maxMarks: 50,
      description: "Solve the given quadratic equations using different methods",
    },
    {
      id: "2",
      title: "Newton's Laws Lab Report",
      subject: "Physics",
      class: "Grade 11B",
      dueDate: "2024-01-22",
      assignedDate: "2024-01-10",
      totalStudents: 42,
      submitted: 40,
      pending: 2,
      status: "Active",
      type: "Lab Report",
      maxMarks: 100,
      description: "Write a detailed lab report on Newton's laws experiment",
    },
    {
      id: "3",
      title: "Chemical Bonding Assignment",
      subject: "Chemistry",
      class: "Grade 11A",
      dueDate: "2024-01-18",
      assignedDate: "2024-01-08",
      totalStudents: 43,
      submitted: 43,
      pending: 0,
      status: "Completed",
      type: "Assignment",
      maxMarks: 75,
      description: "Explain different types of chemical bonding with examples",
    },
    {
      id: "4",
      title: "Essay on Climate Change",
      subject: "English",
      class: "Grade 12A",
      dueDate: "2024-01-30",
      assignedDate: "2024-01-20",
      totalStudents: 41,
      submitted: 15,
      pending: 26,
      status: "Active",
      type: "Essay",
      maxMarks: 100,
      description: "Write a 1000-word essay on the impact of climate change",
    },
    {
      id: "5",
      title: "Programming Project - Calculator",
      subject: "Computer Science",
      class: "Grade 12B",
      dueDate: "2024-02-05",
      assignedDate: "2024-01-15",
      totalStudents: 38,
      submitted: 22,
      pending: 16,
      status: "Active",
      type: "Project",
      maxMarks: 150,
      description: "Create a scientific calculator using Python",
    },
  ];

  const recentSubmissions = [
    { student: "John Doe", assignment: "Quadratic Equations Practice", submittedAt: "2024-01-20 14:30", status: "On Time", grade: "A" },
    { student: "Alice Smith", assignment: "Newton's Laws Lab Report", submittedAt: "2024-01-21 16:45", status: "On Time", grade: "B+" },
    { student: "Michael Johnson", assignment: "Chemical Bonding Assignment", submittedAt: "2024-01-19 10:15", status: "Late", grade: "B" },
    { student: "Emma Davis", assignment: "Essay on Climate Change", submittedAt: "2024-01-20 09:30", status: "On Time", grade: "Pending" },
    { student: "Robert Wilson", assignment: "Programming Project", submittedAt: "2024-01-21 18:20", status: "On Time", grade: "Pending" },
  ];

  const upcomingDeadlines = [
    { title: "Quadratic Equations Practice", class: "Grade 10A", dueDate: "2024-01-25", daysLeft: 5, submitted: 38, total: 45 },
    { title: "Essay on Climate Change", class: "Grade 12A", dueDate: "2024-01-30", daysLeft: 10, submitted: 15, total: 41 },
    { title: "Programming Project - Calculator", class: "Grade 12B", dueDate: "2024-02-05", daysLeft: 16, submitted: 22, total: 38 },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Active": return "text-blue-600 bg-blue-100";
      case "Completed": return "text-green-600 bg-green-100";
      case "Overdue": return "text-red-600 bg-red-100";
      case "Draft": return "text-gray-600 bg-gray-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const getSubmissionStatusColor = (status: string) => {
    switch (status) {
      case "On Time": return "text-green-600 bg-green-100";
      case "Late": return "text-red-600 bg-red-100";
      case "Pending": return "text-yellow-600 bg-yellow-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case "Homework": return "text-blue-600 bg-blue-100";
      case "Lab Report": return "text-purple-600 bg-purple-100";
      case "Assignment": return "text-green-600 bg-green-100";
      case "Essay": return "text-orange-600 bg-orange-100";
      case "Project": return "text-red-600 bg-red-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const filteredAssignments = assignments.filter(assignment => {
    const matchesSearch = assignment.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         assignment.subject.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesClass = selectedClass === "all" || assignment.class === selectedClass;
    const matchesSubject = selectedSubject === "all" || assignment.subject === selectedSubject;
    const matchesStatus = selectedStatus === "all" || assignment.status === selectedStatus;
    return matchesSearch && matchesClass && matchesSubject && matchesStatus;
  });

  const calculateSubmissionRate = (submitted: number, total: number) => {
    return ((submitted / total) * 100).toFixed(1);
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Assignment Management</h1>
            <p className="text-gray-600">Create, manage, and track student assignments</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Upload className="h-4 w-4 mr-2" />
              Import
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Assignment
            </Button>
          </div>
        </div>

        {/* Assignment Overview Stats */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <FileText className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {assignmentStats.totalAssignments}
                  </div>
                  <p className="text-sm text-gray-600">Total Assignments</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    {assignmentStats.activeAssignments}
                  </div>
                  <p className="text-sm text-gray-600">Active Assignments</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {assignmentStats.pendingReview}
                  </div>
                  <p className="text-sm text-gray-600">Pending Review</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Target className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {assignmentStats.averageSubmissionRate}%
                  </div>
                  <p className="text-sm text-gray-600">Submission Rate</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600">+5.2%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search assignments by title or subject..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <select
                  value={selectedClass}
                  onChange={(e) => setSelectedClass(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Classes</option>
                  <option value="Grade 10A">Grade 10A</option>
                  <option value="Grade 10B">Grade 10B</option>
                  <option value="Grade 11A">Grade 11A</option>
                  <option value="Grade 11B">Grade 11B</option>
                  <option value="Grade 12A">Grade 12A</option>
                  <option value="Grade 12B">Grade 12B</option>
                </select>
                <select
                  value={selectedSubject}
                  onChange={(e) => setSelectedSubject(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Subjects</option>
                  <option value="Mathematics">Mathematics</option>
                  <option value="Physics">Physics</option>
                  <option value="Chemistry">Chemistry</option>
                  <option value="English">English</option>
                  <option value="Computer Science">Computer Science</option>
                </select>
                <select
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Status</option>
                  <option value="Active">Active</option>
                  <option value="Completed">Completed</option>
                  <option value="Draft">Draft</option>
                  <option value="Overdue">Overdue</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Main Content Grid */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Assignments List */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center">
                <BookOpen className="h-5 w-5 mr-2" />
                Assignments ({filteredAssignments.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredAssignments.map((assignment) => (
                  <div key={assignment.id} className="p-4 border rounded-lg hover:bg-gray-50">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <h3 className="font-semibold text-gray-900 mb-1">{assignment.title}</h3>
                        <p className="text-sm text-gray-600 mb-2">{assignment.description}</p>
                        <div className="flex items-center space-x-4 text-sm text-gray-500">
                          <span>{assignment.subject}</span>
                          <span>•</span>
                          <span>{assignment.class}</span>
                          <span>•</span>
                          <span>Due: {assignment.dueDate}</span>
                        </div>
                      </div>
                      <div className="flex items-center space-x-2">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(assignment.status)}`}>
                          {assignment.status}
                        </span>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getTypeColor(assignment.type)}`}>
                          {assignment.type}
                        </span>
                      </div>
                    </div>

                    <div className="grid gap-3 md:grid-cols-4 text-sm mb-3">
                      <div className="text-center">
                        <div className="text-lg font-bold text-blue-600">{assignment.totalStudents}</div>
                        <div className="text-gray-500">Total Students</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-green-600">{assignment.submitted}</div>
                        <div className="text-gray-500">Submitted</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-red-600">{assignment.pending}</div>
                        <div className="text-gray-500">Pending</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-purple-600">
                          {calculateSubmissionRate(assignment.submitted, assignment.totalStudents)}%
                        </div>
                        <div className="text-gray-500">Submission Rate</div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="w-full bg-gray-200 rounded-full h-2 mr-4">
                        <div 
                          className="bg-blue-500 h-2 rounded-full"
                          style={{ width: `${calculateSubmissionRate(assignment.submitted, assignment.totalStudents)}%` }}
                        />
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Eye className="h-3 w-3" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Edit className="h-3 w-3" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <MessageSquare className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Upcoming Deadlines */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Calendar className="h-5 w-5 mr-2" />
                  Upcoming Deadlines
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {upcomingDeadlines.map((deadline, index) => (
                    <div key={index} className="p-3 border rounded-lg">
                      <div className="font-medium text-gray-900 mb-1">{deadline.title}</div>
                      <div className="text-sm text-gray-600 mb-2">{deadline.class}</div>
                      <div className="flex items-center justify-between text-sm">
                        <span className="text-gray-500">Due: {deadline.dueDate}</span>
                        <span className={`font-medium ${
                          deadline.daysLeft <= 3 ? 'text-red-600' :
                          deadline.daysLeft <= 7 ? 'text-yellow-600' : 'text-green-600'
                        }`}>
                          {deadline.daysLeft} days left
                        </span>
                      </div>
                      <div className="mt-2">
                        <div className="flex justify-between text-xs text-gray-500 mb-1">
                          <span>Submissions</span>
                          <span>{deadline.submitted}/{deadline.total}</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-1">
                          <div 
                            className="bg-blue-500 h-1 rounded-full"
                            style={{ width: `${(deadline.submitted / deadline.total) * 100}%` }}
                          />
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Recent Submissions */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <Send className="h-5 w-5 mr-2" />
                  Recent Submissions
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {recentSubmissions.map((submission, index) => (
                    <div key={index} className="p-3 border rounded-lg">
                      <div className="font-medium text-gray-900 mb-1">{submission.student}</div>
                      <div className="text-sm text-gray-600 mb-2">{submission.assignment}</div>
                      <div className="flex items-center justify-between text-xs">
                        <span className="text-gray-500">{submission.submittedAt}</span>
                        <div className="flex items-center space-x-2">
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getSubmissionStatusColor(submission.status)}`}>
                            {submission.status}
                          </span>
                          {submission.grade !== "Pending" && (
                            <span className="font-medium text-gray-900">{submission.grade}</span>
                          )}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
