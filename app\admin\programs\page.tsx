"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  GraduationCap,
  Search,
  Filter,
  Download,
  Plus,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  Users,
  BookOpen,
  Calendar,
  Award,
  Loader2,
  RefreshCw,
  TrendingUp,
  Building,
} from "lucide-react";
import { useGetPrograms, useDeleteProgram } from "@/features/api/use-programs";
import { toast } from "sonner";

export default function AdminPrograms() {
  const [user, setUser] = useState<any>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedType, setSelectedType] = useState("all");
  const [selectedStatus, setSelectedStatus] = useState("all");
  const router = useRouter();

  // API hooks
  const { data: programsData, isLoading, error, refetch } = useGetPrograms({
    page: currentPage,
    limit: 10,
    search: searchTerm,
    type: selectedType === "all" ? undefined : selectedType,
    status: selectedStatus === "all" ? undefined : selectedStatus,
  });

  const deleteProgramMutation = useDeleteProgram();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "super_admin") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  const handleDeleteProgram = async (programId: string, programName: string) => {
    try {
      await deleteProgramMutation.mutateAsync(programId);
      toast.success(`Program "${programName}" has been deleted`);
    } catch (error) {
      console.error("Error deleting program:", error);
    }
  };

  const handleExport = () => {
    toast.info("Export functionality coming soon!");
  };

  const handleRefresh = () => {
    refetch();
    toast.success("Programs list refreshed!");
  };

  if (!user) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-600" />
            <p className="mt-2 text-gray-600">Loading...</p>
          </div>
        </div>
      </AppLayout>
    );
  }

  // Get programs and pagination from API
  const programs = programsData?.data || [];
  const pagination = { page: 1, limit: 10, total: programs.length, totalPages: Math.ceil(programs.length / 10) };

  // Calculate statistics
  const programStats = {
    total: pagination.total,
    active: programs.filter((p: any) => p.status === "active").length,
    inactive: programs.filter((p: any) => p.status === "inactive").length,
    byType: {
      school: programs.filter((p: any) => p.type === "school").length,
      undergraduate: programs.filter((p: any) => p.type === "undergraduate").length,
      postgraduate: programs.filter((p: any) => p.type === "postgraduate").length,
      diploma: programs.filter((p: any) => p.type === "diploma").length,
      certificate: programs.filter((p: any) => p.type === "certificate").length,
    },
  };

  const getTypeColor = (type: string) => {
    const colors = {
      school: "bg-blue-100 text-blue-800",
      undergraduate: "bg-green-100 text-green-800",
      postgraduate: "bg-purple-100 text-purple-800",
      diploma: "bg-orange-100 text-orange-800",
      certificate: "bg-yellow-100 text-yellow-800",
    };
    return colors[type as keyof typeof colors] || "bg-gray-100 text-gray-800";
  };

  const getStatusColor = (status: string) => {
    return status === "active"
      ? "bg-green-100 text-green-800"
      : "bg-red-100 text-red-800";
  };

  const getAdmissionStatusColor = (status: string) => {
    const colors = {
      open: "bg-green-100 text-green-800",
      closed: "bg-red-100 text-red-800",
      waitlist: "bg-yellow-100 text-yellow-800",
    };
    return colors[status as keyof typeof colors] || "bg-gray-100 text-gray-800";
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Academic Programs</h1>
            <p className="text-gray-600">Manage all academic programs and courses</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleRefresh} disabled={isLoading}>
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button variant="outline" onClick={handleExport}>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button onClick={() => router.push("/admin/programs/new")}>
              <Plus className="h-4 w-4 mr-2" />
              Add Program
            </Button>
          </div>
        </div>

        {/* Error State */}
        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 text-red-800">
                <Trash2 className="h-5 w-5" />
                <p>Failed to load programs. Please try again.</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Statistics Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Programs</CardTitle>
              <GraduationCap className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{programStats.total}</div>
              <p className="text-xs text-muted-foreground">
                {programStats.active} active, {programStats.inactive} inactive
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">School Programs</CardTitle>
              <Building className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{programStats.byType.school}</div>
              <p className="text-xs text-muted-foreground">
                Primary & Secondary education
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Higher Education</CardTitle>
              <Award className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {programStats.byType.undergraduate + programStats.byType.postgraduate}
              </div>
              <p className="text-xs text-muted-foreground">
                UG & PG programs
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Professional Courses</CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {programStats.byType.diploma + programStats.byType.certificate}
              </div>
              <p className="text-xs text-muted-foreground">
                Diploma & Certificate courses
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    type="text"
                    placeholder="Search programs by name, code, or department..."
                    className="pl-10"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
              <div className="flex gap-2 flex-wrap">
                <Select value={selectedType} onValueChange={(value) => setSelectedType(value === "all" ? "all" : value)}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Filter by type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Types</SelectItem>
                    <SelectItem value="school">School</SelectItem>
                    <SelectItem value="undergraduate">Undergraduate</SelectItem>
                    <SelectItem value="postgraduate">Postgraduate</SelectItem>
                    <SelectItem value="diploma">Diploma</SelectItem>
                    <SelectItem value="certificate">Certificate</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={selectedStatus} onValueChange={(value) => setSelectedStatus(value === "all" ? "all" : value)}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Programs Table */}
        <Card>
          <CardHeader>
            <CardTitle>Programs List</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center h-32">
                <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
              </div>
            ) : programs.length === 0 ? (
              <div className="text-center py-8">
                <GraduationCap className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No programs found</h3>
                <p className="text-gray-600 mb-4">
                  {searchTerm || selectedType !== "all" || selectedStatus !== "all"
                    ? "No programs match your current filters."
                    : "Get started by creating your first academic program."}
                </p>
                <Button onClick={() => router.push("/admin/programs/new")}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add First Program
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {programs.map((program: any) => (
                  <div
                    key={program.id}
                    className="border rounded-lg p-4 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-lg font-semibold text-gray-900">
                            {program.name}
                          </h3>
                          <Badge className={getTypeColor(program.type)}>
                            {program.type.charAt(0).toUpperCase() + program.type.slice(1)}
                          </Badge>
                          <Badge className={getStatusColor(program.status)}>
                            {program.status.charAt(0).toUpperCase() + program.status.slice(1)}
                          </Badge>
                          <Badge className={getAdmissionStatusColor(program.admissionStatus)}>
                            {program.admissionStatus === "open" ? "Open" :
                             program.admissionStatus === "closed" ? "Closed" : "Waitlist"}
                          </Badge>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-gray-600">
                          <div className="flex items-center gap-2">
                            <BookOpen className="h-4 w-4" />
                            <span>Code: {program.code}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Calendar className="h-4 w-4" />
                            <span>Duration: {program.duration} year{program.duration > 1 ? 's' : ''}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Users className="h-4 w-4" />
                            <span>Seats: {program.totalSeats}</span>
                          </div>
                          {program.department && (
                            <div className="flex items-center gap-2">
                              <Building className="h-4 w-4" />
                              <span>{program.department}</span>
                            </div>
                          )}
                        </div>

                        {program.description && (
                          <p className="mt-2 text-sm text-gray-600 line-clamp-2">
                            {program.description}
                          </p>
                        )}

                        {program.statistics && (
                          <div className="mt-3 flex gap-4 text-xs text-gray-500">
                            <span>{program.statistics.totalBatches} batches</span>
                            <span>{program.statistics.totalStudents} students</span>
                            <span>{program.statistics.totalClasses} classes</span>
                            <span>{program.statistics.availableSeats} available seats</span>
                          </div>
                        )}
                      </div>

                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/admin/programs/${program.id}`)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>

                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={() => router.push(`/admin/programs/${program.id}/edit`)}
                            >
                              <Edit className="h-4 w-4 mr-2" />
                              Edit Program
                            </DropdownMenuItem>
                            <DropdownMenuItem
                              onClick={() => router.push(`/admin/programs/${program.id}/batches`)}
                            >
                              <Users className="h-4 w-4 mr-2" />
                              Manage Batches
                            </DropdownMenuItem>
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <DropdownMenuItem
                                  onSelect={(e) => e.preventDefault()}
                                  className="text-red-600 focus:text-red-600"
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Delete Program
                                </DropdownMenuItem>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Delete Program</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Are you sure you want to delete &ldquo;{program.name}&rdquo;? This action cannot be undone.
                                    This will also delete all associated batches and classes.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={() => handleDeleteProgram(program.id, program.name)}
                                    className="bg-red-600 hover:bg-red-700"
                                  >
                                    Delete
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>

        {/* Pagination */}
        {pagination.totalPages > 1 && (
          <div className="flex items-center justify-between">
            <div className="text-sm text-gray-700">
              Showing {((pagination.page - 1) * pagination.limit) + 1} to{" "}
              {Math.min(pagination.page * pagination.limit, pagination.total)} of{" "}
              {pagination.total} programs
            </div>
            <div className="flex gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(pagination.page - 1)}
                disabled={pagination.page <= 1}
              >
                Previous
              </Button>
              {Array.from({ length: pagination.totalPages }, (_, i) => i + 1)
                .filter(page =>
                  page === 1 ||
                  page === pagination.totalPages ||
                  Math.abs(page - pagination.page) <= 1
                )
                .map((page, index, array) => (
                  <div key={page} className="flex items-center">
                    {index > 0 && array[index - 1] !== page - 1 && (
                      <span className="px-2 text-gray-400">...</span>
                    )}
                    <Button
                      variant={page === pagination.page ? "default" : "outline"}
                      size="sm"
                      onClick={() => setCurrentPage(page)}
                    >
                      {page}
                    </Button>
                  </div>
                ))}
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(pagination.page + 1)}
                disabled={pagination.page >= pagination.totalPages}
              >
                Next
              </Button>
            </div>
          </div>
        )}
      </div>
    </AppLayout>
  );
}