import { <PERSON><PERSON> } from "hono";
import { zValidator } from "@hono/zod-validator";
import { z } from "zod";
import { db } from "@/lib/db";
import {
  students,
  users,
  academicPrograms,
  academicBatches,
  academicSections,
  classes,
  classEnrollments,
  attendance,
  grades
} from "@/lib/db/schema";
import { eq, count, and, desc, sql } from "drizzle-orm";

// Enhanced student schema for school management with parent management
const enhancedStudentSchema = z.object({
  firstName: z.string().min(2, "First name must be at least 2 characters"),
  lastName: z.string().min(2, "Last name must be at least 2 characters"),
  email: z.string().email("Invalid email address"),
  phone: z.string().min(10, "Phone number must be at least 10 digits"),
  rollNumber: z.string().min(1, "Roll number is required"),
  programId: z.string().min(1, "Class ID is required"),
  batchId: z.string().min(1, "Academic session ID is required"),
  sectionId: z.string().min(1, "Section ID is required"),
  grade: z.enum(["Nursery", "LKG", "UKG", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"]),
  section: z.string().min(1, "Section is required"),
  studentType: z.enum(["regular", "lateral", "distance"]), // Keep database compatibility

  // Parent management
  useExistingParent: z.boolean().default(false),
  existingParentId: z.string().optional(),
  parentFirstName: z.string().optional(),
  parentLastName: z.string().optional(),
  parentEmail: z.string().email().optional(),
  parentPhone: z.string().optional(),
  parentAddress: z.string().optional(),
  relationship: z.enum(["father", "mother", "guardian"]).default("father"),
  isTransferStudent: z.boolean().optional(), // Changed from isLateralEntry
  dateOfBirth: z.string(),
  address: z.string().min(5, "Address must be at least 5 characters"),
  parentName: z.string().min(2, "Parent name must be at least 2 characters"),
  admissionDate: z.string(),
  bloodGroup: z.string().optional(),
  emergencyContact: z.string().optional(),
  medicalInfo: z.string().optional(),
  previousSchool: z.string().optional(), // Changed from previousEducation
  transferCertificate: z.string().optional(),
});

const updateStudentSchema = z.object({
  firstName: z.string().min(2, "First name must be at least 2 characters").optional(),
  lastName: z.string().min(2, "Last name must be at least 2 characters").optional(),
  email: z.string().email("Invalid email address").optional(),
  phone: z.string().min(10, "Phone number must be at least 10 digits").optional(),
  rollNumber: z.string().min(1, "Roll number is required").optional(),
  programId: z.string().min(1, "Class ID is required").optional(),
  batchId: z.string().min(1, "Academic session ID is required").optional(),
  grade: z.enum(["Nursery", "LKG", "UKG", "1", "2", "3", "4", "5", "6", "7", "8", "9", "10", "11", "12"]).optional(),
  section: z.string().min(1, "Section is required").optional(),
  studentType: z.enum(["regular", "lateral", "distance"]).optional(),
  isTransferStudent: z.boolean().optional(),
  dateOfBirth: z.string().optional(),
  address: z.string().min(5, "Address must be at least 5 characters").optional(),
  parentName: z.string().min(2, "Parent name must be at least 2 characters").optional(),
  parentPhone: z.string().min(10, "Parent phone must be at least 10 digits").optional(),
  parentEmail: z.string().email("Invalid parent email address").optional(),
  admissionDate: z.string().optional(),
  bloodGroup: z.string().optional(),
  emergencyContact: z.string().optional(),
  medicalInfo: z.string().optional(),
  previousSchool: z.string().optional(),
  transferCertificate: z.string().optional(),
});

const app = new Hono()
  .get("/", async (c) => {
    try {
      // Get query parameters for pagination and filtering
      const page = parseInt(c.req.query("page") || "1");
      const limit = parseInt(c.req.query("limit") || "50");
      const search = c.req.query("search");
      const grade = c.req.query("grade");
      const section = c.req.query("section");
      const programId = c.req.query("programId");
      const batchId = c.req.query("batchId");
      const sectionId = c.req.query("sectionId");
      const studentType = c.req.query("studentType");
      const status = c.req.query("status");
      const teacherId = c.req.query("teacherId");

      // Build query conditions
      let whereConditions = [];

      if (grade) {
        whereConditions.push(eq(students.grade, grade));
      }

      if (section) {
        whereConditions.push(eq(students.section, section));
      }

      if (programId) {
        whereConditions.push(eq(students.programId, programId));
      }

      if (batchId) {
        whereConditions.push(eq(students.batchId, batchId));
      }

      if (sectionId) {
        whereConditions.push(eq(students.sectionId, sectionId));
      }

      if (studentType) {
        whereConditions.push(eq(students.studentType, studentType as any));
      }

      if (status) {
        whereConditions.push(eq(students.status, status as any));
      }

      // Get students with related data
      const allStudents = await db
        .select({
          id: students.id,
          rollNumber: students.rollNumber,
          grade: students.grade,
          section: students.section,
          studentType: students.studentType,
          isLateralEntry: students.isLateralEntry, // Will be renamed to isTransferStudent in response
          dateOfBirth: students.dateOfBirth,
          address: students.address,
          parentName: students.parentName,
          parentPhone: students.parentPhone,
          parentEmail: students.parentEmail,
          admissionDate: students.admissionDate,
          bloodGroup: students.bloodGroup,
          emergencyContact: students.emergencyContact,
          medicalInfo: students.medicalInfo,
          previousEducation: students.previousEducation,
          transferCertificate: students.transferCertificate,
          status: students.status,
          createdAt: students.createdAt,
          updatedAt: students.updatedAt,
          // User details
          firstName: users.firstName,
          lastName: users.lastName,
          email: users.email,
          phone: users.phone,
          // Program details
          programId: academicPrograms.id,
          programName: academicPrograms.name,
          programCode: academicPrograms.code,
          programType: academicPrograms.type,
          // Batch details
          batchId: academicBatches.id,
          batchName: academicBatches.batchName,
          batchStartYear: academicBatches.startYear,
          batchEndYear: academicBatches.endYear,
          // Section details
          sectionId: academicSections.id,
          sectionName: academicSections.name,
          sectionDisplayName: academicSections.displayName,
        })
        .from(students)
        .leftJoin(users, eq(students.userId, users.id))
        .leftJoin(academicPrograms, eq(students.programId, academicPrograms.id))
        .leftJoin(academicBatches, eq(students.batchId, academicBatches.id))
        .leftJoin(academicSections, eq(students.sectionId, academicSections.id))
        .where(whereConditions.length > 0 ? and(...whereConditions) : undefined);

      // Apply search filter if provided
      let filteredStudents = allStudents;
      if (search) {
        const searchLower = search.toLowerCase();
        filteredStudents = allStudents.filter(student =>
          student.firstName?.toLowerCase().includes(searchLower) ||
          student.lastName?.toLowerCase().includes(searchLower) ||
          student.email?.toLowerCase().includes(searchLower) ||
          student.rollNumber.toLowerCase().includes(searchLower) ||
          student.programName?.toLowerCase().includes(searchLower) ||
          student.batchName?.toLowerCase().includes(searchLower) ||
          student.sectionDisplayName?.toLowerCase().includes(searchLower)
        );
      }

      // Transform data to include structured hierarchical information
      const transformedStudents = filteredStudents.map(student => ({
        id: student.id,
        rollNumber: student.rollNumber,
        firstName: student.firstName,
        lastName: student.lastName,
        email: student.email,
        phone: student.phone,
        studentType: student.studentType,
        isTransferStudent: student.isLateralEntry, // Renamed for school context
        admissionDate: student.admissionDate,
        status: student.status,
        dateOfBirth: student.dateOfBirth,
        address: student.address,
        parentName: student.parentName,
        parentPhone: student.parentPhone,
        parentEmail: student.parentEmail,
        bloodGroup: student.bloodGroup,
        emergencyContact: student.emergencyContact,
        medicalInfo: student.medicalInfo,
        previousSchool: student.previousEducation, // Renamed for school context
        transferCertificate: student.transferCertificate,
        grade: student.grade,
        sectionLegacy: student.section, // Legacy field
        createdAt: student.createdAt,
        updatedAt: student.updatedAt,
        // Structured hierarchical data (simplified for school-only)
        program: student.programId ? {
          id: student.programId,
          name: student.programName,
          code: student.programCode,
          type: student.programType,
        } : null,
        batch: student.batchId ? {
          id: student.batchId,
          batchName: student.batchName,
          startYear: student.batchStartYear,
          endYear: student.batchEndYear,
        } : null,
        section: student.sectionId ? {
          id: student.sectionId,
          name: student.sectionName,
          displayName: student.sectionDisplayName,
        } : null,
      }));

      // Pagination
      const offset = (page - 1) * limit;
      const paginatedStudents = transformedStudents.slice(offset, offset + limit);

      const meta = {
        page,
        limit,
        total: transformedStudents.length,
        totalPages: Math.ceil(transformedStudents.length / limit),
        hasNext: page < Math.ceil(transformedStudents.length / limit),
        hasPrev: page > 1,
      };

      return c.json({ data: paginatedStudents, meta });
    } catch (error) {
      console.error("Error fetching students:", error);
      return c.json({ error: "Failed to fetch students" }, 500);
    }
  })
  .get("/:id", async (c) => {
    try {
      const id = c.req.param("id");

      const [student] = await db
        .select({
          id: students.id,
          rollNumber: students.rollNumber,
          grade: students.grade,
          section: students.section,
          studentType: students.studentType,
          isLateralEntry: students.isLateralEntry,
          dateOfBirth: students.dateOfBirth,
          address: students.address,
          parentName: students.parentName,
          parentPhone: students.parentPhone,
          parentEmail: students.parentEmail,
          admissionDate: students.admissionDate,
          bloodGroup: students.bloodGroup,
          emergencyContact: students.emergencyContact,
          medicalInfo: students.medicalInfo,
          previousEducation: students.previousEducation,
          transferCertificate: students.transferCertificate,
          status: students.status,
          createdAt: students.createdAt,
          updatedAt: students.updatedAt,
          // User details
          firstName: users.firstName,
          lastName: users.lastName,
          email: users.email,
          phone: users.phone,
          // Program details
          programName: academicPrograms.name,
          programCode: academicPrograms.code,
          programType: academicPrograms.type,
          // Batch details
          batchName: academicBatches.batchName,
          batchStartYear: academicBatches.startYear,
          batchEndYear: academicBatches.endYear,
        })
        .from(students)
        .leftJoin(users, eq(students.userId, users.id))
        .leftJoin(academicPrograms, eq(students.programId, academicPrograms.id))
        .leftJoin(academicBatches, eq(students.batchId, academicBatches.id))
        .where(eq(students.id, id));

      if (!student) {
        return c.json({ error: "Student not found" }, 404);
      }

      // Get detailed statistics
      const [attendanceStats] = await db
        .select({
          total: count(),
          present: sql<number>`COUNT(CASE WHEN ${attendance.status} = 'present' THEN 1 END)`,
        })
        .from(attendance)
        .where(eq(attendance.studentId, id));

      const recentGrades = await db
        .select({
          subject: grades.subject,
          obtainedMarks: grades.obtainedMarks,
          totalMarks: grades.totalMarks,
          grade: grades.grade,
          percentage: grades.percentage,
          examType: grades.examType,
          examDate: grades.examDate,
        })
        .from(grades)
        .where(eq(grades.studentId, id))
        .orderBy(desc(grades.createdAt))
        .limit(10);

      const enrolledClasses = await db
        .select({
          classId: classes.id,
          className: classes.name,
          subject: classes.subject,
          teacherName: users.firstName,
          room: classes.room,
          // Removed credits - school-only mode doesn't use credits
        })
        .from(classEnrollments)
        .leftJoin(classes, eq(classEnrollments.classId, classes.id))
        .leftJoin(students, eq(classEnrollments.studentId, students.id))
        .leftJoin(users, eq(classes.teacherId, users.id))
        .where(eq(classEnrollments.studentId, id));

      const attendancePercentage = attendanceStats.total > 0
        ? Math.round((attendanceStats.present / attendanceStats.total) * 100)
        : 0;

      return c.json({
        data: {
          ...student,
          statistics: {
            attendancePercentage,
            totalClasses: attendanceStats.total,
            presentDays: attendanceStats.present,
            recentGrades,
            enrolledClasses,
            totalGrades: recentGrades.length,
          },
        },
      });
    } catch (error) {
      console.error("Error fetching student:", error);
      return c.json({ error: "Failed to fetch student" }, 500);
    }
  })
  .post(
    "/",
    zValidator("json", enhancedStudentSchema),
    async (c) => {
      try {
        const values = c.req.valid("json");

        // Validate program and batch exist
        const [program] = await db
          .select()
          .from(academicPrograms)
          .where(eq(academicPrograms.id, values.programId));

        if (!program) {
          return c.json({ error: "Academic program not found" }, 404);
        }

        const [batch] = await db
          .select()
          .from(academicBatches)
          .where(eq(academicBatches.id, values.batchId));

        if (!batch) {
          return c.json({ error: "Academic batch not found" }, 404);
        }

        // Validate section exists and has available seats
        const [section] = await db
          .select()
          .from(academicSections)
          .where(eq(academicSections.id, values.sectionId));

        if (!section) {
          return c.json({ error: "Academic section not found" }, 404);
        }

        if (section.availableSeats <= 0) {
          return c.json({ error: "No seats available in this section" }, 400);
        }

        // Check for duplicate roll number
        const [existingStudent] = await db
          .select()
          .from(students)
          .where(eq(students.rollNumber, values.rollNumber));

        if (existingStudent) {
          return c.json({ error: "Roll number already exists" }, 400);
        }

        // Check for duplicate email
        const [existingUser] = await db
          .select()
          .from(users)
          .where(eq(users.email, values.email));

        if (existingUser) {
          return c.json({ error: "Email already exists" }, 400);
        }

        let parentUserId = null;

        // Handle parent management
        if (values.useExistingParent && values.existingParentId) {
          // Use existing parent
          const [existingParent] = await db
            .select()
            .from(users)
            .where(and(eq(users.id, values.existingParentId), eq(users.role, "parent")));

          if (!existingParent) {
            return c.json({ error: "Selected parent not found" }, 404);
          }
          parentUserId = existingParent.id;
        } else if (values.parentFirstName && values.parentLastName && values.parentEmail) {
          // Create new parent
          const [newParent] = await db
            .insert(users)
            .values({
              email: values.parentEmail,
              password: "parent123", // Default password
              firstName: values.parentFirstName,
              lastName: values.parentLastName,
              phone: values.parentPhone || "",
              role: "parent",
            })
            .returning();
          parentUserId = newParent.id;
        }

        // Create student user account
        const [newUser] = await db
          .insert(users)
          .values({
            email: values.email,
            password: "student123", // Default password - should be changed
            firstName: values.firstName,
            lastName: values.lastName,
            phone: values.phone,
            role: "student",
          })
          .returning();

        // Create student record
        const [newStudent] = await db
          .insert(students)
          .values({
            userId: newUser.id,
            rollNumber: values.rollNumber,
            programId: values.programId,
            batchId: values.batchId,
            sectionId: values.sectionId,
            grade: values.grade,
            section: values.section,
            studentType: values.studentType,
            isLateralEntry: values.isTransferStudent || false,
            dateOfBirth: values.dateOfBirth,
            address: values.address,
            parentName: values.useExistingParent
              ? `${values.parentFirstName} ${values.parentLastName}`
              : values.parentName,
            parentPhone: values.parentPhone,
            parentEmail: values.parentEmail,
            admissionDate: values.admissionDate || new Date().toISOString().split('T')[0],
            bloodGroup: values.bloodGroup,
            emergencyContact: values.emergencyContact,
            medicalInfo: values.medicalInfo,
            previousEducation: values.previousSchool, // Renamed for school context
            transferCertificate: values.transferCertificate,
          })
          .returning();

        // Update section seat counts
        await db
          .update(academicSections)
          .set({
            occupiedSeats: section.occupiedSeats + 1,
            availableSeats: section.availableSeats - 1,
            updatedAt: new Date(),
          })
          .where(eq(academicSections.id, values.sectionId));

        // Update batch seat counts
        await db
          .update(academicBatches)
          .set({
            occupiedSeats: batch.occupiedSeats + 1,
            availableSeats: batch.availableSeats - 1,
            updatedAt: new Date(),
          })
          .where(eq(academicBatches.id, values.batchId));

        return c.json({
          data: {
            ...newStudent,
            user: newUser,
            parentUserId,
            assignedSection: section.displayName,
            message: `Student successfully admitted to ${section.displayName}`
          }
        }, 201);
      } catch (error) {
        console.error("Error creating student:", error);
        if (error instanceof Error && error.message.includes("unique")) {
          return c.json({ error: "Email or roll number already exists" }, 400);
        }
        return c.json({ error: "Failed to create student" }, 500);
      }
    }
  )
  .put(
    "/:id",
    zValidator("json", updateStudentSchema),
    async (c) => {
      try {
        const id = c.req.param("id");
        const values = c.req.valid("json");

        // Check if student exists
        const [existingStudent] = await db
          .select()
          .from(students)
          .where(eq(students.id, id));

        if (!existingStudent) {
          return c.json({ error: "Student not found" }, 404);
        }

        // If batch is being changed, validate seat availability
        if (values.batchId && values.batchId !== existingStudent.batchId) {
          const [newBatch] = await db
            .select()
            .from(academicBatches)
            .where(eq(academicBatches.id, values.batchId));

          if (!newBatch) {
            return c.json({ error: "New batch not found" }, 404);
          }

          if (newBatch.availableSeats <= 0) {
            return c.json({ error: "No seats available in the new batch" }, 400);
          }

          // Update old batch seat count
          await db
            .update(academicBatches)
            .set({
              occupiedSeats: sql`${academicBatches.occupiedSeats} - 1`,
              availableSeats: sql`${academicBatches.availableSeats} + 1`,
              updatedAt: new Date(),
            })
            .where(eq(academicBatches.id, existingStudent.batchId));

          // Update new batch seat count
          await db
            .update(academicBatches)
            .set({
              occupiedSeats: sql`${academicBatches.occupiedSeats} + 1`,
              availableSeats: sql`${academicBatches.availableSeats} - 1`,
              updatedAt: new Date(),
            })
            .where(eq(academicBatches.id, values.batchId));
        }

        // Update user information if provided
        if (values.firstName || values.lastName || values.email || values.phone) {
          await db
            .update(users)
            .set({
              firstName: values.firstName,
              lastName: values.lastName,
              email: values.email,
              phone: values.phone,
              updatedAt: new Date(),
            })
            .where(eq(users.id, existingStudent.userId));
        }

        // Update student record
        const [updatedStudent] = await db
          .update(students)
          .set({
            ...values,
            updatedAt: new Date(),
          })
          .where(eq(students.id, id))
          .returning();

        return c.json({ data: updatedStudent });
      } catch (error) {
        console.error("Error updating student:", error);
        return c.json({ error: "Failed to update student" }, 500);
      }
    }
  )
  .delete("/:id", async (c) => {
    try {
      const id = c.req.param("id");

      // Get student details before deletion
      const [student] = await db
        .select()
        .from(students)
        .where(eq(students.id, id));

      if (!student) {
        return c.json({ error: "Student not found" }, 404);
      }

      // Update batch seat count
      await db
        .update(academicBatches)
        .set({
          occupiedSeats: sql`${academicBatches.occupiedSeats} - 1`,
          availableSeats: sql`${academicBatches.availableSeats} + 1`,
          updatedAt: new Date(),
        })
        .where(eq(academicBatches.id, student.batchId));

      // Delete student record (this will cascade to related records)
      const [deletedStudent] = await db
        .delete(students)
        .where(eq(students.id, id))
        .returning();

      // Delete user account
      await db
        .delete(users)
        .where(eq(users.id, student.userId));

      return c.json({ data: deletedStudent });
    } catch (error) {
      console.error("Error deleting student:", error);
      return c.json({ error: "Failed to delete student" }, 500);
    }
  })

  // Additional endpoint to get students by batch and section
  .get("/batch/:batchId/section/:section", async (c) => {
    try {
      const batchId = c.req.param("batchId");
      const section = c.req.param("section");

      const studentsInSection = await db
        .select({
          id: students.id,
          rollNumber: students.rollNumber,
          firstName: users.firstName,
          lastName: users.lastName,
          email: users.email,
          studentType: students.studentType,
          isLateralEntry: students.isLateralEntry,
          status: students.status,
        })
        .from(students)
        .leftJoin(users, eq(students.userId, users.id))
        .where(
          and(
            eq(students.batchId, batchId),
            eq(students.section, section),
            eq(students.status, "active")
          )
        )
        .orderBy(students.rollNumber);

      return c.json({ data: studentsInSection });
    } catch (error) {
      console.error("Error fetching students by section:", error);
      return c.json({ error: "Failed to fetch students" }, 500);
    }
  });

export default app;
