"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Textarea } from "@/components/ui/textarea";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  CheckCircle,
  XCircle,
  Clock,
  IndianRupee,
  Users,
  FileText,
  Shield,
  BookOpen,
  AlertTriangle,
  Eye,
  Download,
  Filter,
  Search,
  RefreshCw
} from "lucide-react";
import { toast } from "sonner";

interface Approval {
  id: string;
  type: "budget" | "staff" | "policy" | "disciplinary" | "curriculum";
  title: string;
  description: string;
  amount?: number;
  requestedBy: string;
  department: string;
  priority: "high" | "medium" | "low";
  submittedDate: string;
  status: "pending" | "approved" | "rejected";
  documents: string[];
}

export default function PrincipalApprovalsPage() {
  const [user, setUser] = useState<any>(null);
  const router = useRouter();
  const [approvals, setApprovals] = useState<Approval[]>([]);
  const [loading, setLoading] = useState(true);
  const [selectedApproval, setSelectedApproval] = useState<Approval | null>(null);
  const [actionComments, setActionComments] = useState("");
  const [filter, setFilter] = useState<string>("all");

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "principal") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
    fetchApprovals();
  }, [router]);

  const fetchApprovals = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/principal/approvals");
      const result = await response.json();
      
      if (response.ok) {
        setApprovals(result.data);
      } else {
        toast.error("Failed to fetch approvals");
      }
    } catch (error) {
      console.error("Error fetching approvals:", error);
      toast.error("Failed to fetch approvals");
    } finally {
      setLoading(false);
    }
  };

  const handleApprovalAction = async (approvalId: string, action: "approve" | "reject") => {
    try {
      const response = await fetch(`/api/principal/approvals/${approvalId}/action`, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          action,
          comments: actionComments,
        }),
      });

      if (response.ok) {
        toast.success(`Request ${action}d successfully`);
        setApprovals(prev => 
          prev.map(approval => 
            approval.id === approvalId 
              ? { ...approval, status: action === "approve" ? "approved" : "rejected" }
              : approval
          )
        );
        setSelectedApproval(null);
        setActionComments("");
      } else {
        toast.error(`Failed to ${action} request`);
      }
    } catch (error) {
      console.error(`Error ${action}ing request:`, error);
      toast.error(`Failed to ${action} request`);
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "budget": return IndianRupee;
      case "staff": return Users;
      case "policy": return FileText;
      case "disciplinary": return Shield;
      case "curriculum": return BookOpen;
      default: return FileText;
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case "budget": return "bg-green-100 text-green-800";
      case "staff": return "bg-blue-100 text-blue-800";
      case "policy": return "bg-purple-100 text-purple-800";
      case "disciplinary": return "bg-red-100 text-red-800";
      case "curriculum": return "bg-yellow-100 text-yellow-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "bg-red-100 text-red-800";
      case "medium": return "bg-yellow-100 text-yellow-800";
      case "low": return "bg-green-100 text-green-800";
      default: return "bg-gray-100 text-gray-800";
    }
  };

  const filteredApprovals = approvals.filter(approval => {
    if (filter === "all") return true;
    if (filter === "pending") return approval.status === "pending";
    if (filter === "high") return approval.priority === "high";
    return approval.type === filter;
  });

  if (!user) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Pending Approvals</h1>
            <p className="text-gray-600">
              Review and approve requests requiring principal authorization
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={fetchApprovals}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
          </div>
        </div>

        {/* Filter Tabs */}
        <div className="flex gap-2 flex-wrap">
          {[
            { key: "all", label: "All", count: approvals.length },
            { key: "pending", label: "Pending", count: approvals.filter(a => a.status === "pending").length },
            { key: "high", label: "High Priority", count: approvals.filter(a => a.priority === "high").length },
            { key: "budget", label: "Budget", count: approvals.filter(a => a.type === "budget").length },
            { key: "staff", label: "Staff", count: approvals.filter(a => a.type === "staff").length },
          ].map((tab) => (
            <Button
              key={tab.key}
              variant={filter === tab.key ? "default" : "outline"}
              size="sm"
              onClick={() => setFilter(tab.key)}
            >
              {tab.label} ({tab.count})
            </Button>
          ))}
        </div>

        {/* Approvals List */}
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <div className="grid gap-4">
            {filteredApprovals.map((approval) => {
              const TypeIcon = getTypeIcon(approval.type);
              return (
                <Card key={approval.id} className="hover:shadow-md transition-shadow">
                  <CardContent className="p-6">
                    <div className="flex items-start justify-between">
                      <div className="flex items-start space-x-4 flex-1">
                        <div className={`p-3 rounded-lg ${getTypeColor(approval.type)}`}>
                          <TypeIcon className="h-5 w-5" />
                        </div>
                        <div className="flex-1">
                          <div className="flex items-center gap-2 mb-2">
                            <h3 className="font-semibold text-gray-900">{approval.title}</h3>
                            <Badge className={getPriorityColor(approval.priority)}>
                              {approval.priority}
                            </Badge>
                            {approval.priority === "high" && (
                              <AlertTriangle className="h-4 w-4 text-red-500" />
                            )}
                          </div>
                          <p className="text-gray-600 mb-2">{approval.description}</p>
                          <div className="flex items-center gap-4 text-sm text-gray-500">
                            <span>Requested by: {approval.requestedBy}</span>
                            <span>Department: {approval.department}</span>
                            <span>Date: {new Date(approval.submittedDate).toLocaleDateString()}</span>
                            {approval.amount && (
                              <span className="font-medium text-green-600">
                                ₹{approval.amount.toLocaleString()}
                              </span>
                            )}
                          </div>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Dialog>
                          <DialogTrigger asChild>
                            <Button
                              variant="outline"
                              size="sm"
                              onClick={() => setSelectedApproval(approval)}
                            >
                              <Eye className="h-4 w-4 mr-2" />
                              Review
                            </Button>
                          </DialogTrigger>
                          <DialogContent className="max-w-2xl">
                            <DialogHeader>
                              <DialogTitle>{approval.title}</DialogTitle>
                              <DialogDescription>
                                Review and take action on this approval request
                              </DialogDescription>
                            </DialogHeader>
                            <div className="space-y-4">
                              <div>
                                <h4 className="font-medium mb-2">Description</h4>
                                <p className="text-gray-600">{approval.description}</p>
                              </div>
                              <div className="grid grid-cols-2 gap-4">
                                <div>
                                  <h4 className="font-medium mb-1">Requested By</h4>
                                  <p className="text-gray-600">{approval.requestedBy}</p>
                                </div>
                                <div>
                                  <h4 className="font-medium mb-1">Department</h4>
                                  <p className="text-gray-600">{approval.department}</p>
                                </div>
                                <div>
                                  <h4 className="font-medium mb-1">Priority</h4>
                                  <Badge className={getPriorityColor(approval.priority)}>
                                    {approval.priority}
                                  </Badge>
                                </div>
                                {approval.amount && (
                                  <div>
                                    <h4 className="font-medium mb-1">Amount</h4>
                                    <p className="text-green-600 font-medium">
                                      ₹{approval.amount.toLocaleString()}
                                    </p>
                                  </div>
                                )}
                              </div>
                              <div>
                                <h4 className="font-medium mb-2">Comments (Optional)</h4>
                                <Textarea
                                  placeholder="Add your comments or conditions..."
                                  value={actionComments}
                                  onChange={(e) => setActionComments(e.target.value)}
                                />
                              </div>
                            </div>
                            <DialogFooter className="gap-2">
                              <Button
                                variant="outline"
                                onClick={() => handleApprovalAction(approval.id, "reject")}
                                className="text-red-600 hover:text-red-700"
                              >
                                <XCircle className="h-4 w-4 mr-2" />
                                Reject
                              </Button>
                              <Button
                                onClick={() => handleApprovalAction(approval.id, "approve")}
                                className="bg-green-600 hover:bg-green-700"
                              >
                                <CheckCircle className="h-4 w-4 mr-2" />
                                Approve
                              </Button>
                            </DialogFooter>
                          </DialogContent>
                        </Dialog>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              );
            })}
          </div>
        )}

        {filteredApprovals.length === 0 && !loading && (
          <Card>
            <CardContent className="p-12 text-center">
              <CheckCircle className="h-12 w-12 text-green-500 mx-auto mb-4" />
              <h3 className="text-lg font-medium text-gray-900 mb-2">
                No pending approvals
              </h3>
              <p className="text-gray-600">
                All approval requests have been processed.
              </p>
            </CardContent>
          </Card>
        )}
      </div>
    </AppLayout>
  );
}
