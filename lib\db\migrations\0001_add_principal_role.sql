-- Migration: Add principal role to user_role enum
-- This migration adds the 'principal' role to the existing user_role enum

-- First, we need to add the new value to the enum
ALTER TYPE "user_role" ADD VALUE 'principal';

-- Update any existing users that should be principals
-- This is optional and depends on your existing data
-- UPDATE users SET role = 'principal' WHERE email = '<EMAIL>' AND role = 'admin';

-- Add any additional constraints or indexes if needed
-- CREATE INDEX IF NOT EXISTS "users_principal_idx" ON "users" ("role") WHERE role = 'principal';
