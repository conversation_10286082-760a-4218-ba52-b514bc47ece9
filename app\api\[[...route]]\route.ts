import { handle } from "hono/vercel";
import { Hono } from "hono";
import { cors } from "hono/cors";

import auth from "./auth";
import users from "./users";
import students from "./students";
import teachers from "./teachers";
import classes from "./classes";
import attendance from "./attendance";
import grades from "./grades";
import assignments from "./assignments";
import dashboard from "./dashboard";
import finance from "./finance";
import library from "./library";
import transport from "./transport";
import hostel from "./hostel";
import exams from "./exams";
import timetable from "./timetable";
import database from "./database";
import academicPrograms from "./academic-programs";
import academicBatches from "./academic-batches";
import institutionConfig from "./institution-config";
import academicSections from "./academic-sections";
import feeStructures from "./fee-structures";
import systemSettings from "./system-settings";
import systemBackups from "./system-backups";
import auditLogs from "./audit-logs";
import principal from "./principal";

export const runtime = "nodejs";

const app = new Hono().basePath("/api");

// Add CORS middleware
app.use("*", cors({
  origin: ["http://localhost:3000", "https://localhost:3000"],
  allowMethods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
  allowHeaders: ["Content-Type", "Authorization"],
}));

// Route registration using method chaining
const routes = app
  .route("/auth", auth)
  .route("/users", users)
  .route("/students", students)
  .route("/teachers", teachers)
  .route("/classes", classes)
  .route("/attendance", attendance)
  .route("/grades", grades)
  .route("/assignments", assignments)
  .route("/dashboard", dashboard)
  .route("/finance", finance)
  .route("/library", library)
  .route("/transport", transport)
  .route("/hostel", hostel)
  .route("/exams", exams)
  .route("/timetable", timetable)
  .route("/database", database)
  .route("/academic-programs", academicPrograms)
  .route("/academic-batches", academicBatches)
  .route("/institution-config", institutionConfig)
  .route("/academic-sections", academicSections)
  .route("/fee-structures", feeStructures)
  .route("/system-settings", systemSettings)
  .route("/system-backups", systemBackups)
  .route("/audit-logs", auditLogs)
  .route("/principal", principal);

// Export handlers for Next.js
export const GET = handle(app);
export const POST = handle(app);
export const PUT = handle(app);
export const DELETE = handle(app);
export const PATCH = handle(app);

// Export type for client-side usage
export type AppType = typeof routes;
