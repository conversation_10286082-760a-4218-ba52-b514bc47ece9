"use client";

import { useState } from "react";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Building, 
  Search, 
  Plus, 
  Filter,
  Users,
  Bed,
  CheckCircle,
  AlertTriangle,
  User,
  Phone,
  MapPin
} from "lucide-react";

// Mock hostel data
const mockHostels = [
  {
    id: "1",
    name: "Boys Hostel Block A",
    type: "boys",
    totalRooms: 50,
    occupiedRooms: 45,
    totalBeds: 100,
    occupiedBeds: 85,
    wardenName: "Mr. <PERSON><PERSON>",
    wardenPhone: "9876543212",
    address: "School Campus, Block A",
    facilities: ["WiFi", "Mess", "Study Hall", "Recreation Room", "Laundry"],
    monthlyFee: 15000,
    status: "active",
  },
  {
    id: "2",
    name: "Girls Hostel Block B",
    type: "girls",
    totalRooms: 40,
    occupiedRooms: 38,
    totalBeds: 80,
    occupiedBeds: 72,
    wardenName: "Mrs. <PERSON>",
    wardenPhone: "9876543213",
    address: "School Campus, Block B",
    facilities: ["WiFi", "Mess", "Study Hall", "Common Room", "Laundry", "Security"],
    monthlyFee: 15000,
    status: "active",
  },
];

const mockRooms = [
  {
    id: "1",
    hostelId: "1",
    hostelName: "Boys Hostel Block A",
    roomNumber: "A101",
    roomType: "double",
    capacity: 2,
    occupiedBeds: 2,
    monthlyRent: 7500,
    facilities: ["Attached Bathroom", "Study Table", "Wardrobe"],
    status: "occupied",
    students: ["John Doe", "Mike Smith"],
  },
  {
    id: "2",
    hostelId: "1",
    hostelName: "Boys Hostel Block A",
    roomNumber: "A102",
    roomType: "single",
    capacity: 1,
    occupiedBeds: 0,
    monthlyRent: 10000,
    facilities: ["Attached Bathroom", "Study Table", "Wardrobe", "AC"],
    status: "available",
    students: [],
  },
  {
    id: "3",
    hostelId: "2",
    hostelName: "Girls Hostel Block B",
    roomNumber: "B201",
    roomType: "triple",
    capacity: 3,
    occupiedBeds: 3,
    monthlyRent: 5000,
    facilities: ["Shared Bathroom", "Study Table", "Wardrobe"],
    status: "occupied",
    students: ["Alice Johnson", "Sarah Wilson", "Emma Davis"],
  },
];

const statusColors = {
  available: "text-green-600 bg-green-100",
  occupied: "text-blue-600 bg-blue-100",
  maintenance: "text-yellow-600 bg-yellow-100",
};

const statusIcons = {
  available: CheckCircle,
  occupied: Users,
  maintenance: AlertTriangle,
};

export default function HostelPage() {
  const [search, setSearch] = useState("");
  const [activeTab, setActiveTab] = useState("hostels");

  // Calculate hostel statistics
  const totalHostels = mockHostels.length;
  const totalRooms = mockRooms.length;
  const occupiedRooms = mockRooms.filter(r => r.status === "occupied").length;
  const totalBeds = mockRooms.reduce((sum, room) => sum + room.capacity, 0);
  const occupiedBeds = mockRooms.reduce((sum, room) => sum + room.occupiedBeds, 0);
  const occupancyRate = totalBeds > 0 ? Math.round((occupiedBeds / totalBeds) * 100) : 0;

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Hostel Management</h1>
            <p className="text-gray-600">Manage hostel accommodations and room allocations</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Building className="h-4 w-4 mr-2" />
              Reports
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Room
            </Button>
          </div>
        </div>

        {/* Hostel Stats */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Building className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {totalHostels}
                  </div>
                  <p className="text-sm text-gray-600">Total Hostels</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Bed className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {totalRooms}
                  </div>
                  <p className="text-sm text-gray-600">Total Rooms</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {occupiedBeds}
                  </div>
                  <p className="text-sm text-gray-600">Occupied Beds</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    {occupancyRate}%
                  </div>
                  <p className="text-sm text-gray-600">Occupancy Rate</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab("hostels")}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === "hostels"
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              Hostels
            </button>
            <button
              onClick={() => setActiveTab("rooms")}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === "rooms"
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              Rooms
            </button>
          </nav>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={`Search ${activeTab}...`}
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex gap-2">
                <select className="px-3 py-2 border border-gray-300 rounded-md text-sm bg-white">
                  <option value="">All Types</option>
                  <option value="boys">Boys</option>
                  <option value="girls">Girls</option>
                  <option value="mixed">Mixed</option>
                </select>
                <Button variant="outline" size="sm">
                  <Filter className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Hostels Tab */}
        {activeTab === "hostels" && (
          <Card>
            <CardHeader>
              <CardTitle>Hostel Buildings</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockHostels.map((hostel) => (
                  <div
                    key={hostel.id}
                    className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <Building className="h-6 w-6 text-blue-600" />
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">
                          {hostel.name}
                        </div>
                        <div className="text-sm text-gray-500">
                          {hostel.type.charAt(0).toUpperCase() + hostel.type.slice(1)} Hostel • {hostel.address}
                        </div>
                        <div className="text-xs text-gray-400 flex items-center mt-1">
                          <User className="h-3 w-3 mr-1" />
                          Warden: {hostel.wardenName} • 
                          <Phone className="h-3 w-3 ml-2 mr-1" />
                          {hostel.wardenPhone}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center space-x-4">
                        <div className="text-sm">
                          <div className="font-medium text-blue-600">
                            {hostel.occupiedRooms}/{hostel.totalRooms} rooms
                          </div>
                          <div className="text-gray-500">
                            {hostel.occupiedBeds}/{hostel.totalBeds} beds
                          </div>
                          <div className="text-xs text-gray-400">
                            ₹{hostel.monthlyFee.toLocaleString()}/month
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm">
                            View Rooms
                          </Button>
                          <Button variant="outline" size="sm">
                            Edit
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Rooms Tab */}
        {activeTab === "rooms" && (
          <Card>
            <CardHeader>
              <CardTitle>Room Management</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockRooms.map((room) => {
                  const StatusIcon = statusIcons[room.status as keyof typeof statusIcons];
                  
                  return (
                    <div
                      key={room.id}
                      className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-center space-x-4">
                        <div className={`p-2 rounded-full ${statusColors[room.status as keyof typeof statusColors]}`}>
                          <StatusIcon className="h-4 w-4" />
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">
                            Room {room.roomNumber}
                          </div>
                          <div className="text-sm text-gray-500">
                            {room.hostelName} • {room.roomType.charAt(0).toUpperCase() + room.roomType.slice(1)} Room
                          </div>
                          <div className="text-xs text-gray-400">
                            {room.facilities.join(" • ")}
                          </div>
                          {room.students.length > 0 && (
                            <div className="text-xs text-blue-600 mt-1">
                              Students: {room.students.join(", ")}
                            </div>
                          )}
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="flex items-center space-x-4">
                          <div className="text-sm">
                            <div className="font-medium text-gray-900">
                              {room.occupiedBeds}/{room.capacity} beds
                            </div>
                            <div className="text-gray-500">
                              ₹{room.monthlyRent.toLocaleString()}/month
                            </div>
                            <div className={`text-xs font-medium capitalize ${
                              room.status === "available" ? "text-green-600" :
                              room.status === "occupied" ? "text-blue-600" : "text-yellow-600"
                            }`}>
                              {room.status}
                            </div>
                          </div>
                          <div className="flex gap-2">
                            {room.status === "available" && (
                              <Button variant="outline" size="sm" className="text-blue-600">
                                Allocate
                              </Button>
                            )}
                            {room.status === "occupied" && (
                              <Button variant="outline" size="sm" className="text-red-600">
                                Vacate
                              </Button>
                            )}
                            <Button variant="outline" size="sm">
                              Edit
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Hostel Analytics */}
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Occupancy by Hostel</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockHostels.map((hostel) => {
                  const occupancyRate = Math.round((hostel.occupiedBeds / hostel.totalBeds) * 100);
                  
                  return (
                    <div key={hostel.id}>
                      <div className="flex items-center justify-between mb-2">
                        <span className="text-sm font-medium">{hostel.name}</span>
                        <span className="text-sm text-gray-500">
                          {hostel.occupiedBeds}/{hostel.totalBeds} beds ({occupancyRate}%)
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: `${occupancyRate}%` }}
                        />
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Room Types Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Single Rooms</span>
                  <span className="font-medium">33%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-green-600 h-2 rounded-full w-[33%]" />
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm">Double Rooms</span>
                  <span className="font-medium">34%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-600 h-2 rounded-full w-[34%]" />
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm">Triple Rooms</span>
                  <span className="font-medium">33%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-purple-600 h-2 rounded-full w-[33%]" />
                </div>
                
                <div className="pt-4 border-t">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-blue-600">
                      ₹{(15000 * occupiedBeds / 1000).toFixed(0)}K
                    </div>
                    <div className="text-sm text-gray-500">Monthly Revenue</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  );
}
