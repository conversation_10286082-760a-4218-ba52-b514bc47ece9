"use client";

import { useState } from "react";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  GraduationCap, 
  Search, 
  Plus, 
  Filter,
  Download,
  Upload,
  Edit,
  Trash2,
  Eye,
  Mail,
  Phone,
  BookOpen,
  Users
} from "lucide-react";

// Mock teacher data
const mockTeachers = [
  {
    id: "1",
    employeeId: "EMP001",
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    email: "<EMAIL>",
    phone: "1234567890",
    subject: "Mathematics",
    qualification: "M.Sc Mathematics, B.Ed",
    experience: 8,
    joiningDate: "2020-06-15",
    classes: ["10A", "10B", "11A"],
    totalStudents: 85,
    status: "active",
    salary: 75000,
  },
  {
    id: "2",
    employeeId: "EMP002",
    firstName: "<PERSON>",
    lastName: "<PERSON>",
    email: "<EMAIL>",
    phone: "1234567891",
    subject: "Physics",
    qualification: "M.Sc Physics, B.Ed",
    experience: 12,
    joiningDate: "2018-04-01",
    classes: ["11B", "12A"],
    totalStudents: 65,
    status: "active",
    salary: 85000,
  },
  {
    id: "3",
    employeeId: "EMP003",
    firstName: "Sarah",
    lastName: "Johnson",
    email: "<EMAIL>",
    phone: "1234567892",
    subject: "Chemistry",
    qualification: "M.Sc Chemistry, Ph.D",
    experience: 15,
    joiningDate: "2015-08-20",
    classes: ["10A", "11A", "12B"],
    totalStudents: 92,
    status: "active",
    salary: 95000,
  },
  {
    id: "4",
    employeeId: "EMP004",
    firstName: "Lisa",
    lastName: "Anderson",
    email: "<EMAIL>",
    phone: "1234567893",
    subject: "English",
    qualification: "M.A English Literature, B.Ed",
    experience: 6,
    joiningDate: "2022-01-10",
    classes: ["10B", "11B"],
    totalStudents: 58,
    status: "active",
    salary: 65000,
  },
];

const statusColors = {
  active: "text-green-600 bg-green-100",
  inactive: "text-red-600 bg-red-100",
  on_leave: "text-yellow-600 bg-yellow-100",
};

export default function AdminTeachersPage() {
  const [search, setSearch] = useState("");
  const [selectedSubject, setSelectedSubject] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("");

  // Filter teachers based on search and filters
  const filteredTeachers = mockTeachers.filter(teacher => {
    const matchesSearch = search === "" || 
      teacher.firstName.toLowerCase().includes(search.toLowerCase()) ||
      teacher.lastName.toLowerCase().includes(search.toLowerCase()) ||
      teacher.employeeId.toLowerCase().includes(search.toLowerCase()) ||
      teacher.email.toLowerCase().includes(search.toLowerCase()) ||
      teacher.subject.toLowerCase().includes(search.toLowerCase());
    
    const matchesSubject = selectedSubject === "" || teacher.subject === selectedSubject;
    const matchesStatus = selectedStatus === "" || teacher.status === selectedStatus;
    
    return matchesSearch && matchesSubject && matchesStatus;
  });

  // Calculate statistics
  const totalTeachers = mockTeachers.length;
  const activeTeachers = mockTeachers.filter(t => t.status === "active").length;
  const averageExperience = mockTeachers.reduce((sum, t) => sum + t.experience, 0) / totalTeachers;
  const totalStudentsTeaching = mockTeachers.reduce((sum, t) => sum + t.totalStudents, 0);

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Teacher Management</h1>
            <p className="text-gray-600">Manage teaching staff and faculty</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Upload className="h-4 w-4 mr-2" />
              Import
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Teacher
            </Button>
          </div>
        </div>

        {/* Teacher Stats */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <GraduationCap className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {totalTeachers}
                  </div>
                  <p className="text-sm text-gray-600">Total Teachers</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <GraduationCap className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {activeTeachers}
                  </div>
                  <p className="text-sm text-gray-600">Active Teachers</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <BookOpen className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {averageExperience.toFixed(1)}
                  </div>
                  <p className="text-sm text-gray-600">Avg Experience (years)</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    {totalStudentsTeaching}
                  </div>
                  <p className="text-sm text-gray-600">Students Teaching</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search teachers by name, employee ID, subject, or email..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex gap-2">
                <select 
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm bg-white"
                  value={selectedSubject}
                  onChange={(e) => setSelectedSubject(e.target.value)}
                >
                  <option value="">All Subjects</option>
                  <option value="Mathematics">Mathematics</option>
                  <option value="Physics">Physics</option>
                  <option value="Chemistry">Chemistry</option>
                  <option value="Biology">Biology</option>
                  <option value="English">English</option>
                  <option value="History">History</option>
                </select>
                <select 
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm bg-white"
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                >
                  <option value="">All Status</option>
                  <option value="active">Active</option>
                  <option value="inactive">Inactive</option>
                  <option value="on_leave">On Leave</option>
                </select>
                <Button variant="outline" size="sm">
                  <Filter className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Teachers Table */}
        <Card>
          <CardHeader>
            <CardTitle>Teachers ({filteredTeachers.length})</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Teacher</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Employee ID</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Subject</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Contact</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Experience</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Classes</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredTeachers.map((teacher) => (
                    <tr key={teacher.id} className="border-b hover:bg-gray-50">
                      <td className="py-3 px-4">
                        <div>
                          <div className="font-medium text-gray-900">
                            {teacher.firstName} {teacher.lastName}
                          </div>
                          <div className="text-sm text-gray-500">{teacher.qualification}</div>
                        </div>
                      </td>
                      <td className="py-3 px-4 font-medium">{teacher.employeeId}</td>
                      <td className="py-3 px-4">
                        <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                          {teacher.subject}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-sm">
                          <div className="flex items-center">
                            <Phone className="h-3 w-3 mr-1" />
                            {teacher.phone}
                          </div>
                          <div className="flex items-center text-gray-500">
                            <Mail className="h-3 w-3 mr-1" />
                            {teacher.email.split('@')[0]}
                          </div>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-sm">
                          <div className="font-medium">{teacher.experience} years</div>
                          <div className="text-gray-500">Since {teacher.joiningDate}</div>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <div className="text-sm">
                          <div className="font-medium">{teacher.classes.join(", ")}</div>
                          <div className="text-gray-500">{teacher.totalStudents} students</div>
                        </div>
                      </td>
                      <td className="py-3 px-4">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusColors[teacher.status as keyof typeof statusColors]}`}>
                          {teacher.status.replace('_', ' ').charAt(0).toUpperCase() + teacher.status.replace('_', ' ').slice(1)}
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm">
                            <Eye className="h-3 w-3" />
                          </Button>
                          <Button variant="outline" size="sm">
                            <Edit className="h-3 w-3" />
                          </Button>
                          <Button variant="outline" size="sm" className="text-red-600">
                            <Trash2 className="h-3 w-3" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Subject Distribution */}
        <Card>
          <CardHeader>
            <CardTitle>Subject Distribution</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              {["Mathematics", "Physics", "Chemistry"].map((subject) => {
                const subjectTeachers = mockTeachers.filter(t => t.subject === subject);
                const count = subjectTeachers.length;
                const totalStudents = subjectTeachers.reduce((sum, t) => sum + t.totalStudents, 0);
                const avgExperience = count > 0 ? subjectTeachers.reduce((sum, t) => sum + t.experience, 0) / count : 0;
                
                return (
                  <div key={subject} className="p-4 border rounded-lg">
                    <div className="text-center">
                      <div className="text-2xl font-bold text-blue-600">{count}</div>
                      <div className="text-sm text-gray-600 mb-3">{subject}</div>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>Students:</span>
                          <span className="font-medium">{totalStudents}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Avg Experience:</span>
                          <span className="font-medium">{avgExperience.toFixed(1)} years</span>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
