import { Hono } from "hono";
import { zValidator } from "@hono/zod-validator";
import { z } from "zod";
import { db } from "@/lib/db";
import {
  academicSections,
  academicPrograms,
  academicBatches,
  teachers,
  students
} from "@/lib/db/schema";
import { eq, and, sql, desc } from "drizzle-orm";

const app = new Hono()
  .get("/", async (c) => {
    try {
      const programId = c.req.query("programId");
      const batchId = c.req.query("batchId");
      const isActive = c.req.query("isActive");

      let whereConditions = [];

      if (programId) {
        whereConditions.push(eq(academicSections.programId, programId));
      }
      if (batchId) {
        whereConditions.push(eq(academicSections.batchId, batchId));
      }
      if (isActive !== undefined) {
        whereConditions.push(eq(academicSections.isActive, isActive === "true"));
      }

      const sections = await db
        .select({
          id: academicSections.id,
          name: academicSections.name,
          displayName: academicSections.displayName,
          capacity: academicSections.capacity,
          occupiedSeats: academicSections.occupiedSeats,
          availableSeats: academicSections.availableSeats,
          room: academicSections.room,
          academicYear: academicSections.academicYear,
          // Removed semester field - school-only mode doesn't use semesters
          isActive: academicSections.isActive,
          status: academicSections.status,
          createdAt: academicSections.createdAt,
          program: {
            id: academicPrograms.id,
            name: academicPrograms.name,
            code: academicPrograms.code,
          },
          batch: {
            id: academicBatches.id,
            batchName: academicBatches.batchName,
            startYear: academicBatches.startYear,
            endYear: academicBatches.endYear,
          },

          classTeacher: {
            id: teachers.id,
            firstName: sql`${teachers.userId}`, // This would need proper join with users table
            lastName: sql`${teachers.userId}`,
          },
        })
        .from(academicSections)
        .leftJoin(academicPrograms, eq(academicSections.programId, academicPrograms.id))
        .leftJoin(academicBatches, eq(academicSections.batchId, academicBatches.id))

        .leftJoin(teachers, eq(academicSections.classTeacherId, teachers.id))
        .where(whereConditions.length > 0 ? and(...whereConditions) : undefined)
        .orderBy(academicSections.name);

      return c.json({
        data: sections,
        message: "Academic sections retrieved successfully"
      });
    } catch (error) {
      console.error("Error fetching academic sections:", error);
      return c.json({ error: "Failed to fetch academic sections" }, 500);
    }
  })

  .get("/:id", async (c) => {
    try {
      const id = c.req.param("id");

      const section = await db
        .select()
        .from(academicSections)
        .where(eq(academicSections.id, id))
        .limit(1);

      if (section.length === 0) {
        return c.json({ error: "Academic section not found" }, 404);
      }

      return c.json({
        data: section[0],
        message: "Academic section retrieved successfully"
      });
    } catch (error) {
      console.error("Error fetching academic section:", error);
      return c.json({ error: "Failed to fetch academic section" }, 500);
    }
  })

  .post(
    "/",
    zValidator("json", z.object({
      programId: z.string().uuid("Valid program ID is required"),
      batchId: z.string().uuid("Valid batch ID is required"),
      name: z.string().min(1, "Section name is required"),
      displayName: z.string().min(1, "Display name is required"),
      capacity: z.number().min(1, "Capacity must be at least 1").default(30),
      classTeacherId: z.string().uuid().optional(),
      room: z.string().optional(),
      academicYear: z.string().min(1, "Academic year is required"),
    })),
    async (c) => {
      try {
        const values = c.req.valid("json");

        // Check if section name already exists for the same program/batch combination
        const existingSection = await db
          .select()
          .from(academicSections)
          .where(and(
            eq(academicSections.programId, values.programId),
            eq(academicSections.batchId, values.batchId),
            eq(academicSections.name, values.name),
            eq(academicSections.academicYear, values.academicYear)
          ))
          .limit(1);

        if (existingSection.length > 0) {
          return c.json({
            error: "Section with this name already exists for the selected program and batch"
          }, 409);
        }

        // Verify that program and batch exist
        const program = await db
          .select()
          .from(academicPrograms)
          .where(eq(academicPrograms.id, values.programId))
          .limit(1);

        if (program.length === 0) {
          return c.json({ error: "Invalid program ID" }, 400);
        }

        const batch = await db
          .select()
          .from(academicBatches)
          .where(eq(academicBatches.id, values.batchId))
          .limit(1);

        if (batch.length === 0) {
          return c.json({ error: "Invalid batch ID" }, 400);
        }

        // Stream verification removed - school-only mode doesn't use streams

        // Verify teacher if provided
        if (values.classTeacherId) {
          const teacher = await db
            .select()
            .from(teachers)
            .where(eq(teachers.id, values.classTeacherId))
            .limit(1);

          if (teacher.length === 0) {
            return c.json({ error: "Invalid teacher ID" }, 400);
          }
        }

        const newSection = await db
          .insert(academicSections)
          .values({
            ...values,
            availableSeats: values.capacity,
            occupiedSeats: 0,
          })
          .returning();

        return c.json({
          data: newSection[0],
          message: "Academic section created successfully"
        }, 201);
      } catch (error) {
        console.error("Error creating academic section:", error);
        return c.json({ error: "Failed to create academic section" }, 500);
      }
    }
  )

  // Get available section for student admission (auto-assignment)
  .get("/available", async (c) => {
    try {
      const programId = c.req.query("programId");
      const batchId = c.req.query("batchId");
      if (!programId || !batchId) {
        return c.json({ error: "Program ID and Batch ID are required" }, 400);
      }

      // Build where conditions (removed stream filtering for school-only mode)
      let whereConditions = [
        eq(academicSections.programId, programId),
        eq(academicSections.batchId, batchId),
        eq(academicSections.isActive, true),
        eq(academicSections.status, "active"),
      ];

      // Get sections with available seats, ordered by available seats (descending)
      const availableSections = await db
        .select({
          id: academicSections.id,
          name: academicSections.name,
          displayName: academicSections.displayName,
          capacity: academicSections.capacity,
          occupiedSeats: academicSections.occupiedSeats,
          availableSeats: academicSections.availableSeats,
          room: academicSections.room,
        })
        .from(academicSections)
        .where(and(...whereConditions))
        .orderBy(desc(academicSections.availableSeats));

      // Find the first section with available seats
      const assignableSection = availableSections.find(section => section.availableSeats > 0);

      if (!assignableSection) {
        return c.json({
          data: {
            hasAvailableSeats: false,
            availableSeats: 0,
            assignedSection: null,
            sectionId: null,
            allSections: availableSections,
            message: "No seats available in any section"
          }
        });
      }

      return c.json({
        data: {
          hasAvailableSeats: true,
          availableSeats: assignableSection.availableSeats,
          assignedSection: assignableSection.name,
          sectionId: assignableSection.id,
          sectionDisplayName: assignableSection.displayName,
          room: assignableSection.room,
          capacity: assignableSection.capacity,
          occupiedSeats: assignableSection.occupiedSeats,
          allSections: availableSections,
          message: `Student will be assigned to ${assignableSection.displayName}`
        }
      });
    } catch (error) {
      console.error("Error checking section availability:", error);
      return c.json({ error: "Failed to check section availability" }, 500);
    }
  })

  .put(
    "/:id",
    zValidator("json", z.object({
      name: z.string().min(1).optional(),
      displayName: z.string().min(1).optional(),
      capacity: z.number().min(1).optional(),
      classTeacherId: z.string().uuid().optional(),
      room: z.string().optional(),
      isActive: z.boolean().optional(),
    })),
    async (c) => {
      try {
        const id = c.req.param("id");
        const values = c.req.valid("json");

        let updatedSection;

        // If capacity is being updated, ensure it's not less than occupied seats
        if (values.capacity) {
          const currentSection = await db
            .select()
            .from(academicSections)
            .where(eq(academicSections.id, id))
            .limit(1);

          if (currentSection.length === 0) {
            return c.json({ error: "Academic section not found" }, 404);
          }

          if (values.capacity < currentSection[0].occupiedSeats) {
            return c.json({
              error: `Capacity cannot be less than occupied seats (${currentSection[0].occupiedSeats})`
            }, 400);
          }

          // Calculate available seats if capacity is being updated
          const availableSeats = values.capacity - currentSection[0].occupiedSeats;

          updatedSection = await db
            .update(academicSections)
            .set({
              ...values,
              availableSeats,
              updatedAt: new Date(),
            })
            .where(eq(academicSections.id, id))
            .returning();
        } else {
          updatedSection = await db
            .update(academicSections)
            .set({
              ...values,
              updatedAt: new Date(),
            })
            .where(eq(academicSections.id, id))
            .returning();
        }

        if (updatedSection.length === 0) {
          return c.json({ error: "Academic section not found" }, 404);
        }

        return c.json({
          data: updatedSection[0],
          message: "Academic section updated successfully"
        });
      } catch (error) {
        console.error("Error updating academic section:", error);
        return c.json({ error: "Failed to update academic section" }, 500);
      }
    }
  );

export default app;
