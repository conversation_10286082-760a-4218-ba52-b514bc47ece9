import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { client } from "@/lib/hono";
import { toast } from "sonner";

// Types
export interface CreateBatchData {
  batchName: string;
  programId: string;
  startYear: number;
  endYear: number;
  totalSeats: number;
  description?: string;
  admissionStatus?: "open" | "closed" | "waitlist";
  currentSemester?: "1" | "2" | "3" | "4" | "5" | "6" | "7" | "8";
}

// Get all batches with filtering and pagination
export const useGetBatches = (params?: {
  page?: number;
  limit?: number;
  search?: string;
  status?: string;
  programId?: string;
}) => {
  return useQuery({
    queryKey: ["batches", params],
    queryFn: async () => {
      const searchParams = new URLSearchParams();

      if (params?.page) searchParams.append("page", params.page.toString());
      if (params?.limit) searchParams.append("limit", params.limit.toString());
      if (params?.search) searchParams.append("search", params.search);
      if (params?.status) searchParams.append("status", params.status);
      if (params?.programId) searchParams.append("programId", params.programId);

      const response = await client.api["academic-batches"].$get({
        query: Object.fromEntries(searchParams),
      });

      if (!response.ok) {
        throw new Error("Failed to fetch batches");
      }

      return await response.json();
    },
  });
};

// Get batch by ID
export const useGetBatch = (id: string) => {
  return useQuery({
    queryKey: ["batches", id],
    queryFn: async () => {
      const response = await client.api["academic-batches"][":id"].$get({
        param: { id },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch batch");
      }

      return await response.json();
    },
    enabled: !!id,
  });
};

// Create batch mutation
export const useCreateBatch = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (json: CreateBatchData) => {
      const response = await client.api["academic-batches"].$post({ json });

      if (!response.ok) {
        const errorData = await response.json() as { error?: string };
        throw new Error(errorData.error || "Failed to create batch");
      }

      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["batches"] });
      toast.success("Academic batch created successfully!");
    },
    onError: (error: any) => {
      toast.error(`Failed to create batch: ${error.message}`);
    },
  });
};

// Update batch mutation
export const useUpdateBatch = (id: string) => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (json: Partial<CreateBatchData>) => {
      const response = await client.api["academic-batches"][":id"].$put({
        param: { id },
        json,
      });

      if (!response.ok) {
        const errorData = await response.json() as { error?: string };
        throw new Error(errorData.error || "Failed to update batch");
      }

      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["batches"] });
      queryClient.invalidateQueries({ queryKey: ["batches", id] });
      toast.success("Batch updated successfully!");
    },
    onError: (error: any) => {
      toast.error(`Failed to update batch: ${error.message}`);
    },
  });
};

// Delete batch mutation
export const useDeleteBatch = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await client.api["academic-batches"][":id"].$delete({
        param: { id },
      });

      if (!response.ok) {
        const errorData = await response.json() as { error?: string };
        throw new Error(errorData.error || "Failed to delete batch");
      }

      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["batches"] });
      toast.success("Batch deleted successfully!");
    },
    onError: (error: any) => {
      toast.error(`Failed to delete batch: ${error.message}`);
    },
  });
};

// Get batch students
export const useGetBatchStudents = (batchId: string) => {
  return useQuery({
    queryKey: ["batches", batchId, "students"],
    queryFn: async () => {
      const response = await client.api["academic-batches"][":id"]["students"].$get({
        param: { id: batchId },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch batch students");
      }

      return await response.json();
    },
    enabled: !!batchId,
  });
};

// Get batch statistics
export const useGetBatchStats = () => {
  return useQuery({
    queryKey: ["batches", "stats"],
    queryFn: async () => {
      const response = await client.api["academic-batches"].$get({
        query: { limit: "1" }, // Just get count info
      });

      if (!response.ok) {
        throw new Error("Failed to fetch batch stats");
      }

      const data = await response.json();
      return data.meta;
    },
  });
};

// Get batches by program
export const useGetBatchesByProgram = (programId: string) => {
  return useQuery({
    queryKey: ["batches", "program", programId],
    queryFn: async () => {
      const response = await client.api["academic-batches"].$get({
        query: { programId, limit: "1000" }, // Get all batches for this program
      });

      if (!response.ok) {
        throw new Error("Failed to fetch batches by program");
      }

      return await response.json();
    },
    enabled: !!programId,
  });
};

// Get current batches
export const useGetCurrentBatches = () => {
  return useQuery({
    queryKey: ["batches", "current"],
    queryFn: async () => {
      const response = await client.api["academic-batches"].$get({
        query: { isCurrent: "true", limit: "1000" },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch current batches");
      }

      return await response.json();
    },
  });
};
