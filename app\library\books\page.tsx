"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  BookOpen,
  Search,
  Filter,
  Plus,
  Edit,
  Eye,
  Download,
  Upload,
  Star,
  Calendar,
  User,
  Hash,
  Tag,
  AlertTriangle,
  CheckCircle,
  Clock,
  BarChart3,
  Bookmark,
  Library,
} from "lucide-react";

export default function LibraryBooks() {
  const [user, setUser] = useState<any>(null);
  const [selectedTab, setSelectedTab] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedBook, setSelectedBook] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "library") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  const bookStats = {
    totalBooks: 15420,
    availableBooks: 12890,
    issuedBooks: 2530,
    reservedBooks: 245,
    damagedBooks: 67,
    lostBooks: 23,
  };

  const books = [
    {
      id: "BK001",
      title: "The Complete Guide to Physics",
      author: "Dr. Rajesh Kumar",
      isbn: "978-81-203-4567-8",
      category: "Science",
      subject: "Physics",
      publisher: "Oxford University Press",
      edition: "5th Edition",
      publicationYear: 2023,
      language: "English",
      pages: 856,
      copies: 15,
      available: 12,
      issued: 3,
      reserved: 0,
      location: "Section A - Shelf 15",
      status: "Available",
      rating: 4.5,
      description: "Comprehensive guide covering all aspects of physics for higher secondary students",
      addedDate: "2023-08-15",
      lastIssued: "2024-01-20",
      price: 1250,
    },
    {
      id: "BK002",
      title: "Mathematics for Class XII",
      author: "Prof. Sunita Sharma",
      isbn: "978-81-203-4568-9",
      category: "Mathematics",
      subject: "Mathematics",
      publisher: "NCERT Publications",
      edition: "Latest Edition",
      publicationYear: 2023,
      language: "English",
      pages: 642,
      copies: 25,
      available: 18,
      issued: 7,
      reserved: 2,
      location: "Section B - Shelf 8",
      status: "Available",
      rating: 4.8,
      description: "Official NCERT textbook for Class XII Mathematics",
      addedDate: "2023-07-10",
      lastIssued: "2024-01-22",
      price: 890,
    },
    {
      id: "BK003",
      title: "Indian History and Culture",
      author: "Dr. Anil Mehta",
      isbn: "978-81-203-4569-0",
      category: "History",
      subject: "History",
      publisher: "Pearson Education",
      edition: "3rd Edition",
      publicationYear: 2022,
      language: "English",
      pages: 724,
      copies: 12,
      available: 8,
      issued: 4,
      reserved: 1,
      location: "Section C - Shelf 22",
      status: "Available",
      rating: 4.2,
      description: "Comprehensive study of Indian history from ancient to modern times",
      addedDate: "2023-06-20",
      lastIssued: "2024-01-18",
      price: 1150,
    },
    {
      id: "BK004",
      title: "English Literature Anthology",
      author: "Ms. Priya Gupta",
      isbn: "978-81-203-4570-6",
      category: "Literature",
      subject: "English",
      publisher: "Cambridge University Press",
      edition: "2nd Edition",
      publicationYear: 2023,
      language: "English",
      pages: 512,
      copies: 20,
      available: 15,
      issued: 5,
      reserved: 3,
      location: "Section D - Shelf 5",
      status: "Available",
      rating: 4.6,
      description: "Collection of classic and contemporary English literature pieces",
      addedDate: "2023-09-05",
      lastIssued: "2024-01-21",
      price: 950,
    },
    {
      id: "BK005",
      title: "Computer Science Fundamentals",
      author: "Er. Vikash Singh",
      isbn: "978-81-203-4571-7",
      category: "Technology",
      subject: "Computer Science",
      publisher: "McGraw Hill Education",
      edition: "4th Edition",
      publicationYear: 2023,
      language: "English",
      pages: 698,
      copies: 18,
      available: 0,
      issued: 18,
      reserved: 5,
      location: "Section E - Shelf 12",
      status: "Out of Stock",
      rating: 4.7,
      description: "Comprehensive guide to computer science concepts and programming",
      addedDate: "2023-08-30",
      lastIssued: "2024-01-23",
      price: 1350,
    },
    {
      id: "BK006",
      title: "Chemistry Lab Manual",
      author: "Dr. Kavita Joshi",
      isbn: "978-81-203-4572-8",
      category: "Science",
      subject: "Chemistry",
      publisher: "S. Chand Publishing",
      edition: "6th Edition",
      publicationYear: 2023,
      language: "English",
      pages: 324,
      copies: 10,
      available: 7,
      issued: 2,
      reserved: 1,
      location: "Section A - Shelf 18",
      status: "Available",
      rating: 4.3,
      description: "Practical chemistry experiments and procedures for students",
      addedDate: "2023-07-25",
      lastIssued: "2024-01-19",
      price: 750,
    },
  ];

  const categories = [
    { name: "Science", count: 2456, color: "blue" },
    { name: "Mathematics", count: 1890, color: "green" },
    { name: "Literature", count: 1654, color: "purple" },
    { name: "History", count: 1234, color: "orange" },
    { name: "Technology", count: 987, color: "red" },
    { name: "Arts", count: 756, color: "pink" },
    { name: "Languages", count: 543, color: "indigo" },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Available": return "text-green-600 bg-green-100";
      case "Out of Stock": return "text-red-600 bg-red-100";
      case "Limited": return "text-yellow-600 bg-yellow-100";
      case "Damaged": return "text-orange-600 bg-orange-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const getCategoryColor = (category: string) => {
    const categoryData = categories.find(cat => cat.name === category);
    const color = categoryData?.color || "gray";
    
    switch (color) {
      case "blue": return "text-blue-600 bg-blue-100";
      case "green": return "text-green-600 bg-green-100";
      case "purple": return "text-purple-600 bg-purple-100";
      case "orange": return "text-orange-600 bg-orange-100";
      case "red": return "text-red-600 bg-red-100";
      case "pink": return "text-pink-600 bg-pink-100";
      case "indigo": return "text-indigo-600 bg-indigo-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const getRatingStars = (rating: number) => {
    return Array.from({ length: 5 }, (_, i) => (
      <Star
        key={i}
        className={`h-4 w-4 ${i < Math.floor(rating) ? 'text-yellow-500 fill-current' : 'text-gray-300'}`}
      />
    ));
  };

  const getTabBooks = () => {
    let filtered = books;
    
    switch (selectedTab) {
      case "available":
        filtered = books.filter(book => book.status === "Available" && book.available > 0);
        break;
      case "issued":
        filtered = books.filter(book => book.issued > 0);
        break;
      case "outofstock":
        filtered = books.filter(book => book.available === 0);
        break;
      case "reserved":
        filtered = books.filter(book => book.reserved > 0);
        break;
      default:
        filtered = books;
    }

    return filtered.filter(book =>
      book.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      book.author.toLowerCase().includes(searchTerm.toLowerCase()) ||
      book.isbn.toLowerCase().includes(searchTerm.toLowerCase()) ||
      book.category.toLowerCase().includes(searchTerm.toLowerCase())
    );
  };

  const getAvailabilityPercentage = (available: number, total: number) => {
    return ((available / total) * 100).toFixed(1);
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Book Catalog Management</h1>
            <p className="text-gray-600">Manage library book inventory and catalog</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Upload className="h-4 w-4 mr-2" />
              Import Books
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Catalog
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Book
            </Button>
          </div>
        </div>

        {/* Book Stats */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <BookOpen className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {bookStats.totalBooks.toLocaleString()}
                  </div>
                  <p className="text-sm text-gray-600">Total Books</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {bookStats.availableBooks.toLocaleString()}
                  </div>
                  <p className="text-sm text-gray-600">Available</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <User className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    {bookStats.issuedBooks.toLocaleString()}
                  </div>
                  <p className="text-sm text-gray-600">Issued</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Bookmark className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {bookStats.reservedBooks}
                  </div>
                  <p className="text-sm text-gray-600">Reserved</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <AlertTriangle className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-yellow-600">
                    {bookStats.damagedBooks}
                  </div>
                  <p className="text-sm text-gray-600">Damaged</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-red-600">
                    {bookStats.lostBooks}
                  </div>
                  <p className="text-sm text-gray-600">Lost</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Books List */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
                  {[
                    { key: "all", label: "All Books" },
                    { key: "available", label: "Available" },
                    { key: "issued", label: "Issued" },
                    { key: "outofstock", label: "Out of Stock" },
                    { key: "reserved", label: "Reserved" },
                  ].map((tab) => (
                    <button
                      key={tab.key}
                      onClick={() => setSelectedTab(tab.key)}
                      className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                        selectedTab === tab.key
                          ? "bg-white text-blue-600 shadow-sm"
                          : "text-gray-600 hover:text-gray-900"
                      }`}
                    >
                      {tab.label}
                    </button>
                  ))}
                </div>
                <div className="flex gap-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search books..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 w-64"
                    />
                  </div>
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {getTabBooks().map((book) => (
                  <div
                    key={book.id}
                    onClick={() => setSelectedBook(book)}
                    className={`p-4 border rounded-lg cursor-pointer transition-colors hover:bg-gray-50 ${
                      selectedBook?.id === book.id ? 'bg-blue-50 border-blue-200' : ''
                    }`}
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-start space-x-4">
                        <div className="w-12 h-16 bg-blue-100 rounded flex items-center justify-center">
                          <BookOpen className="h-6 w-6 text-blue-600" />
                        </div>
                        <div className="flex-1">
                          <h4 className="font-semibold text-gray-900 mb-1">{book.title}</h4>
                          <p className="text-sm text-gray-600 mb-2">by {book.author}</p>
                          <div className="flex items-center space-x-3 mb-2">
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getCategoryColor(book.category)}`}>
                              {book.category}
                            </span>
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(book.status)}`}>
                              {book.status}
                            </span>
                          </div>
                          <div className="flex items-center space-x-2">
                            <div className="flex">
                              {getRatingStars(book.rating)}
                            </div>
                            <span className="text-sm text-gray-600">({book.rating})</span>
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-lg font-bold text-blue-600">₹{book.price}</div>
                        <div className="text-sm text-gray-500">{book.edition}</div>
                      </div>
                    </div>

                    <div className="grid gap-3 md:grid-cols-4 text-sm mb-3">
                      <div className="flex items-center text-gray-600">
                        <Hash className="h-3 w-3 mr-1" />
                        {book.isbn}
                      </div>
                      <div className="flex items-center text-gray-600">
                        <Library className="h-3 w-3 mr-1" />
                        {book.location}
                      </div>
                      <div className="flex items-center text-gray-600">
                        <Calendar className="h-3 w-3 mr-1" />
                        {book.publicationYear}
                      </div>
                      <div className="flex items-center text-gray-600">
                        <BookOpen className="h-3 w-3 mr-1" />
                        {book.pages} pages
                      </div>
                    </div>

                    <div className="flex items-center justify-between">
                      <div className="flex items-center space-x-4 text-sm">
                        <span className="text-green-600 font-medium">Available: {book.available}</span>
                        <span className="text-orange-600 font-medium">Issued: {book.issued}</span>
                        {book.reserved > 0 && (
                          <span className="text-purple-600 font-medium">Reserved: {book.reserved}</span>
                        )}
                      </div>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Eye className="h-3 w-3" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Edit className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>

                    <div className="mt-3">
                      <div className="flex items-center justify-between mb-1">
                        <span className="text-sm text-gray-600">Availability</span>
                        <span className="text-sm font-medium text-gray-900">
                          {getAvailabilityPercentage(book.available, book.copies)}%
                        </span>
                      </div>
                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className={`h-2 rounded-full ${
                            book.available === 0 ? 'bg-red-500' :
                            parseFloat(getAvailabilityPercentage(book.available, book.copies)) > 50 ? 'bg-green-500' :
                            'bg-yellow-500'
                          }`}
                          style={{ width: `${getAvailabilityPercentage(book.available, book.copies)}%` }}
                        />
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Book Details / Categories */}
          <div className="space-y-6">
            {selectedBook ? (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>Book Details</span>
                    <div className="flex gap-2">
                      <Button variant="outline" size="sm">
                        <Edit className="h-3 w-3" />
                      </Button>
                      <Button variant="outline" size="sm">
                        <Download className="h-3 w-3" />
                      </Button>
                    </div>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Basic Information */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Basic Information</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Title:</span>
                          <span className="font-medium">{selectedBook.title}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Author:</span>
                          <span className="font-medium">{selectedBook.author}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">ISBN:</span>
                          <span className="font-medium">{selectedBook.isbn}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Publisher:</span>
                          <span className="font-medium">{selectedBook.publisher}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Edition:</span>
                          <span className="font-medium">{selectedBook.edition}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Year:</span>
                          <span className="font-medium">{selectedBook.publicationYear}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Language:</span>
                          <span className="font-medium">{selectedBook.language}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Pages:</span>
                          <span className="font-medium">{selectedBook.pages}</span>
                        </div>
                      </div>
                    </div>

                    {/* Availability */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Availability</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Total Copies:</span>
                          <span className="font-medium">{selectedBook.copies}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Available:</span>
                          <span className="font-medium text-green-600">{selectedBook.available}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Issued:</span>
                          <span className="font-medium text-orange-600">{selectedBook.issued}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Reserved:</span>
                          <span className="font-medium text-purple-600">{selectedBook.reserved}</span>
                        </div>
                      </div>
                    </div>

                    {/* Location & Price */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Location & Price</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Location:</span>
                          <span className="font-medium">{selectedBook.location}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Price:</span>
                          <span className="font-medium">₹{selectedBook.price}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Added Date:</span>
                          <span className="font-medium">{selectedBook.addedDate}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Last Issued:</span>
                          <span className="font-medium">{selectedBook.lastIssued}</span>
                        </div>
                      </div>
                    </div>

                    {/* Description */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Description</h4>
                      <p className="text-sm text-gray-600 bg-gray-50 p-3 rounded-lg">
                        {selectedBook.description}
                      </p>
                    </div>

                    {/* Rating */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Rating</h4>
                      <div className="flex items-center space-x-2">
                        <div className="flex">
                          {getRatingStars(selectedBook.rating)}
                        </div>
                        <span className="font-medium">({selectedBook.rating})</span>
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="p-6 text-center">
                  <BookOpen className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Select a Book</h3>
                  <p className="text-gray-600">Choose a book from the list to view details</p>
                </CardContent>
              </Card>
            )}

            {/* Categories */}
            <Card>
              <CardHeader>
                <CardTitle>Book Categories</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {categories.map((category, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                      <div className="flex items-center">
                        <Tag className="h-4 w-4 text-gray-400 mr-2" />
                        <span className="font-medium text-gray-900">{category.name}</span>
                      </div>
                      <div className="text-right">
                        <div className="font-bold text-gray-900">{category.count.toLocaleString()}</div>
                        <div className="text-xs text-gray-500">books</div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
