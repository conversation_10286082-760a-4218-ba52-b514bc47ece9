{"timestamp": "2025-06-08T10:29:11.079Z", "summary": {"totalPages": 4, "pagesWithAdd": 4, "pagesWithDialog": 4, "pagesWithForms": 4, "fullyFunctional": 3, "overallScore": 75}, "pageDetails": [{"route": "/principal/academic", "status": "fully-functional", "score": 100, "features": ["<PERSON>d <PERSON>", "Dialog Component", "Form Elements", "State Management", "Submit <PERSON>"]}, {"route": "/principal/curriculum", "status": "fully-functional", "score": 100, "features": ["<PERSON>d <PERSON>", "Dialog Component", "Form Elements", "State Management", "Submit <PERSON>"]}, {"route": "/principal/calendar", "status": "fully-functional", "score": 100, "features": ["<PERSON>d <PERSON>", "Dialog Component", "Form Elements", "State Management", "Submit <PERSON>"]}, {"route": "/principal/performance", "status": "partially-functional", "score": 65, "features": ["Dialog Component", "Form Elements", "State Management"]}], "testInstructions": {"loginCredentials": {"email": "<EMAIL>", "password": "principal123"}, "testSteps": ["<PERSON><PERSON> as Principal", "Navigate to Academic Leadership pages", "Test Add functionality", "Verify form submission", "Check success messages"]}}