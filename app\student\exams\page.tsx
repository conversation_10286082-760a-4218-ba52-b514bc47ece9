"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Calendar,
  Clock,
  BookOpen,
  Award,
  FileText,
  Download,
  Eye,
  AlertTriangle,
  CheckCircle,
  Target,
  TrendingUp,
  BarChart3,
  MapPin,
  User,
} from "lucide-react";

export default function StudentExams() {
  const [user, setUser] = useState<any>(null);
  const [selectedTab, setSelectedTab] = useState("upcoming");
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "student") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock student data
  const studentInfo = {
    name: "John Doe",
    rollNo: "10A001",
    class: "Grade 10A",
    section: "A",
    currentGPA: 3.8,
  };

  const examStats = {
    totalExams: 12,
    completed: 8,
    upcoming: 4,
    averageScore: 85.2,
    bestSubject: "Mathematics",
    improvementNeeded: "Physics",
  };

  const upcomingExams = [
    {
      id: "1",
      subject: "Mathematics",
      type: "Mid-term Exam",
      date: "2024-01-25",
      time: "09:00 AM - 12:00 PM",
      duration: "3 hours",
      room: "Room 101",
      teacher: "Dr. Sarah Johnson",
      syllabus: "Chapters 1-5: Algebra, Quadratic Equations",
      maxMarks: 100,
      status: "scheduled",
    },
    {
      id: "2",
      subject: "Physics",
      type: "Unit Test",
      date: "2024-01-28",
      time: "10:00 AM - 11:30 AM",
      duration: "1.5 hours",
      room: "Physics Lab",
      teacher: "Prof. Michael Smith",
      syllabus: "Chapter 3: Motion and Force",
      maxMarks: 50,
      status: "scheduled",
    },
    {
      id: "3",
      subject: "Chemistry",
      type: "Practical Exam",
      date: "2024-02-02",
      time: "02:00 PM - 05:00 PM",
      duration: "3 hours",
      room: "Chemistry Lab",
      teacher: "Dr. Emily Brown",
      syllabus: "Practical experiments 1-8",
      maxMarks: 75,
      status: "scheduled",
    },
    {
      id: "4",
      subject: "English",
      type: "Final Exam",
      date: "2024-02-05",
      time: "09:00 AM - 12:00 PM",
      duration: "3 hours",
      room: "Room 205",
      teacher: "Ms. Lisa Wilson",
      syllabus: "Complete syllabus",
      maxMarks: 100,
      status: "scheduled",
    },
  ];

  const completedExams = [
    {
      id: "5",
      subject: "Mathematics",
      type: "Unit Test 1",
      date: "2024-01-15",
      score: 92,
      maxMarks: 100,
      grade: "A+",
      rank: 2,
      classAverage: 78.5,
      status: "completed",
    },
    {
      id: "6",
      subject: "Physics",
      type: "Quiz 1",
      date: "2024-01-12",
      score: 18,
      maxMarks: 20,
      grade: "A",
      rank: 5,
      classAverage: 16.2,
      status: "completed",
    },
    {
      id: "7",
      subject: "Chemistry",
      type: "Assignment Test",
      date: "2024-01-10",
      score: 38,
      maxMarks: 50,
      grade: "B+",
      rank: 8,
      classAverage: 35.8,
      status: "completed",
    },
    {
      id: "8",
      subject: "English",
      type: "Essay Test",
      date: "2024-01-08",
      score: 85,
      maxMarks: 100,
      grade: "A",
      rank: 3,
      classAverage: 76.3,
      status: "completed",
    },
  ];

  const examSchedule = [
    { date: "2024-01-25", exams: [{ subject: "Mathematics", time: "09:00 AM", type: "Mid-term" }] },
    { date: "2024-01-28", exams: [{ subject: "Physics", time: "10:00 AM", type: "Unit Test" }] },
    { date: "2024-02-02", exams: [{ subject: "Chemistry", time: "02:00 PM", type: "Practical" }] },
    { date: "2024-02-05", exams: [{ subject: "English", time: "09:00 AM", type: "Final" }] },
  ];

  const subjectPerformance = [
    { subject: "Mathematics", exams: 3, avgScore: 88.7, bestScore: 92, trend: "up" },
    { subject: "Physics", exams: 2, avgScore: 82.5, bestScore: 90, trend: "up" },
    { subject: "Chemistry", exams: 2, avgScore: 76.0, bestScore: 80, trend: "stable" },
    { subject: "English", exams: 1, avgScore: 85.0, bestScore: 85, trend: "stable" },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "scheduled": return "text-blue-600 bg-blue-100";
      case "completed": return "text-green-600 bg-green-100";
      case "missed": return "text-red-600 bg-red-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const getGradeColor = (grade: string) => {
    switch (grade) {
      case "A+": return "text-green-600 bg-green-100";
      case "A": return "text-blue-600 bg-blue-100";
      case "B+": return "text-yellow-600 bg-yellow-100";
      case "B": return "text-orange-600 bg-orange-100";
      case "C+": return "text-red-600 bg-red-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const getTrendIcon = (trend: string) => {
    switch (trend) {
      case "up": return <TrendingUp className="h-4 w-4 text-green-500" />;
      case "down": return <TrendingUp className="h-4 w-4 text-red-500 rotate-180" />;
      default: return <Target className="h-4 w-4 text-gray-500" />;
    }
  };

  const getDaysUntilExam = (examDate: string) => {
    const today = new Date();
    const exam = new Date(examDate);
    const diffTime = exam.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">My Exams</h1>
            <p className="text-gray-600">View exam schedules, results, and performance</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Download Schedule
            </Button>
            <Button variant="outline">
              <FileText className="h-4 w-4 mr-2" />
              Admit Card
            </Button>
          </div>
        </div>

        {/* Student Info & Stats */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <User className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-lg font-bold text-blue-600">
                    {studentInfo.rollNo}
                  </div>
                  <p className="text-sm text-gray-600">{studentInfo.class}</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Calendar className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {examStats.upcoming}
                  </div>
                  <p className="text-sm text-gray-600">Upcoming Exams</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Award className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {examStats.averageScore}%
                  </div>
                  <p className="text-sm text-gray-600">Average Score</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600">+5.2%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Target className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    {studentInfo.currentGPA}
                  </div>
                  <p className="text-sm text-gray-600">Current GPA</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Exam Tabs */}
        <Card>
          <CardHeader>
            <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
              {[
                { key: "upcoming", label: "Upcoming Exams" },
                { key: "completed", label: "Results" },
                { key: "schedule", label: "Schedule" },
                { key: "performance", label: "Performance" },
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setSelectedTab(tab.key)}
                  className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                    selectedTab === tab.key
                      ? "bg-white text-blue-600 shadow-sm"
                      : "text-gray-600 hover:text-gray-900"
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </div>
          </CardHeader>
          <CardContent>
            {/* Upcoming Exams */}
            {selectedTab === "upcoming" && (
              <div className="space-y-4">
                {upcomingExams.map((exam) => {
                  const daysLeft = getDaysUntilExam(exam.date);
                  return (
                    <div key={exam.id} className="p-4 border rounded-lg hover:bg-gray-50">
                      <div className="flex items-start justify-between mb-3">
                        <div className="flex-1">
                          <div className="flex items-center space-x-3 mb-2">
                            <h3 className="font-semibold text-gray-900">{exam.subject}</h3>
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(exam.status)}`}>
                              {exam.type}
                            </span>
                            {daysLeft <= 3 && (
                              <Badge className="bg-red-100 text-red-700">
                                <AlertTriangle className="h-3 w-3 mr-1" />
                                {daysLeft === 0 ? "Today" : `${daysLeft} days left`}
                              </Badge>
                            )}
                          </div>
                          <p className="text-sm text-gray-600 mb-2">{exam.syllabus}</p>
                        </div>
                        <div className="text-right">
                          <div className="text-lg font-bold text-blue-600">{exam.maxMarks}</div>
                          <div className="text-sm text-gray-500">Max Marks</div>
                        </div>
                      </div>

                      <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-4 text-sm">
                        <div className="flex items-center space-x-2">
                          <Calendar className="h-4 w-4 text-gray-400" />
                          <span>{exam.date}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Clock className="h-4 w-4 text-gray-400" />
                          <span>{exam.time}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <MapPin className="h-4 w-4 text-gray-400" />
                          <span>{exam.room}</span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <User className="h-4 w-4 text-gray-400" />
                          <span>{exam.teacher}</span>
                        </div>
                      </div>

                      <div className="flex items-center justify-between mt-3 pt-3 border-t">
                        <span className="text-sm text-gray-500">Duration: {exam.duration}</span>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm">
                            <Eye className="h-3 w-3 mr-1" />
                            View Details
                          </Button>
                          <Button variant="outline" size="sm">
                            <Download className="h-3 w-3 mr-1" />
                            Syllabus
                          </Button>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            )}

            {/* Completed Exams/Results */}
            {selectedTab === "completed" && (
              <div className="space-y-4">
                {completedExams.map((exam) => (
                  <div key={exam.id} className="p-4 border rounded-lg">
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="font-semibold text-gray-900">{exam.subject}</h3>
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(exam.status)}`}>
                            {exam.type}
                          </span>
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getGradeColor(exam.grade)}`}>
                            {exam.grade}
                          </span>
                        </div>
                        <p className="text-sm text-gray-600">{exam.date}</p>
                      </div>
                      <div className="text-right">
                        <div className="text-2xl font-bold text-blue-600">
                          {exam.score}/{exam.maxMarks}
                        </div>
                        <div className="text-sm text-gray-500">
                          {((exam.score / exam.maxMarks) * 100).toFixed(1)}%
                        </div>
                      </div>
                    </div>

                    <div className="grid gap-3 md:grid-cols-3 text-sm">
                      <div className="text-center p-3 bg-blue-50 rounded-lg">
                        <div className="font-bold text-blue-600">Rank {exam.rank}</div>
                        <div className="text-gray-500">Class Rank</div>
                      </div>
                      <div className="text-center p-3 bg-green-50 rounded-lg">
                        <div className="font-bold text-green-600">{exam.classAverage}</div>
                        <div className="text-gray-500">Class Average</div>
                      </div>
                      <div className="text-center p-3 bg-purple-50 rounded-lg">
                        <div className="font-bold text-purple-600">
                          {exam.score > exam.classAverage ? "+" : ""}{(exam.score - exam.classAverage).toFixed(1)}
                        </div>
                        <div className="text-gray-500">Above Average</div>
                      </div>
                    </div>

                    <div className="flex items-center justify-between mt-3 pt-3 border-t">
                      <div className="w-full bg-gray-200 rounded-full h-2 mr-4">
                        <div 
                          className="bg-blue-500 h-2 rounded-full"
                          style={{ width: `${(exam.score / exam.maxMarks) * 100}%` }}
                        />
                      </div>
                      <Button variant="outline" size="sm">
                        <FileText className="h-3 w-3 mr-1" />
                        View Paper
                      </Button>
                    </div>
                  </div>
                ))}
              </div>
            )}

            {/* Schedule */}
            {selectedTab === "schedule" && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Exam Calendar</h3>
                <div className="grid gap-4">
                  {examSchedule.map((day, index) => (
                    <div key={index} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium text-gray-900">{day.date}</h4>
                        <span className="text-sm text-gray-500">
                          {new Date(day.date).toLocaleDateString('en-US', { weekday: 'long' })}
                        </span>
                      </div>
                      <div className="space-y-2">
                        {day.exams.map((exam, examIndex) => (
                          <div key={examIndex} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                            <div className="flex items-center space-x-3">
                              <BookOpen className="h-4 w-4 text-blue-500" />
                              <div>
                                <div className="font-medium text-gray-900">{exam.subject}</div>
                                <div className="text-sm text-gray-600">{exam.type}</div>
                              </div>
                            </div>
                            <div className="text-right">
                              <div className="font-medium text-gray-900">{exam.time}</div>
                              <div className="text-sm text-gray-500">
                                {getDaysUntilExam(day.date) === 0 ? "Today" : 
                                 getDaysUntilExam(day.date) === 1 ? "Tomorrow" :
                                 `${getDaysUntilExam(day.date)} days`}
                              </div>
                            </div>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Performance */}
            {selectedTab === "performance" && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900">Subject-wise Performance</h3>
                <div className="grid gap-4 md:grid-cols-2">
                  {subjectPerformance.map((subject, index) => (
                    <div key={index} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium text-gray-900">{subject.subject}</h4>
                        {getTrendIcon(subject.trend)}
                      </div>
                      
                      <div className="grid gap-3 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Exams Taken:</span>
                          <span className="font-medium">{subject.exams}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Average Score:</span>
                          <span className="font-medium text-blue-600">{subject.avgScore}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Best Score:</span>
                          <span className="font-medium text-green-600">{subject.bestScore}%</span>
                        </div>
                      </div>

                      <div className="w-full bg-gray-200 rounded-full h-2 mt-3">
                        <div 
                          className={`h-2 rounded-full ${
                            subject.avgScore >= 90 ? 'bg-green-500' :
                            subject.avgScore >= 80 ? 'bg-blue-500' :
                            subject.avgScore >= 70 ? 'bg-yellow-500' : 'bg-red-500'
                          }`}
                          style={{ width: `${subject.avgScore}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </div>

                <div className="p-4 bg-blue-50 rounded-lg">
                  <h4 className="font-medium text-blue-900 mb-2">Performance Insights</h4>
                  <ul className="text-sm text-blue-700 space-y-1">
                    <li>• Your strongest subject is {examStats.bestSubject} with consistent high scores</li>
                    <li>• Consider focusing more on {examStats.improvementNeeded} to improve overall performance</li>
                    <li>• Your overall trend is positive with a 5.2% improvement this semester</li>
                  </ul>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
