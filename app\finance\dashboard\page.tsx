"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  IndianRupee,
  TrendingUp,
  TrendingDown,
  Users,
  CreditCard,
  AlertTriangle,
  CheckCircle,
  Calendar,
  FileText,
  PieChart,
  Calculator,
  Receipt,
  Banknote,
  Wallet,
} from "lucide-react";

export default function FinanceDashboard() {
  const [user, setUser] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "finance_manager") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock financial data
  const financialStats = {
    totalRevenue: 2850000,
    monthlyRevenue: 485000,
    pendingFees: 125000,
    collectionRate: 94.5,
    totalStudents: 1234,
    paidStudents: 1167,
    pendingStudents: 67,
    overdueAmount: 45000,
  };

  const recentTransactions = [
    { id: "TXN001", student: "John Doe", amount: 25000, type: "Fee Payment", status: "Completed", date: "2024-01-15" },
    { id: "TXN002", student: "Alice Smith", amount: 30000, type: "Admission Fee", status: "Completed", date: "2024-01-15" },
    { id: "TXN003", student: "Bob Johnson", amount: 25000, type: "Tuition Fee", status: "Pending", date: "2024-01-14" },
    { id: "TXN004", student: "Sarah Wilson", amount: 5000, type: "Library Fine", status: "Completed", date: "2024-01-14" },
    { id: "TXN005", student: "Mike Brown", amount: 25000, type: "Fee Payment", status: "Failed", date: "2024-01-13" },
  ];

  const monthlyData = [
    { month: "Aug", revenue: 420000, expenses: 180000 },
    { month: "Sep", revenue: 465000, expenses: 195000 },
    { month: "Oct", revenue: 510000, expenses: 210000 },
    { month: "Nov", revenue: 485000, expenses: 205000 },
    { month: "Dec", revenue: 520000, expenses: 220000 },
    { month: "Jan", revenue: 485000, expenses: 200000 },
  ];

  const quickActions = [
    { title: "Generate Invoice", icon: Receipt, color: "bg-blue-500", href: "/finance/invoices/new" },
    { title: "Fee Collection", icon: IndianRupee, color: "bg-green-500", href: "/finance/collections" },
    { title: "Payment Reports", icon: FileText, color: "bg-purple-500", href: "/finance/reports" },
    { title: "Expense Management", icon: Calculator, color: "bg-orange-500", href: "/finance/expenses" },
  ];

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Welcome Header */}
        <div className="bg-gradient-to-r from-yellow-500 to-orange-600 rounded-lg p-6 text-white">
          <h1 className="text-2xl font-bold mb-2">
            Finance Dashboard
          </h1>
          <p className="text-yellow-100">
            Welcome back, {user.firstName}! Manage school finances efficiently.
          </p>
        </div>

        {/* Financial Overview Stats */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <IndianRupee className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    ₹{(financialStats.totalRevenue / 100000).toFixed(1)}L
                  </div>
                  <p className="text-sm text-gray-600">Total Revenue</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <TrendingUp className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    ₹{(financialStats.monthlyRevenue / 1000).toFixed(0)}K
                  </div>
                  <p className="text-sm text-gray-600">This Month</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <AlertTriangle className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    ₹{(financialStats.pendingFees / 1000).toFixed(0)}K
                  </div>
                  <p className="text-sm text-gray-600">Pending Fees</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {financialStats.collectionRate}%
                  </div>
                  <p className="text-sm text-gray-600">Collection Rate</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-4">
              {quickActions.map((action, index) => (
                <Button
                  key={index}
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center space-y-2"
                  onClick={() => router.push(action.href)}
                >
                  <div className={`w-12 h-12 ${action.color} rounded-lg flex items-center justify-center`}>
                    <action.icon className="h-6 w-6 text-white" />
                  </div>
                  <span className="font-medium">{action.title}</span>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Main Content Grid */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Recent Transactions */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center">
                <CreditCard className="h-5 w-5 mr-2" />
                Recent Transactions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentTransactions.map((transaction) => (
                  <div key={transaction.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                        <Wallet className="h-5 w-5 text-blue-600" />
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">{transaction.student}</div>
                        <div className="text-sm text-gray-500">
                          {transaction.type} • {transaction.date}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-gray-900">₹{transaction.amount.toLocaleString()}</div>
                      <div className={`text-sm ${
                        transaction.status === 'Completed' ? 'text-green-600' :
                        transaction.status === 'Pending' ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        {transaction.status}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Payment Summary */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <PieChart className="h-5 w-5 mr-2" />
                Payment Summary
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Total Students</span>
                  <span className="font-medium">{financialStats.totalStudents}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Paid Students</span>
                  <span className="font-medium text-green-600">{financialStats.paidStudents}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Pending Students</span>
                  <span className="font-medium text-orange-600">{financialStats.pendingStudents}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Overdue Amount</span>
                  <span className="font-medium text-red-600">₹{financialStats.overdueAmount.toLocaleString()}</span>
                </div>

                <div className="pt-4">
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div
                      className="bg-green-600 h-3 rounded-full"
                      style={{ width: `${(financialStats.paidStudents / financialStats.totalStudents) * 100}%` }}
                    />
                  </div>
                  <div className="text-center text-sm text-gray-600 mt-2">
                    {((financialStats.paidStudents / financialStats.totalStudents) * 100).toFixed(1)}% Collection Rate
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Monthly Revenue Chart */}
        <Card>
          <CardHeader>
            <CardTitle>Monthly Revenue vs Expenses</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 md:grid-cols-6">
              {monthlyData.map((month, index) => (
                <div key={index} className="text-center">
                  <div className="text-sm font-medium text-gray-900 mb-2">{month.month}</div>
                  <div className="space-y-2">
                    <div className="w-full bg-gray-200 rounded h-20 flex flex-col justify-end">
                      <div
                        className="bg-green-500 rounded-b"
                        style={{ height: `${(month.revenue / 600000) * 100}%` }}
                      />
                    </div>
                    <div className="text-xs text-green-600">₹{(month.revenue / 1000).toFixed(0)}K</div>
                    <div className="w-full bg-gray-200 rounded h-16 flex flex-col justify-end">
                      <div
                        className="bg-red-500 rounded-b"
                        style={{ height: `${(month.expenses / 250000) * 100}%` }}
                      />
                    </div>
                    <div className="text-xs text-red-600">₹{(month.expenses / 1000).toFixed(0)}K</div>
                  </div>
                </div>
              ))}
            </div>
            <div className="flex justify-center space-x-6 mt-4">
              <div className="flex items-center">
                <div className="w-3 h-3 bg-green-500 rounded mr-2" />
                <span className="text-sm text-gray-600">Revenue</span>
              </div>
              <div className="flex items-center">
                <div className="w-3 h-3 bg-red-500 rounded mr-2" />
                <span className="text-sm text-gray-600">Expenses</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
