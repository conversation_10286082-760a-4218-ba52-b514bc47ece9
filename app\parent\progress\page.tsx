"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  TrendingUp,
  TrendingDown,
  Award,
  BookOpen,
  Calendar,
  Clock,
  Target,
  BarChart3,
  CheckCircle,
  AlertTriangle,
  Star,
  User,
  Download,
  MessageCircle,
  Eye,
} from "lucide-react";

export default function ParentProgress() {
  const [user, setUser] = useState<any>(null);
  const [selectedPeriod, setSelectedPeriod] = useState("current");
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "parent") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock child data
  const childData = {
    name: "John Doe",
    grade: "10A",
    rollNumber: "STU001",
    photo: "/api/placeholder/100/100",
    currentGPA: 3.8,
    previousGPA: 3.6,
    attendance: 94.5,
    rank: 15,
    totalStudents: 120,
  };

  const subjectProgress = [
    {
      subject: "Mathematics",
      currentGrade: "A-",
      currentScore: 88,
      previousScore: 82,
      trend: "up",
      teacher: "Dr. Emily Wilson",
      lastAssignment: "Calculus Test",
      lastScore: 92,
      assignments: 8,
      averageScore: 88.5,
    },
    {
      subject: "Physics",
      currentGrade: "B+",
      currentScore: 85,
      previousScore: 87,
      trend: "down",
      teacher: "Mr. David Brown",
      lastAssignment: "Lab Report",
      lastScore: 78,
      assignments: 6,
      averageScore: 85.2,
    },
    {
      subject: "Chemistry",
      currentGrade: "A",
      currentScore: 92,
      previousScore: 89,
      trend: "up",
      teacher: "Dr. Sarah Johnson",
      lastAssignment: "Organic Chemistry Quiz",
      lastScore: 95,
      assignments: 7,
      averageScore: 91.8,
    },
    {
      subject: "English",
      currentGrade: "A-",
      currentScore: 87,
      previousScore: 85,
      trend: "up",
      teacher: "Ms. Lisa Anderson",
      lastAssignment: "Essay on Literature",
      lastScore: 89,
      assignments: 5,
      averageScore: 87.4,
    },
    {
      subject: "Computer Science",
      currentGrade: "A",
      currentScore: 94,
      previousScore: 91,
      trend: "up",
      teacher: "Prof. Michael Chen",
      lastAssignment: "Programming Project",
      lastScore: 96,
      assignments: 4,
      averageScore: 93.5,
    },
  ];

  const recentActivities = [
    {
      type: "grade",
      subject: "Computer Science",
      activity: "Programming Project graded",
      score: 96,
      date: "2024-01-20",
      teacher: "Prof. Michael Chen",
    },
    {
      type: "assignment",
      subject: "Chemistry",
      activity: "Organic Chemistry Quiz submitted",
      score: 95,
      date: "2024-01-19",
      teacher: "Dr. Sarah Johnson",
    },
    {
      type: "attendance",
      subject: "Mathematics",
      activity: "Attended class",
      date: "2024-01-18",
      teacher: "Dr. Emily Wilson",
    },
    {
      type: "announcement",
      subject: "Physics",
      activity: "Lab schedule updated",
      date: "2024-01-17",
      teacher: "Mr. David Brown",
    },
  ];

  const monthlyProgress = [
    { month: "Sep", gpa: 3.4, attendance: 92 },
    { month: "Oct", gpa: 3.5, attendance: 94 },
    { month: "Nov", gpa: 3.6, attendance: 93 },
    { month: "Dec", gpa: 3.7, attendance: 95 },
    { month: "Jan", gpa: 3.8, attendance: 94.5 },
  ];

  const strengths = [
    { area: "Problem Solving", score: 95, description: "Excellent analytical skills" },
    { area: "Programming", score: 94, description: "Strong coding abilities" },
    { area: "Mathematics", score: 88, description: "Good mathematical foundation" },
  ];

  const areasForImprovement = [
    { area: "Physics Lab Work", score: 78, description: "Needs more practice with experiments" },
    { area: "Time Management", score: 82, description: "Could improve assignment submission timing" },
  ];

  const getGradeColor = (grade: string) => {
    switch (grade) {
      case "A": return "text-green-600 bg-green-100";
      case "A-": return "text-green-600 bg-green-100";
      case "B+": return "text-blue-600 bg-blue-100";
      case "B": return "text-yellow-600 bg-yellow-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const getTrendIcon = (trend: string) => {
    return trend === "up" ? (
      <TrendingUp className="h-4 w-4 text-green-600" />
    ) : (
      <TrendingDown className="h-4 w-4 text-red-600" />
    );
  };

  const getActivityIcon = (type: string) => {
    switch (type) {
      case "grade": return <Award className="h-4 w-4 text-yellow-600" />;
      case "assignment": return <BookOpen className="h-4 w-4 text-blue-600" />;
      case "attendance": return <CheckCircle className="h-4 w-4 text-green-600" />;
      case "announcement": return <MessageCircle className="h-4 w-4 text-purple-600" />;
      default: return <Star className="h-4 w-4 text-gray-600" />;
    }
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Child&apos;s Progress</h1>
            <p className="text-gray-600">Track {childData.name}&apos;s academic journey</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <MessageCircle className="h-4 w-4 mr-2" />
              Contact Teachers
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Download Report
            </Button>
          </div>
        </div>

        {/* Child Overview */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-6">
              <div className="w-20 h-20 bg-blue-500 rounded-full flex items-center justify-center">
                <User className="h-10 w-10 text-white" />
              </div>
              <div className="flex-1">
                <h2 className="text-xl font-bold text-gray-900">{childData.name}</h2>
                <p className="text-gray-600">Grade {childData.grade} • Roll No: {childData.rollNumber}</p>
                <div className="flex items-center space-x-6 mt-3">
                  <div className="flex items-center space-x-2">
                    <Award className="h-5 w-5 text-yellow-600" />
                    <span className="font-medium">GPA: {childData.currentGPA}</span>
                    {childData.currentGPA > childData.previousGPA && (
                      <TrendingUp className="h-4 w-4 text-green-600" />
                    )}
                  </div>
                  <div className="flex items-center space-x-2">
                    <CheckCircle className="h-5 w-5 text-green-600" />
                    <span className="font-medium">Attendance: {childData.attendance}%</span>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Target className="h-5 w-5 text-blue-600" />
                    <span className="font-medium">Rank: #{childData.rank} of {childData.totalStudents}</span>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Subject Progress */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BookOpen className="h-5 w-5 mr-2" />
              Subject-wise Progress
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {subjectProgress.map((subject, index) => (
                <div key={index} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center space-x-4">
                      <h3 className="font-semibold text-gray-900">{subject.subject}</h3>
                      <Badge className={`${getGradeColor(subject.currentGrade)} border-0`}>
                        {subject.currentGrade}
                      </Badge>
                      {getTrendIcon(subject.trend)}
                    </div>
                    <div className="text-right">
                      <div className="font-bold text-gray-900">{subject.currentScore}%</div>
                      <div className="text-sm text-gray-500">
                        {subject.trend === "up" ? "+" : ""}{subject.currentScore - subject.previousScore}% from last term
                      </div>
                    </div>
                  </div>

                  <div className="grid gap-4 md:grid-cols-3 text-sm">
                    <div>
                      <div className="text-gray-600">Teacher</div>
                      <div className="font-medium">{subject.teacher}</div>
                    </div>
                    <div>
                      <div className="text-gray-600">Last Assignment</div>
                      <div className="font-medium">{subject.lastAssignment}</div>
                      <div className="text-sm text-gray-500">Score: {subject.lastScore}%</div>
                    </div>
                    <div>
                      <div className="text-gray-600">Average Score</div>
                      <div className="font-medium">{subject.averageScore}%</div>
                      <div className="text-sm text-gray-500">{subject.assignments} assignments</div>
                    </div>
                  </div>

                  <div className="mt-3">
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full"
                        style={{ width: `${subject.currentScore}%` }}
                      />
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Progress Charts and Activities */}
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Monthly Progress */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="h-5 w-5 mr-2" />
                Monthly Progress
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid gap-4 md:grid-cols-5">
                  {monthlyProgress.map((month, index) => (
                    <div key={index} className="text-center">
                      <div className="text-sm font-medium text-gray-900 mb-2">{month.month}</div>
                      <div className="space-y-2">
                        <div className="w-full bg-gray-200 rounded h-16 flex flex-col justify-end">
                          <div
                            className="bg-blue-500 rounded-b"
                            style={{ height: `${(month.gpa / 4) * 100}%` }}
                          />
                        </div>
                        <div className="text-xs text-blue-600">GPA: {month.gpa}</div>
                        <div className="w-full bg-gray-200 rounded h-12 flex flex-col justify-end">
                          <div
                            className="bg-green-500 rounded-b"
                            style={{ height: `${month.attendance}%` }}
                          />
                        </div>
                        <div className="text-xs text-green-600">{month.attendance}%</div>
                      </div>
                    </div>
                  ))}
                </div>
                <div className="flex justify-center space-x-6 mt-4">
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-blue-500 rounded mr-2" />
                    <span className="text-sm text-gray-600">GPA</span>
                  </div>
                  <div className="flex items-center">
                    <div className="w-3 h-3 bg-green-500 rounded mr-2" />
                    <span className="text-sm text-gray-600">Attendance</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Recent Activities */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Clock className="h-5 w-5 mr-2" />
                Recent Activities
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentActivities.map((activity, index) => (
                  <div key={index} className="flex items-start space-x-4 p-3 bg-gray-50 rounded-lg">
                    <div className="flex-shrink-0 mt-1">
                      {getActivityIcon(activity.type)}
                    </div>
                    <div className="flex-1">
                      <div className="font-medium text-gray-900">{activity.activity}</div>
                      <div className="text-sm text-gray-600">{activity.subject}</div>
                      <div className="text-sm text-gray-500">{activity.teacher} • {activity.date}</div>
                      {activity.score && (
                        <div className="text-sm font-medium text-green-600 mt-1">
                          Score: {activity.score}%
                        </div>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Strengths and Areas for Improvement */}
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Strengths */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Star className="h-5 w-5 mr-2 text-yellow-600" />
                Strengths
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {strengths.map((strength, index) => (
                  <div key={index} className="p-3 bg-green-50 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-green-900">{strength.area}</h4>
                      <span className="text-sm font-bold text-green-700">{strength.score}%</span>
                    </div>
                    <p className="text-sm text-green-700">{strength.description}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Areas for Improvement */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertTriangle className="h-5 w-5 mr-2 text-orange-600" />
                Areas for Improvement
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {areasForImprovement.map((area, index) => (
                  <div key={index} className="p-3 bg-orange-50 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <h4 className="font-medium text-orange-900">{area.area}</h4>
                      <span className="text-sm font-bold text-orange-700">{area.score}%</span>
                    </div>
                    <p className="text-sm text-orange-700">{area.description}</p>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  );
}
