"use client";

import { useState } from "react";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  Calendar, 
  Search, 
  Plus, 
  Filter,
  Clock,
  MapPin,
  User,
  CheckCircle,
  AlertTriangle,
  PlayCircle,
  XCircle,
  FileText
} from "lucide-react";

// Mock exam data
const mockExams = [
  {
    id: "1",
    name: "Mid-term Examination",
    type: "midterm",
    grade: "10",
    subject: "Mathematics",
    date: "2024-02-15",
    startTime: "09:00",
    endTime: "12:00",
    totalMarks: 100,
    passingMarks: 35,
    room: "Exam Hall A",
    invigilator: "Dr. <PERSON>",
    status: "scheduled",
    studentsEnrolled: 45,
  },
  {
    id: "2",
    name: "Physics Practical",
    type: "practical",
    grade: "11",
    subject: "Physics",
    date: "2024-02-20",
    startTime: "10:00",
    endTime: "13:00",
    totalMarks: 50,
    passingMarks: 18,
    room: "Physics Lab",
    invigilator: "Mr. <PERSON>",
    status: "scheduled",
    studentsEnrolled: 38,
  },
  {
    id: "3",
    name: "Chemistry Unit Test",
    type: "unit_test",
    grade: "10",
    subject: "Chemistry",
    date: "2024-01-25",
    startTime: "14:00",
    endTime: "15:30",
    totalMarks: 50,
    passingMarks: 18,
    room: "Room 301",
    invigilator: "Dr. Sarah Johnson",
    status: "completed",
    studentsEnrolled: 42,
  },
];

const statusColors = {
  scheduled: "text-blue-600 bg-blue-100",
  ongoing: "text-green-600 bg-green-100",
  completed: "text-gray-600 bg-gray-100",
  cancelled: "text-red-600 bg-red-100",
};

const statusIcons = {
  scheduled: Calendar,
  ongoing: PlayCircle,
  completed: CheckCircle,
  cancelled: XCircle,
};

const examTypeColors = {
  midterm: "bg-blue-500",
  final: "bg-purple-500",
  unit_test: "bg-green-500",
  practical: "bg-orange-500",
  oral: "bg-pink-500",
};

export default function ExamsPage() {
  const [search, setSearch] = useState("");
  const [selectedGrade, setSelectedGrade] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("");

  // Calculate exam statistics
  const totalExams = mockExams.length;
  const scheduledExams = mockExams.filter(e => e.status === "scheduled").length;
  const ongoingExams = mockExams.filter(e => e.status === "ongoing").length;
  const completedExams = mockExams.filter(e => e.status === "completed").length;

  // Get upcoming exams (next 7 days)
  const today = new Date();
  const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
  const upcomingExams = mockExams.filter(exam => {
    const examDate = new Date(exam.date);
    return examDate >= today && examDate <= nextWeek && exam.status === "scheduled";
  });

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Examination Management</h1>
            <p className="text-gray-600">Schedule and manage examinations</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <FileText className="h-4 w-4 mr-2" />
              Reports
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Schedule Exam
            </Button>
          </div>
        </div>

        {/* Exam Stats */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Calendar className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {totalExams}
                  </div>
                  <p className="text-sm text-gray-600">Total Exams</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    {scheduledExams}
                  </div>
                  <p className="text-sm text-gray-600">Scheduled</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <PlayCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {ongoingExams}
                  </div>
                  <p className="text-sm text-gray-600">Ongoing</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {completedExams}
                  </div>
                  <p className="text-sm text-gray-600">Completed</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Upcoming Exams Alert */}
        {upcomingExams.length > 0 && (
          <Card className="border-orange-200 bg-orange-50">
            <CardContent className="p-4">
              <div className="flex items-center">
                <AlertTriangle className="h-5 w-5 text-orange-600 mr-3" />
                <div>
                  <h3 className="font-medium text-orange-800">
                    {upcomingExams.length} exam(s) scheduled in the next 7 days
                  </h3>
                  <p className="text-sm text-orange-600">
                    Review exam schedules and ensure all preparations are complete.
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Search and Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search exams..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex gap-2">
                <select 
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm bg-white"
                  value={selectedGrade}
                  onChange={(e) => setSelectedGrade(e.target.value)}
                >
                  <option value="">All Grades</option>
                  <option value="10">Grade 10</option>
                  <option value="11">Grade 11</option>
                  <option value="12">Grade 12</option>
                </select>
                <select 
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm bg-white"
                  value={selectedStatus}
                  onChange={(e) => setSelectedStatus(e.target.value)}
                >
                  <option value="">All Status</option>
                  <option value="scheduled">Scheduled</option>
                  <option value="ongoing">Ongoing</option>
                  <option value="completed">Completed</option>
                  <option value="cancelled">Cancelled</option>
                </select>
                <Button variant="outline" size="sm">
                  <Filter className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Exams List */}
        <Card>
          <CardHeader>
            <CardTitle>Examination Schedule</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {mockExams.map((exam) => {
                const StatusIcon = statusIcons[exam.status as keyof typeof statusIcons];
                
                return (
                  <div
                    key={exam.id}
                    className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center space-x-4">
                      <div className={`w-12 h-12 ${examTypeColors[exam.type as keyof typeof examTypeColors]} rounded-lg flex items-center justify-center`}>
                        <FileText className="h-6 w-6 text-white" />
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">
                          {exam.name}
                        </div>
                        <div className="text-sm text-gray-500">
                          Grade {exam.grade} • {exam.subject} • {exam.type.replace('_', ' ').toUpperCase()}
                        </div>
                        <div className="text-xs text-gray-400 flex items-center mt-1">
                          <Calendar className="h-3 w-3 mr-1" />
                          {exam.date} • 
                          <Clock className="h-3 w-3 ml-2 mr-1" />
                          {exam.startTime} - {exam.endTime} • 
                          <MapPin className="h-3 w-3 ml-2 mr-1" />
                          {exam.room}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center space-x-4">
                        <div className="text-sm">
                          <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusColors[exam.status as keyof typeof statusColors]}`}>
                            <StatusIcon className="h-3 w-3 mr-1" />
                            {exam.status.charAt(0).toUpperCase() + exam.status.slice(1)}
                          </div>
                          <div className="text-gray-500 mt-1">
                            {exam.studentsEnrolled} students • {exam.totalMarks} marks
                          </div>
                          <div className="text-xs text-gray-400">
                            Invigilator: {exam.invigilator}
                          </div>
                        </div>
                        <div className="flex gap-2">
                          {exam.status === "scheduled" && (
                            <>
                              <Button variant="outline" size="sm" className="text-green-600">
                                Start
                              </Button>
                              <Button variant="outline" size="sm" className="text-red-600">
                                Cancel
                              </Button>
                            </>
                          )}
                          {exam.status === "ongoing" && (
                            <Button variant="outline" size="sm" className="text-blue-600">
                              Complete
                            </Button>
                          )}
                          <Button variant="outline" size="sm">
                            View
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Exam Analytics */}
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Exam Types Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Mid-term Exams</span>
                  <span className="font-medium">33%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-600 h-2 rounded-full w-[33%]" />
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm">Practical Exams</span>
                  <span className="font-medium">33%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-orange-600 h-2 rounded-full w-[33%]" />
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm">Unit Tests</span>
                  <span className="font-medium">34%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-green-600 h-2 rounded-full w-[34%]" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Upcoming Schedule</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {upcomingExams.length > 0 ? (
                  upcomingExams.map((exam) => (
                    <div key={exam.id} className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                      <div>
                        <div className="font-medium text-blue-900">{exam.name}</div>
                        <div className="text-sm text-blue-600">
                          {exam.date} • {exam.startTime} • {exam.room}
                        </div>
                      </div>
                      <div className="text-sm font-medium text-blue-600">
                        Grade {exam.grade}
                      </div>
                    </div>
                  ))
                ) : (
                  <div className="text-center py-8 text-gray-500">
                    <Calendar className="h-12 w-12 mx-auto mb-4 text-gray-300" />
                    <p>No exams scheduled in the next 7 days</p>
                  </div>
                )}
                
                <div className="pt-4 border-t">
                  <Button variant="outline" className="w-full">
                    View Full Calendar
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  );
}
