import { Hono } from "hono";
import { zValidator } from "@hono/zod-validator";
import { mockVehicles, mockTransportRoutes } from "@/lib/mock-db";
import { vehicleSchema, transportRouteSchema } from "@/lib/schemas";
import { generateId } from "@/lib/utils";

const app = new Hono()
  // Routes
  .get("/routes", async (c) => {
    const page = parseInt(c.req.query("page") || "1");
    const limit = parseInt(c.req.query("limit") || "10");
    const search = c.req.query("search");
    const status = c.req.query("status");

    let filteredRoutes = [...mockTransportRoutes];

    if (search) {
      const searchLower = search.toLowerCase();
      filteredRoutes = filteredRoutes.filter(
        (route) =>
          route.routeName.toLowerCase().includes(searchLower) ||
          route.startPoint.toLowerCase().includes(searchLower) ||
          route.endPoint.toLowerCase().includes(searchLower)
      );
    }

    if (status) {
      filteredRoutes = filteredRoutes.filter((route) => route.status === status);
    }

    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedRoutes = filteredRoutes.slice(startIndex, endIndex);

    return c.json({
      data: paginatedRoutes,
      pagination: {
        page,
        limit,
        total: filteredRoutes.length,
        totalPages: Math.ceil(filteredRoutes.length / limit),
      },
    });
  })
  .post(
    "/routes",
    zValidator("json", transportRouteSchema.omit({ id: true, createdAt: true, updatedAt: true })),
    async (c) => {
      const values = c.req.valid("json");
      
      const newRoute = {
        ...values,
        id: generateId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      mockTransportRoutes.push(newRoute);

      return c.json({ data: newRoute }, 201);
    }
  )
  .get("/routes/:id", async (c) => {
    const id = c.req.param("id");
    const route = mockTransportRoutes.find((r) => r.id === id);

    if (!route) {
      return c.json({ error: "Route not found" }, 404);
    }

    return c.json({ data: route });
  })
  .put(
    "/routes/:id",
    zValidator("json", transportRouteSchema.omit({ id: true, createdAt: true, updatedAt: true })),
    async (c) => {
      const id = c.req.param("id");
      const values = c.req.valid("json");

      const routeIndex = mockTransportRoutes.findIndex((r) => r.id === id);

      if (routeIndex === -1) {
        return c.json({ error: "Route not found" }, 404);
      }

      mockTransportRoutes[routeIndex] = {
        ...mockTransportRoutes[routeIndex],
        ...values,
        updatedAt: new Date().toISOString(),
      };

      return c.json({ data: mockTransportRoutes[routeIndex] });
    }
  )
  .delete("/routes/:id", async (c) => {
    const id = c.req.param("id");
    const routeIndex = mockTransportRoutes.findIndex((r) => r.id === id);

    if (routeIndex === -1) {
      return c.json({ error: "Route not found" }, 404);
    }

    mockTransportRoutes.splice(routeIndex, 1);

    return c.json({ message: "Route deleted successfully" });
  })

  // Vehicles
  .get("/vehicles", async (c) => {
    const page = parseInt(c.req.query("page") || "1");
    const limit = parseInt(c.req.query("limit") || "10");
    const search = c.req.query("search");
    const vehicleType = c.req.query("vehicleType");
    const status = c.req.query("status");
    const routeId = c.req.query("routeId");

    let filteredVehicles = [...mockVehicles];

    if (search) {
      const searchLower = search.toLowerCase();
      filteredVehicles = filteredVehicles.filter(
        (vehicle) =>
          vehicle.vehicleNumber.toLowerCase().includes(searchLower) ||
          vehicle.driverName.toLowerCase().includes(searchLower) ||
          vehicle.driverPhone.includes(search)
      );
    }

    if (vehicleType) {
      filteredVehicles = filteredVehicles.filter((vehicle) => vehicle.vehicleType === vehicleType);
    }

    if (status) {
      filteredVehicles = filteredVehicles.filter((vehicle) => vehicle.status === status);
    }

    if (routeId) {
      filteredVehicles = filteredVehicles.filter((vehicle) => vehicle.routeId === routeId);
    }

    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedVehicles = filteredVehicles.slice(startIndex, endIndex);

    return c.json({
      data: paginatedVehicles,
      pagination: {
        page,
        limit,
        total: filteredVehicles.length,
        totalPages: Math.ceil(filteredVehicles.length / limit),
      },
    });
  })
  .post(
    "/vehicles",
    zValidator("json", vehicleSchema.omit({ id: true, createdAt: true, updatedAt: true })),
    async (c) => {
      const values = c.req.valid("json");
      
      const newVehicle = {
        ...values,
        id: generateId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      mockVehicles.push(newVehicle);

      return c.json({ data: newVehicle }, 201);
    }
  )
  .get("/vehicles/:id", async (c) => {
    const id = c.req.param("id");
    const vehicle = mockVehicles.find((v) => v.id === id);

    if (!vehicle) {
      return c.json({ error: "Vehicle not found" }, 404);
    }

    return c.json({ data: vehicle });
  })
  .put(
    "/vehicles/:id",
    zValidator("json", vehicleSchema.omit({ id: true, createdAt: true, updatedAt: true })),
    async (c) => {
      const id = c.req.param("id");
      const values = c.req.valid("json");

      const vehicleIndex = mockVehicles.findIndex((v) => v.id === id);

      if (vehicleIndex === -1) {
        return c.json({ error: "Vehicle not found" }, 404);
      }

      mockVehicles[vehicleIndex] = {
        ...mockVehicles[vehicleIndex],
        ...values,
        updatedAt: new Date().toISOString(),
      };

      return c.json({ data: mockVehicles[vehicleIndex] });
    }
  )
  .delete("/vehicles/:id", async (c) => {
    const id = c.req.param("id");
    const vehicleIndex = mockVehicles.findIndex((v) => v.id === id);

    if (vehicleIndex === -1) {
      return c.json({ error: "Vehicle not found" }, 404);
    }

    mockVehicles.splice(vehicleIndex, 1);

    return c.json({ message: "Vehicle deleted successfully" });
  })

  // Transport Reports
  .get("/reports/summary", async (c) => {
    const totalVehicles = mockVehicles.length;
    const activeVehicles = mockVehicles.filter(v => v.status === "active").length;
    const maintenanceVehicles = mockVehicles.filter(v => v.status === "maintenance").length;
    const totalRoutes = mockTransportRoutes.length;
    const activeRoutes = mockTransportRoutes.filter(r => r.status === "active").length;

    const vehicleTypeStats = mockVehicles.reduce((acc, vehicle) => {
      acc[vehicle.vehicleType] = (acc[vehicle.vehicleType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const totalCapacity = mockVehicles.reduce((sum, vehicle) => sum + vehicle.capacity, 0);
    const averageCapacity = totalVehicles > 0 ? Math.round(totalCapacity / totalVehicles) : 0;

    const routeStats = mockTransportRoutes.map(route => {
      const assignedVehicles = mockVehicles.filter(v => v.routeId === route.id);
      return {
        routeId: route.id,
        routeName: route.routeName,
        vehicleCount: assignedVehicles.length,
        totalCapacity: assignedVehicles.reduce((sum, v) => sum + v.capacity, 0),
        distance: route.distance,
        fee: route.fee,
      };
    });

    return c.json({
      data: {
        totalVehicles,
        activeVehicles,
        maintenanceVehicles,
        totalRoutes,
        activeRoutes,
        vehicleTypeStats,
        totalCapacity,
        averageCapacity,
        routeStats,
      },
    });
  })

  // Vehicle maintenance
  .post("/vehicles/:id/maintenance", async (c) => {
    const id = c.req.param("id");
    const vehicleIndex = mockVehicles.findIndex((v) => v.id === id);

    if (vehicleIndex === -1) {
      return c.json({ error: "Vehicle not found" }, 404);
    }

    mockVehicles[vehicleIndex] = {
      ...mockVehicles[vehicleIndex],
      status: "maintenance",
      updatedAt: new Date().toISOString(),
    };

    return c.json({ data: mockVehicles[vehicleIndex] });
  })

  // Vehicle activate
  .post("/vehicles/:id/activate", async (c) => {
    const id = c.req.param("id");
    const vehicleIndex = mockVehicles.findIndex((v) => v.id === id);

    if (vehicleIndex === -1) {
      return c.json({ error: "Vehicle not found" }, 404);
    }

    mockVehicles[vehicleIndex] = {
      ...mockVehicles[vehicleIndex],
      status: "active",
      updatedAt: new Date().toISOString(),
    };

    return c.json({ data: mockVehicles[vehicleIndex] });
  });

export default app;
