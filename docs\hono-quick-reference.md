# Hono.js + TanStack Query Quick Reference

## 🚀 Quick Setup Checklist

### 1. Install Dependencies
```bash
npm install hono @tanstack/react-query
npm install @hono/zod-validator zod  # For validation
```

### 2. Basic File Structure
```
app/
├── api/
│   └── [[...route]]/
│       ├── route.ts          # Main API entry
│       ├── users.ts          # User routes
│       ├── blogs.ts          # Blog routes
│       └── chat.ts           # Chat routes
├── layout.tsx                # Root layout with providers
lib/
├── hono.ts                   # Hono client setup
providers/
├── query-provider.tsx        # TanStack Query provider
features/
└── api/
    ├── use-get-blogs.ts      # Query hooks
    └── use-create-request.ts # Mutation hooks
```

## 🔧 Essential Code Snippets

### Main API Setup
```typescript
// app/api/[[...route]]/route.ts
import { handle } from "hono/vercel";
import { Hono } from "hono";

const app = new Hono().basePath("/api");

const routes = app
    .route("/users", users)
    .route("/blogs", blogs)

export const GET = handle(app)
export const POST = handle(app)
export type Apptype = typeof routes; // 🔑 Key export for type safety
```

### Client Setup
```typescript
// lib/hono.ts
import { hc } from "hono/client";
import { Apptype } from "@/app/api/[[...route]]/route";

export const client = hc<Apptype>(process.env.NEXT_PUBLIC_APP_URL!)
```

### Provider Setup
```typescript
// providers/query-provider.tsx
'use client'
import { QueryClient, QueryClientProvider } from '@tanstack/react-query'

function makeQueryClient() {
  return new QueryClient({
    defaultOptions: {
      queries: { staleTime: 60 * 1000 },
    },
  })
}

export function QueryProviders({ children }: { children: React.ReactNode }) {
  const queryClient = getQueryClient()
  return <QueryClientProvider client={queryClient}>{children}</QueryClientProvider>
}
```

### Layout Integration
```typescript
// app/layout.tsx
import { QueryProviders } from "@/providers/query-provider";

export default function RootLayout({ children }: { children: React.ReactNode }) {
  return (
    <html>
      <body>
        <QueryProviders>
          {children}
        </QueryProviders>
      </body>
    </html>
  );
}
```

## 🛠 Route Patterns

### Basic GET Route
```typescript
const app = new Hono()
    .get("/items", async (c) => {
        const data = await db.select().from(itemsTable)
        return c.json({ data })
    })
```

### POST with Validation
```typescript
import { zValidator } from "@hono/zod-validator"

const app = new Hono()
    .post(
        "/items",
        zValidator("json", itemSchema),
        async (c) => {
            const values = c.req.valid("json")
            const [data] = await db.insert(itemsTable).values(values).returning()
            return c.json({ data })
        }
    )
```

### Dynamic Parameters
```typescript
const app = new Hono()
    .get("/:id", async (c) => {
        const id = c.req.param("id")
        const data = await db.select().from(itemsTable).where(eq(itemsTable.id, id))
        return c.json({ data })
    })
```

### Query Parameters (Pagination)
```typescript
const app = new Hono()
    .get("/paginated", async (c) => {
        // Extract query parameters
        const page = parseInt(c.req.query("page") || "1");
        const limit = parseInt(c.req.query("limit") || "10");
        const search = c.req.query("search");

        // Calculate offset
        const offset = (page - 1) * limit;

        // Build query with optional search
        let query = db.select().from(itemsTable);
        if (search) {
            query = query.where(like(itemsTable.name, `%${search}%`));
        }

        // Get paginated data
        const data = await query.limit(limit).offset(offset);

        // Get total count
        const [{ count }] = await db.select({ count: sql`count(*)` }).from(itemsTable);

        const meta = {
            page,
            limit,
            total: Number(count),
            totalPages: Math.ceil(Number(count) / limit),
            hasNext: page < Math.ceil(Number(count) / limit),
            hasPrev: page > 1,
        };

        return c.json({ data, meta });
    })
```

### Method Chaining
```typescript
const app = new Hono()
    .get("/", getAllHandler)
    .post("/", createHandler)
    .get("/:id", getByIdHandler)
    .put("/:id", updateHandler)
    .delete("/:id", deleteHandler)
```

## 🔍 Query Hooks

### Basic Query Hook
```typescript
// features/api/use-get-items.ts
import { client } from "@/lib/hono";
import { useQuery } from "@tanstack/react-query";

export const useGetItems = () => {
    return useQuery({
        queryKey: ["items"],
        queryFn: async () => {
            const response = await client.api.items.$get();
            if (!response.ok) throw new Error("Failed to fetch");
            const { data } = await response.json();
            return data;
        },
    });
};
```

### Parameterized Query (Slug/ID)
```typescript
export const useGetItem = (id: string) => {
    return useQuery({
        queryKey: ["items", id],
        queryFn: async () => {
            const response = await client.api.items[":id"].$get({ param: { id } });
            if (!response.ok) throw new Error("Failed to fetch");
            const { data } = await response.json();
            return data;
        },
        enabled: !!id, // Only run if id exists
    });
};
```

### Query with Parameters (Pagination)
```typescript
interface PaginationParams {
    page?: number;
    limit?: number;
    search?: string;
}

export const useGetItemsPaginated = ({ page = 1, limit = 10, search }: PaginationParams = {}) => {
    return useQuery({
        queryKey: ["items", "paginated", { page, limit, search }],
        queryFn: async () => {
            const response = await client.api.items.paginated.$get({
                query: {
                    page: page.toString(),
                    limit: limit.toString(),
                    ...(search && { search }),
                }
            });
            if (!response.ok) throw new Error("Failed to fetch");
            const { data, meta } = await response.json();
            return { data, meta };
        },
    });
};
```

## 🔄 Mutation Hooks

### Basic Mutation
```typescript
// features/api/use-create-item.ts
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { InferRequestType, InferResponseType } from "hono";
import { client } from "@/lib/hono";

type ResponseType = InferResponseType<typeof client.api.items.$post>;
type RequestType = InferRequestType<typeof client.api.items.$post>["json"];

export const useCreateItem = () => {
    const queryClient = useQueryClient();

    return useMutation<ResponseType, Error, RequestType>({
        mutationFn: async (json) => {
            const response = await client.api.items.$post({ json });
            return await response.json();
        },
        onSuccess: () => {
            queryClient.invalidateQueries({ queryKey: ["items"] });
        },
    });
};
```

## 🎯 Component Usage

### Query Component
```typescript
import { useGetItems } from "@/features/api/use-get-items";

export function ItemList() {
    const { data: items, isLoading, error } = useGetItems();

    if (isLoading) return <div>Loading...</div>;
    if (error) return <div>Error: {error.message}</div>;

    return (
        <div>
            {items?.map(item => (
                <div key={item.id}>{item.name}</div>
            ))}
        </div>
    );
}
```

### Mutation Component
```typescript
import { useCreateItem } from "@/features/api/use-create-item";

export function CreateItemForm() {
    const createItem = useCreateItem();

    const handleSubmit = async (formData: FormData) => {
        try {
            await createItem.mutateAsync({
                name: formData.get("name") as string,
            });
        } catch (error) {
            console.error("Failed to create item");
        }
    };

    return (
        <form onSubmit={handleSubmit}>
            <input name="name" required />
            <button type="submit" disabled={createItem.isPending}>
                {createItem.isPending ? "Creating..." : "Create"}
            </button>
        </form>
    );
}
```

## 🔧 Common Patterns

### Error Handling
```typescript
const { data, error, isError } = useGetItems();

if (isError) {
    console.error("Query failed:", error.message);
}
```

### Loading States
```typescript
const { isLoading, isFetching, isPending } = useGetItems();
const mutation = useCreateItem();

// isLoading: First time loading
// isFetching: Any time fetching (including background)
// isPending: Mutation in progress
```

### Cache Invalidation
```typescript
const queryClient = useQueryClient();

// Invalidate specific query
queryClient.invalidateQueries({ queryKey: ["items"] });

// Invalidate all queries starting with "items"
queryClient.invalidateQueries({ queryKey: ["items"], exact: false });
```

## 🎯 Real Project Hooks

### Existing Hooks in features/api/
```typescript
// Query Hooks
useGetAllBlogs()                    // Fetch all blogs
useGetBlogById(blogId: string)      // Fetch blog by ID/slug

// Mutation Hooks
useCreateRequest()                  // Create user request
useCreateChat()                     // Chat with AI
useGetFreeQuote()                   // Submit quote request
```

### Usage Examples from Your Project
```typescript
// Blog listing
const { data: blogs, isLoading } = useGetAllBlogs();

// Blog detail page
const { data: blog } = useGetBlogById(params.blogId);

// Contact form
const createRequest = useCreateRequest();
await createRequest.mutateAsync({
    userName: "John Doe",
    email: "<EMAIL>",
    number: "+1234567890",
    socialId: "@johndoe",
    intrest: "Web Development"
});

// Chat functionality
const createChat = useCreateChat();
await createChat.mutateAsync({
    userText: "Hello!",
    previousChats: []
});

// Free quote
const getFreeQuote = useGetFreeQuote();
await getFreeQuote.mutateAsync({
    name: "John Doe",
    mobile: "+1234567890",
    skype: "john.doe",
    message: "I need a website"
});
```

## 🚨 Common Gotchas

1. **Missing AppType**: Always export and import the AppType for type safety
2. **Environment Variables**: Ensure `NEXT_PUBLIC_APP_URL` is set
3. **Query Keys**: Use consistent, descriptive query keys
4. **Error Boundaries**: Wrap components with error boundaries for better UX
5. **Stale Time**: Configure appropriate stale times for your use case
6. **Query Parameters**: Remember to convert to strings when sending to API
7. **Enabled Queries**: Use `enabled` option for conditional queries

## 📚 Useful Resources

- [Hono Documentation](https://hono.dev/)
- [TanStack Query Documentation](https://tanstack.com/query/latest)
- [Zod Validation](https://zod.dev/)
- [Next.js API Routes](https://nextjs.org/docs/app/building-your-application/routing/route-handlers)
