"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  BookOpen,
  Users,
  RefreshCw,
  AlertTriangle,
  CheckCircle,
  Calendar,
  Search,
  Plus,
  TrendingUp,
  Clock,
  Bookmark,
  Library,
  FileText,
  BarChart3,
} from "lucide-react";

export default function LibraryDashboard() {
  const [user, setUser] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "librarian") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock library data
  const libraryStats = {
    totalBooks: 5678,
    availableBooks: 4892,
    issuedBooks: 786,
    overdueBooks: 45,
    totalMembers: 1234,
    activeMembers: 892,
    newMembersThisMonth: 23,
    popularGenre: "Science Fiction",
  };

  const recentTransactions = [
    { id: "LIB001", member: "John Doe", book: "Introduction to Physics", action: "Issued", date: "2024-01-15", dueDate: "2024-01-29" },
    { id: "LIB002", member: "Alice Smith", book: "Advanced Mathematics", action: "Returned", date: "2024-01-15", dueDate: "2024-01-15" },
    { id: "LIB003", member: "Bob Johnson", book: "Chemistry Fundamentals", action: "Issued", date: "2024-01-14", dueDate: "2024-01-28" },
    { id: "LIB004", member: "Sarah Wilson", book: "English Literature", action: "Overdue", date: "2024-01-01", dueDate: "2024-01-15" },
    { id: "LIB005", member: "Mike Brown", book: "Computer Science", action: "Reserved", date: "2024-01-14", dueDate: "2024-01-21" },
  ];

  const popularBooks = [
    { title: "Introduction to Physics", author: "Dr. Smith", issued: 45, available: 3 },
    { title: "Advanced Mathematics", author: "Prof. Johnson", issued: 38, available: 7 },
    { title: "Chemistry Fundamentals", author: "Dr. Brown", issued: 32, available: 5 },
    { title: "English Literature", author: "Ms. Davis", issued: 28, available: 12 },
    { title: "Computer Science", author: "Mr. Wilson", issued: 25, available: 8 },
  ];

  const quickActions = [
    { title: "Issue Book", icon: BookOpen, color: "bg-blue-500", href: "/library/issue" },
    { title: "Return Book", icon: RefreshCw, color: "bg-green-500", href: "/library/return" },
    { title: "Add New Book", icon: Plus, color: "bg-purple-500", href: "/library/books/new" },
    { title: "Search Books", icon: Search, color: "bg-orange-500", href: "/library/search" },
  ];

  const overdueBooks = [
    { member: "Sarah Wilson", book: "English Literature", daysOverdue: 5, fine: 50 },
    { member: "Tom Anderson", book: "History of Science", daysOverdue: 3, fine: 30 },
    { member: "Lisa Garcia", book: "Art Appreciation", daysOverdue: 8, fine: 80 },
  ];

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Welcome Header */}
        <div className="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg p-6 text-white">
          <h1 className="text-2xl font-bold mb-2">
            Library Dashboard
          </h1>
          <p className="text-indigo-100">
            Welcome back, {user.firstName}! Manage the library efficiently.
          </p>
        </div>

        {/* Library Overview Stats */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <BookOpen className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {libraryStats.totalBooks.toLocaleString()}
                  </div>
                  <p className="text-sm text-gray-600">Total Books</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {libraryStats.availableBooks.toLocaleString()}
                  </div>
                  <p className="text-sm text-gray-600">Available</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <RefreshCw className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    {libraryStats.issuedBooks}
                  </div>
                  <p className="text-sm text-gray-600">Issued</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <AlertTriangle className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-red-600">
                    {libraryStats.overdueBooks}
                  </div>
                  <p className="text-sm text-gray-600">Overdue</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-4">
              {quickActions.map((action, index) => (
                <Button
                  key={index}
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center space-y-2"
                  onClick={() => router.push(action.href)}
                >
                  <div className={`w-12 h-12 ${action.color} rounded-lg flex items-center justify-center`}>
                    <action.icon className="h-6 w-6 text-white" />
                  </div>
                  <span className="font-medium">{action.title}</span>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Main Content Grid */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Recent Transactions */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Clock className="h-5 w-5 mr-2" />
                Recent Transactions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentTransactions.map((transaction) => (
                  <div key={transaction.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className={`w-10 h-10 rounded-lg flex items-center justify-center ${
                        transaction.action === 'Issued' ? 'bg-blue-100' :
                        transaction.action === 'Returned' ? 'bg-green-100' :
                        transaction.action === 'Overdue' ? 'bg-red-100' : 'bg-yellow-100'
                      }`}>
                        {transaction.action === 'Issued' ? <BookOpen className="h-5 w-5 text-blue-600" /> :
                         transaction.action === 'Returned' ? <CheckCircle className="h-5 w-5 text-green-600" /> :
                         transaction.action === 'Overdue' ? <AlertTriangle className="h-5 w-5 text-red-600" /> :
                         <Bookmark className="h-5 w-5 text-yellow-600" />}
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">{transaction.member}</div>
                        <div className="text-sm text-gray-500">{transaction.book}</div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`font-medium ${
                        transaction.action === 'Issued' ? 'text-blue-600' :
                        transaction.action === 'Returned' ? 'text-green-600' :
                        transaction.action === 'Overdue' ? 'text-red-600' : 'text-yellow-600'
                      }`}>
                        {transaction.action}
                      </div>
                      <div className="text-sm text-gray-500">
                        Due: {transaction.dueDate}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Library Statistics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="h-5 w-5 mr-2" />
                Library Stats
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Total Members</span>
                  <span className="font-medium">{libraryStats.totalMembers}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Active Members</span>
                  <span className="font-medium text-green-600">{libraryStats.activeMembers}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">New This Month</span>
                  <span className="font-medium text-blue-600">{libraryStats.newMembersThisMonth}</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm text-gray-600">Popular Genre</span>
                  <span className="font-medium">{libraryStats.popularGenre}</span>
                </div>
                
                <div className="pt-4">
                  <div className="text-sm text-gray-600 mb-2">Book Availability</div>
                  <div className="w-full bg-gray-200 rounded-full h-3">
                    <div 
                      className="bg-green-600 h-3 rounded-full" 
                      style={{ width: `${(libraryStats.availableBooks / libraryStats.totalBooks) * 100}%` }}
                    />
                  </div>
                  <div className="text-center text-sm text-gray-600 mt-2">
                    {((libraryStats.availableBooks / libraryStats.totalBooks) * 100).toFixed(1)}% Available
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Popular Books and Overdue Books */}
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Popular Books */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <TrendingUp className="h-5 w-5 mr-2" />
                Popular Books
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {popularBooks.map((book, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
                    <div>
                      <div className="font-medium text-gray-900">{book.title}</div>
                      <div className="text-sm text-gray-500">by {book.author}</div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-blue-600">{book.issued} issued</div>
                      <div className="text-sm text-gray-500">{book.available} available</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Overdue Books */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertTriangle className="h-5 w-5 mr-2 text-red-600" />
                Overdue Books
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {overdueBooks.map((item, index) => (
                  <div key={index} className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                    <div>
                      <div className="font-medium text-gray-900">{item.member}</div>
                      <div className="text-sm text-gray-500">{item.book}</div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-red-600">{item.daysOverdue} days</div>
                      <div className="text-sm text-gray-500">Fine: ₹{item.fine}</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  );
}
