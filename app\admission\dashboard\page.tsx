"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  UserPlus,
  Users,
  Calendar,
  Clock,
  CheckCircle,
  AlertTriangle,
  FileText,
  Phone,
  Mail,
  TrendingUp,
  Plus,
  GraduationCap,
  BookOpen,
  MapPin
} from "lucide-react";

export default function AdmissionDashboard() {
  // All hooks must be at the top level
  const [user, setUser] = useState<any>(null);
  const [availablePrograms, setAvailablePrograms] = useState<any[]>([]);
  const [loading, setLoading] = useState(false);
  const router = useRouter();

  // Fetch available programs from API
  const fetchAvailablePrograms = async () => {
    setLoading(true);
    try {
      const response = await fetch('/api/academic-programs');
      const data = await response.json();
      setAvailablePrograms(data.data || []);
    } catch (error) {
      console.error('Error fetching programs:', error);
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "admission_officer") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  // Load data on component mount
  useEffect(() => {
    fetchAvailablePrograms();
  }, []);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock admission data
  const admissionStats = {
    totalApplications: 156,
    pendingReview: 23,
    approved: 98,
    rejected: 35,
    todayInterviews: 8,
    thisWeekAdmissions: 12,
  };

  // Mock data for fallback
  const mockAvailablePrograms = [
    {
      id: "1",
      name: "Class 10",
      code: "CLS10",
      type: "school",
      department: "Secondary Education",
      totalSeats: 120,
      occupiedSeats: 95,
      availableSeats: 25,
      admissionStatus: "open",
      batches: [
        {
          id: "1",
          batchName: "2024-2025",
          totalSeats: 60,
          availableSeats: 15,
          admissionStatus: "open",
        },
        {
          id: "2",
          batchName: "2023-2024",
          totalSeats: 60,
          availableSeats: 10,
          admissionStatus: "open",
        },
      ],
    },
    {
      id: "2",
      name: "B.Tech Computer Science",
      code: "BTECHCS",
      type: "undergraduate",
      department: "Computer Science",
      totalSeats: 240,
      occupiedSeats: 180,
      availableSeats: 60,
      admissionStatus: "open",
      batches: [
        {
          id: "3",
          batchName: "2024-2028",
          totalSeats: 60,
          availableSeats: 30,
          admissionStatus: "open",
        },
        {
          id: "4",
          batchName: "2023-2027",
          totalSeats: 60,
          availableSeats: 10,
          admissionStatus: "waitlist",
        },
        {
          id: "5",
          batchName: "2022-2026",
          totalSeats: 60,
          availableSeats: 5,
          admissionStatus: "waitlist",
        },
      ],
    },
    {
      id: "3",
      name: "M.Sc Physics",
      code: "MSCPHY",
      type: "postgraduate",
      department: "Physics",
      totalSeats: 60,
      occupiedSeats: 58,
      availableSeats: 2,
      admissionStatus: "waitlist",
      batches: [
        {
          id: "6",
          batchName: "2024-2026",
          totalSeats: 30,
          availableSeats: 2,
          admissionStatus: "waitlist",
        },
        {
          id: "7",
          batchName: "2023-2025",
          totalSeats: 30,
          availableSeats: 0,
          admissionStatus: "closed",
        },
      ],
    },
  ];

  const recentApplications = [
    {
      id: "1",
      name: "Alex Johnson",
      grade: "10",
      applicationDate: "2024-01-15",
      status: "pending",
      phone: "9876543210",
      email: "<EMAIL>",
      interviewDate: "2024-01-20",
    },
    {
      id: "2",
      name: "Maria Garcia",
      grade: "11",
      applicationDate: "2024-01-14",
      status: "approved",
      phone: "9876543211",
      email: "<EMAIL>",
      interviewDate: "2024-01-18",
    },
    {
      id: "3",
      name: "David Chen",
      grade: "9",
      applicationDate: "2024-01-13",
      status: "interview_scheduled",
      phone: "9876543212",
      email: "<EMAIL>",
      interviewDate: "2024-01-22",
    },
  ];

  const todayInterviews = [
    {
      time: "09:00 AM",
      name: "Sarah Wilson",
      grade: "10",
      phone: "9876543213",
      status: "scheduled",
    },
    {
      time: "10:30 AM",
      name: "Michael Brown",
      grade: "11",
      phone: "9876543214",
      status: "completed",
    },
    {
      time: "02:00 PM",
      name: "Emma Davis",
      grade: "9",
      phone: "9876543215",
      status: "scheduled",
    },
  ];

  const admissionTasks = [
    {
      task: "Review pending applications",
      count: 23,
      priority: "high",
      dueDate: "Today",
    },
    {
      task: "Schedule interviews for next week",
      count: 15,
      priority: "medium",
      dueDate: "Tomorrow",
    },
    {
      task: "Send admission letters",
      count: 8,
      priority: "high",
      dueDate: "Today",
    },
    {
      task: "Update admission records",
      count: 12,
      priority: "low",
      dueDate: "This week",
    },
  ];

  const statusColors = {
    pending: "text-yellow-600 bg-yellow-100",
    approved: "text-green-600 bg-green-100",
    rejected: "text-red-600 bg-red-100",
    interview_scheduled: "text-blue-600 bg-blue-100",
    completed: "text-gray-600 bg-gray-100",
    scheduled: "text-purple-600 bg-purple-100",
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Welcome Header */}
        <div className="bg-gradient-to-r from-teal-500 to-blue-600 rounded-lg p-6 text-white">
          <h1 className="text-2xl font-bold mb-2">
            Welcome, {user.firstName}!
          </h1>
          <p className="text-teal-100">
            Manage student admissions and enrollment processes
          </p>
        </div>

        {/* Admission Stats */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <FileText className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {admissionStats.totalApplications}
                  </div>
                  <p className="text-sm text-gray-600">Total Applications</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-yellow-600">
                    {admissionStats.pendingReview}
                  </div>
                  <p className="text-sm text-gray-600">Pending Review</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {admissionStats.approved}
                  </div>
                  <p className="text-sm text-gray-600">Approved</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Calendar className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {admissionStats.todayInterviews}
                  </div>
                  <p className="text-sm text-gray-600">Today&apos;s Interviews</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Available Programs Section */}
        <Card>
          <CardHeader>
            <div className="flex items-center justify-between">
              <CardTitle className="flex items-center">
                <GraduationCap className="h-5 w-5 mr-2" />
                Available Programs & Seat Status
              </CardTitle>
              <Button variant="outline" size="sm">
                View All Programs
              </Button>
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {loading ? (
                <div className="text-center py-8">
                  <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                  <p className="mt-2 text-gray-500">Loading available programs...</p>
                </div>
              ) : availablePrograms.length === 0 ? (
                <div className="text-center py-8 text-gray-500">
                  <GraduationCap className="h-12 w-12 mx-auto mb-4 text-gray-400" />
                  <p>No programs available for admission</p>
                </div>
              ) : (
                availablePrograms.map((program) => (
                <div key={program.id} className="border rounded-lg p-4">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h3 className="font-semibold text-lg text-gray-900">{program.name}</h3>
                      <p className="text-sm text-gray-500">
                        {program.code} • {program.department}
                      </p>
                    </div>
                    <div className="text-right">
                      <div className="text-2xl font-bold text-blue-600">
                        {program.statistics?.availableSeats || 0}
                      </div>
                      <div className="text-sm text-gray-500">Available Seats</div>
                      <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium mt-1 ${
                        program.admissionStatus === "open" ? "text-green-600 bg-green-100" :
                        program.admissionStatus === "waitlist" ? "text-yellow-600 bg-yellow-100" :
                        "text-red-600 bg-red-100"
                      }`}>
                        {program.admissionStatus.toUpperCase()}
                      </span>
                    </div>
                  </div>

                  <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-3">
                    {/* Show program-level admission info since batches need separate API call */}
                    <div className="bg-gray-50 rounded-lg p-3">
                      <div className="flex items-center justify-between mb-2">
                        <div className="font-medium text-sm">Current Admission</div>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                          program.admissionStatus === "open" ? "text-green-600 bg-green-100" :
                          program.admissionStatus === "waitlist" ? "text-yellow-600 bg-yellow-100" :
                          "text-red-600 bg-red-100"
                        }`}>
                          {program.admissionStatus}
                        </span>
                      </div>
                      <div className="text-sm text-gray-600">
                        <div className="flex justify-between">
                          <span>Available:</span>
                          <span className="font-medium">{program.statistics?.availableSeats || 0}/{program.totalSeats}</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2 mt-2">
                          <div
                            className={`h-2 rounded-full ${
                              (program.statistics?.availableSeats || 0) > 10 ? "bg-green-600" :
                              (program.statistics?.availableSeats || 0) > 5 ? "bg-yellow-600" : "bg-red-600"
                            }`}
                            style={{
                              width: `${Math.max(5, ((program.statistics?.availableSeats || 0) / program.totalSeats) * 100)}%`
                            }}
                          />
                        </div>
                      </div>
                      <Button
                        variant="outline"
                        size="sm"
                        className="w-full mt-3"
                        disabled={program.admissionStatus === "closed"}
                      >
                        {program.admissionStatus === "closed" ? "Closed" : "Apply Now"}
                      </Button>
                    </div>

                    {/* Additional info cards */}
                    <div className="bg-blue-50 rounded-lg p-3">
                      <div className="font-medium text-sm text-blue-900 mb-2">Program Info</div>
                      <div className="text-sm text-blue-700">
                        <div className="flex justify-between">
                          <span>Duration:</span>
                          <span className="font-medium">{program.duration} years</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Type:</span>
                          <span className="font-medium capitalize">{program.type}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Students:</span>
                          <span className="font-medium">{program.statistics?.totalStudents || 0}</span>
                        </div>
                      </div>
                    </div>

                    <div className="bg-green-50 rounded-lg p-3">
                      <div className="font-medium text-sm text-green-900 mb-2">Statistics</div>
                      <div className="text-sm text-green-700">
                        <div className="flex justify-between">
                          <span>Batches:</span>
                          <span className="font-medium">{program.statistics?.totalBatches || 0}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Classes:</span>
                          <span className="font-medium">{program.statistics?.totalClasses || 0}</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Occupancy:</span>
                          <span className="font-medium">
                            {Math.round(((program.totalSeats - (program.statistics?.availableSeats || 0)) / program.totalSeats) * 100)}%
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              ))
            )}
            </div>
          </CardContent>
        </Card>

        {/* Main Content Grid */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Recent Applications */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center">
                  <UserPlus className="h-5 w-5 mr-2" />
                  Recent Applications
                </CardTitle>
                <Button variant="outline" size="sm">
                  View All
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentApplications.map((application) => (
                  <div key={application.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                        <Users className="h-5 w-5 text-blue-600" />
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">{application.name}</div>
                        <div className="text-sm text-gray-500">
                          Grade {application.grade} • Applied: {application.applicationDate}
                        </div>
                        <div className="text-xs text-gray-400 flex items-center mt-1">
                          <Phone className="h-3 w-3 mr-1" />
                          {application.phone} •
                          <Mail className="h-3 w-3 ml-2 mr-1" />
                          {application.email}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusColors[application.status as keyof typeof statusColors]}`}>
                        {application.status.replace('_', ' ').toUpperCase()}
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        Interview: {application.interviewDate}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button className="w-full justify-start" variant="outline">
                <Plus className="h-4 w-4 mr-2" />
                New Application
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <Calendar className="h-4 w-4 mr-2" />
                Schedule Interview
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <CheckCircle className="h-4 w-4 mr-2" />
                Review Applications
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <Mail className="h-4 w-4 mr-2" />
                Send Notifications
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <FileText className="h-4 w-4 mr-2" />
                Generate Reports
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Today's Interviews and Tasks */}
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Today's Interviews */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calendar className="h-5 w-5 mr-2" />
                Today&apos;s Interviews
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {todayInterviews.map((interview, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div className="flex items-center space-x-3">
                      <div className="text-sm font-medium text-gray-600 w-20">
                        {interview.time}
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">{interview.name}</div>
                        <div className="text-sm text-gray-500">
                          Grade {interview.grade} • {interview.phone}
                        </div>
                      </div>
                    </div>
                    <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusColors[interview.status as keyof typeof statusColors]}`}>
                      {interview.status.toUpperCase()}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Pending Tasks */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertTriangle className="h-5 w-5 mr-2" />
                Pending Tasks
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {admissionTasks.map((task, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div className="font-medium text-gray-900">{task.task}</div>
                      <div className="text-sm text-gray-500">{task.count} items</div>
                    </div>
                    <div className="text-right">
                      <div className={`text-sm font-medium ${
                        task.priority === "high" ? "text-red-600" :
                        task.priority === "medium" ? "text-yellow-600" : "text-green-600"
                      }`}>
                        Due: {task.dueDate}
                      </div>
                      <div className={`text-xs px-2 py-1 rounded-full ${
                        task.priority === "high" ? "bg-red-100 text-red-600" :
                        task.priority === "medium" ? "bg-yellow-100 text-yellow-600" : "bg-green-100 text-green-600"
                      }`}>
                        {task.priority.toUpperCase()}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Admission Analytics */}
        <Card>
          <CardHeader>
            <CardTitle>Admission Analytics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 md:grid-cols-3">
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 mb-2">
                  {Math.round((admissionStats.approved / admissionStats.totalApplications) * 100)}%
                </div>
                <div className="text-sm text-gray-600">Approval Rate</div>
                <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-green-600 h-2 rounded-full w-[63%]" />
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">
                  {admissionStats.thisWeekAdmissions}
                </div>
                <div className="text-sm text-gray-600">This Week Admissions</div>
                <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-600 h-2 rounded-full w-[75%]" />
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600 mb-2">
                  {Math.round((admissionStats.pendingReview / admissionStats.totalApplications) * 100)}%
                </div>
                <div className="text-sm text-gray-600">Pending Review</div>
                <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-purple-600 h-2 rounded-full w-[15%]" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
