"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  ArrowLeft,
  Plus,
  Search,
  Filter,
  Download,
  RefreshCw,
  Eye,
  Edit,
  MoreHorizontal,
  BookOpen,
  GraduationCap,
  TrendingUp,
  TrendingDown,
  Award,
  Calendar,
  Users,
  BarChart3,
  Target,
  CheckCircle,
  AlertTriangle,
  Clock
} from "lucide-react";
import { toast } from "sonner";

export default function PrincipalAcademicPage() {
  const [user, setUser] = useState<any>(null);
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState<any>({});
  const [showAddDialog, setShowAddDialog] = useState(false);
  const [addType, setAddType] = useState<"subject" | "section" | "batch">("subject");
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    classLevel: "",
    section: "",
    subject: "",
    capacity: "",
    teacherId: "",
    academicYear: "",
    batchName: ""
  });
  const [availableClasses, setAvailableClasses] = useState<any[]>([]);
  const [availableTeachers, setAvailableTeachers] = useState<any[]>([]);

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "principal") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
    loadData();
  }, [router]);

  const loadData = async () => {
    try {
      setLoading(true);

      // Mock academic data with proper school structure
      const mockData = {
        overview: {
          totalClasses: 15, // Nursery to Grade 12 + streams
          totalSections: 45, // Multiple sections per class
          totalSubjects: 12,
          averagePerformance: 87.5,
          attendanceRate: 94.2,
          teacherStudentRatio: "1:28"
        },
        classStructure: [
          { level: "Nursery", sections: ["A", "B"], capacity: 25, subjects: ["Play Way", "Rhymes", "Drawing"] },
          { level: "LKG", sections: ["A", "B"], capacity: 25, subjects: ["English", "Math", "EVS", "Drawing"] },
          { level: "UKG", sections: ["A", "B"], capacity: 25, subjects: ["English", "Math", "EVS", "Drawing"] },
          { level: "Grade 1", sections: ["A", "B", "C"], capacity: 30, subjects: ["English", "Math", "EVS", "Hindi"] },
          { level: "Grade 2", sections: ["A", "B", "C"], capacity: 30, subjects: ["English", "Math", "EVS", "Hindi"] },
          { level: "Grade 3", sections: ["A", "B", "C"], capacity: 30, subjects: ["English", "Math", "Science", "Social Studies", "Hindi"] },
          { level: "Grade 4", sections: ["A", "B", "C"], capacity: 30, subjects: ["English", "Math", "Science", "Social Studies", "Hindi"] },
          { level: "Grade 5", sections: ["A", "B", "C"], capacity: 30, subjects: ["English", "Math", "Science", "Social Studies", "Hindi"] },
          { level: "Grade 6", sections: ["A", "B", "C"], capacity: 30, subjects: ["English", "Math", "Science", "Social Studies", "Hindi", "Computer Science"] },
          { level: "Grade 7", sections: ["A", "B", "C"], capacity: 30, subjects: ["English", "Math", "Science", "Social Studies", "Hindi", "Computer Science"] },
          { level: "Grade 8", sections: ["A", "B", "C"], capacity: 30, subjects: ["English", "Math", "Science", "Social Studies", "Hindi", "Computer Science"] },
          { level: "Grade 9", sections: ["A", "B", "C"], capacity: 30, subjects: ["English", "Math", "Science", "Social Studies", "Hindi", "Computer Science"] },
          { level: "Grade 10", sections: ["A", "B", "C"], capacity: 30, subjects: ["English", "Math", "Science", "Social Studies", "Hindi", "Computer Science"] },
          { level: "Grade 11 Science", sections: ["A", "B"], capacity: 30, subjects: ["English", "Physics", "Chemistry", "Math", "Biology/Computer Science"] },
          { level: "Grade 11 Commerce", sections: ["A"], capacity: 30, subjects: ["English", "Accountancy", "Business Studies", "Economics", "Math"] },
          { level: "Grade 11 Arts", sections: ["A"], capacity: 30, subjects: ["English", "History", "Political Science", "Geography", "Psychology"] },
          { level: "Grade 12 Science", sections: ["A", "B"], capacity: 30, subjects: ["English", "Physics", "Chemistry", "Math", "Biology/Computer Science"] },
          { level: "Grade 12 Commerce", sections: ["A"], capacity: 30, subjects: ["English", "Accountancy", "Business Studies", "Economics", "Math"] },
          { level: "Grade 12 Arts", sections: ["A"], capacity: 30, subjects: ["English", "History", "Political Science", "Geography", "Psychology"] }
        ],
        recentActivities: [
          { id: 1, type: "section", title: "New Section Added to Grade 5", date: "2024-01-20", status: "completed" },
          { id: 2, type: "subject", title: "Computer Science Added to Grade 6", date: "2024-01-18", status: "in_progress" },
          { id: 3, type: "batch", title: "Academic Year 2024-25 Planning", date: "2024-01-15", status: "pending" }
        ]
      };

      // Mock available classes and teachers
      setAvailableClasses(mockData.classStructure);
      setAvailableTeachers([
        { id: "1", name: "Dr. Smith", subject: "Mathematics" },
        { id: "2", name: "Ms. Johnson", subject: "English" },
        { id: "3", name: "Prof. Williams", subject: "Science" },
        { id: "4", name: "Mr. Brown", subject: "Social Studies" },
        { id: "5", name: "Ms. Davis", subject: "Computer Science" }
      ]);

      setData(mockData);
      toast.success("Academic data loaded successfully");
    } catch (error) {
      console.error("Error loading data:", error);
      toast.error("Failed to load data");
    } finally {
      setLoading(false);
    }
  };

  const handleAddNew = (type: "subject" | "section" | "batch") => {
    setAddType(type);
    setFormData({
      name: "",
      description: "",
      classLevel: "",
      section: "",
      subject: "",
      capacity: "30",
      teacherId: "",
      academicYear: "2024-25",
      batchName: ""
    });
    setShowAddDialog(true);
  };

  const handleSubmitAdd = async () => {
    try {
      // TODO: Implement API call to add new academic item
      console.log("Adding new", addType, formData);

      let successMessage = "";
      if (addType === "subject") {
        successMessage = `Subject "${formData.subject}" added to ${formData.classLevel} - Section ${formData.section}`;
      } else if (addType === "section") {
        successMessage = `Section "${formData.section}" added to ${formData.classLevel} with capacity ${formData.capacity}`;
      } else if (addType === "batch") {
        successMessage = `Academic batch "${formData.batchName}" created for ${formData.academicYear}`;
      }

      toast.success(successMessage);
      setShowAddDialog(false);

      // Refresh data
      loadData();
    } catch (error) {
      console.error("Error adding new item:", error);
      toast.error(`Failed to add new ${addType}`);
    }
  };

  if (!user) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.back()}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900"> - Academic</h1>
              <p className="text-gray-600">
                Principal academic management and oversight
              </p>
            </div>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={loadData}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Dialog open={showAddDialog} onOpenChange={setShowAddDialog}>
              <DialogTrigger asChild>
                <Button onClick={() => handleAddNew("subject")}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add New
                </Button>
              </DialogTrigger>
              <DialogContent className="max-w-2xl">
                <DialogHeader>
                  <DialogTitle>Add New {addType.charAt(0).toUpperCase() + addType.slice(1)}</DialogTitle>
                  <DialogDescription>
                    {addType === "subject" && "Add a subject to a specific class and section"}
                    {addType === "section" && "Create a new section for a class level"}
                    {addType === "batch" && "Create a new academic batch/session"}
                  </DialogDescription>
                </DialogHeader>
                <div className="space-y-4">
                  {addType === "subject" && (
                    <>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="classLevel">Class Level</Label>
                          <Select value={formData.classLevel} onValueChange={(value) => setFormData({...formData, classLevel: value})}>
                            <SelectTrigger>
                              <SelectValue placeholder="Select class level" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="Nursery">Nursery</SelectItem>
                              <SelectItem value="LKG">LKG</SelectItem>
                              <SelectItem value="UKG">UKG</SelectItem>
                              <SelectItem value="Grade 1">Grade 1</SelectItem>
                              <SelectItem value="Grade 2">Grade 2</SelectItem>
                              <SelectItem value="Grade 3">Grade 3</SelectItem>
                              <SelectItem value="Grade 4">Grade 4</SelectItem>
                              <SelectItem value="Grade 5">Grade 5</SelectItem>
                              <SelectItem value="Grade 6">Grade 6</SelectItem>
                              <SelectItem value="Grade 7">Grade 7</SelectItem>
                              <SelectItem value="Grade 8">Grade 8</SelectItem>
                              <SelectItem value="Grade 9">Grade 9</SelectItem>
                              <SelectItem value="Grade 10">Grade 10</SelectItem>
                              <SelectItem value="Grade 11 Science">Grade 11 Science</SelectItem>
                              <SelectItem value="Grade 11 Commerce">Grade 11 Commerce</SelectItem>
                              <SelectItem value="Grade 11 Arts">Grade 11 Arts</SelectItem>
                              <SelectItem value="Grade 12 Science">Grade 12 Science</SelectItem>
                              <SelectItem value="Grade 12 Commerce">Grade 12 Commerce</SelectItem>
                              <SelectItem value="Grade 12 Arts">Grade 12 Arts</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div>
                          <Label htmlFor="section">Section</Label>
                          <Select value={formData.section} onValueChange={(value) => setFormData({...formData, section: value})}>
                            <SelectTrigger>
                              <SelectValue placeholder="Select section" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="A">Section A</SelectItem>
                              <SelectItem value="B">Section B</SelectItem>
                              <SelectItem value="C">Section C</SelectItem>
                              <SelectItem value="D">Section D</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="subject">Subject</Label>
                          <Select value={formData.subject} onValueChange={(value) => setFormData({...formData, subject: value})}>
                            <SelectTrigger>
                              <SelectValue placeholder="Select subject" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="English">English</SelectItem>
                              <SelectItem value="Mathematics">Mathematics</SelectItem>
                              <SelectItem value="Science">Science</SelectItem>
                              <SelectItem value="Physics">Physics</SelectItem>
                              <SelectItem value="Chemistry">Chemistry</SelectItem>
                              <SelectItem value="Biology">Biology</SelectItem>
                              <SelectItem value="Social Studies">Social Studies</SelectItem>
                              <SelectItem value="History">History</SelectItem>
                              <SelectItem value="Geography">Geography</SelectItem>
                              <SelectItem value="Political Science">Political Science</SelectItem>
                              <SelectItem value="Economics">Economics</SelectItem>
                              <SelectItem value="Accountancy">Accountancy</SelectItem>
                              <SelectItem value="Business Studies">Business Studies</SelectItem>
                              <SelectItem value="Computer Science">Computer Science</SelectItem>
                              <SelectItem value="Hindi">Hindi</SelectItem>
                              <SelectItem value="EVS">Environmental Studies</SelectItem>
                              <SelectItem value="Physical Education">Physical Education</SelectItem>
                              <SelectItem value="Art">Art & Craft</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                        <div>
                          <Label htmlFor="teacherId">Assign Teacher</Label>
                          <Select value={formData.teacherId} onValueChange={(value) => setFormData({...formData, teacherId: value})}>
                            <SelectTrigger>
                              <SelectValue placeholder="Select teacher" />
                            </SelectTrigger>
                            <SelectContent>
                              {availableTeachers.map((teacher) => (
                                <SelectItem key={teacher.id} value={teacher.id}>
                                  {teacher.name} ({teacher.subject})
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </>
                  )}

                  {addType === "section" && (
                    <>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="classLevel">Class Level</Label>
                          <Select value={formData.classLevel} onValueChange={(value) => setFormData({...formData, classLevel: value})}>
                            <SelectTrigger>
                              <SelectValue placeholder="Select class level" />
                            </SelectTrigger>
                            <SelectContent>
                              {availableClasses.map((cls) => (
                                <SelectItem key={cls.level} value={cls.level}>
                                  {cls.level}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                        <div>
                          <Label htmlFor="section">Section Name</Label>
                          <Select value={formData.section} onValueChange={(value) => setFormData({...formData, section: value})}>
                            <SelectTrigger>
                              <SelectValue placeholder="Select section" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="A">Section A</SelectItem>
                              <SelectItem value="B">Section B</SelectItem>
                              <SelectItem value="C">Section C</SelectItem>
                              <SelectItem value="D">Section D</SelectItem>
                              <SelectItem value="E">Section E</SelectItem>
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="capacity">Section Capacity</Label>
                          <Input
                            id="capacity"
                            type="number"
                            value={formData.capacity}
                            onChange={(e) => setFormData({...formData, capacity: e.target.value})}
                            placeholder="Enter section capacity"
                            min="20"
                            max="40"
                          />
                        </div>
                        <div>
                          <Label htmlFor="teacherId">Class Teacher</Label>
                          <Select value={formData.teacherId} onValueChange={(value) => setFormData({...formData, teacherId: value})}>
                            <SelectTrigger>
                              <SelectValue placeholder="Select class teacher" />
                            </SelectTrigger>
                            <SelectContent>
                              {availableTeachers.map((teacher) => (
                                <SelectItem key={teacher.id} value={teacher.id}>
                                  {teacher.name}
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>
                        </div>
                      </div>
                    </>
                  )}

                  {addType === "batch" && (
                    <>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <Label htmlFor="academicYear">Academic Year</Label>
                          <Input
                            id="academicYear"
                            value={formData.academicYear}
                            onChange={(e) => setFormData({...formData, academicYear: e.target.value})}
                            placeholder="e.g., 2024-25"
                          />
                        </div>
                        <div>
                          <Label htmlFor="batchName">Batch Name</Label>
                          <Input
                            id="batchName"
                            value={formData.batchName}
                            onChange={(e) => setFormData({...formData, batchName: e.target.value})}
                            placeholder="e.g., Academic Session 2024-25"
                          />
                        </div>
                      </div>
                      <div>
                        <Label htmlFor="description">Description</Label>
                        <Textarea
                          id="description"
                          value={formData.description}
                          onChange={(e) => setFormData({...formData, description: e.target.value})}
                          placeholder="Enter batch description"
                          rows={3}
                        />
                      </div>
                    </>
                  )}
                </div>
                <DialogFooter>
                  <Button variant="outline" onClick={() => setShowAddDialog(false)}>
                    Cancel
                  </Button>
                  <Button
                    onClick={handleSubmitAdd}
                    disabled={
                      (addType === "subject" && (!formData.classLevel || !formData.section || !formData.subject)) ||
                      (addType === "section" && (!formData.classLevel || !formData.section || !formData.capacity)) ||
                      (addType === "batch" && (!formData.academicYear || !formData.batchName))
                    }
                  >
                    Add {addType.charAt(0).toUpperCase() + addType.slice(1)}
                  </Button>
                </DialogFooter>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Content Area */}
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Academic Overview Cards */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Class Levels</p>
                      <p className="text-2xl font-bold text-gray-900">{data.overview?.totalClasses}</p>
                      <p className="text-xs text-gray-500">Nursery to Grade 12</p>
                    </div>
                    <BookOpen className="h-8 w-8 text-blue-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Sections</p>
                      <p className="text-2xl font-bold text-gray-900">{data.overview?.totalSections}</p>
                      <p className="text-xs text-gray-500">Across all classes</p>
                    </div>
                    <GraduationCap className="h-8 w-8 text-green-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Avg Performance</p>
                      <p className="text-2xl font-bold text-gray-900">{data.overview?.averagePerformance}%</p>
                    </div>
                    <TrendingUp className="h-8 w-8 text-purple-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Attendance</p>
                      <p className="text-2xl font-bold text-gray-900">{data.overview?.attendanceRate}%</p>
                    </div>
                    <Calendar className="h-8 w-8 text-orange-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Teacher:Student</p>
                      <p className="text-2xl font-bold text-gray-900">{data.overview?.teacherStudentRatio}</p>
                    </div>
                    <Users className="h-8 w-8 text-red-600" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Quick Actions */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Button
                className="h-auto p-6 flex flex-col items-start space-y-2"
                variant="outline"
                onClick={() => router.push("/principal/curriculum")}
              >
                <BookOpen className="h-6 w-6 text-blue-600" />
                <div className="text-left">
                  <div className="font-medium">Curriculum Planning</div>
                  <div className="text-sm text-gray-600">Design and update curriculum</div>
                </div>
              </Button>

              <Button
                className="h-auto p-6 flex flex-col items-start space-y-2"
                variant="outline"
                onClick={() => router.push("/principal/calendar")}
              >
                <Calendar className="h-6 w-6 text-green-600" />
                <div className="text-left">
                  <div className="font-medium">Academic Calendar</div>
                  <div className="text-sm text-gray-600">Plan academic year events</div>
                </div>
              </Button>

              <Button
                className="h-auto p-6 flex flex-col items-start space-y-2"
                variant="outline"
                onClick={() => router.push("/principal/performance")}
              >
                <BarChart3 className="h-6 w-6 text-purple-600" />
                <div className="text-left">
                  <div className="font-medium">Performance Analysis</div>
                  <div className="text-sm text-gray-600">Analyze academic performance</div>
                </div>
              </Button>

              <Button
                className="h-auto p-6 flex flex-col items-start space-y-2"
                variant="outline"
                onClick={() => router.push("/principal/analytics/academic")}
              >
                <Target className="h-6 w-6 text-orange-600" />
                <div className="text-left">
                  <div className="font-medium">Academic Analytics</div>
                  <div className="text-sm text-gray-600">Detailed academic insights</div>
                </div>
              </Button>
            </div>

            {/* Add New Items Section */}
            <Card>
              <CardHeader>
                <CardTitle>Academic Management</CardTitle>
                <p className="text-sm text-gray-600">
                  Manage school academic structure: Classes (Nursery to Grade 12) → Sections (A, B, C) → Subjects
                </p>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-3">
                  <Button
                    className="h-auto p-4 flex flex-col items-center space-y-2"
                    variant="outline"
                    onClick={() => handleAddNew("subject")}
                  >
                    <Plus className="h-6 w-6 text-blue-600" />
                    <div className="text-center">
                      <div className="font-medium">Add Subject to Class</div>
                      <div className="text-sm text-gray-600">Assign subject to specific class & section</div>
                    </div>
                  </Button>

                  <Button
                    className="h-auto p-4 flex flex-col items-center space-y-2"
                    variant="outline"
                    onClick={() => handleAddNew("section")}
                  >
                    <Plus className="h-6 w-6 text-green-600" />
                    <div className="text-center">
                      <div className="font-medium">Add Section</div>
                      <div className="text-sm text-gray-600">Create new section for a class</div>
                    </div>
                  </Button>

                  <Button
                    className="h-auto p-4 flex flex-col items-center space-y-2"
                    variant="outline"
                    onClick={() => handleAddNew("batch")}
                  >
                    <Plus className="h-6 w-6 text-purple-600" />
                    <div className="text-center">
                      <div className="font-medium">Add Academic Batch</div>
                      <div className="text-sm text-gray-600">Create new academic session</div>
                    </div>
                  </Button>
                </div>
              </CardContent>
            </Card>

            {/* Class Structure Overview */}
            <Card>
              <CardHeader>
                <CardTitle>School Class Structure</CardTitle>
                <p className="text-sm text-gray-600">
                  Overview of all class levels, sections, and subjects
                </p>
              </CardHeader>
              <CardContent>
                <div className="space-y-4 max-h-96 overflow-y-auto">
                  {data.classStructure?.map((classLevel: any, index: number) => (
                    <div key={index} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center space-x-3">
                          <div className="p-2 rounded-lg bg-blue-100">
                            <BookOpen className="h-5 w-5 text-blue-600" />
                          </div>
                          <div>
                            <h4 className="font-medium">{classLevel.level}</h4>
                            <p className="text-sm text-gray-600">
                              {classLevel.sections.length} sections • Capacity: {classLevel.capacity} each
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline">
                            {classLevel.sections.join(", ")}
                          </Badge>
                          <Button variant="outline" size="sm">
                            <Eye className="h-4 w-4 mr-2" />
                            Manage
                          </Button>
                        </div>
                      </div>
                      <div className="pl-11">
                        <div className="text-sm text-gray-600">
                          <strong>Subjects:</strong> {classLevel.subjects.join(", ")}
                        </div>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </AppLayout>
  );
}