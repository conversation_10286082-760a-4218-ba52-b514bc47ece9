"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, <PERSON>bsContent, <PERSON>bs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
  ArrowLeft,
  Plus,
  Search,
  Filter,
  Download,
  RefreshCw,
  Eye,
  Edit,
  MoreHorizontal,
  BookOpen,
  GraduationCap,
  TrendingUp,
  TrendingDown,
  Award,
  Calendar,
  Users,
  BarChart3,
  Target,
  CheckCircle,
  AlertTriangle,
  Clock
} from "lucide-react";
import { toast } from "sonner";

export default function PrincipalAcademicPage() {
  const [user, setUser] = useState<any>(null);
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [data, setData] = useState([]);

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "principal") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
    loadData();
  }, [router]);

  const loadData = async () => {
    try {
      setLoading(true);

      // Mock academic data
      const mockData = {
        overview: {
          totalClasses: 45,
          totalSubjects: 12,
          averagePerformance: 87.5,
          attendanceRate: 94.2,
          teacherStudentRatio: "1:28"
        },
        subjects: [
          { name: "Mathematics", classes: 8, teachers: 6, avgScore: 85.2, trend: "up" },
          { name: "Science", classes: 8, teachers: 5, avgScore: 88.7, trend: "up" },
          { name: "English", classes: 8, teachers: 4, avgScore: 82.1, trend: "stable" },
          { name: "Social Studies", classes: 6, teachers: 3, avgScore: 79.8, trend: "down" },
          { name: "Computer Science", classes: 4, teachers: 2, avgScore: 91.3, trend: "up" }
        ],
        recentActivities: [
          { id: 1, type: "curriculum", title: "New AI/ML Module Approved", date: "2024-01-20", status: "completed" },
          { id: 2, type: "assessment", title: "Mid-term Evaluations", date: "2024-01-18", status: "in_progress" },
          { id: 3, type: "planning", title: "Next Academic Year Planning", date: "2024-01-15", status: "pending" }
        ]
      };

      setData(mockData);
      toast.success("Academic data loaded successfully");
    } catch (error) {
      console.error("Error loading data:", error);
      toast.error("Failed to load data");
    } finally {
      setLoading(false);
    }
  };

  if (!user) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.back()}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900"> - Academic</h1>
              <p className="text-gray-600">
                Principal academic management and oversight
              </p>
            </div>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={loadData}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add New
            </Button>
          </div>
        </div>

        {/* Content Area */}
        {loading ? (
          <div className="flex items-center justify-center h-64">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
          </div>
        ) : (
          <div className="space-y-6">
            {/* Academic Overview Cards */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Total Classes</p>
                      <p className="text-2xl font-bold text-gray-900">{data.overview?.totalClasses}</p>
                    </div>
                    <BookOpen className="h-8 w-8 text-blue-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Subjects</p>
                      <p className="text-2xl font-bold text-gray-900">{data.overview?.totalSubjects}</p>
                    </div>
                    <GraduationCap className="h-8 w-8 text-green-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Avg Performance</p>
                      <p className="text-2xl font-bold text-gray-900">{data.overview?.averagePerformance}%</p>
                    </div>
                    <TrendingUp className="h-8 w-8 text-purple-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Attendance</p>
                      <p className="text-2xl font-bold text-gray-900">{data.overview?.attendanceRate}%</p>
                    </div>
                    <Calendar className="h-8 w-8 text-orange-600" />
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardContent className="p-6">
                  <div className="flex items-center justify-between">
                    <div>
                      <p className="text-sm font-medium text-gray-600">Teacher:Student</p>
                      <p className="text-2xl font-bold text-gray-900">{data.overview?.teacherStudentRatio}</p>
                    </div>
                    <Users className="h-8 w-8 text-red-600" />
                  </div>
                </CardContent>
              </Card>
            </div>

            {/* Quick Actions */}
            <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
              <Button
                className="h-auto p-6 flex flex-col items-start space-y-2"
                variant="outline"
                onClick={() => router.push("/principal/curriculum")}
              >
                <BookOpen className="h-6 w-6 text-blue-600" />
                <div className="text-left">
                  <div className="font-medium">Curriculum Planning</div>
                  <div className="text-sm text-gray-600">Design and update curriculum</div>
                </div>
              </Button>

              <Button
                className="h-auto p-6 flex flex-col items-start space-y-2"
                variant="outline"
                onClick={() => router.push("/principal/calendar")}
              >
                <Calendar className="h-6 w-6 text-green-600" />
                <div className="text-left">
                  <div className="font-medium">Academic Calendar</div>
                  <div className="text-sm text-gray-600">Plan academic year events</div>
                </div>
              </Button>

              <Button
                className="h-auto p-6 flex flex-col items-start space-y-2"
                variant="outline"
                onClick={() => router.push("/principal/performance")}
              >
                <BarChart3 className="h-6 w-6 text-purple-600" />
                <div className="text-left">
                  <div className="font-medium">Performance Analysis</div>
                  <div className="text-sm text-gray-600">Analyze academic performance</div>
                </div>
              </Button>

              <Button
                className="h-auto p-6 flex flex-col items-start space-y-2"
                variant="outline"
                onClick={() => router.push("/principal/analytics/academic")}
              >
                <Target className="h-6 w-6 text-orange-600" />
                <div className="text-left">
                  <div className="font-medium">Academic Analytics</div>
                  <div className="text-sm text-gray-600">Detailed academic insights</div>
                </div>
              </Button>
            </div>

            {/* Subject Performance */}
            <Card>
              <CardHeader>
                <CardTitle>Subject Performance Overview</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {data.subjects?.map((subject, index) => (
                    <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className="p-2 rounded-lg bg-blue-100">
                          <BookOpen className="h-5 w-5 text-blue-600" />
                        </div>
                        <div>
                          <h4 className="font-medium">{subject.name}</h4>
                          <p className="text-sm text-gray-600">
                            {subject.classes} classes • {subject.teachers} teachers
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center space-x-4">
                        <div className="text-right">
                          <div className="font-medium">{subject.avgScore}%</div>
                          <div className="text-sm text-gray-600">Average Score</div>
                        </div>
                        <div className="flex items-center">
                          {subject.trend === "up" && <TrendingUp className="h-4 w-4 text-green-500" />}
                          {subject.trend === "down" && <TrendingDown className="h-4 w-4 text-red-500" />}
                          {subject.trend === "stable" && <BarChart3 className="h-4 w-4 text-gray-500" />}
                        </div>
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-2" />
                          View Details
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        )}
      </div>
    </AppLayout>
  );
}