const fs = require('fs');
const path = require('path');

console.log("=== COMPREHENSIVE PRINCIPAL ROUTES TEST ===\n");

// All principal routes that should exist
const principalRoutes = [
  "/principal/dashboard",
  "/principal/academic",
  "/principal/curriculum", 
  "/principal/calendar",
  "/principal/performance",
  "/principal/staff",
  "/principal/staff/reviews",
  "/principal/staff/development",
  "/principal/staff/reports",
  "/principal/students",
  "/principal/students/discipline",
  "/principal/students/achievements",
  "/principal/students/welfare",
  "/principal/finance",
  "/principal/finance/approvals",
  "/principal/finance/reports",
  "/principal/finance/fees",
  "/principal/parents",
  "/principal/parents/meetings",
  "/principal/parents/feedback",
  "/principal/parents/communication",
  "/principal/planning",
  "/principal/planning/quality",
  "/principal/planning/external",
  "/principal/analytics",
  "/principal/analytics/academic",
  "/principal/analytics/performance",
  "/principal/analytics/comparison",
  "/principal/approvals"
];

console.log(`🔍 Testing ${principalRoutes.length} Principal Routes...\n`);

let results = {
  existing: 0,
  missing: 0,
  enhanced: 0,
  basic: 0,
  details: []
};

principalRoutes.forEach(route => {
  const filePath = path.join(__dirname, 'app', route, 'page.tsx');
  
  if (fs.existsSync(filePath)) {
    results.existing++;
    
    // Check if page is enhanced or basic
    const content = fs.readFileSync(filePath, 'utf8');
    const isEnhanced = content.includes('mockData') || content.includes('Tabs') || content.includes('CardContent');
    
    if (isEnhanced) {
      results.enhanced++;
      console.log(`✅ ${route} - EXISTS (Enhanced)`);
      results.details.push({ route, status: 'enhanced', exists: true });
    } else {
      results.basic++;
      console.log(`⚠️ ${route} - EXISTS (Basic)`);
      results.details.push({ route, status: 'basic', exists: true });
    }
  } else {
    results.missing++;
    console.log(`❌ ${route} - MISSING`);
    results.details.push({ route, status: 'missing', exists: false });
  }
});

console.log(`\n📊 PRINCIPAL ROUTES SUMMARY:`);
console.log(`✅ Total Existing: ${results.existing}/${principalRoutes.length} (${Math.round((results.existing/principalRoutes.length)*100)}%)`);
console.log(`🎨 Enhanced Pages: ${results.enhanced}`);
console.log(`📄 Basic Pages: ${results.basic}`);
console.log(`❌ Missing Pages: ${results.missing}`);

// Test navigation config
console.log(`\n🔍 Testing Navigation Configuration...`);
try {
  const navConfigPath = path.join(__dirname, 'lib/navigation-config.ts');
  const navContent = fs.readFileSync(navConfigPath, 'utf8');
  
  let navRoutes = 0;
  principalRoutes.forEach(route => {
    if (navContent.includes(`"${route}"`)) {
      navRoutes++;
    }
  });
  
  console.log(`✅ Navigation Config: ${navRoutes}/${principalRoutes.length} routes configured`);
} catch (error) {
  console.log(`❌ Navigation Config: Error reading file`);
}

// Test API endpoints
console.log(`\n🔍 Testing Principal API Endpoints...`);
try {
  const apiPath = path.join(__dirname, 'app/api/[[...route]]/principal.ts');
  if (fs.existsSync(apiPath)) {
    const apiContent = fs.readFileSync(apiPath, 'utf8');
    const endpoints = ['/dashboard', '/approvals', '/analytics'];
    let foundEndpoints = 0;
    
    endpoints.forEach(endpoint => {
      if (apiContent.includes(endpoint)) {
        foundEndpoints++;
      }
    });
    
    console.log(`✅ API Endpoints: ${foundEndpoints}/${endpoints.length} endpoints available`);
  } else {
    console.log(`❌ API Endpoints: Principal API file missing`);
  }
} catch (error) {
  console.log(`❌ API Endpoints: Error reading file`);
}

// Test authentication
console.log(`\n🔍 Testing Principal Authentication...`);
try {
  const mockDbPath = path.join(__dirname, 'lib/mock-db.ts');
  const mockDbContent = fs.readFileSync(mockDbPath, 'utf8');
  
  if (mockDbContent.includes('role: "principal"')) {
    console.log(`✅ Authentication: Principal user configured`);
    
    if (mockDbContent.includes('principalData:')) {
      console.log(`✅ Authentication: Enhanced principal profile data`);
    } else {
      console.log(`⚠️ Authentication: Basic principal profile`);
    }
  } else {
    console.log(`❌ Authentication: Principal user not found`);
  }
} catch (error) {
  console.log(`❌ Authentication: Error reading mock database`);
}

// Generate route accessibility report
console.log(`\n🌐 ROUTE ACCESSIBILITY REPORT:`);
console.log(`📧 Login: <EMAIL>`);
console.log(`🔑 Password: principal123`);
console.log(`🌍 Base URL: http://localhost:3000`);
console.log(`\n📋 Available Routes:`);

const categorizedRoutes = {
  'Academic Leadership': [
    '/principal/academic',
    '/principal/curriculum',
    '/principal/calendar',
    '/principal/performance'
  ],
  'Staff Management': [
    '/principal/staff',
    '/principal/staff/reviews',
    '/principal/staff/development',
    '/principal/staff/reports'
  ],
  'Student Affairs': [
    '/principal/students',
    '/principal/students/discipline',
    '/principal/students/achievements',
    '/principal/students/welfare'
  ],
  'Financial Oversight': [
    '/principal/finance',
    '/principal/finance/approvals',
    '/principal/finance/reports',
    '/principal/finance/fees'
  ],
  'Parent Relations': [
    '/principal/parents',
    '/principal/parents/meetings',
    '/principal/parents/feedback',
    '/principal/parents/communication'
  ],
  'Strategic Planning': [
    '/principal/planning',
    '/principal/planning/quality',
    '/principal/planning/external'
  ],
  'Analytics & Reports': [
    '/principal/analytics',
    '/principal/analytics/academic',
    '/principal/analytics/performance',
    '/principal/analytics/comparison'
  ],
  'Core Functions': [
    '/principal/dashboard',
    '/principal/approvals'
  ]
};

Object.entries(categorizedRoutes).forEach(([category, routes]) => {
  console.log(`\n📁 ${category}:`);
  routes.forEach(route => {
    const routeResult = results.details.find(r => r.route === route);
    const status = routeResult?.status || 'missing';
    const icon = status === 'enhanced' ? '🎨' : status === 'basic' ? '📄' : '❌';
    console.log(`   ${icon} ${route}`);
  });
});

// Overall assessment
console.log(`\n🎯 OVERALL ASSESSMENT:`);
const completionRate = Math.round((results.existing/principalRoutes.length)*100);
const enhancementRate = Math.round((results.enhanced/results.existing)*100);

if (completionRate >= 95) {
  console.log(`🎉 EXCELLENT: ${completionRate}% of principal routes are available!`);
} else if (completionRate >= 80) {
  console.log(`👍 VERY GOOD: ${completionRate}% of principal routes are available.`);
} else if (completionRate >= 60) {
  console.log(`👌 GOOD: ${completionRate}% of principal routes are available.`);
} else {
  console.log(`⚠️ NEEDS IMPROVEMENT: Only ${completionRate}% of principal routes are available.`);
}

if (results.enhanced > 0) {
  console.log(`🎨 Enhancement Rate: ${enhancementRate}% of existing pages are enhanced.`);
}

console.log(`\n📈 RECOMMENDATIONS:`);
if (results.missing > 0) {
  console.log(`1. Create ${results.missing} missing route(s)`);
}
if (results.basic > 0) {
  console.log(`2. Enhance ${results.basic} basic page(s) with better UI and functionality`);
}
if (results.enhanced > 0) {
  console.log(`3. Continue enhancing pages with real data integration`);
}

// Save detailed report
const report = {
  timestamp: new Date().toISOString(),
  summary: {
    totalRoutes: principalRoutes.length,
    existingRoutes: results.existing,
    enhancedRoutes: results.enhanced,
    basicRoutes: results.basic,
    missingRoutes: results.missing,
    completionRate,
    enhancementRate
  },
  routeDetails: results.details,
  categorizedRoutes,
  loginCredentials: {
    email: "<EMAIL>",
    password: "principal123",
    baseUrl: "http://localhost:3000"
  }
};

fs.writeFileSync('principal-routes-complete-report.json', JSON.stringify(report, null, 2));
console.log(`\n📊 Complete report saved to: principal-routes-complete-report.json`);
