"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Bar<PERSON><PERSON>3,
  <PERSON><PERSON><PERSON>,
  TrendingUp,
  TrendingDown,
  Users,
  GraduationCap,
  Award,
  Calendar,
  Download,
  Filter,
  RefreshCw,
  Target,
  CheckCircle,
  AlertTriangle,
  BookOpen,
  Clock,
  IndianRupee,
  FileText,
} from "lucide-react";

export default function AdminAnalytics() {
  const [user, setUser] = useState<any>(null);
  const [selectedPeriod, setSelectedPeriod] = useState("monthly");
  const [selectedMetric, setSelectedMetric] = useState("overview");
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "super_admin" && parsedUser.role !== "admin") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock analytics data
  const analyticsOverview = {
    totalStudents: 1234,
    totalTeachers: 89,
    averageAttendance: 92.5,
    averageGrade: 3.7,
    collectionRate: 94.5,
    graduationRate: 96.8,
    studentSatisfaction: 4.2,
    teacherRetention: 87.3,
  };

  const enrollmentTrends = [
    { month: "Aug", students: 1180, target: 1200, growth: 2.1 },
    { month: "Sep", students: 1205, target: 1200, growth: 2.1 },
    { month: "Oct", students: 1198, target: 1200, growth: -0.6 },
    { month: "Nov", students: 1215, target: 1200, growth: 1.4 },
    { month: "Dec", students: 1228, target: 1200, growth: 1.1 },
    { month: "Jan", students: 1234, target: 1200, growth: 0.5 },
  ];

  const gradeDistribution = [
    { grade: "A+", count: 185, percentage: 15.0, color: "bg-green-500" },
    { grade: "A", count: 247, percentage: 20.0, color: "bg-blue-500" },
    { grade: "B+", count: 370, percentage: 30.0, color: "bg-yellow-500" },
    { grade: "B", count: 309, percentage: 25.0, color: "bg-orange-500" },
    { grade: "C+", count: 123, percentage: 10.0, color: "bg-red-500" },
  ];

  const departmentPerformance = [
    { department: "Science", students: 456, avgGrade: 3.8, attendance: 94.2, satisfaction: 4.3 },
    { department: "Commerce", students: 389, avgGrade: 3.6, attendance: 91.8, satisfaction: 4.1 },
    { department: "Arts", students: 234, avgGrade: 3.9, attendance: 93.5, satisfaction: 4.4 },
    { department: "Computer Science", students: 155, avgGrade: 4.0, attendance: 95.1, satisfaction: 4.5 },
  ];

  const financialMetrics = [
    { metric: "Revenue Growth", value: "+12.5%", trend: "up", color: "text-green-600" },
    { metric: "Cost per Student", value: "₹18,450", trend: "down", color: "text-green-600" },
    { metric: "Fee Collection", value: "94.5%", trend: "up", color: "text-green-600" },
    { metric: "Operating Margin", value: "23.8%", trend: "up", color: "text-green-600" },
  ];

  const keyInsights = [
    {
      title: "Enrollment Growth",
      description: "Student enrollment increased by 2.9% this academic year",
      impact: "Positive",
      recommendation: "Expand infrastructure to accommodate growth",
    },
    {
      title: "Attendance Improvement",
      description: "Overall attendance improved by 3.2% compared to last year",
      impact: "Positive",
      recommendation: "Continue current engagement strategies",
    },
    {
      title: "Grade Distribution",
      description: "65% of students achieving A or B+ grades",
      impact: "Positive",
      recommendation: "Focus on supporting C+ grade students",
    },
    {
      title: "Teacher Retention",
      description: "Teacher retention rate at 87.3%, slightly below target",
      impact: "Neutral",
      recommendation: "Review compensation and professional development programs",
    },
  ];

  const getGradeColor = (grade: string) => {
    switch (grade) {
      case "A+": return "text-green-600";
      case "A": return "text-blue-600";
      case "B+": return "text-yellow-600";
      case "B": return "text-orange-600";
      default: return "text-red-600";
    }
  };

  const getPerformanceColor = (value: number, type: string) => {
    if (type === "grade") {
      return value >= 3.8 ? "text-green-600" : value >= 3.5 ? "text-blue-600" : "text-orange-600";
    }
    if (type === "attendance") {
      return value >= 95 ? "text-green-600" : value >= 90 ? "text-blue-600" : "text-orange-600";
    }
    if (type === "satisfaction") {
      return value >= 4.0 ? "text-green-600" : value >= 3.5 ? "text-blue-600" : "text-orange-600";
    }
    return "text-gray-600";
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Analytics & Reports</h1>
            <p className="text-gray-600">Comprehensive insights and performance analytics</p>
          </div>
          <div className="flex gap-2">
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="weekly">Weekly</option>
              <option value="monthly">Monthly</option>
              <option value="quarterly">Quarterly</option>
              <option value="yearly">Yearly</option>
            </select>
            <Button variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Key Metrics Overview */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {analyticsOverview.totalStudents}
                  </div>
                  <p className="text-sm text-gray-600">Total Students</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600">+2.9%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {analyticsOverview.averageAttendance}%
                  </div>
                  <p className="text-sm text-gray-600">Avg Attendance</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600">+3.2%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Award className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {analyticsOverview.averageGrade}
                  </div>
                  <p className="text-sm text-gray-600">Average GPA</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600">+0.2</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Target className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    {analyticsOverview.graduationRate}%
                  </div>
                  <p className="text-sm text-gray-600">Graduation Rate</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600">+1.5%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Analytics Tabs */}
        <Card>
          <CardHeader>
            <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
              {[
                { key: "overview", label: "Overview" },
                { key: "academic", label: "Academic" },
                { key: "financial", label: "Financial" },
                { key: "insights", label: "Insights" },
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setSelectedMetric(tab.key)}
                  className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                    selectedMetric === tab.key
                      ? "bg-white text-blue-600 shadow-sm"
                      : "text-gray-600 hover:text-gray-900"
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </div>
          </CardHeader>
          <CardContent>
            {/* Overview Tab */}
            {selectedMetric === "overview" && (
              <div className="space-y-6">
                {/* Enrollment Trends */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Enrollment Trends</h3>
                  <div className="grid gap-6 md:grid-cols-6">
                    {enrollmentTrends.map((month, index) => (
                      <div key={index} className="text-center">
                        <div className="text-sm font-medium text-gray-900 mb-2">{month.month}</div>
                        <div className="w-full bg-gray-200 rounded h-24 flex flex-col justify-end mb-2">
                          <div 
                            className={`rounded-b ${
                              month.students >= month.target ? 'bg-green-500' : 'bg-blue-500'
                            }`}
                            style={{ height: `${(month.students / 1300) * 100}%` }}
                          />
                        </div>
                        <div className="text-sm font-bold text-gray-900">{month.students}</div>
                        <div className={`text-xs ${month.growth >= 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {month.growth >= 0 ? '+' : ''}{month.growth}%
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Department Performance */}
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Department Performance</h3>
                  <div className="space-y-3">
                    {departmentPerformance.map((dept, index) => (
                      <div key={index} className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="font-medium text-gray-900">{dept.department}</h4>
                          <span className="text-sm text-gray-500">{dept.students} students</span>
                        </div>
                        <div className="grid gap-4 md:grid-cols-3">
                          <div className="text-center">
                            <div className={`text-lg font-bold ${getPerformanceColor(dept.avgGrade, 'grade')}`}>
                              {dept.avgGrade}
                            </div>
                            <div className="text-xs text-gray-500">Avg GPA</div>
                          </div>
                          <div className="text-center">
                            <div className={`text-lg font-bold ${getPerformanceColor(dept.attendance, 'attendance')}`}>
                              {dept.attendance}%
                            </div>
                            <div className="text-xs text-gray-500">Attendance</div>
                          </div>
                          <div className="text-center">
                            <div className={`text-lg font-bold ${getPerformanceColor(dept.satisfaction, 'satisfaction')}`}>
                              {dept.satisfaction}/5
                            </div>
                            <div className="text-xs text-gray-500">Satisfaction</div>
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Academic Tab */}
            {selectedMetric === "academic" && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Grade Distribution</h3>
                  <div className="space-y-3">
                    {gradeDistribution.map((grade, index) => (
                      <div key={index} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center space-x-3">
                          <div className={`w-4 h-4 ${grade.color} rounded`} />
                          <div>
                            <div className={`font-medium ${getGradeColor(grade.grade)}`}>Grade {grade.grade}</div>
                            <div className="text-sm text-gray-500">{grade.count} students</div>
                          </div>
                        </div>
                        <div className="text-right">
                          <div className="font-bold text-gray-900">{grade.percentage}%</div>
                          <div className="w-24 bg-gray-200 rounded-full h-2 mt-1">
                            <div 
                              className={`h-2 rounded-full ${grade.color}`}
                              style={{ width: `${grade.percentage * 2}%` }}
                            />
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Financial Tab */}
            {selectedMetric === "financial" && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Financial Metrics</h3>
                  <div className="grid gap-4 md:grid-cols-2">
                    {financialMetrics.map((metric, index) => (
                      <div key={index} className="p-4 border rounded-lg">
                        <div className="flex items-center justify-between">
                          <div>
                            <div className="font-medium text-gray-900">{metric.metric}</div>
                            <div className={`text-2xl font-bold ${metric.color}`}>
                              {metric.value}
                            </div>
                          </div>
                          <div className="text-right">
                            {metric.trend === "up" ? (
                              <TrendingUp className="h-6 w-6 text-green-500" />
                            ) : (
                              <TrendingDown className="h-6 w-6 text-red-500" />
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}

            {/* Insights Tab */}
            {selectedMetric === "insights" && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold text-gray-900 mb-4">Key Insights & Recommendations</h3>
                  <div className="space-y-4">
                    {keyInsights.map((insight, index) => (
                      <div key={index} className="p-4 border rounded-lg">
                        <div className="flex items-start justify-between mb-3">
                          <h4 className="font-medium text-gray-900">{insight.title}</h4>
                          <Badge className={
                            insight.impact === "Positive" ? "bg-green-100 text-green-700" :
                            insight.impact === "Neutral" ? "bg-yellow-100 text-yellow-700" :
                            "bg-red-100 text-red-700"
                          }>
                            {insight.impact}
                          </Badge>
                        </div>
                        <p className="text-gray-600 mb-3">{insight.description}</p>
                        <div className="bg-blue-50 p-3 rounded-lg">
                          <div className="text-sm font-medium text-blue-900 mb-1">Recommendation:</div>
                          <div className="text-sm text-blue-700">{insight.recommendation}</div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
