"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  BookOpen,
  Search,
  Filter,
  Download,
  Plus,
  Eye,
  Edit,
  Trash2,
  Calendar,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  User,
  Phone,
  Mail,
  BookMarked,
  History,
} from "lucide-react";

export default function LibraryReservations() {
  const [user, setUser] = useState<any>(null);
  const [selectedTab, setSelectedTab] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedReservation, setSelectedReservation] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "library") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  const reservationStats = {
    totalReservations: 245,
    activeReservations: 156,
    expiredReservations: 67,
    fulfilledReservations: 22,
    pendingReservations: 134,
    cancelledReservations: 89,
  };

  const mockReservations = [
    {
      id: "RES001",
      bookTitle: "Advanced Mathematics",
      bookId: "BK001",
      author: "Dr. R.K. Sharma",
      isbn: "978-81-203-4567-8",
      memberName: "Aarav Sharma",
      memberId: "LM001",
      memberType: "Student",
      class: "Class 12-A",
      reservationDate: "2024-01-15",
      expiryDate: "2024-01-22",
      status: "Active",
      priority: 1,
      notificationSent: true,
    },
    {
      id: "RES002",
      bookTitle: "Physics Fundamentals",
      bookId: "BK002",
      author: "Prof. S.N. Gupta",
      isbn: "978-81-203-4568-9",
      memberName: "Priya Patel",
      memberId: "LM002",
      memberType: "Teacher",
      department: "Physics",
      reservationDate: "2024-01-10",
      expiryDate: "2024-01-17",
      status: "Expired",
      priority: 2,
      notificationSent: true,
    },
    {
      id: "RES003",
      bookTitle: "Chemistry Lab Manual",
      bookId: "BK003",
      author: "Dr. M.K. Singh",
      isbn: "978-81-203-4569-0",
      memberName: "Rahul Kumar",
      memberId: "LM003",
      memberType: "Student",
      class: "Class 11-B",
      reservationDate: "2024-01-18",
      expiryDate: "2024-01-25",
      status: "Fulfilled",
      priority: 1,
      notificationSent: false,
    },
    {
      id: "RES004",
      bookTitle: "English Literature",
      bookId: "BK004",
      author: "Prof. A.K. Verma",
      isbn: "978-81-203-4570-6",
      memberName: "Sneha Reddy",
      memberId: "LM004",
      memberType: "Staff",
      department: "Administration",
      reservationDate: "2024-01-20",
      expiryDate: "2024-01-27",
      status: "Pending",
      priority: 3,
      notificationSent: false,
    },
    {
      id: "RES005",
      bookTitle: "Computer Science Basics",
      bookId: "BK005",
      author: "Dr. V.K. Jain",
      isbn: "978-81-203-4571-3",
      memberName: "Vikram Singh",
      memberId: "LM005",
      memberType: "Student",
      class: "Class 10-C",
      reservationDate: "2024-01-12",
      expiryDate: "2024-01-19",
      status: "Cancelled",
      priority: 2,
      notificationSent: true,
    },
  ];

  const filteredReservations = mockReservations.filter((reservation) => {
    const matchesSearch = reservation.bookTitle.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         reservation.memberName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         reservation.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         reservation.isbn.toLowerCase().includes(searchTerm.toLowerCase());

    if (selectedTab === "all") return matchesSearch;
    if (selectedTab === "active") return matchesSearch && reservation.status === "Active";
    if (selectedTab === "expired") return matchesSearch && reservation.status === "Expired";
    if (selectedTab === "fulfilled") return matchesSearch && reservation.status === "Fulfilled";
    if (selectedTab === "pending") return matchesSearch && reservation.status === "Pending";
    if (selectedTab === "cancelled") return matchesSearch && reservation.status === "Cancelled";

    return matchesSearch;
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Active":
        return <Badge className="bg-green-100 text-green-800">Active</Badge>;
      case "Expired":
        return <Badge className="bg-red-100 text-red-800">Expired</Badge>;
      case "Fulfilled":
        return <Badge className="bg-blue-100 text-blue-800">Fulfilled</Badge>;
      case "Pending":
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case "Cancelled":
        return <Badge className="bg-gray-100 text-gray-800">Cancelled</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }
  };

  const getPriorityBadge = (priority: number) => {
    switch (priority) {
      case 1:
        return <Badge variant="outline" className="text-red-600 border-red-200">High</Badge>;
      case 2:
        return <Badge variant="outline" className="text-yellow-600 border-yellow-200">Medium</Badge>;
      case 3:
        return <Badge variant="outline" className="text-green-600 border-green-200">Low</Badge>;
      default:
        return <Badge variant="outline">Normal</Badge>;
    }
  };

  const viewReservationDetails = (reservation: any) => {
    setSelectedReservation(reservation);
  };

  const fulfillReservation = (reservationId: string) => {
    alert(`Fulfilling reservation ${reservationId}`);
  };

  const cancelReservation = (reservationId: string) => {
    alert(`Cancelling reservation ${reservationId}`);
  };

  const sendNotification = (reservationId: string) => {
    alert(`Sending notification for reservation ${reservationId}`);
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Book Reservations</h1>
            <p className="text-gray-600">Manage book reservations and waiting lists</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Reservation
            </Button>
          </div>
        </div>

        {/* Reservation Statistics */}
        <div className="grid gap-4 md:grid-cols-3 lg:grid-cols-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <BookMarked className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {reservationStats.totalReservations}
                  </div>
                  <p className="text-sm text-gray-600">Total</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {reservationStats.activeReservations}
                  </div>
                  <p className="text-sm text-gray-600">Active</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-yellow-600">
                    {reservationStats.pendingReservations}
                  </div>
                  <p className="text-sm text-gray-600">Pending</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <XCircle className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-red-600">
                    {reservationStats.expiredReservations}
                  </div>
                  <p className="text-sm text-gray-600">Expired</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <BookOpen className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {reservationStats.fulfilledReservations}
                  </div>
                  <p className="text-sm text-gray-600">Fulfilled</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <AlertTriangle className="h-8 w-8 text-gray-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-gray-600">
                    {reservationStats.cancelledReservations}
                  </div>
                  <p className="text-sm text-gray-600">Cancelled</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filter */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search by book title, member name, or reservation ID..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </div>

            {/* Tabs */}
            <div className="flex flex-wrap gap-2 mt-4">
              {[
                { key: "all", label: "All", count: reservationStats.totalReservations },
                { key: "active", label: "Active", count: reservationStats.activeReservations },
                { key: "pending", label: "Pending", count: reservationStats.pendingReservations },
                { key: "expired", label: "Expired", count: reservationStats.expiredReservations },
                { key: "fulfilled", label: "Fulfilled", count: reservationStats.fulfilledReservations },
                { key: "cancelled", label: "Cancelled", count: reservationStats.cancelledReservations },
              ].map((tab) => (
                <Button
                  key={tab.key}
                  variant={selectedTab === tab.key ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedTab(tab.key)}
                  className="text-xs"
                >
                  {tab.label} ({tab.count})
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Reservations Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BookMarked className="h-5 w-5 mr-2" />
              Reservations List ({filteredReservations.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-4 font-medium text-gray-900">Book Details</th>
                    <th className="text-left p-4 font-medium text-gray-900">Member</th>
                    <th className="text-left p-4 font-medium text-gray-900">Dates</th>
                    <th className="text-left p-4 font-medium text-gray-900">Status</th>
                    <th className="text-left p-4 font-medium text-gray-900">Priority</th>
                    <th className="text-left p-4 font-medium text-gray-900">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredReservations.map((reservation) => (
                    <tr key={reservation.id} className="border-b hover:bg-gray-50">
                      <td className="p-4">
                        <div className="space-y-1">
                          <div className="font-medium text-gray-900">{reservation.bookTitle}</div>
                          <div className="text-sm text-gray-500">by {reservation.author}</div>
                          <div className="text-sm text-gray-500">ISBN: {reservation.isbn}</div>
                          <div className="text-sm text-gray-500">ID: {reservation.bookId}</div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <User className="h-5 w-5 text-blue-600" />
                          </div>
                          <div>
                            <div className="font-medium text-gray-900">{reservation.memberName}</div>
                            <div className="text-sm text-gray-500">{reservation.memberId}</div>
                            <div className="text-sm text-gray-500">
                              {reservation.memberType === "Student" ? reservation.class : reservation.department}
                            </div>
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-1">
                          <div className="text-sm">
                            <span className="font-medium">Reserved:</span>
                            <div className="text-gray-500">{new Date(reservation.reservationDate).toLocaleDateString()}</div>
                          </div>
                          <div className="text-sm">
                            <span className="font-medium">Expires:</span>
                            <div className="text-gray-500">{new Date(reservation.expiryDate).toLocaleDateString()}</div>
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-2">
                          {getStatusBadge(reservation.status)}
                          {reservation.notificationSent && (
                            <div className="text-xs text-green-600 flex items-center">
                              <CheckCircle className="h-3 w-3 mr-1" />
                              Notified
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="p-4">
                        {getPriorityBadge(reservation.priority)}
                      </td>
                      <td className="p-4">
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => viewReservationDetails(reservation)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          {reservation.status === "Active" && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => fulfillReservation(reservation.id)}
                            >
                              <CheckCircle className="h-4 w-4" />
                            </Button>
                          )}
                          {!reservation.notificationSent && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => sendNotification(reservation.id)}
                            >
                              <Mail className="h-4 w-4" />
                            </Button>
                          )}
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => cancelReservation(reservation.id)}
                          >
                            <XCircle className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}