import { config } from "dotenv";
import { db } from "./index";
import { academicPrograms, academicBatches, students, classes } from "./schema";

config({ path: ".env.local" });

async function seedSimpleClasses() {
  console.log("🏫 Seeding simple school classes...");

  try {
    // Clear existing data in the correct order to avoid foreign key constraints
    console.log("🗑️ Clearing existing data...");
    await db.delete(students);
    await db.delete(classes);
    await db.delete(academicBatches);
    await db.delete(academicPrograms);
    console.log("✅ Cleared existing data");

    // Insert school classes using the current structure (without new fields)
    const schoolClasses = [
      {
        name: "Nursery",
        code: "NUR",
        type: "school" as const,
        duration: 1,
        totalSeats: 25,
        admissionStatus: "open" as const,
        status: "active" as const,
        description: "Pre-primary education for children aged 3-4 years"
      },
      {
        name: "LKG",
        code: "LKG",
        type: "school" as const,
        duration: 1,
        totalSeats: 25,
        admissionStatus: "open" as const,
        status: "active" as const,
        description: "Lower Kindergarten for children aged 4-5 years"
      },
      {
        name: "UKG",
        code: "UKG",
        type: "school" as const,
        duration: 1,
        totalSeats: 25,
        admissionStatus: "open" as const,
        status: "active" as const,
        description: "Upper Kindergarten for children aged 5-6 years"
      },
      {
        name: "Grade 1",
        code: "GR1",
        type: "school" as const,
        duration: 1,
        totalSeats: 30,
        admissionStatus: "open" as const,
        status: "active" as const,
        description: "Primary education - Grade 1"
      },
      {
        name: "Grade 2",
        code: "GR2",
        type: "school" as const,
        duration: 1,
        totalSeats: 30,
        admissionStatus: "open" as const,
        status: "active" as const,
        description: "Primary education - Grade 2"
      },
      {
        name: "Grade 3",
        code: "GR3",
        type: "school" as const,
        duration: 1,
        totalSeats: 30,
        admissionStatus: "open" as const,
        status: "active" as const,
        description: "Primary education - Grade 3"
      },
      {
        name: "Grade 4",
        code: "GR4",
        type: "school" as const,
        duration: 1,
        totalSeats: 30,
        admissionStatus: "open" as const,
        status: "active" as const,
        description: "Primary education - Grade 4"
      },
      {
        name: "Grade 5",
        code: "GR5",
        type: "school" as const,
        duration: 1,
        totalSeats: 30,
        admissionStatus: "open" as const,
        status: "active" as const,
        description: "Primary education - Grade 5"
      },
      {
        name: "Grade 6",
        code: "GR6",
        type: "school" as const,
        duration: 1,
        totalSeats: 30,
        admissionStatus: "open" as const,
        status: "active" as const,
        description: "Middle school - Grade 6"
      },
      {
        name: "Grade 7",
        code: "GR7",
        type: "school" as const,
        duration: 1,
        totalSeats: 30,
        admissionStatus: "open" as const,
        status: "active" as const,
        description: "Middle school - Grade 7"
      },
      {
        name: "Grade 8",
        code: "GR8",
        type: "school" as const,
        duration: 1,
        totalSeats: 30,
        admissionStatus: "open" as const,
        status: "active" as const,
        description: "Middle school - Grade 8"
      },
      {
        name: "Grade 9",
        code: "GR9",
        type: "school" as const,
        duration: 1,
        totalSeats: 30,
        admissionStatus: "open" as const,
        status: "active" as const,
        description: "High school - Grade 9"
      },
      {
        name: "Grade 10",
        code: "GR10",
        type: "school" as const,
        duration: 1,
        totalSeats: 30,
        admissionStatus: "open" as const,
        status: "active" as const,
        description: "High school - Grade 10 (Board exam year)"
      },
      {
        name: "Grade 11 Science",
        code: "GR11S",
        type: "school" as const,
        duration: 1,
        totalSeats: 30,
        admissionStatus: "open" as const,
        status: "active" as const,
        description: "Senior secondary - Grade 11 Science stream"
      },
      {
        name: "Grade 11 Commerce",
        code: "GR11C",
        type: "school" as const,
        duration: 1,
        totalSeats: 30,
        admissionStatus: "open" as const,
        status: "active" as const,
        description: "Senior secondary - Grade 11 Commerce stream"
      },
      {
        name: "Grade 11 Arts",
        code: "GR11A",
        type: "school" as const,
        duration: 1,
        totalSeats: 30,
        admissionStatus: "open" as const,
        status: "active" as const,
        description: "Senior secondary - Grade 11 Arts stream"
      },
      {
        name: "Grade 12 Science",
        code: "GR12S",
        type: "school" as const,
        duration: 1,
        totalSeats: 30,
        admissionStatus: "open" as const,
        status: "active" as const,
        description: "Senior secondary - Grade 12 Science stream (Board exam year)"
      },
      {
        name: "Grade 12 Commerce",
        code: "GR12C",
        type: "school" as const,
        duration: 1,
        totalSeats: 30,
        admissionStatus: "open" as const,
        status: "active" as const,
        description: "Senior secondary - Grade 12 Commerce stream (Board exam year)"
      },
      {
        name: "Grade 12 Arts",
        code: "GR12A",
        type: "school" as const,
        duration: 1,
        totalSeats: 30,
        admissionStatus: "open" as const,
        status: "active" as const,
        description: "Senior secondary - Grade 12 Arts stream (Board exam year)"
      }
    ];

    // Insert all classes
    await db.insert(academicPrograms).values(schoolClasses);

    console.log("✅ Successfully seeded school classes!");
    console.log(`📚 Created ${schoolClasses.length} school classes from Nursery to Grade 12`);
    console.log("🎯 Classes include all streams for Grade 11 and 12");

  } catch (error) {
    console.error("❌ Error seeding school classes:", error);
    throw error;
  }
}

// Run the seeding
seedSimpleClasses()
  .then(() => {
    console.log("🎉 School classes seeding completed!");
    process.exit(0);
  })
  .catch((error) => {
    console.error("💥 School classes seeding failed:", error);
    process.exit(1);
  });
