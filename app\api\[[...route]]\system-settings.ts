import { <PERSON>o } from "hono";
import { zValidator } from "@hono/zod-validator";
import { z } from "zod";
import { db } from "@/lib/db";
import { systemSettings } from "@/lib/db/schema";
import { eq, and } from "drizzle-orm";

const app = new Hono()

  // Get all settings or by category
  .get("/", async (c) => {
    try {
      const category = c.req.query("category");
      
      const settings = await db
        .select()
        .from(systemSettings)
        .where(category ? eq(systemSettings.category, category) : undefined);
      
      // Transform to nested object structure
      const settingsObject: Record<string, Record<string, any>> = {};
      
      settings.forEach(setting => {
        if (!settingsObject[setting.category]) {
          settingsObject[setting.category] = {};
        }
        
        // Parse value based on data type
        let value: any = setting.value;
        switch (setting.dataType) {
          case 'boolean':
            value = setting.value === 'true';
            break;
          case 'number':
            value = parseFloat(setting.value);
            break;
          case 'json':
            try {
              value = JSON.parse(setting.value);
            } catch {
              value = setting.value;
            }
            break;
          default:
            value = setting.value;
        }
        
        settingsObject[setting.category][setting.key] = value;
      });
      
      return c.json({ data: settingsObject });
    } catch (error) {
      console.error("Error fetching settings:", error);
      return c.json({ error: "Failed to fetch settings" }, 500);
    }
  })

  // Update settings
  .put(
    "/",
    zValidator("json", z.object({
      category: z.string(),
      settings: z.record(z.any()),
    })),
    async (c) => {
      try {
        const { category, settings } = c.req.valid("json");
        
        // Update each setting
        for (const [key, value] of Object.entries(settings)) {
          // Determine data type
          let dataType = 'string';
          let stringValue = String(value);
          
          if (typeof value === 'boolean') {
            dataType = 'boolean';
            stringValue = value.toString();
          } else if (typeof value === 'number') {
            dataType = 'number';
            stringValue = value.toString();
          } else if (typeof value === 'object') {
            dataType = 'json';
            stringValue = JSON.stringify(value);
          }
          
          // Check if setting exists
          const [existingSetting] = await db
            .select()
            .from(systemSettings)
            .where(and(
              eq(systemSettings.category, category),
              eq(systemSettings.key, key)
            ));
          
          if (existingSetting) {
            // Update existing setting
            await db
              .update(systemSettings)
              .set({
                value: stringValue,
                dataType,
                updatedAt: new Date(),
              })
              .where(eq(systemSettings.id, existingSetting.id));
          } else {
            // Create new setting
            await db
              .insert(systemSettings)
              .values({
                category,
                key,
                value: stringValue,
                dataType,
                isSystem: false,
              });
          }
        }
        
        return c.json({ message: "Settings updated successfully" });
      } catch (error) {
        console.error("Error updating settings:", error);
        return c.json({ error: "Failed to update settings" }, 500);
      }
    }
  )

  // Get specific setting
  .get("/:category/:key", async (c) => {
    try {
      const category = c.req.param("category");
      const key = c.req.param("key");
      
      const [setting] = await db
        .select()
        .from(systemSettings)
        .where(and(
          eq(systemSettings.category, category),
          eq(systemSettings.key, key)
        ));
      
      if (!setting) {
        return c.json({ error: "Setting not found" }, 404);
      }
      
      // Parse value based on data type
      let value: any = setting.value;
      switch (setting.dataType) {
        case 'boolean':
          value = setting.value === 'true';
          break;
        case 'number':
          value = parseFloat(setting.value);
          break;
        case 'json':
          try {
            value = JSON.parse(setting.value);
          } catch {
            value = setting.value;
          }
          break;
      }
      
      return c.json({ 
        data: {
          ...setting,
          value
        }
      });
    } catch (error) {
      console.error("Error fetching setting:", error);
      return c.json({ error: "Failed to fetch setting" }, 500);
    }
  })

  // Reset settings to defaults
  .post("/reset", async (c) => {
    try {
      const category = c.req.query("category");
      
      if (category) {
        // Reset specific category
        await db
          .delete(systemSettings)
          .where(and(
            eq(systemSettings.category, category),
            eq(systemSettings.isSystem, false)
          ));
      } else {
        // Reset all non-system settings
        await db
          .delete(systemSettings)
          .where(eq(systemSettings.isSystem, false));
      }
      
      return c.json({ message: "Settings reset successfully" });
    } catch (error) {
      console.error("Error resetting settings:", error);
      return c.json({ error: "Failed to reset settings" }, 500);
    }
  });

export default app;
