"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  ArrowLeft,
  Plus,
  Search,
  Filter,
  Download,
  RefreshCw,
  Eye,
  Edit,
  MoreHorizontal,
  TrendingUp,
  Users,
  GraduationCap,
  Calendar,
  CheckCircle,
  AlertTriangle,
  Clock,
  ArrowRight,
  UserCheck,
  UserX,
  Settings,
  FileText,
  Award,
  Target,
  Zap
} from "lucide-react";
import { toast } from "sonner";

interface Student {
  id: string;
  name: string;
  rollNumber: string;
  currentClass: string;
  currentSection: string;
  totalMarks: number;
  percentage: number;
  attendance: number;
  status: "pass" | "fail" | "detained";
  nextClass?: string;
  nextSection?: string;
  remarks?: string;
}

interface PromotionCriteria {
  minimumPercentage: number;
  minimumAttendance: number;
  graceMarks: number;
  detentionThreshold: number;
}

export default function PrincipalPromotionPage() {
  const [user, setUser] = useState<any>(null);
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("bulk-promotion");
  const [selectedSession, setSelectedSession] = useState("2024-25");
  const [selectedClass, setSelectedClass] = useState("");
  const [students, setStudents] = useState<Student[]>([]);
  const [promotionCriteria, setPromotionCriteria] = useState<PromotionCriteria>({
    minimumPercentage: 40,
    minimumAttendance: 75,
    graceMarks: 5,
    detentionThreshold: 25
  });
  const [showCriteriaDialog, setShowCriteriaDialog] = useState(false);
  const [showNewSessionDialog, setShowNewSessionDialog] = useState(false);
  const [promotionStats, setPromotionStats] = useState({
    totalStudents: 0,
    eligible: 0,
    detained: 0,
    failed: 0
  });

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "principal") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
    loadPromotionData();
  }, [router]);

  const loadPromotionData = async () => {
    try {
      setLoading(true);
      
      // Mock promotion data
      const mockStudents: Student[] = [
        {
          id: "1",
          name: "Aarav Sharma",
          rollNumber: "GR5A001",
          currentClass: "Grade 5",
          currentSection: "A",
          totalMarks: 450,
          percentage: 90,
          attendance: 95,
          status: "pass",
          nextClass: "Grade 6",
          nextSection: "A"
        },
        {
          id: "2",
          name: "Priya Patel",
          rollNumber: "GR5A002",
          currentClass: "Grade 5",
          currentSection: "A",
          totalMarks: 380,
          percentage: 76,
          attendance: 88,
          status: "pass",
          nextClass: "Grade 6",
          nextSection: "A"
        },
        {
          id: "3",
          name: "Rahul Kumar",
          rollNumber: "GR5A003",
          currentClass: "Grade 5",
          currentSection: "A",
          totalMarks: 180,
          percentage: 36,
          attendance: 65,
          status: "fail",
          remarks: "Below minimum percentage and attendance"
        },
        {
          id: "4",
          name: "Ananya Singh",
          rollNumber: "GR5A004",
          currentClass: "Grade 5",
          currentSection: "A",
          totalMarks: 200,
          percentage: 40,
          attendance: 70,
          status: "detained",
          remarks: "Attendance below threshold"
        }
      ];

      setStudents(mockStudents);
      
      // Calculate promotion stats
      const stats = {
        totalStudents: mockStudents.length,
        eligible: mockStudents.filter(s => s.status === "pass").length,
        detained: mockStudents.filter(s => s.status === "detained").length,
        failed: mockStudents.filter(s => s.status === "fail").length
      };
      setPromotionStats(stats);
      
      toast.success("Promotion data loaded successfully");
    } catch (error) {
      console.error("Error loading promotion data:", error);
      toast.error("Failed to load promotion data");
    } finally {
      setLoading(false);
    }
  };

  const calculatePromotionStatus = (student: Student): "pass" | "fail" | "detained" => {
    if (student.attendance < promotionCriteria.detentionThreshold) {
      return "detained";
    }
    
    if (student.percentage < promotionCriteria.minimumPercentage || 
        student.attendance < promotionCriteria.minimumAttendance) {
      return "fail";
    }
    
    return "pass";
  };

  const handleBulkPromotion = async () => {
    try {
      const eligibleStudents = students.filter(s => s.status === "pass");
      
      // TODO: Implement API call for bulk promotion
      console.log("Promoting students:", eligibleStudents);
      
      toast.success(`Successfully promoted ${eligibleStudents.length} students to next class!`);
      loadPromotionData();
    } catch (error) {
      console.error("Error in bulk promotion:", error);
      toast.error("Failed to promote students");
    }
  };

  const handleCreateNewSession = async (sessionData: any) => {
    try {
      // TODO: Implement API call to create new academic session
      console.log("Creating new session:", sessionData);
      
      toast.success(`New academic session ${sessionData.year} created successfully!`);
      setShowNewSessionDialog(false);
    } catch (error) {
      console.error("Error creating new session:", error);
      toast.error("Failed to create new session");
    }
  };

  if (!user) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.back()}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Student Promotion System</h1>
              <p className="text-gray-600">
                Automated bulk promotion and academic session management
              </p>
            </div>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => setShowCriteriaDialog(true)}>
              <Settings className="h-4 w-4 mr-2" />
              Criteria
            </Button>
            <Button variant="outline">
              <FileText className="h-4 w-4 mr-2" />
              Export Report
            </Button>
            <Button onClick={() => setShowNewSessionDialog(true)}>
              <Plus className="h-4 w-4 mr-2" />
              New Session
            </Button>
          </div>
        </div>

        {/* Session Selection */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div>
                  <Label htmlFor="session">Academic Session</Label>
                  <Select value={selectedSession} onValueChange={setSelectedSession}>
                    <SelectTrigger className="w-40">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="2024-25">2024-25</SelectItem>
                      <SelectItem value="2023-24">2023-24</SelectItem>
                      <SelectItem value="2022-23">2022-23</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div>
                  <Label htmlFor="class">Class Level</Label>
                  <Select value={selectedClass} onValueChange={setSelectedClass}>
                    <SelectTrigger className="w-40">
                      <SelectValue placeholder="Select class" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Grade 5">Grade 5</SelectItem>
                      <SelectItem value="Grade 6">Grade 6</SelectItem>
                      <SelectItem value="Grade 7">Grade 7</SelectItem>
                      <SelectItem value="Grade 8">Grade 8</SelectItem>
                      <SelectItem value="Grade 9">Grade 9</SelectItem>
                      <SelectItem value="Grade 10">Grade 10</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="text-right">
                <div className="text-sm text-gray-600">Session Status</div>
                <Badge variant="secondary">Active</Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Promotion Stats */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Total Students</p>
                  <p className="text-2xl font-bold text-gray-900">{promotionStats.totalStudents}</p>
                </div>
                <Users className="h-8 w-8 text-blue-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Eligible for Promotion</p>
                  <p className="text-2xl font-bold text-green-600">{promotionStats.eligible}</p>
                </div>
                <CheckCircle className="h-8 w-8 text-green-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Detained</p>
                  <p className="text-2xl font-bold text-yellow-600">{promotionStats.detained}</p>
                </div>
                <Clock className="h-8 w-8 text-yellow-600" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm font-medium text-gray-600">Failed</p>
                  <p className="text-2xl font-bold text-red-600">{promotionStats.failed}</p>
                </div>
                <AlertTriangle className="h-8 w-8 text-red-600" />
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-3">
            <TabsTrigger value="bulk-promotion">Bulk Promotion</TabsTrigger>
            <TabsTrigger value="manual-review">Manual Review</TabsTrigger>
            <TabsTrigger value="session-management">Session Management</TabsTrigger>
          </TabsList>

          {/* Bulk Promotion Tab */}
          <TabsContent value="bulk-promotion" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5" />
                  Automated Bulk Promotion
                </CardTitle>
                <p className="text-sm text-gray-600">
                  Promote students automatically based on marks, attendance, and promotion criteria
                </p>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="bg-blue-50 p-4 rounded-lg">
                    <h4 className="font-medium text-blue-900 mb-2">Promotion Criteria</h4>
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div>
                        <span className="text-blue-700">Minimum Percentage:</span>
                        <div className="font-medium">{promotionCriteria.minimumPercentage}%</div>
                      </div>
                      <div>
                        <span className="text-blue-700">Minimum Attendance:</span>
                        <div className="font-medium">{promotionCriteria.minimumAttendance}%</div>
                      </div>
                      <div>
                        <span className="text-blue-700">Grace Marks:</span>
                        <div className="font-medium">{promotionCriteria.graceMarks}</div>
                      </div>
                      <div>
                        <span className="text-blue-700">Detention Threshold:</span>
                        <div className="font-medium">{promotionCriteria.detentionThreshold}%</div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex items-center justify-between">
                    <div>
                      <h4 className="font-medium">Ready for Bulk Promotion</h4>
                      <p className="text-sm text-gray-600">
                        {promotionStats.eligible} students meet promotion criteria
                      </p>
                    </div>
                    <AlertDialog>
                      <AlertDialogTrigger asChild>
                        <Button 
                          disabled={promotionStats.eligible === 0}
                          className="bg-green-600 hover:bg-green-700"
                        >
                          <ArrowRight className="h-4 w-4 mr-2" />
                          Promote {promotionStats.eligible} Students
                        </Button>
                      </AlertDialogTrigger>
                      <AlertDialogContent>
                        <AlertDialogHeader>
                          <AlertDialogTitle>Confirm Bulk Promotion</AlertDialogTitle>
                          <AlertDialogDescription>
                            This will promote {promotionStats.eligible} students to the next class level.
                            This action cannot be undone. Are you sure you want to proceed?
                          </AlertDialogDescription>
                        </AlertDialogHeader>
                        <AlertDialogFooter>
                          <AlertDialogCancel>Cancel</AlertDialogCancel>
                          <AlertDialogAction onClick={handleBulkPromotion}>
                            Yes, Promote Students
                          </AlertDialogAction>
                        </AlertDialogFooter>
                      </AlertDialogContent>
                    </AlertDialog>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Manual Review Tab */}
          <TabsContent value="manual-review" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Student Review & Manual Promotion</CardTitle>
                <p className="text-sm text-gray-600">
                  Review individual students and make manual promotion decisions
                </p>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  {students.map((student) => (
                    <div key={student.id} className="border rounded-lg p-4">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center space-x-4">
                          <div className={`p-2 rounded-lg ${
                            student.status === "pass" ? "bg-green-100" :
                            student.status === "detained" ? "bg-yellow-100" : "bg-red-100"
                          }`}>
                            {student.status === "pass" && <UserCheck className="h-5 w-5 text-green-600" />}
                            {student.status === "detained" && <Clock className="h-5 w-5 text-yellow-600" />}
                            {student.status === "fail" && <UserX className="h-5 w-5 text-red-600" />}
                          </div>
                          <div>
                            <h4 className="font-medium">{student.name}</h4>
                            <p className="text-sm text-gray-600">
                              {student.rollNumber} • {student.currentClass} - {student.currentSection}
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center space-x-4">
                          <div className="text-right text-sm">
                            <div>Marks: {student.percentage}%</div>
                            <div>Attendance: {student.attendance}%</div>
                          </div>
                          <Badge variant={
                            student.status === "pass" ? "default" :
                            student.status === "detained" ? "secondary" : "destructive"
                          }>
                            {student.status}
                          </Badge>
                          <Button variant="outline" size="sm">
                            <Edit className="h-4 w-4 mr-2" />
                            Review
                          </Button>
                        </div>
                      </div>
                      {student.remarks && (
                        <div className="mt-3 p-2 bg-gray-50 rounded text-sm text-gray-600">
                          <strong>Remarks:</strong> {student.remarks}
                        </div>
                      )}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {/* Session Management Tab */}
          <TabsContent value="session-management" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Academic Session Management</CardTitle>
                <p className="text-sm text-gray-600">
                  Manage academic sessions and year-end processes
                </p>
              </CardHeader>
              <CardContent>
                <div className="space-y-4">
                  <div className="grid gap-4 md:grid-cols-2">
                    <div className="border rounded-lg p-4">
                      <h4 className="font-medium mb-2">Current Session: 2024-25</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span>Status:</span>
                          <Badge variant="default">Active</Badge>
                        </div>
                        <div className="flex justify-between">
                          <span>Start Date:</span>
                          <span>April 1, 2024</span>
                        </div>
                        <div className="flex justify-between">
                          <span>End Date:</span>
                          <span>March 31, 2025</span>
                        </div>
                        <div className="flex justify-between">
                          <span>Total Students:</span>
                          <span>1,247</span>
                        </div>
                      </div>
                    </div>
                    
                    <div className="border rounded-lg p-4">
                      <h4 className="font-medium mb-2">Session Progress</h4>
                      <div className="space-y-3">
                        <div>
                          <div className="flex justify-between text-sm mb-1">
                            <span>Academic Year</span>
                            <span>75%</span>
                          </div>
                          <Progress value={75} />
                        </div>
                        <div className="text-sm text-gray-600">
                          <div>• Final exams completed</div>
                          <div>• Results published</div>
                          <div>• Promotion pending</div>
                        </div>
                      </div>
                    </div>
                  </div>
                  
                  <div className="flex gap-4">
                    <Button variant="outline">
                      <Calendar className="h-4 w-4 mr-2" />
                      Close Current Session
                    </Button>
                    <Button onClick={() => setShowNewSessionDialog(true)}>
                      <Plus className="h-4 w-4 mr-2" />
                      Create New Session
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>

        {/* Promotion Criteria Dialog */}
        <Dialog open={showCriteriaDialog} onOpenChange={setShowCriteriaDialog}>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Promotion Criteria Settings</DialogTitle>
              <DialogDescription>
                Configure the criteria for automatic student promotion
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              <div>
                <Label htmlFor="minPercentage">Minimum Percentage (%)</Label>
                <Input
                  id="minPercentage"
                  type="number"
                  value={promotionCriteria.minimumPercentage}
                  onChange={(e) => setPromotionCriteria({
                    ...promotionCriteria,
                    minimumPercentage: parseInt(e.target.value)
                  })}
                  min="0"
                  max="100"
                />
              </div>
              <div>
                <Label htmlFor="minAttendance">Minimum Attendance (%)</Label>
                <Input
                  id="minAttendance"
                  type="number"
                  value={promotionCriteria.minimumAttendance}
                  onChange={(e) => setPromotionCriteria({
                    ...promotionCriteria,
                    minimumAttendance: parseInt(e.target.value)
                  })}
                  min="0"
                  max="100"
                />
              </div>
              <div>
                <Label htmlFor="graceMarks">Grace Marks</Label>
                <Input
                  id="graceMarks"
                  type="number"
                  value={promotionCriteria.graceMarks}
                  onChange={(e) => setPromotionCriteria({
                    ...promotionCriteria,
                    graceMarks: parseInt(e.target.value)
                  })}
                  min="0"
                  max="20"
                />
              </div>
              <div>
                <Label htmlFor="detentionThreshold">Detention Threshold (%)</Label>
                <Input
                  id="detentionThreshold"
                  type="number"
                  value={promotionCriteria.detentionThreshold}
                  onChange={(e) => setPromotionCriteria({
                    ...promotionCriteria,
                    detentionThreshold: parseInt(e.target.value)
                  })}
                  min="0"
                  max="50"
                />
                <p className="text-xs text-gray-600 mt-1">
                  Students below this attendance will be detained
                </p>
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowCriteriaDialog(false)}>
                Cancel
              </Button>
              <Button onClick={() => {
                toast.success("Promotion criteria updated successfully!");
                setShowCriteriaDialog(false);
              }}>
                Save Criteria
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>

        {/* New Session Dialog */}
        <Dialog open={showNewSessionDialog} onOpenChange={setShowNewSessionDialog}>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Create New Academic Session</DialogTitle>
              <DialogDescription>
                Set up a new academic year with automatic student promotion
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-6">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="sessionYear">Academic Year</Label>
                  <Input
                    id="sessionYear"
                    placeholder="e.g., 2025-26"
                  />
                </div>
                <div>
                  <Label htmlFor="sessionName">Session Name</Label>
                  <Input
                    id="sessionName"
                    placeholder="e.g., Academic Session 2025-26"
                  />
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label htmlFor="startDate">Start Date</Label>
                  <Input
                    id="startDate"
                    type="date"
                  />
                </div>
                <div>
                  <Label htmlFor="endDate">End Date</Label>
                  <Input
                    id="endDate"
                    type="date"
                  />
                </div>
              </div>

              <div className="border rounded-lg p-4 bg-blue-50">
                <h4 className="font-medium text-blue-900 mb-3">Automatic Promotion Settings</h4>
                <div className="space-y-3">
                  <div className="flex items-center space-x-2">
                    <Checkbox id="autoPromote" defaultChecked />
                    <Label htmlFor="autoPromote" className="text-sm">
                      Automatically promote eligible students from previous session
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="createSections" defaultChecked />
                    <Label htmlFor="createSections" className="text-sm">
                      Create new sections based on enrollment
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="assignTeachers" />
                    <Label htmlFor="assignTeachers" className="text-sm">
                      Auto-assign teachers to new sections
                    </Label>
                  </div>
                  <div className="flex items-center space-x-2">
                    <Checkbox id="generateReports" defaultChecked />
                    <Label htmlFor="generateReports" className="text-sm">
                      Generate promotion reports
                    </Label>
                  </div>
                </div>
              </div>

              <div>
                <Label htmlFor="sessionDescription">Session Description</Label>
                <Textarea
                  id="sessionDescription"
                  placeholder="Enter session description and any special notes"
                  rows={3}
                />
              </div>
            </div>
            <DialogFooter>
              <Button variant="outline" onClick={() => setShowNewSessionDialog(false)}>
                Cancel
              </Button>
              <Button onClick={() => handleCreateNewSession({
                year: "2025-26",
                name: "Academic Session 2025-26",
                autoPromote: true
              })}>
                Create Session & Promote Students
              </Button>
            </DialogFooter>
          </DialogContent>
        </Dialog>
      </div>
    </AppLayout>
  );
}
