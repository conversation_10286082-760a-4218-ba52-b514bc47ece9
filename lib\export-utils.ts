// Export utilities for CSV and Excel export functionality

export interface ExportUser {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone?: string;
  role: string;
  status: string;
  lastLogin?: string;
  createdAt: string;
}

export function exportToCSV(users: ExportUser[], filename: string = 'users-export') {
  const headers = [
    'ID',
    'First Name',
    'Last Name',
    'Email',
    'Phone',
    'Role',
    'Status',
    'Last Login',
    'Created At'
  ];

  const csvContent = [
    headers.join(','),
    ...users.map(user => [
      user.id,
      user.firstName,
      user.lastName,
      user.email,
      user.phone || '',
      user.role.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase()),
      user.status,
      user.lastLogin ? new Date(user.lastLogin).toLocaleDateString() : 'Never',
      new Date(user.createdAt).toLocaleDateString()
    ].map(field => `"${field}"`).join(','))
  ].join('\n');

  const blob = new Blob([csvContent], { type: 'text/csv;charset=utf-8;' });
  const link = document.createElement('a');
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}-${new Date().toISOString().split('T')[0]}.csv`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}

export function exportToJSON(users: ExportUser[], filename: string = 'users-export') {
  const jsonContent = JSON.stringify(users, null, 2);
  const blob = new Blob([jsonContent], { type: 'application/json;charset=utf-8;' });
  const link = document.createElement('a');
  
  if (link.download !== undefined) {
    const url = URL.createObjectURL(blob);
    link.setAttribute('href', url);
    link.setAttribute('download', `${filename}-${new Date().toISOString().split('T')[0]}.json`);
    link.style.visibility = 'hidden';
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  }
}

// Generate user statistics for export
export function generateUserStats(users: ExportUser[]) {
  const stats = {
    total: users.length,
    byRole: {} as Record<string, number>,
    byStatus: {} as Record<string, number>,
    recentLogins: users.filter(u => u.lastLogin && 
      new Date(u.lastLogin) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)
    ).length,
    neverLoggedIn: users.filter(u => !u.lastLogin).length,
  };

  // Count by role
  users.forEach(user => {
    stats.byRole[user.role] = (stats.byRole[user.role] || 0) + 1;
  });

  // Count by status
  users.forEach(user => {
    stats.byStatus[user.status] = (stats.byStatus[user.status] || 0) + 1;
  });

  return stats;
}

// Validate CSV import data
export function validateImportData(csvData: string[][]): { valid: boolean; errors: string[] } {
  const errors: string[] = [];
  const requiredColumns = ['firstName', 'lastName', 'email', 'role'];
  
  if (csvData.length === 0) {
    errors.push('CSV file is empty');
    return { valid: false, errors };
  }

  const headers = csvData[0];
  const missingColumns = requiredColumns.filter(col => 
    !headers.some(header => header.toLowerCase().includes(col.toLowerCase()))
  );

  if (missingColumns.length > 0) {
    errors.push(`Missing required columns: ${missingColumns.join(', ')}`);
  }

  // Validate email format in data rows
  csvData.slice(1).forEach((row, index) => {
    const emailIndex = headers.findIndex(h => h.toLowerCase().includes('email'));
    if (emailIndex !== -1 && row[emailIndex]) {
      const email = row[emailIndex];
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(email)) {
        errors.push(`Invalid email format in row ${index + 2}: ${email}`);
      }
    }
  });

  return { valid: errors.length === 0, errors };
}

// Parse CSV content
export function parseCSV(csvContent: string): string[][] {
  const lines = csvContent.split('\n');
  const result: string[][] = [];

  for (const line of lines) {
    if (line.trim()) {
      // Simple CSV parsing - handles quoted fields
      const fields: string[] = [];
      let current = '';
      let inQuotes = false;

      for (let i = 0; i < line.length; i++) {
        const char = line[i];
        
        if (char === '"') {
          inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
          fields.push(current.trim());
          current = '';
        } else {
          current += char;
        }
      }
      
      fields.push(current.trim());
      result.push(fields.map(field => field.replace(/^"|"$/g, '')));
    }
  }

  return result;
}
