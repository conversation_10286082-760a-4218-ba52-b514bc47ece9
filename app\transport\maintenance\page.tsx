"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Wrench,
  Search,
  Filter,
  Download,
  Plus,
  Eye,
  Edit,
  Calendar,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  IndianRupee,
  Bus,
  User,
  FileText,
  Settings,
  TrendingUp,
} from "lucide-react";

export default function TransportMaintenance() {
  const [user, setUser] = useState<any>(null);
  const [selectedTab, setSelectedTab] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedMaintenance, setSelectedMaintenance] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "transport_manager") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  const maintenanceStats = {
    totalRecords: 45,
    scheduledMaintenance: 12,
    inProgressMaintenance: 8,
    completedMaintenance: 20,
    overdueMaintenace: 5,
    totalCost: 125000,
    avgCostPerVehicle: 8333,
  };

  const mockMaintenance = [
    {
      id: "MT001",
      vehicleNumber: "DL-1CA-1234",
      vehicleModel: "Tata LP 909",
      maintenanceType: "Routine Service",
      description: "Engine oil change, brake inspection, tire rotation",
      scheduledDate: "2024-01-25",
      completedDate: "2024-01-25",
      status: "Completed",
      priority: "Medium",
      cost: 8500,
      mechanicName: "Ramesh Sharma",
      workshopName: "City Auto Service",
      nextServiceDue: "2024-04-25",
      mileage: 45678,
      partsReplaced: ["Engine Oil", "Oil Filter", "Air Filter"],
      notes: "All systems functioning normally. Recommended tire replacement in next service.",
    },
    {
      id: "MT002",
      vehicleNumber: "DL-1CB-5678",
      vehicleModel: "Mahindra Bolero",
      maintenanceType: "Emergency Repair",
      description: "Brake system failure, immediate repair required",
      scheduledDate: "2024-01-22",
      completedDate: null,
      status: "In Progress",
      priority: "High",
      cost: 15000,
      mechanicName: "Suresh Kumar",
      workshopName: "Express Auto Repair",
      nextServiceDue: "2024-03-22",
      mileage: 32145,
      partsReplaced: ["Brake Pads", "Brake Fluid"],
      notes: "Critical safety issue. Vehicle out of service until repair completion.",
    },
    {
      id: "MT003",
      vehicleNumber: "DL-1CC-9012",
      vehicleModel: "Ashok Leyland",
      maintenanceType: "Preventive Maintenance",
      description: "Monthly inspection and preventive maintenance",
      scheduledDate: "2024-01-30",
      completedDate: null,
      status: "Scheduled",
      priority: "Low",
      cost: 6000,
      mechanicName: "Vikram Singh",
      workshopName: "Highway Service Center",
      nextServiceDue: "2024-02-28",
      mileage: 67890,
      partsReplaced: [],
      notes: "Regular monthly checkup scheduled.",
    },
    {
      id: "MT004",
      vehicleNumber: "DL-1CD-3456",
      vehicleModel: "Force Traveller",
      maintenanceType: "Major Overhaul",
      description: "Engine overhaul and transmission service",
      scheduledDate: "2024-01-15",
      completedDate: null,
      status: "Overdue",
      priority: "High",
      cost: 35000,
      mechanicName: "Amit Gupta",
      workshopName: "Professional Auto Works",
      nextServiceDue: "2024-07-15",
      mileage: 28765,
      partsReplaced: ["Engine Parts", "Transmission Oil"],
      notes: "Major service overdue. Immediate attention required.",
    },
    {
      id: "MT005",
      vehicleNumber: "DL-1CE-7890",
      vehicleModel: "Tata LP 1109",
      maintenanceType: "Routine Service",
      description: "Regular service and inspection",
      scheduledDate: "2024-01-28",
      completedDate: "2024-01-28",
      status: "Completed",
      priority: "Medium",
      cost: 9500,
      mechanicName: "Rajesh Sharma",
      workshopName: "City Auto Service",
      nextServiceDue: "2024-04-28",
      mileage: 54321,
      partsReplaced: ["Engine Oil", "Coolant", "Spark Plugs"],
      notes: "Service completed successfully. All systems operational.",
    },
  ];

  const filteredMaintenance = mockMaintenance.filter((maintenance) => {
    const matchesSearch = maintenance.vehicleNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         maintenance.vehicleModel.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         maintenance.maintenanceType.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         maintenance.mechanicName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         maintenance.workshopName.toLowerCase().includes(searchTerm.toLowerCase());

    if (selectedTab === "all") return matchesSearch;
    if (selectedTab === "scheduled") return matchesSearch && maintenance.status === "Scheduled";
    if (selectedTab === "in_progress") return matchesSearch && maintenance.status === "In Progress";
    if (selectedTab === "completed") return matchesSearch && maintenance.status === "Completed";
    if (selectedTab === "overdue") return matchesSearch && maintenance.status === "Overdue";

    return matchesSearch;
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Completed":
        return <Badge className="bg-green-100 text-green-800">Completed</Badge>;
      case "In Progress":
        return <Badge className="bg-blue-100 text-blue-800">In Progress</Badge>;
      case "Scheduled":
        return <Badge className="bg-yellow-100 text-yellow-800">Scheduled</Badge>;
      case "Overdue":
        return <Badge className="bg-red-100 text-red-800">Overdue</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "High":
        return <Badge variant="outline" className="text-red-600 border-red-200">High</Badge>;
      case "Medium":
        return <Badge variant="outline" className="text-yellow-600 border-yellow-200">Medium</Badge>;
      case "Low":
        return <Badge variant="outline" className="text-green-600 border-green-200">Low</Badge>;
      default:
        return <Badge variant="outline">{priority}</Badge>;
    }
  };

  const viewMaintenanceDetails = (maintenance: any) => {
    setSelectedMaintenance(maintenance);
  };

  const editMaintenance = (maintenanceId: string) => {
    alert(`Editing maintenance record ${maintenanceId}`);
  };

  const scheduleMaintenance = () => {
    alert("Scheduling new maintenance");
  };

  const markCompleted = (maintenanceId: string) => {
    alert(`Marking maintenance ${maintenanceId} as completed`);
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Vehicle Maintenance</h1>
            <p className="text-gray-600">Track and manage vehicle maintenance schedules and records</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
            <Button onClick={scheduleMaintenance}>
              <Plus className="h-4 w-4 mr-2" />
              Schedule Maintenance
            </Button>
          </div>
        </div>

        {/* Maintenance Statistics */}
        <div className="grid gap-4 md:grid-cols-4 lg:grid-cols-7">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <FileText className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {maintenanceStats.totalRecords}
                  </div>
                  <p className="text-sm text-gray-600">Total Records</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Calendar className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-yellow-600">
                    {maintenanceStats.scheduledMaintenance}
                  </div>
                  <p className="text-sm text-gray-600">Scheduled</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Settings className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {maintenanceStats.inProgressMaintenance}
                  </div>
                  <p className="text-sm text-gray-600">In Progress</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {maintenanceStats.completedMaintenance}
                  </div>
                  <p className="text-sm text-gray-600">Completed</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <AlertTriangle className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-red-600">
                    {maintenanceStats.overdueMaintenace}
                  </div>
                  <p className="text-sm text-gray-600">Overdue</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <IndianRupee className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    ₹{(maintenanceStats.totalCost / 1000).toFixed(0)}K
                  </div>
                  <p className="text-sm text-gray-600">Total Cost</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <TrendingUp className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    ₹{(maintenanceStats.avgCostPerVehicle / 1000).toFixed(1)}K
                  </div>
                  <p className="text-sm text-gray-600">Avg/Vehicle</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filter */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search by vehicle, type, mechanic, or workshop..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </div>

            {/* Tabs */}
            <div className="flex flex-wrap gap-2 mt-4">
              {[
                { key: "all", label: "All Records", count: maintenanceStats.totalRecords },
                { key: "scheduled", label: "Scheduled", count: maintenanceStats.scheduledMaintenance },
                { key: "in_progress", label: "In Progress", count: maintenanceStats.inProgressMaintenance },
                { key: "completed", label: "Completed", count: maintenanceStats.completedMaintenance },
                { key: "overdue", label: "Overdue", count: maintenanceStats.overdueMaintenace },
              ].map((tab) => (
                <Button
                  key={tab.key}
                  variant={selectedTab === tab.key ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedTab(tab.key)}
                  className="text-xs"
                >
                  {tab.label} ({tab.count})
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Maintenance Records Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Wrench className="h-5 w-5 mr-2" />
              Maintenance Records ({filteredMaintenance.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-4 font-medium text-gray-900">Vehicle Details</th>
                    <th className="text-left p-4 font-medium text-gray-900">Maintenance Info</th>
                    <th className="text-left p-4 font-medium text-gray-900">Schedule & Status</th>
                    <th className="text-left p-4 font-medium text-gray-900">Cost & Priority</th>
                    <th className="text-left p-4 font-medium text-gray-900">Service Provider</th>
                    <th className="text-left p-4 font-medium text-gray-900">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredMaintenance.map((maintenance) => (
                    <tr key={maintenance.id} className="border-b hover:bg-gray-50">
                      <td className="p-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <Bus className="h-6 w-6 text-blue-600" />
                          </div>
                          <div>
                            <div className="font-medium text-gray-900">{maintenance.vehicleNumber}</div>
                            <div className="text-sm text-gray-500">{maintenance.vehicleModel}</div>
                            <div className="text-sm text-gray-500">{maintenance.mileage.toLocaleString()} km</div>
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-1">
                          <div className="font-medium text-gray-900">{maintenance.maintenanceType}</div>
                          <div className="text-sm text-gray-500">{maintenance.description}</div>
                          <div className="text-xs text-gray-400">ID: {maintenance.id}</div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-2">
                          <div className="text-sm">
                            <span className="font-medium">Scheduled:</span>
                            <div className="text-gray-500">{new Date(maintenance.scheduledDate).toLocaleDateString()}</div>
                          </div>
                          {maintenance.completedDate && (
                            <div className="text-sm">
                              <span className="font-medium">Completed:</span>
                              <div className="text-gray-500">{new Date(maintenance.completedDate).toLocaleDateString()}</div>
                            </div>
                          )}
                          {getStatusBadge(maintenance.status)}
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-2">
                          <div className="font-medium text-gray-900">
                            ₹{maintenance.cost.toLocaleString()}
                          </div>
                          {getPriorityBadge(maintenance.priority)}
                          <div className="text-sm text-gray-500">
                            Next: {new Date(maintenance.nextServiceDue).toLocaleDateString()}
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-1">
                          <div className="font-medium text-gray-900">{maintenance.mechanicName}</div>
                          <div className="text-sm text-gray-500">{maintenance.workshopName}</div>
                          {maintenance.partsReplaced.length > 0 && (
                            <div className="text-xs text-gray-400">
                              Parts: {maintenance.partsReplaced.slice(0, 2).join(", ")}
                              {maintenance.partsReplaced.length > 2 && ` +${maintenance.partsReplaced.length - 2} more`}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => viewMaintenanceDetails(maintenance)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => editMaintenance(maintenance.id)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          {maintenance.status === "In Progress" && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => markCompleted(maintenance.id)}
                            >
                              <CheckCircle className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}