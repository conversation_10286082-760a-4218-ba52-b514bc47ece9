"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Award,
  TrendingUp,
  TrendingDown,
  Calendar,
  BookOpen,
  BarChart3,
  Download,
  Filter,
  Star,
  Target,
  CheckCircle,
  AlertTriangle,
} from "lucide-react";

export default function StudentGrades() {
  const [user, setUser] = useState<any>(null);
  const [selectedSemester, setSelectedSemester] = useState("current");
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "student") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock grades data
  const currentGrades = [
    { 
      subject: "Mathematics", 
      teacher: "Dr. Emily Wilson", 
      assignments: [
        { name: "Assignment 1", score: 85, maxScore: 100, date: "2024-01-10", type: "Assignment" },
        { name: "Mid-term Exam", score: 78, maxScore: 100, date: "2024-01-20", type: "Exam" },
        { name: "Quiz 1", score: 92, maxScore: 100, date: "2024-01-15", type: "Quiz" },
      ],
      currentGrade: "B+",
      percentage: 85.0,
      credits: 4
    },
    { 
      subject: "Physics", 
      teacher: "Mr. David Brown", 
      assignments: [
        { name: "Lab Report 1", score: 88, maxScore: 100, date: "2024-01-12", type: "Lab" },
        { name: "Mid-term Exam", score: 82, maxScore: 100, date: "2024-01-22", type: "Exam" },
        { name: "Assignment 1", score: 90, maxScore: 100, date: "2024-01-08", type: "Assignment" },
      ],
      currentGrade: "B+",
      percentage: 86.7,
      credits: 4
    },
    { 
      subject: "Chemistry", 
      teacher: "Dr. Sarah Johnson", 
      assignments: [
        { name: "Lab Practical", score: 95, maxScore: 100, date: "2024-01-18", type: "Practical" },
        { name: "Theory Exam", score: 88, maxScore: 100, date: "2024-01-25", type: "Exam" },
        { name: "Assignment 1", score: 87, maxScore: 100, date: "2024-01-05", type: "Assignment" },
      ],
      currentGrade: "A-",
      percentage: 90.0,
      credits: 4
    },
    { 
      subject: "English", 
      teacher: "Ms. Lisa Anderson", 
      assignments: [
        { name: "Essay 1", score: 92, maxScore: 100, date: "2024-01-14", type: "Essay" },
        { name: "Mid-term Exam", score: 85, maxScore: 100, date: "2024-01-21", type: "Exam" },
        { name: "Presentation", score: 88, maxScore: 100, date: "2024-01-16", type: "Presentation" },
      ],
      currentGrade: "A-",
      percentage: 88.3,
      credits: 3
    },
    { 
      subject: "Computer Science", 
      teacher: "Prof. Michael Chen", 
      assignments: [
        { name: "Programming Project", score: 96, maxScore: 100, date: "2024-01-19", type: "Project" },
        { name: "Mid-term Exam", score: 89, maxScore: 100, date: "2024-01-23", type: "Exam" },
        { name: "Lab Assignment", score: 94, maxScore: 100, date: "2024-01-11", type: "Lab" },
      ],
      currentGrade: "A",
      percentage: 93.0,
      credits: 4
    },
  ];

  const semesterSummary = {
    totalCredits: 19,
    earnedCredits: 19,
    gpa: 3.8,
    totalSubjects: 5,
    averagePercentage: 88.6,
    rank: 15,
    totalStudents: 120,
  };

  const gradeDistribution = [
    { grade: "A", count: 2, percentage: 40 },
    { grade: "A-", count: 2, percentage: 40 },
    { grade: "B+", count: 1, percentage: 20 },
    { grade: "B", count: 0, percentage: 0 },
    { grade: "B-", count: 0, percentage: 0 },
  ];

  const getGradeColor = (grade: string) => {
    switch (grade) {
      case "A": return "text-green-600 bg-green-100";
      case "A-": return "text-green-600 bg-green-100";
      case "B+": return "text-blue-600 bg-blue-100";
      case "B": return "text-yellow-600 bg-yellow-100";
      case "B-": return "text-orange-600 bg-orange-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const getTypeColor = (type: string) => {
    switch (type) {
      case "Exam": return "bg-red-100 text-red-700";
      case "Assignment": return "bg-blue-100 text-blue-700";
      case "Quiz": return "bg-green-100 text-green-700";
      case "Lab": return "bg-purple-100 text-purple-700";
      case "Project": return "bg-orange-100 text-orange-700";
      default: return "bg-gray-100 text-gray-700";
    }
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">My Grades</h1>
            <p className="text-gray-600">Track your academic performance and progress</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Academic Summary */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Award className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-yellow-600">
                    {semesterSummary.gpa}
                  </div>
                  <p className="text-sm text-gray-600">Current GPA</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Target className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {semesterSummary.averagePercentage}%
                  </div>
                  <p className="text-sm text-gray-600">Average Score</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <TrendingUp className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    #{semesterSummary.rank}
                  </div>
                  <p className="text-sm text-gray-600">Class Rank</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {semesterSummary.earnedCredits}/{semesterSummary.totalCredits}
                  </div>
                  <p className="text-sm text-gray-600">Credits</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Subject Grades */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BookOpen className="h-5 w-5 mr-2" />
              Subject Grades
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {currentGrades.map((subject, index) => (
                <div key={index} className="border rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <div>
                      <h3 className="text-lg font-semibold text-gray-900">{subject.subject}</h3>
                      <p className="text-sm text-gray-500">Instructor: {subject.teacher}</p>
                    </div>
                    <div className="text-right">
                      <Badge className={`${getGradeColor(subject.currentGrade)} border-0`}>
                        {subject.currentGrade}
                      </Badge>
                      <div className="text-sm text-gray-500 mt-1">
                        {subject.percentage}% • {subject.credits} credits
                      </div>
                    </div>
                  </div>

                  <div className="grid gap-4 md:grid-cols-3">
                    {subject.assignments.map((assignment, assignmentIndex) => (
                      <div key={assignmentIndex} className="bg-gray-50 rounded-lg p-4">
                        <div className="flex items-center justify-between mb-2">
                          <h4 className="font-medium text-gray-900">{assignment.name}</h4>
                          <Badge variant="outline" className={getTypeColor(assignment.type)}>
                            {assignment.type}
                          </Badge>
                        </div>
                        <div className="flex items-center justify-between">
                          <span className="text-2xl font-bold text-gray-900">
                            {assignment.score}/{assignment.maxScore}
                          </span>
                          <span className="text-sm text-gray-500">{assignment.date}</span>
                        </div>
                        <div className="mt-2">
                          <div className="w-full bg-gray-200 rounded-full h-2">
                            <div 
                              className="bg-blue-600 h-2 rounded-full" 
                              style={{ width: `${(assignment.score / assignment.maxScore) * 100}%` }}
                            />
                          </div>
                          <div className="text-center text-sm text-gray-600 mt-1">
                            {((assignment.score / assignment.maxScore) * 100).toFixed(1)}%
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Grade Distribution */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <BarChart3 className="h-5 w-5 mr-2" />
              Grade Distribution
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-5">
              {gradeDistribution.map((grade, index) => (
                <div key={index} className="text-center">
                  <div className="text-2xl font-bold text-gray-900 mb-2">{grade.grade}</div>
                  <div className="w-full bg-gray-200 rounded-full h-20 flex flex-col justify-end mb-2">
                    <div 
                      className="bg-blue-500 rounded-b-full"
                      style={{ height: `${grade.percentage}%` }}
                    />
                  </div>
                  <div className="text-sm text-gray-600">{grade.count} subjects</div>
                  <div className="text-xs text-gray-500">{grade.percentage}%</div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
