"use client";

import { useState, useEffect, use<PERSON><PERSON>back, Suspense } from "react";
import { use<PERSON><PERSON><PERSON>, useSearchParams } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Alert, AlertDescription } from "@/components/ui/alert";
import {
  Users,
  Search,
  Filter,
  Download,
  Eye,
  Mail,
  Phone,
  RefreshCw,
  X,
  AlertCircle,
  Loader2,
  GraduationCap,
  BookOpen,
  Grid3X3,
  <PERSON>rCheck,
  MessageCircle,
  Award,
  Target,
  BarChart3
} from "lucide-react";
import { toast } from "sonner";

// Interfaces for teacher-specific data
interface TeacherClass {
  id: string;
  name: string;
  subject: string;
  grade: string;
  section: string;
  programId: string;
  batchId: string;
  sectionId?: string;
  streamId?: string;
  program?: {
    id: string;
    name: string;
    code: string;
  };
  batch?: {
    id: string;
    batchName: string;
    startYear: number;
    endYear: number;
  };
  academicSection?: {
    id: string;
    name: string;
    displayName: string;
  };
  stream?: {
    id: string;
    name: string;
    code: string;
  };
  studentCount: number;
}

interface Student {
  id: string;
  rollNumber: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  studentType: "regular" | "lateral" | "distance";
  admissionDate: string;
  status: "active" | "inactive" | "suspended";
  attendance?: number;
  lastGrade?: string;
  gpa?: number;
  currentGrade?: string;
  behavior?: string;
  lastAssignment?: string;
  lastScore?: number;
  submittedAssignments?: number;
  totalAssignments?: number;
}

const statusColors = {
  active: "text-green-600 bg-green-100",
  inactive: "text-red-600 bg-red-100",
  suspended: "text-yellow-600 bg-yellow-100",
};

const studentTypeColors = {
  regular: "text-blue-600 bg-blue-100",
  lateral: "text-purple-600 bg-purple-100",
  distance: "text-orange-600 bg-orange-100",
};

const gradeColors = {
  "A": "text-green-600 bg-green-100",
  "A-": "text-green-600 bg-green-100",
  "B+": "text-blue-600 bg-blue-100",
  "B": "text-yellow-600 bg-yellow-100",
  "C+": "text-orange-600 bg-orange-100",
  "C": "text-red-600 bg-red-100",
};

function TeacherStudentsContent() {
  const [user, setUser] = useState<any>(null);
  const [isLoading, setIsLoading] = useState(true);
  const router = useRouter();
  const searchParams = useSearchParams();

  // Filter states
  const [selectedClass, setSelectedClass] = useState<string>(searchParams.get("class") || "");
  const [searchTerm, setSearchTerm] = useState<string>(searchParams.get("search") || "");
  const [selectedFilter, setSelectedFilter] = useState<string>("all");

  // Data states
  const [teacherClasses, setTeacherClasses] = useState<TeacherClass[]>([]);
  const [students, setStudents] = useState<Student[]>([]);

  // Loading states
  const [loadingClasses, setLoadingClasses] = useState(false);
  const [loadingStudents, setLoadingStudents] = useState(false);

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "teacher") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
    fetchTeacherClasses(parsedUser.id);
  }, [router]);

  const fetchStudents = useCallback(async () => {
    if (!selectedClass) return;

    setLoadingStudents(true);
    try {
      const selectedClassData = teacherClasses.find(c => c.id === selectedClass);
      if (!selectedClassData) return;

      const params = new URLSearchParams({
        programId: selectedClassData.programId,
        batchId: selectedClassData.batchId,
      });

      if (selectedClassData.sectionId) {
        params.append("sectionId", selectedClassData.sectionId);
      }

      if (selectedClassData.streamId) {
        params.append("streamId", selectedClassData.streamId);
      }

      const response = await fetch(`/api/students?${params}`);
      const result = await response.json();

      if (response.ok) {
        setStudents(result.data);
      } else {
        // Fallback to mock data
        const mockStudents = [
          {
            id: "STU001",
            rollNumber: "001",
            firstName: "John",
            lastName: "Doe",
            email: "<EMAIL>",
            phone: "******-567-8901",
            studentType: "regular" as const,
            admissionDate: "2023-04-01",
            status: "active" as const,
            attendance: 94.5,
            gpa: 3.8,
            currentGrade: "A-",
            behavior: "Excellent",
            lastAssignment: "Algebra Test",
            lastScore: 88,
            submittedAssignments: 8,
            totalAssignments: 10,
          },
          {
            id: "STU002",
            rollNumber: "002",
            firstName: "Alice",
            lastName: "Smith",
            email: "<EMAIL>",
            phone: "******-567-8903",
            studentType: "regular" as const,
            admissionDate: "2023-04-01",
            status: "active" as const,
            attendance: 96.2,
            gpa: 3.9,
            currentGrade: "A",
            behavior: "Outstanding",
            lastAssignment: "Geometry Quiz",
            lastScore: 95,
            submittedAssignments: 10,
            totalAssignments: 10,
          },
        ];
        setStudents(mockStudents);
      }
    } catch (error) {
      console.error("Error fetching students:", error);
      // Use mock data as fallback
      const mockStudents = [
        {
          id: "STU001",
          rollNumber: "001",
          firstName: "John",
          lastName: "Doe",
          email: "<EMAIL>",
          phone: "******-567-8901",
          studentType: "regular" as const,
          admissionDate: "2023-04-01",
          status: "active" as const,
          attendance: 94.5,
          gpa: 3.8,
          currentGrade: "A-",
        },
      ];
      setStudents(mockStudents);
    } finally {
      setLoadingStudents(false);
    }
  }, [selectedClass, teacherClasses]);

  useEffect(() => {
    if (selectedClass) {
      fetchStudents();
    } else {
      setStudents([]);
    }
  }, [selectedClass, fetchStudents]);

  // Update URL parameters when filters change
  useEffect(() => {
    const params = new URLSearchParams();
    if (selectedClass) params.set("class", selectedClass);
    if (searchTerm) params.set("search", searchTerm);

    const newUrl = `${window.location.pathname}${params.toString() ? `?${params.toString()}` : ""}`;
    window.history.replaceState({}, "", newUrl);
  }, [selectedClass, searchTerm]);

  const fetchTeacherClasses = async (teacherId: string) => {
    setLoadingClasses(true);
    try {
      // This would fetch classes assigned to the current teacher
      const response = await fetch(`/api/classes?teacherId=${teacherId}`);
      const result = await response.json();

      if (response.ok) {
        // Transform the data to match our interface
        const transformedClasses = result.data.map((cls: any) => ({
          id: cls.id,
          name: `${cls.grade} - ${cls.subject}`,
          subject: cls.subject,
          grade: cls.grade,
          section: cls.section || "",
          programId: cls.programId,
          batchId: cls.batchId,
          sectionId: cls.sectionId,
          streamId: cls.streamId,
          program: cls.program,
          batch: cls.batch,
          academicSection: cls.academicSection,
          stream: cls.stream,
          studentCount: cls.enrolledStudents || 0,
        }));
        setTeacherClasses(transformedClasses);
      } else {
        // Fallback to mock data for development
        const mockClasses = [
          {
            id: "CLASS001",
            name: "10A - Mathematics",
            subject: "Mathematics",
            grade: "10A",
            section: "A",
            programId: "PROG001",
            batchId: "BATCH001",
            studentCount: 32,
          },
          {
            id: "CLASS002",
            name: "10B - Mathematics",
            subject: "Mathematics",
            grade: "10B",
            section: "B",
            programId: "PROG001",
            batchId: "BATCH001",
            studentCount: 28,
          },
          {
            id: "CLASS003",
            name: "11A - Advanced Math",
            subject: "Advanced Mathematics",
            grade: "11A",
            section: "A",
            programId: "PROG002",
            batchId: "BATCH002",
            studentCount: 25,
          },
        ];
        setTeacherClasses(mockClasses);
      }
    } catch (error) {
      console.error("Error fetching teacher classes:", error);
      // Use mock data as fallback
      const mockClasses = [
        {
          id: "CLASS001",
          name: "10A - Mathematics",
          subject: "Mathematics",
          grade: "10A",
          section: "A",
          programId: "PROG001",
          batchId: "BATCH001",
          studentCount: 32,
        },
        {
          id: "CLASS002",
          name: "10B - Mathematics",
          subject: "Mathematics",
          grade: "10B",
          section: "B",
          programId: "PROG001",
          batchId: "BATCH001",
          studentCount: 28,
        },
      ];
      setTeacherClasses(mockClasses);
    } finally {
      setLoadingClasses(false);
      setIsLoading(false);
    }
  };

  const clearFilters = () => {
    setSelectedClass("");
    setSearchTerm("");
    setSelectedFilter("all");
    setStudents([]);
  };

  const exportStudents = () => {
    if (filteredStudents.length === 0) {
      toast.error("No students to export");
      return;
    }

    const selectedClassData = teacherClasses.find(c => c.id === selectedClass);
    const csvContent = [
      ["Roll Number", "Name", "Email", "Phone", "Student Type", "Status", "Class", "Subject", "Grade", "Attendance"],
      ...filteredStudents.map(student => [
        student.rollNumber,
        `${student.firstName} ${student.lastName}`,
        student.email,
        student.phone,
        student.studentType,
        student.status,
        selectedClassData?.name || "",
        selectedClassData?.subject || "",
        student.currentGrade || "",
        student.attendance ? `${student.attendance}%` : "",
      ])
    ].map(row => row.join(",")).join("\n");

    const blob = new Blob([csvContent], { type: "text/csv" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `students-${selectedClassData?.name || "class"}-${new Date().toISOString().split("T")[0]}.csv`;
    a.click();
    window.URL.revokeObjectURL(url);
    toast.success("Students exported successfully");
  };

  // Filter students based on search term and performance filter
  const filteredStudents = students.filter(student => {
    // Search filter
    const matchesSearch = !searchTerm || (
      student.rollNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.firstName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.lastName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
      student.phone.includes(searchTerm)
    );

    // Performance filter
    let matchesFilter = true;
    if (selectedFilter === "excellent") {
      matchesFilter = (student.gpa || 0) >= 3.8;
    } else if (selectedFilter === "needs_attention") {
      matchesFilter = (student.gpa || 0) < 3.0 || (student.attendance || 0) < 90;
    } else if (selectedFilter === "low_attendance") {
      matchesFilter = (student.attendance || 0) < 90;
    } else if (selectedFilter === "pending_assignments") {
      matchesFilter = (student.submittedAssignments || 0) < (student.totalAssignments || 0);
    }

    return matchesSearch && matchesFilter;
  });

  // Calculate statistics
  const totalStudents = filteredStudents.length;
  const activeStudents = filteredStudents.filter(s => s.status === "active").length;
  const averageGPA = totalStudents > 0 ? filteredStudents.reduce((sum, s) => sum + (s.gpa || 0), 0) / totalStudents : 0;
  const averageAttendance = totalStudents > 0 ? filteredStudents.reduce((sum, s) => sum + (s.attendance || 0), 0) / totalStudents : 0;
  const excellentPerformers = filteredStudents.filter(s => (s.gpa || 0) >= 3.8).length;
  const needsAttention = filteredStudents.filter(s => (s.gpa || 0) < 3.0 || (s.attendance || 0) < 90).length;

  if (isLoading) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-600" />
            <p className="mt-2 text-gray-600">Loading your classes...</p>
          </div>
        </div>
      </AppLayout>
    );
  }

  if (!user) {
    return null;
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900 flex items-center gap-2">
              <Users className="h-6 w-6" />
              My Students
            </h1>
            <p className="text-gray-600">
              View and manage students from your assigned classes
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={clearFilters}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Clear Filters
            </Button>
            <Button variant="outline" onClick={exportStudents} disabled={filteredStudents.length === 0}>
              <Download className="h-4 w-4 mr-2" />
              Export ({filteredStudents.length})
            </Button>
          </div>
        </div>

        {/* Class Selection and Search */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Filter className="h-5 w-5" />
              Select Class and Search Students
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {/* Class Selection */}
              <div>
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Select Class *
                </label>
                <Select value={selectedClass} onValueChange={setSelectedClass}>
                  <SelectTrigger>
                    <SelectValue placeholder="Select a class to view students" />
                  </SelectTrigger>
                  <SelectContent>
                    {teacherClasses.map((classItem) => (
                      <SelectItem key={classItem.id} value={classItem.id}>
                        <div className="flex flex-col">
                          <span className="font-medium">{classItem.name}</span>
                          <span className="text-sm text-gray-500">
                            {classItem.subject} • {classItem.studentCount} students
                          </span>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>

              {/* Search within selected class */}
              {selectedClass && (
                <div>
                  <label className="text-sm font-medium text-gray-700 mb-2 block">
                    Search Students
                  </label>
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search by name, roll number, email..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10"
                    />
                    {searchTerm && (
                      <Button
                        variant="ghost"
                        size="sm"
                        className="absolute right-2 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
                        onClick={() => setSearchTerm("")}
                      >
                        <X className="h-3 w-3" />
                      </Button>
                    )}
                  </div>
                </div>
              )}
            </div>

            {/* Performance Filters */}
            {selectedClass && (
              <div className="border-t pt-4">
                <label className="text-sm font-medium text-gray-700 mb-2 block">
                  Performance Filters
                </label>
                <div className="flex flex-wrap gap-2">
                  {[
                    { key: "all", label: "All Students" },
                    { key: "excellent", label: "Excellent (GPA ≥ 3.8)" },
                    { key: "needs_attention", label: "Needs Attention" },
                    { key: "low_attendance", label: "Low Attendance" },
                    { key: "pending_assignments", label: "Pending Work" },
                  ].map((filter) => (
                    <Button
                      key={filter.key}
                      variant={selectedFilter === filter.key ? "default" : "outline"}
                      size="sm"
                      onClick={() => setSelectedFilter(filter.key)}
                    >
                      {filter.label}
                    </Button>
                  ))}
                </div>
              </div>
            )}

            {!selectedClass && (
              <Alert>
                <AlertCircle className="h-4 w-4" />
                <AlertDescription>
                  Please select a class to view students. You can only view students from classes assigned to you.
                </AlertDescription>
              </Alert>
            )}
          </CardContent>
        </Card>

        {/* Student Statistics */}
        {selectedClass && (
          <div className="grid gap-4 md:grid-cols-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Users className="h-8 w-8 text-blue-600" />
                  <div className="ml-4">
                    <div className="text-2xl font-bold text-blue-600">
                      {totalStudents}
                    </div>
                    <p className="text-sm text-gray-600">Total Students</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Award className="h-8 w-8 text-green-600" />
                  <div className="ml-4">
                    <div className="text-2xl font-bold text-green-600">
                      {averageGPA.toFixed(1)}
                    </div>
                    <p className="text-sm text-gray-600">Average GPA</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <UserCheck className="h-8 w-8 text-blue-600" />
                  <div className="ml-4">
                    <div className="text-2xl font-bold text-blue-600">
                      {averageAttendance.toFixed(1)}%
                    </div>
                    <p className="text-sm text-gray-600">Avg Attendance</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Target className="h-8 w-8 text-purple-600" />
                  <div className="ml-4">
                    <div className="text-2xl font-bold text-purple-600">
                      {excellentPerformers}
                    </div>
                    <p className="text-sm text-gray-600">Excellent Performers</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Students Table */}
        {selectedClass ? (
          <Card>
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center gap-2">
                  <Users className="h-5 w-5" />
                  Students ({filteredStudents.length})
                  {loadingStudents && <Loader2 className="h-4 w-4 animate-spin" />}
                </CardTitle>
                {searchTerm && (
                  <Badge variant="outline">
                    Filtered by: &quot;{searchTerm}&quot;
                  </Badge>
                )}
              </div>
            </CardHeader>
            <CardContent>
              {loadingStudents ? (
                <div className="flex items-center justify-center py-8">
                  <div className="text-center">
                    <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-600" />
                    <p className="mt-2 text-gray-600">Loading students...</p>
                  </div>
                </div>
              ) : filteredStudents.length === 0 ? (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <p className="text-gray-500 text-lg">No students found</p>
                  <p className="text-gray-400 text-sm">
                    {students.length === 0
                      ? "No students enrolled in this class"
                      : "Try adjusting your search or filter criteria"
                    }
                  </p>
                </div>
              ) : (
                <div className="overflow-x-auto">
                  <Table>
                    <TableHeader>
                      <TableRow>
                        <TableHead>Student</TableHead>
                        <TableHead>Roll Number</TableHead>
                        <TableHead>Type</TableHead>
                        <TableHead>Contact</TableHead>
                        <TableHead>Performance</TableHead>
                        <TableHead>Attendance</TableHead>
                        <TableHead>Status</TableHead>
                        <TableHead className="text-right">Actions</TableHead>
                      </TableRow>
                    </TableHeader>
                    <TableBody>
                      {filteredStudents.map((student) => (
                        <TableRow key={student.id}>
                          <TableCell>
                            <div>
                              <div className="font-medium text-gray-900">
                                {student.firstName} {student.lastName}
                              </div>
                              <div className="text-sm text-gray-500">{student.email}</div>
                            </div>
                          </TableCell>
                          <TableCell className="font-medium">{student.rollNumber}</TableCell>
                          <TableCell>
                            <Badge
                              variant="outline"
                              className={studentTypeColors[student.studentType]}
                            >
                              {student.studentType}
                            </Badge>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              <div className="flex items-center">
                                <Phone className="h-3 w-3 mr-1" />
                                {student.phone}
                              </div>
                              <div className="flex items-center text-gray-500">
                                <Mail className="h-3 w-3 mr-1" />
                                {student.email.split('@')[0]}
                              </div>
                            </div>
                          </TableCell>
                          <TableCell>
                            <div className="text-sm">
                              {student.currentGrade && (
                                <Badge
                                  variant="outline"
                                  className={gradeColors[student.currentGrade as keyof typeof gradeColors] || "text-gray-600 bg-gray-100"}
                                >
                                  {student.currentGrade}
                                </Badge>
                              )}
                              {student.gpa && (
                                <div className="text-gray-500 mt-1">GPA: {student.gpa}</div>
                              )}
                            </div>
                          </TableCell>
                          <TableCell>
                            {student.attendance && (
                              <div className={`font-medium ${
                                student.attendance >= 95 ? "text-green-600" :
                                student.attendance >= 90 ? "text-blue-600" :
                                student.attendance >= 85 ? "text-yellow-600" : "text-red-600"
                              }`}>
                                {student.attendance}%
                              </div>
                            )}
                          </TableCell>
                          <TableCell>
                            <Badge
                              variant="outline"
                              className={statusColors[student.status]}
                            >
                              {student.status}
                            </Badge>
                          </TableCell>
                          <TableCell className="text-right">
                            <div className="flex justify-end gap-2">
                              <Button variant="outline" size="sm">
                                <Eye className="h-3 w-3" />
                              </Button>
                              <Button variant="outline" size="sm">
                                <MessageCircle className="h-3 w-3" />
                              </Button>
                              <Button variant="outline" size="sm">
                                <BarChart3 className="h-3 w-3" />
                              </Button>
                            </div>
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </div>
              )}
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardContent className="p-12">
              <div className="text-center">
                <Filter className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">Select a Class to View Students</h3>
                <p className="text-gray-500">
                  Please select one of your assigned classes to view and manage students.
                </p>
                <p className="text-sm text-gray-400 mt-2">
                  You can only view students from classes where you are the assigned teacher.
                </p>
              </div>
            </CardContent>
          </Card>
        )}

      </div>
    </AppLayout>
  );
}

export default function TeacherStudents() {
  return (
    <Suspense fallback={
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <div className="h-8 w-8 animate-spin mx-auto border-4 border-blue-600 border-t-transparent rounded-full" />
            <p className="mt-2 text-gray-600">Loading...</p>
          </div>
        </div>
      </AppLayout>
    }>
      <TeacherStudentsContent />
    </Suspense>
  );
}
