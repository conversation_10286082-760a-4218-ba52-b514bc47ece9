"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";

export default function AdminExamsRedirect() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the main exams management page
    router.replace("/exams");
  }, [router]);

  return (
    <div className="flex items-center justify-center h-64">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p className="mt-2 text-gray-600">Redirecting to Exam Management...</p>
      </div>
    </div>
  );
}
