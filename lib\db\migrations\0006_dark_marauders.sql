CREATE TYPE "public"."class_level" AS ENUM('nursery', 'lkg', 'ukg', 'grade_1', 'grade_2', 'grade_3', 'grade_4', 'grade_5', 'grade_6', 'grade_7', 'grade_8', 'grade_9', 'grade_10', 'grade_11', 'grade_12');--> statement-breakpoint
ALTER TABLE "academic_branches" DISABLE ROW LEVEL SECURITY;--> statement-breakpoint
ALTER TABLE "academic_streams" DISABLE ROW LEVEL SECURITY;--> statement-breakpoint
DROP TABLE "academic_branches" CASCADE;--> statement-breakpoint
DROP TABLE "academic_streams" CASCADE;--> statement-breakpoint
ALTER TABLE "academic_programs" DROP CONSTRAINT "academic_programs_stream_id_academic_streams_id_fk";
--> statement-breakpoint
ALTER TABLE "academic_programs" DROP CONSTRAINT "academic_programs_branch_id_academic_branches_id_fk";
--> statement-breakpoint
ALTER TABLE "academic_sections" DROP CONSTRAINT "academic_sections_stream_id_academic_streams_id_fk";
--> statement-breakpoint
ALTER TABLE "academic_sections" DROP CONSTRAINT "academic_sections_branch_id_academic_branches_id_fk";
--> statement-breakpoint
ALTER TABLE "students" DROP CONSTRAINT "students_stream_id_academic_streams_id_fk";
--> statement-breakpoint
ALTER TABLE "students" DROP CONSTRAINT "students_branch_id_academic_branches_id_fk";
--> statement-breakpoint
ALTER TABLE "academic_programs" ALTER COLUMN "type" SET DATA TYPE text;--> statement-breakpoint
ALTER TABLE "academic_programs" ALTER COLUMN "type" SET DEFAULT 'school'::text;--> statement-breakpoint
DROP TYPE "public"."program_type";--> statement-breakpoint
CREATE TYPE "public"."program_type" AS ENUM('school');--> statement-breakpoint
ALTER TABLE "academic_programs" ALTER COLUMN "type" SET DEFAULT 'school'::"public"."program_type";--> statement-breakpoint
ALTER TABLE "academic_programs" ALTER COLUMN "type" SET DATA TYPE "public"."program_type" USING "type"::"public"."program_type";--> statement-breakpoint
ALTER TABLE "academic_programs" ALTER COLUMN "stream_type" SET DATA TYPE text;--> statement-breakpoint
DROP TYPE "public"."stream_type";--> statement-breakpoint
CREATE TYPE "public"."stream_type" AS ENUM('science', 'commerce', 'arts');--> statement-breakpoint
ALTER TABLE "academic_programs" ALTER COLUMN "stream_type" SET DATA TYPE "public"."stream_type" USING "stream_type"::"public"."stream_type";--> statement-breakpoint
ALTER TABLE "academic_programs" ALTER COLUMN "duration" SET DEFAULT 1;--> statement-breakpoint
ALTER TABLE "institution_config" ALTER COLUMN "grading_system" SET DEFAULT 'Grade-based (A-F)';--> statement-breakpoint
ALTER TABLE "academic_programs" ADD COLUMN "class_level" "class_level" NOT NULL;--> statement-breakpoint
ALTER TABLE "academic_programs" ADD COLUMN "stream_type" "stream_type";--> statement-breakpoint
ALTER TABLE "academic_batches" DROP COLUMN "current_semester";--> statement-breakpoint
ALTER TABLE "academic_programs" DROP COLUMN "stream_id";--> statement-breakpoint
ALTER TABLE "academic_programs" DROP COLUMN "branch_id";--> statement-breakpoint
ALTER TABLE "academic_programs" DROP COLUMN "total_semesters";--> statement-breakpoint
ALTER TABLE "academic_programs" DROP COLUMN "department";--> statement-breakpoint
ALTER TABLE "academic_sections" DROP COLUMN "stream_id";--> statement-breakpoint
ALTER TABLE "academic_sections" DROP COLUMN "branch_id";--> statement-breakpoint
ALTER TABLE "academic_sections" DROP COLUMN "semester";--> statement-breakpoint
ALTER TABLE "classes" DROP COLUMN "semester";--> statement-breakpoint
ALTER TABLE "classes" DROP COLUMN "credits";--> statement-breakpoint
ALTER TABLE "classes" DROP COLUMN "is_elective";--> statement-breakpoint
ALTER TABLE "classes" DROP COLUMN "prerequisites";--> statement-breakpoint
ALTER TABLE "institution_config" DROP COLUMN "institution_type";--> statement-breakpoint
ALTER TABLE "students" DROP COLUMN "stream_id";--> statement-breakpoint
ALTER TABLE "students" DROP COLUMN "branch_id";--> statement-breakpoint
ALTER TABLE "students" DROP COLUMN "current_semester";--> statement-breakpoint
DROP TYPE "public"."institution_type";--> statement-breakpoint
DROP TYPE "public"."semester";--> statement-breakpoint
DROP TYPE "public"."stream_category";