"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import { Tabs, Ta<PERSON>Content, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Users,
  GraduationCap,
  IndianRupee,
  Calendar,
  Award,
  BookOpen,
  Target,
  PieChart,
  Activity,
  Download,
  RefreshCw,
  Filter
} from "lucide-react";
import { toast } from "sonner";

interface AnalyticsData {
  academic: {
    overallPerformance: number;
    gradeDistribution: Record<string, number>;
    subjectPerformance: Array<{
      subject: string;
      average: number;
      trend: "up" | "down" | "stable";
    }>;
  };
  attendance: {
    overall: number;
    byGrade: Array<{
      grade: string;
      attendance: number;
    }>;
    trends: {
      thisMonth: number;
      lastMonth: number;
      change: number;
    };
  };
  financial: {
    budgetUtilization: number;
    feeCollection: number;
    expenses: Record<string, number>;
  };
  staff: {
    totalTeachers: number;
    teacherRetention: number;
    averageExperience: number;
    qualifications: Record<string, number>;
  };
}

export default function PrincipalAnalyticsPage() {
  const [user, setUser] = useState<any>(null);
  const router = useRouter();
  const [analytics, setAnalytics] = useState<AnalyticsData | null>(null);
  const [loading, setLoading] = useState(true);
  const [activeTab, setActiveTab] = useState("academic");

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "principal") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
    fetchAnalytics();
  }, [router]);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      const response = await fetch("/api/principal/analytics");
      const result = await response.json();
      
      if (response.ok) {
        setAnalytics(result.data);
      } else {
        toast.error("Failed to fetch analytics");
      }
    } catch (error) {
      console.error("Error fetching analytics:", error);
      toast.error("Failed to fetch analytics");
    } finally {
      setLoading(false);
    }
  };

  const exportReport = () => {
    if (!analytics) return;
    
    const reportData = {
      generatedAt: new Date().toISOString(),
      academic: analytics.academic,
      attendance: analytics.attendance,
      financial: analytics.financial,
      staff: analytics.staff
    };

    const blob = new Blob([JSON.stringify(reportData, null, 2)], { type: "application/json" });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement("a");
    a.href = url;
    a.download = `school-analytics-${new Date().toISOString().split("T")[0]}.json`;
    a.click();
    window.URL.revokeObjectURL(url);
    toast.success("Analytics report exported successfully");
  };

  if (!user) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </AppLayout>
    );
  }

  if (loading || !analytics) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">School Analytics</h1>
            <p className="text-gray-600">
              Comprehensive insights into school performance and operations
            </p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={fetchAnalytics}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button variant="outline" onClick={exportReport}>
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
          </div>
        </div>

        {/* Analytics Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="academic">Academic</TabsTrigger>
            <TabsTrigger value="attendance">Attendance</TabsTrigger>
            <TabsTrigger value="financial">Financial</TabsTrigger>
            <TabsTrigger value="staff">Staff</TabsTrigger>
          </TabsList>

          {/* Academic Analytics */}
          <TabsContent value="academic" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Award className="h-5 w-5 mr-2" />
                    Overall Performance
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-green-600 mb-2">
                    {analytics.academic.overallPerformance}%
                  </div>
                  <Progress value={analytics.academic.overallPerformance} className="mb-2" />
                  <p className="text-sm text-gray-600">School-wide academic performance</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Grade Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    {Object.entries(analytics.academic.gradeDistribution).map(([grade, percentage]) => (
                      <div key={grade} className="flex items-center justify-between">
                        <span className="text-sm font-medium">{grade}</span>
                        <div className="flex items-center gap-2">
                          <Progress value={percentage} className="w-20" />
                          <span className="text-sm text-gray-600">{percentage}%</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Subject Performance</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {analytics.academic.subjectPerformance.map((subject) => (
                      <div key={subject.subject} className="flex items-center justify-between">
                        <div>
                          <div className="font-medium">{subject.subject}</div>
                          <div className="text-sm text-gray-600">{subject.average}% average</div>
                        </div>
                        <div className="flex items-center">
                          {subject.trend === "up" && <TrendingUp className="h-4 w-4 text-green-500" />}
                          {subject.trend === "down" && <TrendingDown className="h-4 w-4 text-red-500" />}
                          {subject.trend === "stable" && <Activity className="h-4 w-4 text-gray-500" />}
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Attendance Analytics */}
          <TabsContent value="attendance" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Calendar className="h-5 w-5 mr-2" />
                    Overall Attendance
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-blue-600 mb-2">
                    {analytics.attendance.overall}%
                  </div>
                  <Progress value={analytics.attendance.overall} className="mb-2" />
                  <div className="flex items-center gap-2 text-sm">
                    <TrendingUp className="h-4 w-4 text-green-500" />
                    <span className="text-green-600">+{analytics.attendance.trends.change}%</span>
                    <span className="text-gray-600">from last month</span>
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Attendance by Grade</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {analytics.attendance.byGrade.map((grade) => (
                      <div key={grade.grade} className="flex items-center justify-between">
                        <span className="font-medium">{grade.grade}</span>
                        <div className="flex items-center gap-2">
                          <Progress value={grade.attendance} className="w-20" />
                          <span className="text-sm text-gray-600">{grade.attendance}%</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Monthly Trends</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    <div className="flex justify-between">
                      <span className="text-gray-600">This Month</span>
                      <span className="font-bold">{analytics.attendance.trends.thisMonth}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Last Month</span>
                      <span className="font-bold">{analytics.attendance.trends.lastMonth}%</span>
                    </div>
                    <div className="flex justify-between">
                      <span className="text-gray-600">Change</span>
                      <span className={`font-bold ${analytics.attendance.trends.change > 0 ? 'text-green-600' : 'text-red-600'}`}>
                        {analytics.attendance.trends.change > 0 ? '+' : ''}{analytics.attendance.trends.change}%
                      </span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Financial Analytics */}
          <TabsContent value="financial" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <IndianRupee className="h-5 w-5 mr-2" />
                    Budget Utilization
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-purple-600 mb-2">
                    {analytics.financial.budgetUtilization}%
                  </div>
                  <Progress value={analytics.financial.budgetUtilization} className="mb-2" />
                  <p className="text-sm text-gray-600">Annual budget utilized</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Fee Collection</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-green-600 mb-2">
                    {analytics.financial.feeCollection}%
                  </div>
                  <Progress value={analytics.financial.feeCollection} className="mb-2" />
                  <p className="text-sm text-gray-600">Fee collection rate</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Expense Breakdown</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {Object.entries(analytics.financial.expenses).map(([category, percentage]) => (
                      <div key={category} className="flex items-center justify-between">
                        <span className="font-medium capitalize">{category}</span>
                        <div className="flex items-center gap-2">
                          <Progress value={percentage} className="w-20" />
                          <span className="text-sm text-gray-600">{percentage}%</span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>

          {/* Staff Analytics */}
          <TabsContent value="staff" className="space-y-6">
            <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center">
                    <Users className="h-5 w-5 mr-2" />
                    Total Teachers
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-blue-600 mb-2">
                    {analytics.staff.totalTeachers}
                  </div>
                  <p className="text-sm text-gray-600">Active teaching staff</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Teacher Retention</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="text-3xl font-bold text-green-600 mb-2">
                    {analytics.staff.teacherRetention}%
                  </div>
                  <Progress value={analytics.staff.teacherRetention} className="mb-2" />
                  <p className="text-sm text-gray-600">Annual retention rate</p>
                </CardContent>
              </Card>

              <Card>
                <CardHeader>
                  <CardTitle>Qualifications</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {Object.entries(analytics.staff.qualifications).map(([qualification, count]) => (
                      <div key={qualification} className="flex items-center justify-between">
                        <span className="font-medium">{qualification}</span>
                        <span className="text-lg font-bold text-gray-900">{count}</span>
                      </div>
                    ))}
                  </div>
                  <div className="mt-4 pt-4 border-t">
                    <div className="flex justify-between">
                      <span className="text-gray-600">Average Experience</span>
                      <span className="font-bold">{analytics.staff.averageExperience} years</span>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </div>
          </TabsContent>
        </Tabs>
      </div>
    </AppLayout>
  );
}
