import { Hono } from "hono";
import { zValidator } from "@hono/zod-validator";
import { mockHostels, mockHostelRooms } from "@/lib/mock-db";
import { hostelSchema, hostelRoomSchema } from "@/lib/schemas";
import { generateId } from "@/lib/utils";

const app = new Hono()
  // Hostels
  .get("/hostels", async (c) => {
    const page = parseInt(c.req.query("page") || "1");
    const limit = parseInt(c.req.query("limit") || "10");
    const search = c.req.query("search");
    const type = c.req.query("type");
    const status = c.req.query("status");

    let filteredHostels = [...mockHostels];

    if (search) {
      const searchLower = search.toLowerCase();
      filteredHostels = filteredHostels.filter(
        (hostel) =>
          hostel.name.toLowerCase().includes(searchLower) ||
          hostel.wardenName.toLowerCase().includes(searchLower) ||
          hostel.address.toLowerCase().includes(searchLower)
      );
    }

    if (type) {
      filteredHostels = filteredHostels.filter((hostel) => hostel.type === type);
    }

    if (status) {
      filteredHostels = filteredHostels.filter((hostel) => hostel.status === status);
    }

    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedHostels = filteredHostels.slice(startIndex, endIndex);

    return c.json({
      data: paginatedHostels,
      pagination: {
        page,
        limit,
        total: filteredHostels.length,
        totalPages: Math.ceil(filteredHostels.length / limit),
      },
    });
  })
  .post(
    "/hostels",
    zValidator("json", hostelSchema.omit({ id: true, createdAt: true, updatedAt: true })),
    async (c) => {
      const values = c.req.valid("json");
      
      const newHostel = {
        ...values,
        id: generateId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      mockHostels.push(newHostel);

      return c.json({ data: newHostel }, 201);
    }
  )
  .get("/hostels/:id", async (c) => {
    const id = c.req.param("id");
    const hostel = mockHostels.find((h) => h.id === id);

    if (!hostel) {
      return c.json({ error: "Hostel not found" }, 404);
    }

    return c.json({ data: hostel });
  })
  .put(
    "/hostels/:id",
    zValidator("json", hostelSchema.omit({ id: true, createdAt: true, updatedAt: true })),
    async (c) => {
      const id = c.req.param("id");
      const values = c.req.valid("json");

      const hostelIndex = mockHostels.findIndex((h) => h.id === id);

      if (hostelIndex === -1) {
        return c.json({ error: "Hostel not found" }, 404);
      }

      mockHostels[hostelIndex] = {
        ...mockHostels[hostelIndex],
        ...values,
        updatedAt: new Date().toISOString(),
      };

      return c.json({ data: mockHostels[hostelIndex] });
    }
  )
  .delete("/hostels/:id", async (c) => {
    const id = c.req.param("id");
    const hostelIndex = mockHostels.findIndex((h) => h.id === id);

    if (hostelIndex === -1) {
      return c.json({ error: "Hostel not found" }, 404);
    }

    mockHostels.splice(hostelIndex, 1);

    return c.json({ message: "Hostel deleted successfully" });
  })

  // Hostel Rooms
  .get("/rooms", async (c) => {
    const page = parseInt(c.req.query("page") || "1");
    const limit = parseInt(c.req.query("limit") || "10");
    const hostelId = c.req.query("hostelId");
    const roomType = c.req.query("roomType");
    const status = c.req.query("status");

    let filteredRooms = [...mockHostelRooms];

    if (hostelId) {
      filteredRooms = filteredRooms.filter((room) => room.hostelId === hostelId);
    }

    if (roomType) {
      filteredRooms = filteredRooms.filter((room) => room.roomType === roomType);
    }

    if (status) {
      filteredRooms = filteredRooms.filter((room) => room.status === status);
    }

    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedRooms = filteredRooms.slice(startIndex, endIndex);

    return c.json({
      data: paginatedRooms,
      pagination: {
        page,
        limit,
        total: filteredRooms.length,
        totalPages: Math.ceil(filteredRooms.length / limit),
      },
    });
  })
  .post(
    "/rooms",
    zValidator("json", hostelRoomSchema.omit({ id: true, createdAt: true, updatedAt: true })),
    async (c) => {
      const values = c.req.valid("json");
      
      const newRoom = {
        ...values,
        id: generateId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      mockHostelRooms.push(newRoom);

      return c.json({ data: newRoom }, 201);
    }
  )
  .get("/rooms/:id", async (c) => {
    const id = c.req.param("id");
    const room = mockHostelRooms.find((r) => r.id === id);

    if (!room) {
      return c.json({ error: "Room not found" }, 404);
    }

    return c.json({ data: room });
  })
  .put(
    "/rooms/:id",
    zValidator("json", hostelRoomSchema.omit({ id: true, createdAt: true, updatedAt: true })),
    async (c) => {
      const id = c.req.param("id");
      const values = c.req.valid("json");

      const roomIndex = mockHostelRooms.findIndex((r) => r.id === id);

      if (roomIndex === -1) {
        return c.json({ error: "Room not found" }, 404);
      }

      mockHostelRooms[roomIndex] = {
        ...mockHostelRooms[roomIndex],
        ...values,
        updatedAt: new Date().toISOString(),
      };

      return c.json({ data: mockHostelRooms[roomIndex] });
    }
  )
  .delete("/rooms/:id", async (c) => {
    const id = c.req.param("id");
    const roomIndex = mockHostelRooms.findIndex((r) => r.id === id);

    if (roomIndex === -1) {
      return c.json({ error: "Room not found" }, 404);
    }

    mockHostelRooms.splice(roomIndex, 1);

    return c.json({ message: "Room deleted successfully" });
  })

  // Hostel Reports
  .get("/reports/summary", async (c) => {
    const totalHostels = mockHostels.length;
    const activeHostels = mockHostels.filter(h => h.status === "active").length;
    const totalRooms = mockHostelRooms.length;
    const occupiedRooms = mockHostelRooms.filter(r => r.status === "occupied").length;
    const availableRooms = mockHostelRooms.filter(r => r.status === "available").length;
    const maintenanceRooms = mockHostelRooms.filter(r => r.status === "maintenance").length;

    const totalBeds = mockHostelRooms.reduce((sum, room) => sum + room.capacity, 0);
    const occupiedBeds = mockHostelRooms.reduce((sum, room) => sum + room.occupiedBeds, 0);
    const availableBeds = totalBeds - occupiedBeds;

    const occupancyRate = totalBeds > 0 ? (occupiedBeds / totalBeds) * 100 : 0;

    const hostelTypeStats = mockHostels.reduce((acc, hostel) => {
      acc[hostel.type] = (acc[hostel.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const roomTypeStats = mockHostelRooms.reduce((acc, room) => {
      acc[room.roomType] = (acc[room.roomType] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const hostelStats = mockHostels.map(hostel => {
      const hostelRooms = mockHostelRooms.filter(r => r.hostelId === hostel.id);
      const hostelBeds = hostelRooms.reduce((sum, r) => sum + r.capacity, 0);
      const hostelOccupied = hostelRooms.reduce((sum, r) => sum + r.occupiedBeds, 0);
      
      return {
        hostelId: hostel.id,
        hostelName: hostel.name,
        totalRooms: hostelRooms.length,
        occupiedRooms: hostelRooms.filter(r => r.status === "occupied").length,
        totalBeds: hostelBeds,
        occupiedBeds: hostelOccupied,
        occupancyRate: hostelBeds > 0 ? (hostelOccupied / hostelBeds) * 100 : 0,
        monthlyRevenue: hostelOccupied * hostel.monthlyFee,
      };
    });

    return c.json({
      data: {
        totalHostels,
        activeHostels,
        totalRooms,
        occupiedRooms,
        availableRooms,
        maintenanceRooms,
        totalBeds,
        occupiedBeds,
        availableBeds,
        occupancyRate,
        hostelTypeStats,
        roomTypeStats,
        hostelStats,
      },
    });
  })

  // Room allocation
  .post("/rooms/:id/allocate", async (c) => {
    const id = c.req.param("id");
    const { studentId, bedsToAllocate = 1 } = await c.req.json();

    const roomIndex = mockHostelRooms.findIndex((r) => r.id === id);

    if (roomIndex === -1) {
      return c.json({ error: "Room not found" }, 404);
    }

    const room = mockHostelRooms[roomIndex];

    if (room.occupiedBeds + bedsToAllocate > room.capacity) {
      return c.json({ error: "Not enough beds available" }, 400);
    }

    mockHostelRooms[roomIndex] = {
      ...room,
      occupiedBeds: room.occupiedBeds + bedsToAllocate,
      status: room.occupiedBeds + bedsToAllocate >= room.capacity ? "occupied" : "available",
      updatedAt: new Date().toISOString(),
    };

    return c.json({ data: mockHostelRooms[roomIndex] });
  })

  // Room deallocation
  .post("/rooms/:id/deallocate", async (c) => {
    const id = c.req.param("id");
    const { bedsToFree = 1 } = await c.req.json();

    const roomIndex = mockHostelRooms.findIndex((r) => r.id === id);

    if (roomIndex === -1) {
      return c.json({ error: "Room not found" }, 404);
    }

    const room = mockHostelRooms[roomIndex];

    if (room.occupiedBeds < bedsToFree) {
      return c.json({ error: "Cannot free more beds than occupied" }, 400);
    }

    mockHostelRooms[roomIndex] = {
      ...room,
      occupiedBeds: room.occupiedBeds - bedsToFree,
      status: room.occupiedBeds - bedsToFree === 0 ? "available" : room.status,
      updatedAt: new Date().toISOString(),
    };

    return c.json({ data: mockHostelRooms[roomIndex] });
  });

export default app;
