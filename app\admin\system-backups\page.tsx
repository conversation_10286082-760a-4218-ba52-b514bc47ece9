"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from "@/components/ui/dialog";
import { 
  useGetSystemBackups, 
  useCreateSystemBackup, 
  useDeleteSystemBackup, 
  useDownloadSystemBackup,
  useGetBackupStats 
} from "@/features/api/use-system-backups";
import { useLogUserAction } from "@/features/api/use-audit-logs";
import { toast } from "sonner";
import {
  Database,
  Plus,
  Download,
  Trash2,
  RefreshCw,
  Calendar,
  HardDrive,
  CheckCircle,
  AlertCircle,
  Clock,
  FileText,
  ChevronLeft,
  ChevronRight,
  Play,
  Pause,
} from "lucide-react";

export default function SystemBackupsPage() {
  const [user, setUser] = useState<any>(null);
  const [page, setPage] = useState(1);
  const [showCreateDialog, setShowCreateDialog] = useState(false);
  const [newBackup, setNewBackup] = useState({
    name: "",
    description: "",
    backupType: "manual" as "full" | "incremental" | "manual",
  });
  const router = useRouter();

  // API hooks
  const { data: backupsData, isLoading: backupsLoading, refetch } = useGetSystemBackups(page, 10);
  const { data: statsData } = useGetBackupStats();
  const createBackupMutation = useCreateSystemBackup();
  const deleteBackupMutation = useDeleteSystemBackup();
  const downloadBackupMutation = useDownloadSystemBackup();
  const logUserAction = useLogUserAction();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "super_admin") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  const handleCreateBackup = async () => {
    try {
      await createBackupMutation.mutateAsync({
        ...newBackup,
        createdBy: user.id,
      });
      
      setShowCreateDialog(false);
      setNewBackup({ name: "", description: "", backupType: "manual" });
      logUserAction("create_system_backup", `Created backup: ${newBackup.name}`);
      refetch();
    } catch (error) {
      console.error("Error creating backup:", error);
    }
  };

  const handleDeleteBackup = async (id: string, name: string) => {
    if (confirm(`Are you sure you want to delete backup "${name}"?`)) {
      try {
        await deleteBackupMutation.mutateAsync(id);
        logUserAction("delete_system_backup", `Deleted backup: ${name}`);
        refetch();
      } catch (error) {
        console.error("Error deleting backup:", error);
      }
    }
  };

  const handleDownloadBackup = async (id: string, name: string) => {
    try {
      await downloadBackupMutation.mutateAsync(id);
      logUserAction("download_system_backup", `Downloaded backup: ${name}`);
    } catch (error) {
      console.error("Error downloading backup:", error);
    }
  };

  const backups = backupsData?.data || [];
  const pagination = backupsData?.pagination;
  const stats = statsData?.data;

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "completed":
        return <Badge className="bg-green-100 text-green-800"><CheckCircle className="h-3 w-3 mr-1" />Completed</Badge>;
      case "running":
        return <Badge className="bg-blue-100 text-blue-800"><Play className="h-3 w-3 mr-1" />Running</Badge>;
      case "pending":
        return <Badge className="bg-yellow-100 text-yellow-800"><Clock className="h-3 w-3 mr-1" />Pending</Badge>;
      case "failed":
        return <Badge className="bg-red-100 text-red-800"><AlertCircle className="h-3 w-3 mr-1" />Failed</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }
  };

  const formatFileSize = (bytes: number) => {
    if (!bytes) return "N/A";
    const sizes = ["Bytes", "KB", "MB", "GB"];
    const i = Math.floor(Math.log(bytes) / Math.log(1024));
    return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + " " + sizes[i];
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">System Backups</h1>
            <p className="text-gray-600">Manage database backups and system recovery</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={() => refetch()}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Dialog open={showCreateDialog} onOpenChange={setShowCreateDialog}>
              <DialogTrigger asChild>
                <Button>
                  <Plus className="h-4 w-4 mr-2" />
                  Create Backup
                </Button>
              </DialogTrigger>
              <DialogContent>
                <DialogHeader>
                  <DialogTitle>Create New Backup</DialogTitle>
                </DialogHeader>
                <div className="space-y-4">
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Backup Name</label>
                    <Input
                      placeholder="Enter backup name"
                      value={newBackup.name}
                      onChange={(e) => setNewBackup({ ...newBackup, name: e.target.value })}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Description</label>
                    <Input
                      placeholder="Optional description"
                      value={newBackup.description}
                      onChange={(e) => setNewBackup({ ...newBackup, description: e.target.value })}
                    />
                  </div>
                  <div>
                    <label className="block text-sm font-medium text-gray-700 mb-2">Backup Type</label>
                    <select
                      value={newBackup.backupType}
                      onChange={(e) => setNewBackup({ ...newBackup, backupType: e.target.value as any })}
                      className="w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    >
                      <option value="manual">Manual Backup</option>
                      <option value="full">Full Backup</option>
                      <option value="incremental">Incremental Backup</option>
                    </select>
                  </div>
                  <div className="flex gap-2 pt-4">
                    <Button variant="outline" onClick={() => setShowCreateDialog(false)}>
                      Cancel
                    </Button>
                    <Button 
                      onClick={handleCreateBackup}
                      disabled={!newBackup.name || createBackupMutation.isPending}
                    >
                      {createBackupMutation.isPending ? "Creating..." : "Create Backup"}
                    </Button>
                  </div>
                </div>
              </DialogContent>
            </Dialog>
          </div>
        </div>

        {/* Statistics Cards */}
        {stats && (
          <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <Database className="h-8 w-8 text-blue-600" />
                  <div className="ml-4">
                    <div className="text-2xl font-bold text-blue-600">
                      {stats.totalBackups}
                    </div>
                    <p className="text-sm text-gray-600">Total Backups</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <CheckCircle className="h-8 w-8 text-green-600" />
                  <div className="ml-4">
                    <div className="text-2xl font-bold text-green-600">
                      {stats.statusCounts?.completed || 0}
                    </div>
                    <p className="text-sm text-gray-600">Completed</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <AlertCircle className="h-8 w-8 text-red-600" />
                  <div className="ml-4">
                    <div className="text-2xl font-bold text-red-600">
                      {stats.statusCounts?.failed || 0}
                    </div>
                    <p className="text-sm text-gray-600">Failed</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardContent className="p-6">
                <div className="flex items-center">
                  <HardDrive className="h-8 w-8 text-purple-600" />
                  <div className="ml-4">
                    <div className="text-2xl font-bold text-purple-600">
                      {formatFileSize(stats.totalSize)}
                    </div>
                    <p className="text-sm text-gray-600">Total Size</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Backups Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span className="flex items-center">
                <FileText className="h-5 w-5 mr-2" />
                Backup History
              </span>
              {pagination && (
                <span className="text-sm text-gray-500">
                  Showing {((Number(pagination.page) - 1) * Number(pagination.limit)) + 1} to {Math.min(Number(pagination.page) * Number(pagination.limit), Number(pagination.total))} of {Number(pagination.total)} backups
                </span>
              )}
            </CardTitle>
          </CardHeader>
          <CardContent>
            {backupsLoading ? (
              <div className="flex justify-center py-8">
                <RefreshCw className="h-6 w-6 animate-spin" />
              </div>
            ) : (
              <div className="space-y-4">
                {backups.map((backup: any) => (
                  <div key={backup.id} className="border rounded-lg p-4 hover:bg-gray-50">
                    <div className="flex items-center justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="font-semibold text-gray-900">{backup.name}</h3>
                          {getStatusBadge(backup.status)}
                          <Badge variant="outline">{backup.backupType}</Badge>
                        </div>
                        
                        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm text-gray-600">
                          <div>
                            <span className="font-medium">Created:</span>
                            <br />
                            {new Date(backup.createdAt).toLocaleDateString()}
                          </div>
                          <div>
                            <span className="font-medium">Size:</span>
                            <br />
                            {formatFileSize(backup.fileSize)}
                          </div>
                          <div>
                            <span className="font-medium">Duration:</span>
                            <br />
                            {backup.completedAt && backup.startedAt 
                              ? `${Math.round((new Date(backup.completedAt).getTime() - new Date(backup.startedAt).getTime()) / 1000)}s`
                              : "N/A"
                            }
                          </div>
                          <div>
                            <span className="font-medium">Created by:</span>
                            <br />
                            {backup.createdBy ? `${backup.createdBy.firstName} ${backup.createdBy.lastName}` : "System"}
                          </div>
                        </div>
                        
                        {backup.description && (
                          <p className="mt-2 text-sm text-gray-600">{backup.description}</p>
                        )}
                        
                        {backup.error && (
                          <p className="mt-2 text-sm text-red-600">Error: {backup.error}</p>
                        )}
                      </div>
                      
                      <div className="flex gap-2 ml-4">
                        {backup.status === "completed" && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDownloadBackup(backup.id, backup.name)}
                            disabled={downloadBackupMutation.isPending}
                          >
                            <Download className="h-4 w-4" />
                          </Button>
                        )}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleDeleteBackup(backup.id, backup.name)}
                          disabled={deleteBackupMutation.isPending}
                          className="text-red-600 hover:text-red-700"
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}

                {backups.length === 0 && (
                  <div className="text-center py-8 text-gray-500">
                    No backups found. Create your first backup to get started.
                  </div>
                )}
              </div>
            )}

            {/* Pagination */}
            {pagination && Number(pagination.pages) > 1 && (
              <div className="flex items-center justify-between mt-6">
                <div className="text-sm text-gray-500">
                  Page {pagination.page} of {pagination.pages}
                </div>
                <div className="flex gap-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPage(Number(pagination.page) - 1)}
                    disabled={Number(pagination.page) <= 1}
                  >
                    <ChevronLeft className="h-4 w-4" />
                    Previous
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => setPage(Number(pagination.page) + 1)}
                    disabled={Number(pagination.page) >= Number(pagination.pages)}
                  >
                    Next
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
