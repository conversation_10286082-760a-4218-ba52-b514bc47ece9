# School Mode Transition Plan

## Overview
Transition the school management system from a dual-mode (school/college) system with complex program hierarchies to a school-only system with simple class-based structure.

## Key Changes
- Remove college mode and institution type switching
- Replace programs/streams/branches with simple class structure (Nursery to Grade 12)
- Simplify academic structure to: Classes → Sections → Students
- Remove semester-based features (college-specific)
- Update all UI components and navigation

## Files That Need Changes

### 1. Database Schema Changes
**File: `lib/db/schema.ts`**
- Remove `institutionTypeEnum` and fix `institutionType` to "school"
- Remove `academicStreams`, `academicBranches` tables
- Simplify `academicPrograms` to just represent classes (Grade 1, Grade 2, etc.)
- Update relations to remove stream/branch references
- Remove semester-related fields

### 2. API Routes to Update/Remove

#### Remove Completely:
- `app/api/[[...route]]/academic-streams.ts`
- `app/api/[[...route]]/academic-branches.ts`

#### Update:
- `app/api/[[...route]]/route.ts` - Remove stream/branch route registrations
- `app/api/[[...route]]/academic-programs.ts` - Simplify to class management
- `app/api/[[...route]]/institution-config.ts` - Remove college mode options
- `app/api/[[...route]]/students.ts` - Remove program/stream/branch filtering
- `app/api/[[...route]]/fee-structures.ts` - Remove college-specific options

### 3. Admin Pages to Update/Remove

#### Remove Completely:
- `app/admin/streams/` - Stream management not needed
- `app/admin/programs/new/page.tsx` - Complex program creation
- `app/admin/test-hierarchical/` - Hierarchical testing

#### Update:
- `app/admin/programs/page.tsx` - Convert to simple class management
- `app/admin/academic/page.tsx` - Remove program complexity, focus on classes
- `app/admin/sections/page.tsx` - Remove program/stream filtering
- `app/admin/students/page.tsx` - Remove hierarchical filtering
- `app/admin/students/new/page.tsx` - Simplify admission form
- `app/admin/institution-config/page.tsx` - Remove college mode toggle
- `app/admin/batches/page.tsx` - Simplify to academic sessions

### 4. Admission Pages to Update
- `app/admission/register/page.tsx` - Remove program selection, use simple class selection
- `app/admission/programs/page.tsx` - Convert to class information page

### 5. Components to Update/Remove

#### Remove Completely:
- `components/ui/hierarchical-program-selector.tsx`
- Any stream/branch selector components

#### Update:
- Navigation components to remove program/stream references
- Student forms to use simple class selection
- Dashboard components to remove college-specific metrics

### 6. API Hooks to Update/Remove
**Directory: `features/api/`**
- Remove stream and branch related hooks
- Update program hooks to handle classes
- Simplify hierarchical data hooks

### 7. Navigation and Menu Updates
**File: `lib/navigation-config.ts`**
- Update admin navigation to remove program/stream management
- Rename "Academic Programs" to "Classes"
- Remove college-specific menu items

### 8. Database Migration Requirements
- Create migration to remove stream/branch tables
- Update existing program records to represent classes
- Remove college-specific data
- Update foreign key constraints

## New Class Structure

### Standard School Classes:
```
Nursery, LKG, UKG, 
Grade 1, Grade 2, Grade 3, Grade 4, Grade 5,
Grade 6, Grade 7, Grade 8, Grade 9, Grade 10,
Grade 11 (Science/Commerce/Arts), Grade 12 (Science/Commerce/Arts)
```

### Academic Hierarchy:
```
Academic Year (2024-25)
├── Classes (Grade 1, Grade 2, etc.)
    ├── Sections (A, B, C, etc.)
        ├── Students
```

## Implementation Priority

### Phase 1: Core Structure
1. Update database schema
2. Update API routes for classes and students
3. Update institution config to remove college mode

### Phase 2: Admin Interface
1. Update admin navigation
2. Convert program management to class management
3. Update student admission forms
4. Update academic management pages

### Phase 3: User Interfaces
1. Update admission portal
2. Update teacher/student dashboards
3. Update parent interfaces
4. Update reports and analytics

### Phase 4: Cleanup
1. Remove unused components
2. Remove unused API routes
3. Update documentation
4. Test all functionality

## Benefits of School-Only Mode
- Simplified user experience
- Reduced complexity in codebase
- Better performance with simpler queries
- Easier maintenance and updates
- More intuitive for school administrators
- Reduced training requirements for users

## Features That Need Changes

### 1. Student Admission System
**Current**: Complex program/stream/branch selection with hierarchical dropdowns
**New**: Simple class selection dropdown (Nursery to Grade 12)
- Remove HierarchicalProgramSelector component
- Replace with simple class dropdown
- Remove stream/branch fields from admission forms
- Update admission validation logic

### 2. Academic Management
**Current**: Program → Stream → Branch → Batch hierarchy
**New**: Class → Section → Student hierarchy
- Convert program management to class management
- Remove stream and branch management pages
- Simplify batch management to academic sessions
- Update section assignment to be class-based

### 3. Student Management
**Current**: Filter by Program → Stream → Branch → Section
**New**: Filter by Class → Section
- Remove hierarchical filtering components
- Simplify search and filter options
- Update student listing and management
- Remove program-based student categorization

### 4. Fee Management
**Current**: Program-based fee structures with stream/branch variations
**New**: Class-based fee structures
- Remove program/stream/branch fee categories
- Simplify to class-based fee structures
- Update fee calculation logic
- Remove college-specific fee types

### 5. Academic Structure
**Current**: Semester-based (college) and year-based (school) systems
**New**: Year-based system only
- Remove semester management
- Remove college-specific academic calendars
- Simplify to annual academic sessions
- Update grade and assessment systems

### 6. Reporting and Analytics
**Current**: Program/stream/branch-based reports
**New**: Class-based reports
- Update dashboard metrics
- Simplify academic performance reports
- Remove college-specific analytics
- Update enrollment and capacity reports

### 7. User Interface Navigation
**Current**: Separate school/college navigation paths
**New**: Unified school navigation
- Remove institution type switching
- Update admin navigation menus
- Simplify role-based access
- Remove college-specific pages

### 8. Data Models and APIs
**Current**: Complex relational structure with streams/branches
**New**: Simplified class-based structure
- Update database relationships
- Simplify API endpoints
- Remove unnecessary data models
- Update validation rules

## Detailed File Changes Required

### Database Schema (`lib/db/schema.ts`)
```sql
-- Remove these tables:
DROP TABLE academic_streams;
DROP TABLE academic_branches;

-- Update academic_programs to represent classes:
ALTER TABLE academic_programs
  DROP COLUMN stream_id,
  DROP COLUMN branch_id,
  DROP COLUMN total_semesters,
  ADD COLUMN class_level VARCHAR(20), -- "Grade 1", "Grade 2", etc.
  ADD COLUMN stream_type VARCHAR(20); -- For Grade 11/12: "Science", "Commerce", "Arts"

-- Update institution_config:
ALTER TABLE institution_config
  DROP COLUMN institution_type; -- Always "school" now
```

### API Routes Changes

#### `app/api/[[...route]]/route.ts`
- Remove: `.route("/academic-streams", academicStreams)`
- Remove: `.route("/academic-branches", academicBranches)`

#### `app/api/[[...route]]/academic-programs.ts`
- Remove stream/branch validation
- Update to handle simple class structure
- Remove college-specific logic
- Update seat management for class-based system

#### `app/api/[[...route]]/students.ts`
- Remove hierarchical filtering (stream/branch)
- Update to class-based filtering only
- Simplify admission options API

### Frontend Pages Changes

#### `app/admin/programs/page.tsx` → `app/admin/classes/page.tsx`
- Rename and restructure for class management
- Remove stream/branch columns
- Add class level and stream type fields
- Simplify CRUD operations

#### `app/admin/students/new/page.tsx`
- Remove HierarchicalProgramSelector
- Add simple class selection dropdown
- Remove stream/branch fields
- Update form validation

#### `app/admin/academic/page.tsx`
- Remove program complexity
- Focus on class-based metrics
- Update quick actions
- Remove college-specific features

#### `app/admission/register/page.tsx`
- Replace program selection with class selection
- Remove academic complexity
- Simplify to basic school admission form

### Components to Remove
- `components/ui/hierarchical-program-selector.tsx`
- Any stream/branch selector components
- College-specific form components

### Navigation Updates (`lib/navigation-config.ts`)
- Change "Academic Programs" to "Classes"
- Remove "Streams" and "Branches" menu items
- Update URLs and descriptions
- Remove college-specific navigation

## Testing Requirements
- Test student admission flow with new class structure
- Test academic management with simplified hierarchy
- Test reporting with class-based data
- Test role-based access with updated navigation
- Test data migration from existing structure
