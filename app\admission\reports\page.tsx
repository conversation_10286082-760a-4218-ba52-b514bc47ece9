"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  BarChart3,
  TrendingUp,
  TrendingDown,
  Users,
  Calendar,
  Download,
  Filter,
  Eye,
  FileText,
  PieChart,
  Target,
  Award,
  Clock,
  CheckCircle,
  XCircle,
  AlertTriangle,
  IndianRupee,
  GraduationCap,
  MapPin,
  Phone,
} from "lucide-react";

export default function AdmissionReports() {
  const [user, setUser] = useState<any>(null);
  const [selectedPeriod, setSelectedPeriod] = useState("current");
  const [selectedReport, setSelectedReport] = useState("overview");
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "admission") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  const admissionStats = {
    totalApplications: 1247,
    approvedApplications: 892,
    rejectedApplications: 198,
    pendingApplications: 157,
    interviewsCompleted: 945,
    averageProcessingTime: 7.2,
    approvalRate: 71.5,
    totalRevenue: 11250000,
  };

  const monthlyData = [
    { month: "Sep 2023", applications: 145, approved: 98, rejected: 25, pending: 22, revenue: 1225000 },
    { month: "Oct 2023", applications: 189, approved: 135, rejected: 31, pending: 23, revenue: 1687500 },
    { month: "Nov 2023", applications: 167, approved: 119, rejected: 28, pending: 20, revenue: 1487500 },
    { month: "Dec 2023", applications: 134, approved: 95, rejected: 22, pending: 17, revenue: 1187500 },
    { month: "Jan 2024", applications: 198, approved: 142, rejected: 35, pending: 21, revenue: 1775000 },
    { month: "Feb 2024", applications: 156, approved: 112, rejected: 24, pending: 20, revenue: 1400000 },
    { month: "Mar 2024", applications: 178, approved: 127, rejected: 28, pending: 23, revenue: 1587500 },
    { month: "Apr 2024", applications: 80, approved: 64, rejected: 5, pending: 11, revenue: 800000 },
  ];

  const programWiseData = [
    { program: "Regular Academic", applications: 456, approved: 342, rejected: 67, pending: 47, seats: 400, revenue: 4275000 },
    { program: "Science Stream", applications: 234, approved: 178, rejected: 32, pending: 24, seats: 200, revenue: 2403000 },
    { program: "Commerce Stream", applications: 189, approved: 145, rejected: 26, pending: 18, seats: 150, revenue: 1885000 },
    { program: "Arts Stream", applications: 123, approved: 98, rejected: 15, pending: 10, seats: 100, revenue: 1176000 },
    { program: "Sports Quota", applications: 87, approved: 65, rejected: 12, pending: 10, seats: 80, revenue: 910000 },
    { program: "Music Program", applications: 67, approved: 45, rejected: 15, pending: 7, seats: 50, revenue: 652500 },
    { program: "International (IB)", applications: 91, approved: 19, rejected: 51, pending: 21, seats: 60, revenue: 475000 },
  ];

  const geographicData = [
    { state: "Maharashtra", applications: 387, approved: 278, percentage: 31.1 },
    { state: "Delhi", applications: 234, approved: 167, percentage: 18.7 },
    { state: "Karnataka", applications: 189, approved: 134, percentage: 15.1 },
    { state: "Gujarat", applications: 156, approved: 112, percentage: 12.5 },
    { state: "Rajasthan", applications: 123, approved: 89, percentage: 9.9 },
    { state: "Uttar Pradesh", applications: 98, approved: 67, percentage: 7.5 },
    { state: "Others", applications: 60, approved: 45, percentage: 5.2 },
  ];

  const interviewData = {
    totalInterviews: 945,
    averageRating: 4.2,
    stronglyRecommended: 234,
    recommended: 456,
    conditional: 167,
    notRecommended: 88,
    averageDuration: 32,
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "approved": return "text-green-600 bg-green-100";
      case "rejected": return "text-red-600 bg-red-100";
      case "pending": return "text-yellow-600 bg-yellow-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const calculatePercentage = (value: number, total: number) => {
    return ((value / total) * 100).toFixed(1);
  };

  const formatCurrency = (amount: number) => {
    if (amount >= 10000000) return `₹${(amount / 10000000).toFixed(1)}Cr`;
    if (amount >= 100000) return `₹${(amount / 100000).toFixed(1)}L`;
    if (amount >= 1000) return `₹${(amount / 1000).toFixed(0)}K`;
    return `₹${amount.toLocaleString()}`;
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Admission Reports & Analytics</h1>
            <p className="text-gray-600">Comprehensive admission statistics and performance metrics</p>
          </div>
          <div className="flex gap-2">
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="current">Current Academic Year</option>
              <option value="previous">Previous Academic Year</option>
              <option value="last6months">Last 6 Months</option>
              <option value="custom">Custom Range</option>
            </select>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
          </div>
        </div>

        {/* Key Metrics */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {admissionStats.totalApplications.toLocaleString()}
                  </div>
                  <p className="text-sm text-gray-600">Total Applications</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600">+12.5%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {admissionStats.approvalRate}%
                  </div>
                  <p className="text-sm text-gray-600">Approval Rate</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600">+3.2%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    {admissionStats.averageProcessingTime}
                  </div>
                  <p className="text-sm text-gray-600">Avg Processing Days</p>
                  <div className="flex items-center mt-1">
                    <TrendingDown className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600">-1.3 days</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <IndianRupee className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {formatCurrency(admissionStats.totalRevenue)}
                  </div>
                  <p className="text-sm text-gray-600">Total Revenue</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600">+18.7%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Report Tabs */}
        <Card>
          <CardHeader>
            <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
              {[
                { key: "overview", label: "Overview" },
                { key: "trends", label: "Monthly Trends" },
                { key: "programs", label: "Program Analysis" },
                { key: "geography", label: "Geographic Data" },
                { key: "interviews", label: "Interview Analytics" },
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setSelectedReport(tab.key)}
                  className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                    selectedReport === tab.key
                      ? "bg-white text-blue-600 shadow-sm"
                      : "text-gray-600 hover:text-gray-900"
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </div>
          </CardHeader>
          <CardContent>
            {/* Overview */}
            {selectedReport === "overview" && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900">Admission Overview</h3>
                
                {/* Status Distribution */}
                <div className="grid gap-6 lg:grid-cols-2">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-4">Application Status Distribution</h4>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                        <div className="flex items-center">
                          <CheckCircle className="h-5 w-5 text-green-600 mr-3" />
                          <span className="font-medium text-green-800">Approved</span>
                        </div>
                        <div className="text-right">
                          <div className="font-bold text-green-800">{admissionStats.approvedApplications}</div>
                          <div className="text-sm text-green-600">
                            {calculatePercentage(admissionStats.approvedApplications, admissionStats.totalApplications)}%
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                        <div className="flex items-center">
                          <XCircle className="h-5 w-5 text-red-600 mr-3" />
                          <span className="font-medium text-red-800">Rejected</span>
                        </div>
                        <div className="text-right">
                          <div className="font-bold text-red-800">{admissionStats.rejectedApplications}</div>
                          <div className="text-sm text-red-600">
                            {calculatePercentage(admissionStats.rejectedApplications, admissionStats.totalApplications)}%
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                        <div className="flex items-center">
                          <Clock className="h-5 w-5 text-yellow-600 mr-3" />
                          <span className="font-medium text-yellow-800">Pending</span>
                        </div>
                        <div className="text-right">
                          <div className="font-bold text-yellow-800">{admissionStats.pendingApplications}</div>
                          <div className="text-sm text-yellow-600">
                            {calculatePercentage(admissionStats.pendingApplications, admissionStats.totalApplications)}%
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-gray-900 mb-4">Key Performance Indicators</h4>
                    <div className="space-y-4">
                      <div className="p-4 border rounded-lg">
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-gray-600">Approval Rate</span>
                          <span className="font-bold text-green-600">{admissionStats.approvalRate}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-green-500 h-2 rounded-full"
                            style={{ width: `${admissionStats.approvalRate}%` }}
                          />
                        </div>
                      </div>
                      
                      <div className="p-4 border rounded-lg">
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-gray-600">Interview Completion</span>
                          <span className="font-bold text-blue-600">
                            {calculatePercentage(admissionStats.interviewsCompleted, admissionStats.totalApplications)}%
                          </span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-500 h-2 rounded-full"
                            style={{ width: `${calculatePercentage(admissionStats.interviewsCompleted, admissionStats.totalApplications)}%` }}
                          />
                        </div>
                      </div>
                      
                      <div className="p-4 border rounded-lg">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Average Processing Time</span>
                          <span className="font-bold text-orange-600">{admissionStats.averageProcessingTime} days</span>
                        </div>
                      </div>
                      
                      <div className="p-4 border rounded-lg">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Total Revenue Generated</span>
                          <span className="font-bold text-purple-600">{formatCurrency(admissionStats.totalRevenue)}</span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Monthly Trends */}
            {selectedReport === "trends" && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900">Monthly Application Trends</h3>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Month</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Applications</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Approved</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Rejected</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Pending</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Revenue</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Approval Rate</th>
                      </tr>
                    </thead>
                    <tbody>
                      {monthlyData.map((month, index) => (
                        <tr key={index} className="border-b hover:bg-gray-50">
                          <td className="py-3 px-4 font-medium text-gray-900">{month.month}</td>
                          <td className="py-3 px-4 text-gray-600">{month.applications}</td>
                          <td className="py-3 px-4 text-green-600 font-medium">{month.approved}</td>
                          <td className="py-3 px-4 text-red-600 font-medium">{month.rejected}</td>
                          <td className="py-3 px-4 text-yellow-600 font-medium">{month.pending}</td>
                          <td className="py-3 px-4 text-purple-600 font-medium">{formatCurrency(month.revenue)}</td>
                          <td className="py-3 px-4 text-gray-900">
                            {calculatePercentage(month.approved, month.applications)}%
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* Program Analysis */}
            {selectedReport === "programs" && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900">Program-wise Analysis</h3>
                <div className="space-y-4">
                  {programWiseData.map((program, index) => (
                    <div key={index} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium text-gray-900">{program.program}</h4>
                        <div className="text-right">
                          <div className="text-lg font-bold text-blue-600">{formatCurrency(program.revenue)}</div>
                          <div className="text-sm text-gray-500">Revenue</div>
                        </div>
                      </div>
                      
                      <div className="grid gap-3 md:grid-cols-5 text-sm mb-3">
                        <div className="text-center p-3 bg-blue-50 rounded-lg">
                          <div className="font-bold text-blue-600">{program.applications}</div>
                          <div className="text-gray-500">Applications</div>
                        </div>
                        <div className="text-center p-3 bg-green-50 rounded-lg">
                          <div className="font-bold text-green-600">{program.approved}</div>
                          <div className="text-gray-500">Approved</div>
                        </div>
                        <div className="text-center p-3 bg-red-50 rounded-lg">
                          <div className="font-bold text-red-600">{program.rejected}</div>
                          <div className="text-gray-500">Rejected</div>
                        </div>
                        <div className="text-center p-3 bg-yellow-50 rounded-lg">
                          <div className="font-bold text-yellow-600">{program.pending}</div>
                          <div className="text-gray-500">Pending</div>
                        </div>
                        <div className="text-center p-3 bg-purple-50 rounded-lg">
                          <div className="font-bold text-purple-600">{program.seats}</div>
                          <div className="text-gray-500">Total Seats</div>
                        </div>
                      </div>

                      <div className="flex items-center justify-between">
                        <div className="w-full bg-gray-200 rounded-full h-2 mr-4">
                          <div 
                            className="bg-green-500 h-2 rounded-full"
                            style={{ width: `${calculatePercentage(program.approved, program.seats)}%` }}
                          />
                        </div>
                        <span className="text-sm font-medium text-gray-600">
                          {calculatePercentage(program.approved, program.seats)}% filled
                        </span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Geographic Data */}
            {selectedReport === "geography" && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900">Geographic Distribution</h3>
                <div className="grid gap-4 md:grid-cols-2">
                  {geographicData.map((location, index) => (
                    <div key={index} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-3">
                        <div className="flex items-center">
                          <MapPin className="h-5 w-5 text-blue-500 mr-2" />
                          <h4 className="font-medium text-gray-900">{location.state}</h4>
                        </div>
                        <span className="text-sm font-medium text-gray-600">{location.percentage}%</span>
                      </div>
                      
                      <div className="grid gap-3 md:grid-cols-2 text-sm mb-3">
                        <div className="text-center p-3 bg-blue-50 rounded-lg">
                          <div className="font-bold text-blue-600">{location.applications}</div>
                          <div className="text-gray-500">Applications</div>
                        </div>
                        <div className="text-center p-3 bg-green-50 rounded-lg">
                          <div className="font-bold text-green-600">{location.approved}</div>
                          <div className="text-gray-500">Approved</div>
                        </div>
                      </div>

                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-blue-500 h-2 rounded-full"
                          style={{ width: `${location.percentage}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Interview Analytics */}
            {selectedReport === "interviews" && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900">Interview Performance Analytics</h3>
                
                <div className="grid gap-6 lg:grid-cols-2">
                  <div>
                    <h4 className="font-medium text-gray-900 mb-4">Interview Statistics</h4>
                    <div className="space-y-4">
                      <div className="p-4 border rounded-lg">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Total Interviews Conducted</span>
                          <span className="font-bold text-blue-600">{interviewData.totalInterviews}</span>
                        </div>
                      </div>
                      <div className="p-4 border rounded-lg">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Average Rating</span>
                          <span className="font-bold text-yellow-600">{interviewData.averageRating}/5.0</span>
                        </div>
                      </div>
                      <div className="p-4 border rounded-lg">
                        <div className="flex justify-between items-center">
                          <span className="text-gray-600">Average Duration</span>
                          <span className="font-bold text-purple-600">{interviewData.averageDuration} minutes</span>
                        </div>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h4 className="font-medium text-gray-900 mb-4">Recommendation Distribution</h4>
                    <div className="space-y-3">
                      <div className="flex items-center justify-between p-3 bg-green-50 rounded-lg">
                        <span className="font-medium text-green-800">Strongly Recommended</span>
                        <div className="text-right">
                          <div className="font-bold text-green-800">{interviewData.stronglyRecommended}</div>
                          <div className="text-sm text-green-600">
                            {calculatePercentage(interviewData.stronglyRecommended, interviewData.totalInterviews)}%
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                        <span className="font-medium text-blue-800">Recommended</span>
                        <div className="text-right">
                          <div className="font-bold text-blue-800">{interviewData.recommended}</div>
                          <div className="text-sm text-blue-600">
                            {calculatePercentage(interviewData.recommended, interviewData.totalInterviews)}%
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between p-3 bg-yellow-50 rounded-lg">
                        <span className="font-medium text-yellow-800">Conditional</span>
                        <div className="text-right">
                          <div className="font-bold text-yellow-800">{interviewData.conditional}</div>
                          <div className="text-sm text-yellow-600">
                            {calculatePercentage(interviewData.conditional, interviewData.totalInterviews)}%
                          </div>
                        </div>
                      </div>
                      
                      <div className="flex items-center justify-between p-3 bg-red-50 rounded-lg">
                        <span className="font-medium text-red-800">Not Recommended</span>
                        <div className="text-right">
                          <div className="font-bold text-red-800">{interviewData.notRecommended}</div>
                          <div className="text-sm text-red-600">
                            {calculatePercentage(interviewData.notRecommended, interviewData.totalInterviews)}%
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-4">
              <Button variant="outline" className="justify-start">
                <Download className="h-4 w-4 mr-2" />
                Export Full Report
              </Button>
              <Button variant="outline" className="justify-start">
                <BarChart3 className="h-4 w-4 mr-2" />
                Generate Charts
              </Button>
              <Button variant="outline" className="justify-start">
                <Calendar className="h-4 w-4 mr-2" />
                Schedule Report
              </Button>
              <Button variant="outline" className="justify-start">
                <FileText className="h-4 w-4 mr-2" />
                Custom Report
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
