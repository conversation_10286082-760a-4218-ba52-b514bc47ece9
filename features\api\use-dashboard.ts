import { useQuery } from "@tanstack/react-query";
import { client } from "@/lib/hono";

// Get dashboard statistics
export const useGetDashboardStats = () => {
  return useQuery({
    queryKey: ["dashboard", "stats"],
    queryFn: async () => {
      const response = await client.api.dashboard.stats.$get();

      if (!response.ok) {
        throw new Error("Failed to fetch dashboard stats");
      }

      const { data } = await response.json();
      return data;
    },
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchInterval: 5 * 60 * 1000, // Refetch every 5 minutes
  });
};

// Get dashboard analytics
export const useGetDashboardAnalytics = () => {
  return useQuery({
    queryKey: ["dashboard", "analytics"],
    queryFn: async () => {
      const response = await client.api.dashboard.analytics.$get();

      if (!response.ok) {
        throw new Error("Failed to fetch dashboard analytics");
      }

      const { data } = await response.json();
      return data;
    },
    staleTime: 10 * 60 * 1000, // 10 minutes
    refetchInterval: 10 * 60 * 1000, // Refetch every 10 minutes
  });
};
