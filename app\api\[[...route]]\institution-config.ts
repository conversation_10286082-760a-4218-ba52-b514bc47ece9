import { <PERSON>o } from "hono";
import { zValidator } from "@hono/zod-validator";
import { z } from "zod";
import { db } from "@/lib/db";
import { institutionConfig, users } from "@/lib/db/schema";
import { eq, desc } from "drizzle-orm";

const app = new Hono()
  .get("/", async (c) => {
    try {
      const config = await db
        .select()
        .from(institutionConfig)
        .orderBy(desc(institutionConfig.createdAt))
        .limit(1);

      if (config.length === 0) {
        return c.json({
          data: null,
          isConfigured: false,
          message: "Institution not configured yet"
        });
      }

      return c.json({
        data: config[0],
        isConfigured: config[0].isConfigured,
        message: "Institution configuration retrieved successfully"
      });
    } catch (error) {
      console.error("Error fetching institution config:", error);
      return c.json({ error: "Failed to fetch institution configuration" }, 500);
    }
  })

  .post(
    "/",
    zValidator("json", z.object({
      institutionName: z.string().min(1, "Institution name is required"),
      institutionCode: z.string().min(1, "Institution code is required"),
      address: z.string().optional(),
      phone: z.string().optional(),
      email: z.string().email("Please enter a valid email address").optional().or(z.literal("")),
      website: z.string().url("Please enter a valid website URL").optional().or(z.literal("")),
      establishedYear: z.number().optional(),
      affiliation: z.string().optional(),
      currentAcademicYear: z.string().min(1, "Academic year is required"),
      sessionStartMonth: z.number().min(1).max(12).default(4),
      sessionEndMonth: z.number().min(1).max(12).default(3),
      gradingSystem: z.string().default("Grade-based (A-F)"),
      currency: z.string().default("INR"),
      timezone: z.string().default("Asia/Kolkata"),
      dateFormat: z.string().default("DD/MM/YYYY"),
      language: z.string().default("English"),
      configuredBy: z.string().min(1, "User ID is required"),
    }), (result, c) => {
      if (!result.success) {
        return c.json({ error: result.error }, 400);
      }
    }),
    async (c) => {
      try {
        const values = c.req.valid("json");

        // Check if configuration already exists
        const existingConfig = await db
          .select()
          .from(institutionConfig)
          .limit(1);

        if (existingConfig.length > 0) {
          return c.json({ error: "Institution already configured" }, 409);
        }

        // For development with mock data, skip database user verification
        // In production, this would verify the user exists and has super_admin role
        // const user = await db
        //   .select()
        //   .from(users)
        //   .where(eq(users.id, values.configuredBy))
        //   .limit(1);

        // if (user.length === 0 || user[0].role !== "super_admin") {
        //   return c.json({ error: "Unauthorized: Only super admin can configure institution" }, 403);
        // }

        // For now, just check if the user ID is "1" (super admin in mock data)
        if (values.configuredBy !== "1") {
          return c.json({ error: "Unauthorized: Only super admin can configure institution" }, 403);
        }

        // Remove configuredBy from values since we can't use mock user IDs with UUID field
        const { configuredBy, ...configValues } = values;

        const newConfig = await db
          .insert(institutionConfig)
          .values({
            ...configValues,
            isConfigured: true,
            configuredAt: new Date(),
            // configuredBy is nullable, so we skip it for now with mock data
          })
          .returning();

        return c.json({
          data: newConfig[0],
          message: "Institution configured successfully"
        }, 201);
      } catch (error) {
        console.error("Error creating institution config:", error);
        return c.json({ error: "Failed to configure institution" }, 500);
      }
    }
  )

  .put(
    "/:id",
    zValidator("json", z.object({
      institutionName: z.string().min(1).optional(),
      institutionCode: z.string().min(1).optional(),
      address: z.string().optional(),
      phone: z.string().optional(),
      email: z.string().email().optional(),
      website: z.string().url().optional(),
      establishedYear: z.number().optional(),
      affiliation: z.string().optional(),
      currentAcademicYear: z.string().optional(),
      sessionStartMonth: z.number().min(1).max(12).optional(),
      sessionEndMonth: z.number().min(1).max(12).optional(),
      gradingSystem: z.string().optional(),
      currency: z.string().optional(),
      timezone: z.string().optional(),
      dateFormat: z.string().optional(),
      language: z.string().optional(),
      updatedBy: z.string().min(1, "User ID is required"),
    })),
    async (c) => {
      try {
        const id = c.req.param("id");
        const values = c.req.valid("json");

        // For development with mock data, skip database user verification
        // In production, this would verify the user exists and has super_admin role
        // const user = await db
        //   .select()
        //   .from(users)
        //   .where(eq(users.id, values.updatedBy))
        //   .limit(1);

        // if (user.length === 0 || user[0].role !== "super_admin") {
        //   return c.json({ error: "Unauthorized: Only super admin can update institution configuration" }, 403);
        // }

        // For now, just check if the user ID is "1" (super admin in mock data)
        if (values.updatedBy !== "1") {
          return c.json({ error: "Unauthorized: Only super admin can update institution configuration" }, 403);
        }

        // Remove updatedBy from values since we can't use mock user IDs with UUID field
        const { updatedBy, ...updateValues } = values;

        const updatedConfig = await db
          .update(institutionConfig)
          .set({
            ...updateValues,
            updatedAt: new Date(),
          })
          .where(eq(institutionConfig.id, id))
          .returning();

        if (updatedConfig.length === 0) {
          return c.json({ error: "Institution configuration not found" }, 404);
        }

        return c.json({
          data: updatedConfig[0],
          message: "Institution configuration updated successfully"
        });
      } catch (error) {
        console.error("Error updating institution config:", error);
        return c.json({ error: "Failed to update institution configuration" }, 500);
      }
    }
  );

export default app;
