import { Hono } from "hono";
import { zValidator } from "@hono/zod-validator";
import { z } from "zod";

const app = new Hono()
  // Get principal dashboard data
  .get("/dashboard", async (c) => {
    try {
      // Mock principal dashboard data
      const dashboardData = {
        schoolOverview: {
          totalStudents: 1247,
          totalTeachers: 89,
          totalClasses: 45,
          totalStaff: 156,
          academicYear: "2024-2025",
          currentTerm: "Term 2"
        },
        keyMetrics: {
          academicPerformance: 87.5,
          attendanceRate: 94.2,
          parentSatisfaction: 92.8,
          teacherRetention: 96.5,
          budgetUtilization: 78.5,
          infrastructureScore: 88.3
        },
        pendingApprovals: {
          budgetRequests: 5,
          staffRequests: 3,
          policyChanges: 2,
          disciplinaryMatters: 2,
          curriculumChanges: 1,
          total: 13
        },
        recentActivities: [
          {
            id: 1,
            type: "approval",
            title: "Budget Approval Request",
            description: "Science lab equipment purchase - ₹2,50,000",
            time: "2 hours ago",
            priority: "high",
            category: "finance",
            status: "pending"
          },
          {
            id: 2,
            type: "achievement",
            title: "Student Achievement",
            description: "Class 12 student won National Science Olympiad",
            time: "4 hours ago",
            priority: "medium",
            category: "academic",
            status: "completed"
          },
          {
            id: 3,
            type: "meeting",
            title: "Parent-Teacher Meeting",
            description: "Scheduled for next week - 150 parents registered",
            time: "6 hours ago",
            priority: "medium",
            category: "parent_relations",
            status: "scheduled"
          },
          {
            id: 4,
            type: "disciplinary",
            title: "Disciplinary Matter",
            description: "Grade 10 student incident requires attention",
            time: "1 day ago",
            priority: "high",
            category: "student_affairs",
            status: "pending"
          },
          {
            id: 5,
            type: "staff",
            title: "New Teacher Onboarding",
            description: "Mathematics teacher joining next week",
            time: "2 days ago",
            priority: "medium",
            category: "staff_management",
            status: "in_progress"
          }
        ],
        upcomingEvents: [
          {
            id: 1,
            title: "Board Meeting",
            date: "2024-01-25",
            time: "10:00 AM",
            type: "meeting",
            attendees: 8,
            location: "Conference Room",
            priority: "high"
          },
          {
            id: 2,
            title: "Annual Sports Day",
            date: "2024-01-30",
            time: "9:00 AM",
            type: "event",
            attendees: 1200,
            location: "School Grounds",
            priority: "medium"
          },
          {
            id: 3,
            title: "Teacher Training Workshop",
            date: "2024-01-27",
            time: "2:00 PM",
            type: "training",
            attendees: 45,
            location: "Auditorium",
            priority: "medium"
          },
          {
            id: 4,
            title: "Parent Advisory Committee",
            date: "2024-01-28",
            time: "4:00 PM",
            type: "meeting",
            attendees: 12,
            location: "Principal's Office",
            priority: "high"
          }
        ],
        quickStats: {
          todayAttendance: {
            students: 94.2,
            teachers: 98.9
          },
          thisWeekEvents: 8,
          pendingTasks: 13,
          newAdmissions: 23,
          graduatingStudents: 156
        }
      };

      return c.json({ data: dashboardData });
    } catch (error) {
      console.error("Error fetching principal dashboard:", error);
      return c.json({ error: "Failed to fetch dashboard data" }, 500);
    }
  })

  // Get pending approvals
  .get("/approvals", async (c) => {
    try {
      const approvals = [
        {
          id: "1",
          type: "budget",
          title: "Science Lab Equipment",
          description: "Purchase of advanced microscopes and lab equipment",
          amount: 250000,
          requestedBy: "Dr. Physics Teacher",
          department: "Science",
          priority: "high",
          submittedDate: "2024-01-18",
          status: "pending",
          documents: ["quotation.pdf", "specification.pdf"]
        },
        {
          id: "2",
          type: "staff",
          title: "New Mathematics Teacher",
          description: "Hiring request for additional mathematics teacher",
          requestedBy: "Academic Head",
          department: "Mathematics",
          priority: "medium",
          submittedDate: "2024-01-17",
          status: "pending",
          documents: ["job_description.pdf", "candidate_profile.pdf"]
        },
        {
          id: "3",
          type: "policy",
          title: "Updated Attendance Policy",
          description: "Revision of student attendance requirements",
          requestedBy: "Academic Coordinator",
          department: "Administration",
          priority: "medium",
          submittedDate: "2024-01-16",
          status: "pending",
          documents: ["policy_draft.pdf"]
        },
        {
          id: "4",
          type: "disciplinary",
          title: "Student Disciplinary Action",
          description: "Grade 10 student behavioral incident",
          requestedBy: "Class Teacher",
          department: "Student Affairs",
          priority: "high",
          submittedDate: "2024-01-19",
          status: "pending",
          documents: ["incident_report.pdf"]
        },
        {
          id: "5",
          type: "curriculum",
          title: "New Computer Science Module",
          description: "Addition of AI/ML basics to curriculum",
          requestedBy: "Computer Science Head",
          department: "Computer Science",
          priority: "low",
          submittedDate: "2024-01-15",
          status: "pending",
          documents: ["curriculum_proposal.pdf"]
        }
      ];

      return c.json({ data: approvals });
    } catch (error) {
      console.error("Error fetching approvals:", error);
      return c.json({ error: "Failed to fetch approvals" }, 500);
    }
  })

  // Approve/Reject requests
  .post("/approvals/:id/action", 
    zValidator("json", z.object({
      action: z.enum(["approve", "reject"]),
      comments: z.string().optional(),
      conditions: z.string().optional()
    })),
    async (c) => {
      try {
        const id = c.req.param("id");
        const { action, comments, conditions } = c.req.valid("json");

        // In a real app, update the approval status in database
        console.log(`Principal ${action}d request ${id}`, { comments, conditions });

        return c.json({ 
          message: `Request ${action}d successfully`,
          data: { id, action, comments, conditions, timestamp: new Date().toISOString() }
        });
      } catch (error) {
        console.error("Error processing approval:", error);
        return c.json({ error: "Failed to process approval" }, 500);
      }
    }
  )

  // Get school analytics
  .get("/analytics", async (c) => {
    try {
      const analytics = {
        academic: {
          overallPerformance: 87.5,
          gradeDistribution: {
            "A+": 15,
            "A": 25,
            "B+": 30,
            "B": 20,
            "C": 8,
            "D": 2
          },
          subjectPerformance: [
            { subject: "Mathematics", average: 85.2, trend: "up" },
            { subject: "Science", average: 88.7, trend: "up" },
            { subject: "English", average: 82.1, trend: "stable" },
            { subject: "Social Studies", average: 79.8, trend: "down" },
            { subject: "Computer Science", average: 91.3, trend: "up" }
          ]
        },
        attendance: {
          overall: 94.2,
          byGrade: [
            { grade: "Grade 1", attendance: 96.5 },
            { grade: "Grade 2", attendance: 95.8 },
            { grade: "Grade 3", attendance: 94.2 },
            { grade: "Grade 4", attendance: 93.7 },
            { grade: "Grade 5", attendance: 92.9 }
          ],
          trends: {
            thisMonth: 94.2,
            lastMonth: 93.8,
            change: 0.4
          }
        },
        financial: {
          budgetUtilization: 78.5,
          feeCollection: 96.2,
          expenses: {
            salaries: 65,
            infrastructure: 15,
            utilities: 8,
            activities: 7,
            others: 5
          }
        },
        staff: {
          totalTeachers: 89,
          teacherRetention: 96.5,
          averageExperience: 8.2,
          qualifications: {
            "PhD": 12,
            "Masters": 45,
            "Bachelors": 32
          }
        }
      };

      return c.json({ data: analytics });
    } catch (error) {
      console.error("Error fetching analytics:", error);
      return c.json({ error: "Failed to fetch analytics" }, 500);
    }
  });

export default app;
