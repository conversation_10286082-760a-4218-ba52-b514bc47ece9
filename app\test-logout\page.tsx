"use client";

import { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, CardH<PERSON>er, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { LogoutButton } from "@/components/ui/logout-button";
import { UserMenu } from "@/components/navigation/user-menu";
import { useLogout } from "@/hooks/use-logout";
import {
  User,
  Shield,
  CheckCircle,
  AlertTriangle,
  Info,
  LogOut
} from "lucide-react";

export default function TestLogoutPage() {
  const [user, setUser] = useState<any>(null);
  const [storageData, setStorageData] = useState<any>({});
  const { logout, logoutWithoutConfirmation } = useLogout();

  useEffect(() => {
    // Check user data
    const userData = localStorage.getItem("user");
    if (userData) {
      setUser(JSON.parse(userData));
    }

    // Check all storage data
    const allData = {
      user: localStorage.getItem("user"),
      token: localStorage.getItem("token"),
      preferences: localStorage.getItem("preferences"),
      theme: localStorage.getItem("theme"),
    };
    setStorageData(allData);
  }, []);

  const refreshStorageData = () => {
    const allData = {
      user: localStorage.getItem("user"),
      token: localStorage.getItem("token"),
      preferences: localStorage.getItem("preferences"),
      theme: localStorage.getItem("theme"),
    };
    setStorageData(allData);

    const userData = localStorage.getItem("user");
    setUser(userData ? JSON.parse(userData) : null);
  };

  const simulateLogin = () => {
    const mockUser = {
      id: "test-user-123",
      firstName: "Test",
      lastName: "User",
      email: "<EMAIL>",
      role: "admin",
    };

    localStorage.setItem("user", JSON.stringify(mockUser));
    localStorage.setItem("token", "mock-jwt-token-test-user-123");
    localStorage.setItem("preferences", JSON.stringify({ theme: "light" }));
    localStorage.setItem("theme", "light");

    refreshStorageData();
  };

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        {/* Header */}
        <div className="bg-white rounded-lg p-6 shadow-sm">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Logout System Test Page
          </h1>
          <p className="text-gray-600">
            Test the comprehensive logout functionality for all user roles
          </p>
        </div>

        {/* User Status */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="h-5 w-5 mr-2" />
              Current User Status
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {user ? (
                <div className="flex items-center space-x-3 p-4 bg-green-50 rounded-lg">
                  <CheckCircle className="h-6 w-6 text-green-600" />
                  <div>
                    <div className="font-medium text-green-900">
                      Logged in as {user.firstName} {user.lastName}
                    </div>
                    <div className="text-sm text-green-700">
                      {user.email} • Role: {user.role}
                    </div>
                  </div>
                </div>
              ) : (
                <div className="flex items-center space-x-3 p-4 bg-red-50 rounded-lg">
                  <AlertTriangle className="h-6 w-6 text-red-600" />
                  <div>
                    <div className="font-medium text-red-900">Not logged in</div>
                    <div className="text-sm text-red-700">
                      No user data found in localStorage
                    </div>
                  </div>
                </div>
              )}

              <div className="flex gap-3">
                <Button onClick={simulateLogin} variant="outline">
                  Simulate Login
                </Button>
                <Button onClick={refreshStorageData} variant="outline">
                  Refresh Data
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Storage Data */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Shield className="h-5 w-5 mr-2" />
              Browser Storage Data
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {Object.entries(storageData).map(([key, value]) => (
                <div key={key} className="flex items-center justify-between p-3 border rounded-lg">
                  <div className="font-medium text-gray-900">{key}</div>
                  <div className="text-sm text-gray-500 max-w-md truncate">
                    {value ? (
                      <span className="text-green-600">✓ Present</span>
                    ) : (
                      <span className="text-red-600">✗ Not found</span>
                    )}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Logout Components Test */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <LogOut className="h-5 w-5 mr-2" />
              Logout Components Test
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              {/* UserMenu Component */}
              <div>
                <h3 className="font-medium text-gray-900 mb-3">UserMenu Component</h3>
                <div className="p-4 border rounded-lg">
                  <UserMenu />
                </div>
              </div>

              {/* LogoutButton Variants */}
              <div>
                <h3 className="font-medium text-gray-900 mb-3">LogoutButton Variants</h3>
                <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-3">
                  <LogoutButton variant="default" />
                  <LogoutButton variant="outline" />
                  <LogoutButton variant="ghost" />
                  <LogoutButton variant="destructive" />
                  <LogoutButton
                    variant="outline"
                    showIcon={false}
                    showText={true}
                  />
                  <LogoutButton
                    variant="ghost"
                    showIcon={true}
                    showText={false}
                    size="icon"
                  />
                </div>
              </div>

              {/* Manual Logout Functions */}
              <div>
                <h3 className="font-medium text-gray-900 mb-3">Manual Logout Functions</h3>
                <div className="flex gap-3">
                  <Button
                    variant="outline"
                    onClick={() => logout()}
                  >
                    Logout (with confirmation)
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => logoutWithoutConfirmation()}
                  >
                    Logout (no confirmation)
                  </Button>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Info className="h-5 w-5 mr-2" />
              Test Instructions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm text-gray-600">
              <p>1. Click &quot;Simulate Login&quot; to create mock user data in localStorage</p>
              <p>2. Test different logout components and verify they work correctly</p>
              <p>3. Check that storage data is cleared after logout</p>
              <p>4. Verify redirection to login page occurs</p>
              <p>5. Test with different user roles by modifying the mock data</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
