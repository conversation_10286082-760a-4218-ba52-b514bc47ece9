"use client";

import { useState } from "react";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  GraduationCap,
  BookOpen,
  Calendar,
  Download,
  Filter,
  RefreshCw
} from "lucide-react";

export default function AnalyticsPage() {
  const [selectedPeriod, setSelectedPeriod] = useState("month");

  // Mock analytics data
  const overviewStats = [
    {
      title: "Total Students",
      value: "1,234",
      change: "+12%",
      trend: "up",
      icon: Users,
      color: "text-blue-600",
    },
    {
      title: "Average Attendance",
      value: "94.2%",
      change: "+2.1%",
      trend: "up",
      icon: Calendar,
      color: "text-green-600",
    },
    {
      title: "Average Grade",
      value: "85.7%",
      change: "-1.3%",
      trend: "down",
      icon: GraduationCap,
      color: "text-purple-600",
    },
    {
      title: "Active Classes",
      value: "45",
      change: "+3",
      trend: "up",
      icon: BookOpen,
      color: "text-orange-600",
    },
  ];

  const gradeDistribution = [
    { grade: "A", count: 245, percentage: 35 },
    { grade: "B", count: 189, percentage: 27 },
    { grade: "C", count: 156, percentage: 22 },
    { grade: "D", count: 78, percentage: 11 },
    { grade: "F", count: 35, percentage: 5 },
  ];

  const attendanceByGrade = [
    { grade: "Grade 10", attendance: 96.2, students: 320 },
    { grade: "Grade 11", attendance: 94.8, students: 298 },
    { grade: "Grade 12", attendance: 92.1, students: 275 },
  ];

  const subjectPerformance = [
    { subject: "Mathematics", average: 87.5, students: 450 },
    { subject: "Physics", average: 84.2, students: 380 },
    { subject: "Chemistry", average: 86.1, students: 420 },
    { subject: "Biology", average: 88.9, students: 390 },
    { subject: "English", average: 85.7, students: 500 },
  ];

  const monthlyTrends = [
    { month: "Jan", students: 1180, attendance: 93.2, grades: 84.1 },
    { month: "Feb", students: 1195, attendance: 94.1, grades: 85.3 },
    { month: "Mar", students: 1210, attendance: 95.2, grades: 86.2 },
    { month: "Apr", students: 1225, attendance: 94.8, grades: 85.9 },
    { month: "May", students: 1234, attendance: 94.2, grades: 85.7 },
  ];

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Analytics Dashboard</h1>
            <p className="text-gray-600">Comprehensive insights into school performance</p>
          </div>
          <div className="flex gap-2">
            <select 
              className="px-3 py-2 border border-gray-300 rounded-md text-sm bg-white"
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
            >
              <option value="week">This Week</option>
              <option value="month">This Month</option>
              <option value="quarter">This Quarter</option>
              <option value="year">This Year</option>
            </select>
            <Button variant="outline">
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
            <Button>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
          </div>
        </div>

        {/* Overview Stats */}
        <div className="grid gap-4 md:grid-cols-4">
          {overviewStats.map((stat, index) => (
            <Card key={index}>
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-sm font-medium text-gray-600">{stat.title}</p>
                    <p className="text-2xl font-bold text-gray-900">{stat.value}</p>
                    <p className={`text-sm ${
                      stat.trend === "up" ? "text-green-600" : "text-red-600"
                    }`}>
                      {stat.change} from last {selectedPeriod}
                    </p>
                  </div>
                  <div className={`p-3 rounded-full bg-gray-100 ${stat.color}`}>
                    <stat.icon className="h-6 w-6" />
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Charts Row */}
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Grade Distribution */}
          <Card>
            <CardHeader>
              <CardTitle>Grade Distribution</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {gradeDistribution.map((item, index) => (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center space-x-3">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                        <span className="text-sm font-bold text-blue-600">{item.grade}</span>
                      </div>
                      <span className="font-medium">{item.count} students</span>
                    </div>
                    <div className="flex items-center space-x-3">
                      <div className="w-24 bg-gray-200 rounded-full h-2">
                        <div
                          className="bg-blue-600 h-2 rounded-full"
                          style={{ width: `${item.percentage}%` }}
                        />
                      </div>
                      <span className="text-sm text-gray-600 w-12">{item.percentage}%</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Attendance by Grade */}
          <Card>
            <CardHeader>
              <CardTitle>Attendance by Grade</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {attendanceByGrade.map((item, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="font-medium">{item.grade}</span>
                      <span className="text-sm text-gray-600">
                        {item.attendance}% ({item.students} students)
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-green-600 h-2 rounded-full"
                        style={{ width: `${item.attendance}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Subject Performance */}
        <Card>
          <CardHeader>
            <CardTitle>Subject Performance Analysis</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Subject</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Students</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Average Grade</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Performance</th>
                  </tr>
                </thead>
                <tbody>
                  {subjectPerformance.map((subject, index) => (
                    <tr key={index} className="border-b hover:bg-gray-50">
                      <td className="py-3 px-4 font-medium">{subject.subject}</td>
                      <td className="py-3 px-4 text-gray-600">{subject.students}</td>
                      <td className="py-3 px-4">
                        <span className={`font-medium ${
                          subject.average >= 85 ? "text-green-600" :
                          subject.average >= 75 ? "text-yellow-600" : "text-red-600"
                        }`}>
                          {subject.average}%
                        </span>
                      </td>
                      <td className="py-3 px-4">
                        <div className="w-24 bg-gray-200 rounded-full h-2">
                          <div
                            className={`h-2 rounded-full ${
                              subject.average >= 85 ? "bg-green-600" :
                              subject.average >= 75 ? "bg-yellow-600" : "bg-red-600"
                            }`}
                            style={{ width: `${subject.average}%` }}
                          />
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Monthly Trends */}
        <Card>
          <CardHeader>
            <CardTitle>Monthly Trends</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-6">
              <div className="grid gap-4 md:grid-cols-3">
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">1,234</div>
                  <div className="text-sm text-gray-600">Total Students</div>
                  <div className="text-xs text-green-600">+4.6% this month</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">94.2%</div>
                  <div className="text-sm text-gray-600">Avg Attendance</div>
                  <div className="text-xs text-red-600">-1.0% this month</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">85.7%</div>
                  <div className="text-sm text-gray-600">Avg Grade</div>
                  <div className="text-xs text-red-600">-0.2% this month</div>
                </div>
              </div>
              
              <div className="overflow-x-auto">
                <table className="w-full">
                  <thead>
                    <tr className="border-b">
                      <th className="text-left py-2 px-4 font-medium text-gray-900">Month</th>
                      <th className="text-left py-2 px-4 font-medium text-gray-900">Students</th>
                      <th className="text-left py-2 px-4 font-medium text-gray-900">Attendance</th>
                      <th className="text-left py-2 px-4 font-medium text-gray-900">Avg Grade</th>
                    </tr>
                  </thead>
                  <tbody>
                    {monthlyTrends.map((month, index) => (
                      <tr key={index} className="border-b hover:bg-gray-50">
                        <td className="py-2 px-4 font-medium">{month.month}</td>
                        <td className="py-2 px-4">{month.students.toLocaleString()}</td>
                        <td className="py-2 px-4">{month.attendance}%</td>
                        <td className="py-2 px-4">{month.grades}%</td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Key Insights */}
        <Card>
          <CardHeader>
            <CardTitle>Key Insights</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-2">
              <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
                <div className="flex items-center space-x-2 mb-2">
                  <TrendingUp className="h-5 w-5 text-green-600" />
                  <span className="font-medium text-green-800">Positive Trend</span>
                </div>
                <p className="text-sm text-green-700">
                  Student enrollment has increased by 12% this month, indicating growing trust in the institution.
                </p>
              </div>
              
              <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
                <div className="flex items-center space-x-2 mb-2">
                  <BarChart3 className="h-5 w-5 text-yellow-600" />
                  <span className="font-medium text-yellow-800">Area for Improvement</span>
                </div>
                <p className="text-sm text-yellow-700">
                  Grade 12 attendance has dropped slightly. Consider implementing targeted engagement strategies.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
