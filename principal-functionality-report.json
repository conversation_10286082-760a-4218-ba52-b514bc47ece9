{"timestamp": "2025-06-08T09:52:31.265Z", "summary": {"passed": 13, "warnings": 0, "failed": 0, "successRate": 100, "totalTests": 13}, "testDetails": [{"test": "Principal User Exists", "status": "PASS", "message": "Principal user found in mock database"}, {"test": "Principal Enhanced Data", "status": "PASS", "message": "Principal has enhanced profile data"}, {"test": "Principal Navigation", "status": "PASS", "message": "Principal navigation configuration found"}, {"test": "Principal <PERSON><PERSON>", "status": "PASS", "message": "Found 4/4 expected menu items"}, {"test": "Page: app/principal/dashboard/page.tsx", "status": "PASS", "message": "Page exists"}, {"test": "Page: app/principal/approvals/page.tsx", "status": "PASS", "message": "Page exists"}, {"test": "Page: app/principal/analytics/page.tsx", "status": "PASS", "message": "Page exists"}, {"test": "Page: app/principal/planning/page.tsx", "status": "PASS", "message": "Page exists"}, {"test": "Principal API File", "status": "PASS", "message": "Principal API file exists"}, {"test": "Principal API Endpoints", "status": "PASS", "message": "All 3 endpoints found"}, {"test": "Principal API Registration", "status": "PASS", "message": "Principal API registered in main route"}, {"test": "Principal <PERSON><PERSON>", "status": "PASS", "message": "Principal role handled in middleware"}, {"test": "<PERSON>gin Principal Support", "status": "PASS", "message": "Login page supports principal role"}], "principalCredentials": {"email": "<EMAIL>", "password": "principal123", "loginUrl": "http://localhost:3000/login", "dashboardUrl": "/principal/dashboard"}, "availableFeatures": ["Dashboard with school overview", "Approval system for requests", "Analytics and reporting", "Strategic planning tools"]}