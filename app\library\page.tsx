"use client";

import { useState } from "react";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>Tit<PERSON> } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { 
  BookOpen, 
  Search, 
  Plus, 
  Filter,
  Users,
  Calendar,
  AlertTriangle,
  CheckCircle,
  Clock,
  BarChart3
} from "lucide-react";

// Mock library data
const mockBooks = [
  {
    id: "1",
    title: "Advanced Mathematics",
    author: "Dr. <PERSON>",
    isbn: "978-0123456789",
    category: "Mathematics",
    totalCopies: 50,
    availableCopies: 45,
    issuedCopies: 5,
    location: "Section A, Shelf 1",
    status: "available",
  },
  {
    id: "2",
    title: "Physics Fundamentals",
    author: "Dr. <PERSON>",
    isbn: "978-0987654321",
    category: "Physics",
    totalCopies: 40,
    availableCopies: 38,
    issuedCopies: 2,
    location: "Section B, Shelf 2",
    status: "available",
  },
  {
    id: "3",
    title: "Chemistry Basics",
    author: "Prof<PERSON>",
    isbn: "978-0456789123",
    category: "Chemistry",
    totalCopies: 35,
    availableCopies: 30,
    issuedCopies: 5,
    location: "Section B, Shelf 3",
    status: "available",
  },
];

const mockBookIssues = [
  {
    id: "1",
    bookTitle: "Advanced Mathematics",
    studentName: "John Doe",
    studentId: "STU001",
    issueDate: "2024-01-10",
    dueDate: "2024-01-24",
    status: "issued",
    daysOverdue: 0,
  },
  {
    id: "2",
    bookTitle: "Physics Fundamentals",
    studentName: "Alice Smith",
    studentId: "STU002",
    issueDate: "2024-01-05",
    dueDate: "2024-01-19",
    status: "overdue",
    daysOverdue: 5,
  },
  {
    id: "3",
    bookTitle: "Chemistry Basics",
    studentName: "Michael Johnson",
    studentId: "STU003",
    issueDate: "2024-01-12",
    dueDate: "2024-01-26",
    status: "issued",
    daysOverdue: 0,
  },
];

const statusColors = {
  available: "text-green-600 bg-green-100",
  issued: "text-blue-600 bg-blue-100",
  overdue: "text-red-600 bg-red-100",
  returned: "text-gray-600 bg-gray-100",
};

const statusIcons = {
  available: CheckCircle,
  issued: Clock,
  overdue: AlertTriangle,
  returned: CheckCircle,
};

export default function LibraryPage() {
  const [search, setSearch] = useState("");
  const [activeTab, setActiveTab] = useState("books");

  // Calculate library statistics
  const totalBooks = mockBooks.reduce((sum, book) => sum + book.totalCopies, 0);
  const issuedBooks = mockBooks.reduce((sum, book) => sum + book.issuedCopies, 0);
  const availableBooks = mockBooks.reduce((sum, book) => sum + book.availableCopies, 0);
  const overdueBooks = mockBookIssues.filter(issue => issue.status === "overdue").length;

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Library Management</h1>
            <p className="text-gray-600">Manage books, issues, and library operations</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <BarChart3 className="h-4 w-4 mr-2" />
              Reports
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Book
            </Button>
          </div>
        </div>

        {/* Library Stats */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <BookOpen className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {totalBooks}
                  </div>
                  <p className="text-sm text-gray-600">Total Books</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {availableBooks}
                  </div>
                  <p className="text-sm text-gray-600">Available</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    {issuedBooks}
                  </div>
                  <p className="text-sm text-gray-600">Issued</p>
                </div>
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <AlertTriangle className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-red-600">
                    {overdueBooks}
                  </div>
                  <p className="text-sm text-gray-600">Overdue</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Tab Navigation */}
        <div className="border-b border-gray-200">
          <nav className="-mb-px flex space-x-8">
            <button
              onClick={() => setActiveTab("books")}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === "books"
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              Books Catalog
            </button>
            <button
              onClick={() => setActiveTab("issues")}
              className={`py-2 px-1 border-b-2 font-medium text-sm ${
                activeTab === "issues"
                  ? "border-blue-500 text-blue-600"
                  : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
            >
              Book Issues
            </button>
          </nav>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder={`Search ${activeTab}...`}
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex gap-2">
                <select className="px-3 py-2 border border-gray-300 rounded-md text-sm">
                  <option value="">All Categories</option>
                  <option value="Mathematics">Mathematics</option>
                  <option value="Physics">Physics</option>
                  <option value="Chemistry">Chemistry</option>
                  <option value="Biology">Biology</option>
                </select>
                <Button variant="outline" size="sm">
                  <Filter className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Books Catalog */}
        {activeTab === "books" && (
          <Card>
            <CardHeader>
              <CardTitle>Books Catalog</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockBooks.map((book) => (
                  <div
                    key={book.id}
                    className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                  >
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-16 bg-blue-100 rounded flex items-center justify-center">
                        <BookOpen className="h-6 w-6 text-blue-600" />
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">
                          {book.title}
                        </div>
                        <div className="text-sm text-gray-500">
                          by {book.author} • ISBN: {book.isbn}
                        </div>
                        <div className="text-xs text-gray-400">
                          {book.category} • {book.location}
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="flex items-center space-x-4">
                        <div className="text-sm">
                          <div className="font-medium text-green-600">
                            Available: {book.availableCopies}
                          </div>
                          <div className="text-gray-500">
                            Total: {book.totalCopies}
                          </div>
                        </div>
                        <div className="flex gap-2">
                          <Button variant="outline" size="sm">
                            Issue
                          </Button>
                          <Button variant="outline" size="sm">
                            Edit
                          </Button>
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Book Issues */}
        {activeTab === "issues" && (
          <Card>
            <CardHeader>
              <CardTitle>Book Issues</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {mockBookIssues.map((issue) => {
                  const StatusIcon = statusIcons[issue.status as keyof typeof statusIcons];
                  
                  return (
                    <div
                      key={issue.id}
                      className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-center space-x-4">
                        <div className={`p-2 rounded-full ${statusColors[issue.status as keyof typeof statusColors]}`}>
                          <StatusIcon className="h-4 w-4" />
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">
                            {issue.bookTitle}
                          </div>
                          <div className="text-sm text-gray-500">
                            {issue.studentName} ({issue.studentId})
                          </div>
                          <div className="text-xs text-gray-400">
                            Issued: {issue.issueDate} • Due: {issue.dueDate}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="flex items-center space-x-4">
                          {issue.status === "overdue" && (
                            <div className="text-sm text-red-600 font-medium">
                              {issue.daysOverdue} days overdue
                            </div>
                          )}
                          <div className="flex gap-2">
                            <Button variant="outline" size="sm">
                              Return
                            </Button>
                            <Button variant="outline" size="sm">
                              Extend
                            </Button>
                          </div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Library Analytics */}
        <div className="grid gap-6 md:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Popular Categories</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Mathematics</span>
                  <span className="font-medium">35%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-600 h-2 rounded-full w-[35%]" />
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm">Physics</span>
                  <span className="font-medium">28%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-green-600 h-2 rounded-full w-[28%]" />
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm">Chemistry</span>
                  <span className="font-medium">22%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-yellow-600 h-2 rounded-full w-[22%]" />
                </div>
                
                <div className="flex items-center justify-between">
                  <span className="text-sm">Others</span>
                  <span className="font-medium">15%</span>
                </div>
                <div className="w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-purple-600 h-2 rounded-full w-[15%]" />
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Monthly Statistics</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between">
                  <span className="text-sm">Books Issued</span>
                  <span className="font-medium text-blue-600">245</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Books Returned</span>
                  <span className="font-medium text-green-600">238</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">New Additions</span>
                  <span className="font-medium text-purple-600">12</span>
                </div>
                <div className="flex items-center justify-between">
                  <span className="text-sm">Active Members</span>
                  <span className="font-medium text-orange-600">156</span>
                </div>
                <div className="pt-4 border-t">
                  <Button variant="outline" className="w-full">
                    View Detailed Report
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  );
}
