-- Migration to convert system to school-only mode
-- This migration removes college-specific features and simplifies the structure

-- Step 1: Create new enums for school-only system
CREATE TYPE "public"."class_level" AS ENUM('nursery', 'lkg', 'ukg', 'grade_1', 'grade_2', 'grade_3', 'grade_4', 'grade_5', 'grade_6', 'grade_7', 'grade_8', 'grade_9', 'grade_10', 'grade_11', 'grade_12');

-- Step 2: Update academic_programs table structure
-- Add new columns for school classes
ALTER TABLE "academic_programs" 
ADD COLUMN "class_level" "class_level",
ADD COLUMN "stream_type" "stream_type";

-- Step 3: Remove college-specific columns from academic_programs
ALTER TABLE "academic_programs" 
DROP COLUMN IF EXISTS "stream_id",
DROP COLUMN IF EXISTS "branch_id",
DROP COLUMN IF EXISTS "total_semesters",
DROP COLUMN IF EXISTS "department";

-- Step 4: Update program_type enum to only allow 'school'
-- First, update all existing records to 'school'
UPDATE "academic_programs" SET "type" = 'school' WHERE "type" != 'school';

-- Step 5: Remove institution_type from institution_config
ALTER TABLE "institution_config" 
DROP COLUMN IF EXISTS "institution_type";

-- Step 6: Update academic_batches to remove semester references
ALTER TABLE "academic_batches" 
DROP COLUMN IF EXISTS "current_semester";

-- Step 7: Update academic_sections to remove stream/branch references
ALTER TABLE "academic_sections" 
DROP COLUMN IF EXISTS "stream_id",
DROP COLUMN IF EXISTS "branch_id",
DROP COLUMN IF EXISTS "semester";

-- Step 8: Update students table to remove stream/branch references
ALTER TABLE "students" 
DROP COLUMN IF EXISTS "stream_id",
DROP COLUMN IF EXISTS "branch_id",
DROP COLUMN IF EXISTS "current_semester";

-- Step 9: Update classes table to remove semester and college-specific fields
ALTER TABLE "classes" 
DROP COLUMN IF EXISTS "semester",
DROP COLUMN IF EXISTS "credits",
DROP COLUMN IF EXISTS "is_elective",
DROP COLUMN IF EXISTS "prerequisites";

-- Step 10: Drop academic_streams and academic_branches tables
-- First, we need to handle any foreign key constraints
-- Remove any existing data that references these tables
DROP TABLE IF EXISTS "academic_branches" CASCADE;
DROP TABLE IF EXISTS "academic_streams" CASCADE;

-- Step 11: Update existing academic programs with sample school data
-- Clear existing programs and insert school classes
DELETE FROM "academic_programs";

-- Insert standard school classes
INSERT INTO "academic_programs" ("id", "name", "code", "type", "class_level", "duration", "total_seats", "admission_status", "status", "created_at", "updated_at") VALUES
(gen_random_uuid(), 'Nursery', 'NUR', 'school', 'nursery', 1, 25, 'open', 'active', NOW(), NOW()),
(gen_random_uuid(), 'LKG', 'LKG', 'school', 'lkg', 1, 25, 'open', 'active', NOW(), NOW()),
(gen_random_uuid(), 'UKG', 'UKG', 'school', 'ukg', 1, 25, 'open', 'active', NOW(), NOW()),
(gen_random_uuid(), 'Grade 1', 'GR1', 'school', 'grade_1', 1, 30, 'open', 'active', NOW(), NOW()),
(gen_random_uuid(), 'Grade 2', 'GR2', 'school', 'grade_2', 1, 30, 'open', 'active', NOW(), NOW()),
(gen_random_uuid(), 'Grade 3', 'GR3', 'school', 'grade_3', 1, 30, 'open', 'active', NOW(), NOW()),
(gen_random_uuid(), 'Grade 4', 'GR4', 'school', 'grade_4', 1, 30, 'open', 'active', NOW(), NOW()),
(gen_random_uuid(), 'Grade 5', 'GR5', 'school', 'grade_5', 1, 30, 'open', 'active', NOW(), NOW()),
(gen_random_uuid(), 'Grade 6', 'GR6', 'school', 'grade_6', 1, 30, 'open', 'active', NOW(), NOW()),
(gen_random_uuid(), 'Grade 7', 'GR7', 'school', 'grade_7', 1, 30, 'open', 'active', NOW(), NOW()),
(gen_random_uuid(), 'Grade 8', 'GR8', 'school', 'grade_8', 1, 30, 'open', 'active', NOW(), NOW()),
(gen_random_uuid(), 'Grade 9', 'GR9', 'school', 'grade_9', 1, 30, 'open', 'active', NOW(), NOW()),
(gen_random_uuid(), 'Grade 10', 'GR10', 'school', 'grade_10', 1, 30, 'open', 'active', NOW(), NOW()),
(gen_random_uuid(), 'Grade 11 Science', 'GR11S', 'school', 'grade_11', 1, 30, 'open', 'active', NOW(), NOW()),
(gen_random_uuid(), 'Grade 11 Commerce', 'GR11C', 'school', 'grade_11', 1, 30, 'open', 'active', NOW(), NOW()),
(gen_random_uuid(), 'Grade 11 Arts', 'GR11A', 'school', 'grade_11', 1, 30, 'open', 'active', NOW(), NOW()),
(gen_random_uuid(), 'Grade 12 Science', 'GR12S', 'school', 'grade_12', 1, 30, 'open', 'active', NOW(), NOW()),
(gen_random_uuid(), 'Grade 12 Commerce', 'GR12C', 'school', 'grade_12', 1, 30, 'open', 'active', NOW(), NOW()),
(gen_random_uuid(), 'Grade 12 Arts', 'GR12A', 'school', 'grade_12', 1, 30, 'open', 'active', NOW(), NOW());

-- Step 12: Update stream_type for Grade 11 and 12 classes
UPDATE "academic_programs" SET "stream_type" = 'science' WHERE "code" LIKE '%S';
UPDATE "academic_programs" SET "stream_type" = 'commerce' WHERE "code" LIKE '%C';
UPDATE "academic_programs" SET "stream_type" = 'arts' WHERE "code" LIKE '%A';

-- Step 13: Update institution config to remove institution_type references
UPDATE "institution_config" SET "grading_system" = 'Grade-based (A-F)' WHERE "grading_system" = '10-point GPA';

-- Step 14: Clean up any orphaned records
-- Remove any batches that reference non-existent programs
DELETE FROM "academic_batches" WHERE "program_id" NOT IN (SELECT "id" FROM "academic_programs");

-- Remove any students that reference non-existent programs
DELETE FROM "students" WHERE "program_id" NOT IN (SELECT "id" FROM "academic_programs");

-- Remove any classes that reference non-existent programs
DELETE FROM "classes" WHERE "program_id" NOT IN (SELECT "id" FROM "academic_programs");

-- Step 15: Update enum constraints
-- Drop and recreate program_type enum to only allow 'school'
ALTER TABLE "academic_programs" ALTER COLUMN "type" DROP DEFAULT;
DROP TYPE IF EXISTS "program_type" CASCADE;
CREATE TYPE "program_type" AS ENUM('school');
ALTER TABLE "academic_programs" ALTER COLUMN "type" TYPE "program_type" USING "type"::text::"program_type";
ALTER TABLE "academic_programs" ALTER COLUMN "type" SET DEFAULT 'school';

-- Step 16: Make class_level required for all programs
UPDATE "academic_programs" SET "class_level" = 'grade_1' WHERE "class_level" IS NULL;
ALTER TABLE "academic_programs" ALTER COLUMN "class_level" SET NOT NULL;

-- Step 17: Update default duration to 1 year for all school classes
UPDATE "academic_programs" SET "duration" = 1 WHERE "duration" != 1;
ALTER TABLE "academic_programs" ALTER COLUMN "duration" SET DEFAULT 1;

-- Step 18: Drop unused enum types
DROP TYPE IF EXISTS "institution_type" CASCADE;
DROP TYPE IF EXISTS "stream_category" CASCADE;
DROP TYPE IF EXISTS "semester" CASCADE;

-- Step 19: Add indexes for better performance
CREATE INDEX IF NOT EXISTS "academic_programs_class_level_idx" ON "academic_programs"("class_level");
CREATE INDEX IF NOT EXISTS "academic_programs_stream_type_idx" ON "academic_programs"("stream_type");
CREATE INDEX IF NOT EXISTS "academic_programs_type_status_idx" ON "academic_programs"("type", "status");
