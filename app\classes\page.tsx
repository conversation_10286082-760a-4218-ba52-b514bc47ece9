"use client";

import { useState } from "react";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, CardContent, Card<PERSON>eader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Plus, Clock, Users, MapPin } from "lucide-react";

// Mock data for classes (in a real app, this would come from the API)
const mockClasses = [
  {
    id: "1",
    name: "Algebra I",
    grade: "10",
    section: "A",
    subject: "Mathematics",
    teacher: "Dr. <PERSON>",
    room: "101",
    capacity: 30,
    enrolled: 28,
    schedule: "MWF",
    startTime: "09:00",
    endTime: "10:00",
    status: "active",
  },
  {
    id: "2",
    name: "Physics I",
    grade: "11",
    section: "B",
    subject: "Science",
    teacher: "Mr. <PERSON>",
    room: "201",
    capacity: 25,
    enrolled: 23,
    schedule: "TTH",
    startTime: "10:00",
    endTime: "11:30",
    status: "active",
  },
  {
    id: "3",
    name: "English Literature",
    grade: "12",
    section: "A",
    subject: "English",
    teacher: "<PERSON><PERSON> <PERSON>",
    room: "301",
    capacity: 32,
    enrolled: 30,
    schedule: "MWF",
    startTime: "11:00",
    endTime: "12:00",
    status: "active",
  },
];

export default function ClassesPage() {
  const [search, setSearch] = useState("");
  const [filters, setFilters] = useState({
    grade: "",
    subject: "",
    status: "",
  });

  const filteredClasses = mockClasses.filter((cls) => {
    const matchesSearch = 
      cls.name.toLowerCase().includes(search.toLowerCase()) ||
      cls.teacher.toLowerCase().includes(search.toLowerCase()) ||
      cls.subject.toLowerCase().includes(search.toLowerCase());
    
    const matchesGrade = !filters.grade || cls.grade === filters.grade;
    const matchesSubject = !filters.subject || cls.subject === filters.subject;
    const matchesStatus = !filters.status || cls.status === filters.status;

    return matchesSearch && matchesGrade && matchesSubject && matchesStatus;
  });

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Classes</h1>
            <p className="text-gray-600">Manage class schedules and assignments</p>
          </div>
          <Button className="sm:w-auto">
            <Plus className="h-4 w-4 mr-2" />
            Add Class
          </Button>
        </div>

        {/* Search and Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search classes..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex gap-2">
                <select
                  value={filters.grade}
                  onChange={(e) => setFilters({ ...filters, grade: e.target.value })}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                >
                  <option value="">All Grades</option>
                  <option value="9">Grade 9</option>
                  <option value="10">Grade 10</option>
                  <option value="11">Grade 11</option>
                  <option value="12">Grade 12</option>
                </select>
                <select
                  value={filters.subject}
                  onChange={(e) => setFilters({ ...filters, subject: e.target.value })}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                >
                  <option value="">All Subjects</option>
                  <option value="Mathematics">Mathematics</option>
                  <option value="Science">Science</option>
                  <option value="English">English</option>
                  <option value="History">History</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Classes Grid */}
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-3">
          {filteredClasses.length === 0 ? (
            <div className="col-span-full text-center py-8">
              <p className="text-gray-500">No classes found</p>
            </div>
          ) : (
            filteredClasses.map((cls) => (
              <Card key={cls.id} className="hover:shadow-md transition-shadow">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-lg">{cls.name}</CardTitle>
                    <span
                      className={`px-2 py-1 text-xs font-medium rounded-full ${
                        cls.status === "active"
                          ? "bg-green-100 text-green-800"
                          : "bg-gray-100 text-gray-800"
                      }`}
                    >
                      {cls.status}
                    </span>
                  </div>
                  <p className="text-sm text-gray-600">
                    Grade {cls.grade}{cls.section} • {cls.subject}
                  </p>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="space-y-2">
                    <div className="flex items-center text-sm text-gray-600">
                      <Users className="h-4 w-4 mr-2" />
                      Teacher: {cls.teacher}
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <MapPin className="h-4 w-4 mr-2" />
                      Room {cls.room}
                    </div>
                    <div className="flex items-center text-sm text-gray-600">
                      <Clock className="h-4 w-4 mr-2" />
                      {cls.schedule} • {cls.startTime} - {cls.endTime}
                    </div>
                  </div>

                  <div className="pt-3 border-t">
                    <div className="flex items-center justify-between text-sm">
                      <span className="text-gray-600">Enrollment</span>
                      <span className="font-medium">
                        {cls.enrolled}/{cls.capacity}
                      </span>
                    </div>
                    <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                      <div
                        className="bg-blue-600 h-2 rounded-full"
                        style={{
                          width: `${(cls.enrolled / cls.capacity) * 100}%`,
                        }}
                      />
                    </div>
                  </div>

                  <div className="flex gap-2 pt-2">
                    <Button variant="outline" size="sm" className="flex-1">
                      View Details
                    </Button>
                    <Button variant="outline" size="sm" className="flex-1">
                      Edit
                    </Button>
                  </div>
                </CardContent>
              </Card>
            ))
          )}
        </div>

        {/* Summary Stats */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="text-2xl font-bold text-blue-600">
                {filteredClasses.length}
              </div>
              <p className="text-sm text-gray-600">Total Classes</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="text-2xl font-bold text-green-600">
                {filteredClasses.filter(c => c.status === "active").length}
              </div>
              <p className="text-sm text-gray-600">Active Classes</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="text-2xl font-bold text-purple-600">
                {filteredClasses.reduce((sum, c) => sum + c.enrolled, 0)}
              </div>
              <p className="text-sm text-gray-600">Total Students</p>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="text-2xl font-bold text-orange-600">
                {Math.round(
                  (filteredClasses.reduce((sum, c) => sum + c.enrolled, 0) /
                    filteredClasses.reduce((sum, c) => sum + c.capacity, 0)) * 100
                ) || 0}%
              </div>
              <p className="text-sm text-gray-600">Avg. Capacity</p>
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  );
}
