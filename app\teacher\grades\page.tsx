"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Award,
  BookOpen,
  Users,
  TrendingUp,
  Search,
  Filter,
  Download,
  Upload,
  Edit,
  Eye,
  Plus,
  Save,
  Calculator,
  Target,
  BarChart3,
  FileText,
  CheckCircle,
} from "lucide-react";

export default function TeacherGrades() {
  const [user, setUser] = useState<any>(null);
  const [selectedClass, setSelectedClass] = useState("Grade 10A");
  const [selectedAssignment, setSelectedAssignment] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [grades, setGrades] = useState<any>({});
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "teacher") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock data
  const teacherClasses = [
    { id: "10A", name: "Grade 10A", subject: "Mathematics", students: 45 },
    { id: "10B", name: "Grade 10B", subject: "Mathematics", students: 44 },
    { id: "11A", name: "Grade 11A", subject: "Algebra", students: 43 },
    { id: "12A", name: "Grade 12A", subject: "Statistics", students: 41 },
  ];

  const assignments = [
    { id: "1", title: "Mid-term Exam", type: "Exam", maxMarks: 100, date: "2024-01-15" },
    { id: "2", title: "Quadratic Equations", type: "Assignment", maxMarks: 50, date: "2024-01-10" },
    { id: "3", title: "Unit Test 1", type: "Test", maxMarks: 25, date: "2024-01-05" },
    { id: "4", title: "Homework Set 1", type: "Homework", maxMarks: 20, date: "2024-01-01" },
  ];

  const students = [
    { id: "1", rollNo: "10A001", name: "John Doe", currentGPA: 3.8 },
    { id: "2", rollNo: "10A002", name: "Alice Smith", currentGPA: 3.9 },
    { id: "3", rollNo: "10A003", name: "Michael Johnson", currentGPA: 3.2 },
    { id: "4", rollNo: "10A004", name: "Emma Davis", currentGPA: 4.0 },
    { id: "5", rollNo: "10A005", name: "Robert Wilson", currentGPA: 3.5 },
    { id: "6", rollNo: "10A006", name: "Sarah Brown", currentGPA: 2.8 },
    { id: "7", rollNo: "10A007", name: "David Lee", currentGPA: 3.1 },
    { id: "8", rollNo: "10A008", name: "Lisa Wang", currentGPA: 3.7 },
  ];

  const gradeStats = {
    totalStudents: 45,
    graded: 38,
    pending: 7,
    averageScore: 78.5,
    classAverage: 3.6,
    passRate: 91.1,
  };

  const recentGrades = [
    { student: "John Doe", assignment: "Mid-term Exam", score: 85, maxMarks: 100, grade: "A", date: "2024-01-20" },
    { student: "Alice Smith", assignment: "Mid-term Exam", score: 92, maxMarks: 100, grade: "A+", date: "2024-01-20" },
    { student: "Michael Johnson", assignment: "Quadratic Equations", score: 38, maxMarks: 50, grade: "B+", date: "2024-01-19" },
    { student: "Emma Davis", assignment: "Mid-term Exam", score: 96, maxMarks: 100, grade: "A+", date: "2024-01-20" },
    { student: "Robert Wilson", assignment: "Unit Test 1", score: 20, maxMarks: 25, grade: "A", date: "2024-01-18" },
  ];

  const handleGradeChange = (studentId: string, assignmentId: string, score: string) => {
    setGrades((prev: any) => ({
      ...prev,
      [`${studentId}-${assignmentId}`]: score
    }));
  };

  const getGradeColor = (grade: string) => {
    switch (grade) {
      case "A+": return "text-green-600 bg-green-100";
      case "A": return "text-blue-600 bg-blue-100";
      case "B+": return "text-yellow-600 bg-yellow-100";
      case "B": return "text-orange-600 bg-orange-100";
      case "C+": return "text-red-600 bg-red-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const calculateGrade = (score: number, maxMarks: number) => {
    const percentage = (score / maxMarks) * 100;
    if (percentage >= 95) return "A+";
    if (percentage >= 85) return "A";
    if (percentage >= 75) return "B+";
    if (percentage >= 65) return "B";
    if (percentage >= 55) return "C+";
    return "C";
  };

  const filteredStudents = students.filter(student =>
    student.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    student.rollNo.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const saveGrades = () => {
    alert("Grades saved successfully!");
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Grade Management</h1>
            <p className="text-gray-600">Grade assignments and track student performance</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Upload className="h-4 w-4 mr-2" />
              Import Grades
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button onClick={saveGrades}>
              <Save className="h-4 w-4 mr-2" />
              Save Grades
            </Button>
          </div>
        </div>

        {/* Grade Stats */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {gradeStats.totalStudents}
                  </div>
                  <p className="text-sm text-gray-600">Total Students</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {gradeStats.graded}
                  </div>
                  <p className="text-sm text-gray-600">Graded</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Award className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {gradeStats.classAverage}
                  </div>
                  <p className="text-sm text-gray-600">Class Average GPA</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600">+0.2</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Target className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    {gradeStats.passRate}%
                  </div>
                  <p className="text-sm text-gray-600">Pass Rate</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600">+3.1%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Controls */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search students by name or roll number..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <select
                  value={selectedClass}
                  onChange={(e) => setSelectedClass(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  {teacherClasses.map((cls) => (
                    <option key={cls.id} value={cls.name}>
                      {cls.name} - {cls.subject}
                    </option>
                  ))}
                </select>
                <select
                  value={selectedAssignment}
                  onChange={(e) => setSelectedAssignment(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Assignments</option>
                  {assignments.map((assignment) => (
                    <option key={assignment.id} value={assignment.id}>
                      {assignment.title}
                    </option>
                  ))}
                </select>
                <Button variant="outline">
                  <Plus className="h-4 w-4 mr-2" />
                  New Assignment
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Main Content Grid */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Grading Interface */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calculator className="h-5 w-5 mr-2" />
                Grade Students - {selectedClass}
              </CardTitle>
            </CardHeader>
            <CardContent>
              {selectedAssignment === "all" ? (
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Student</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Roll No.</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Current GPA</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Actions</th>
                      </tr>
                    </thead>
                    <tbody>
                      {filteredStudents.map((student) => (
                        <tr key={student.id} className="border-b hover:bg-gray-50">
                          <td className="py-3 px-4 font-medium text-gray-900">{student.name}</td>
                          <td className="py-3 px-4 text-gray-600">{student.rollNo}</td>
                          <td className="py-3 px-4">
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                              student.currentGPA >= 3.5 ? 'text-green-600 bg-green-100' :
                              student.currentGPA >= 3.0 ? 'text-blue-600 bg-blue-100' :
                              student.currentGPA >= 2.5 ? 'text-yellow-600 bg-yellow-100' :
                              'text-red-600 bg-red-100'
                            }`}>
                              {student.currentGPA}
                            </span>
                          </td>
                          <td className="py-3 px-4">
                            <div className="flex gap-2">
                              <Button variant="outline" size="sm">
                                <Eye className="h-3 w-3" />
                              </Button>
                              <Button variant="outline" size="sm">
                                <Edit className="h-3 w-3" />
                              </Button>
                            </div>
                          </td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              ) : (
                <div className="space-y-4">
                  {filteredStudents.map((student) => {
                    const assignment = assignments.find(a => a.id === selectedAssignment);
                    const gradeKey = `${student.id}-${selectedAssignment}`;
                    const currentScore = grades[gradeKey] || "";

                    return (
                      <div key={student.id} className="flex items-center justify-between p-4 border rounded-lg">
                        <div className="flex items-center space-x-4">
                          <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                            <span className="font-medium text-blue-600">
                              {student.name.split(' ').map(n => n[0]).join('')}
                            </span>
                          </div>
                          <div>
                            <div className="font-medium text-gray-900">{student.name}</div>
                            <div className="text-sm text-gray-500">Roll No: {student.rollNo}</div>
                          </div>
                        </div>

                        <div className="flex items-center space-x-4">
                          <div className="flex items-center space-x-2">
                            <Input
                              type="number"
                              placeholder="Score"
                              value={currentScore}
                              onChange={(e) => handleGradeChange(student.id, selectedAssignment, e.target.value)}
                              className="w-20"
                              max={assignment?.maxMarks}
                              min="0"
                            />
                            <span className="text-gray-500">/ {assignment?.maxMarks}</span>
                          </div>
                          {currentScore && (
                            <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${
                              getGradeColor(calculateGrade(parseInt(currentScore), assignment?.maxMarks || 100))
                            }`}>
                              {calculateGrade(parseInt(currentScore), assignment?.maxMarks || 100)}
                            </span>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              )}
            </CardContent>
          </Card>

          {/* Sidebar */}
          <div className="space-y-6">
            {/* Assignments */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center">
                  <FileText className="h-5 w-5 mr-2" />
                  Assignments
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {assignments.map((assignment) => (
                    <div
                      key={assignment.id}
                      className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                        selectedAssignment === assignment.id ? 'bg-blue-50 border-blue-200' : 'hover:bg-gray-50'
                      }`}
                      onClick={() => setSelectedAssignment(assignment.id)}
                    >
                      <div className="font-medium text-gray-900">{assignment.title}</div>
                      <div className="text-sm text-gray-600">{assignment.type}</div>
                      <div className="text-xs text-gray-500">
                        Max: {assignment.maxMarks} | {assignment.date}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Recent Grades */}
            <Card>
              <CardHeader>
                <CardTitle>Recent Grades</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {recentGrades.map((grade, index) => (
                    <div key={index} className="p-3 border rounded-lg">
                      <div className="flex justify-between items-center mb-2">
                        <span className="font-medium text-gray-900">{grade.student}</span>
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getGradeColor(grade.grade)}`}>
                          {grade.grade}
                        </span>
                      </div>
                      <div className="text-sm text-gray-600">{grade.assignment}</div>
                      <div className="text-xs text-gray-500">
                        {grade.score}/{grade.maxMarks} • {grade.date}
                      </div>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Grade Distribution */}
            <Card>
              <CardHeader>
                <CardTitle>Grade Distribution</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {[
                    { grade: "A+", count: 8, percentage: 17.8 },
                    { grade: "A", count: 12, percentage: 26.7 },
                    { grade: "B+", count: 15, percentage: 33.3 },
                    { grade: "B", count: 8, percentage: 17.8 },
                    { grade: "C+", count: 2, percentage: 4.4 },
                  ].map((item) => (
                    <div key={item.grade} className="flex items-center justify-between">
                      <div className="flex items-center space-x-2">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getGradeColor(item.grade)}`}>
                          {item.grade}
                        </span>
                        <span className="text-sm text-gray-600">{item.count} students</span>
                      </div>
                      <span className="text-sm font-medium">{item.percentage}%</span>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
