import { Hono } from "hono";
import { zValidator } from "@hono/zod-validator";
import { mockFeeStructures, mockFeePayments } from "@/lib/mock-db";
import { feeStructureSchema, feePaymentSchema } from "@/lib/schemas";
import { generateId } from "@/lib/utils";

const app = new Hono()
  // Fee Structures
  .get("/fee-structures", async (c) => {
    const page = parseInt(c.req.query("page") || "1");
    const limit = parseInt(c.req.query("limit") || "10");
    const grade = c.req.query("grade");
    const academicYear = c.req.query("academicYear");
    const status = c.req.query("status");

    let filteredStructures = [...mockFeeStructures];

    if (grade) {
      filteredStructures = filteredStructures.filter((fs) => fs.grade === grade);
    }

    if (academicYear) {
      filteredStructures = filteredStructures.filter((fs) => fs.academicYear === academicYear);
    }

    if (status) {
      filteredStructures = filteredStructures.filter((fs) => fs.status === status);
    }

    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedStructures = filteredStructures.slice(startIndex, endIndex);

    return c.json({
      data: paginatedStructures,
      pagination: {
        page,
        limit,
        total: filteredStructures.length,
        totalPages: Math.ceil(filteredStructures.length / limit),
      },
    });
  })
  .post(
    "/fee-structures",
    zValidator("json", feeStructureSchema.omit({ id: true, createdAt: true, updatedAt: true })),
    async (c) => {
      const values = c.req.valid("json");
      
      const newStructure = {
        ...values,
        id: generateId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      mockFeeStructures.push(newStructure);

      return c.json({ data: newStructure }, 201);
    }
  )
  .get("/fee-structures/:id", async (c) => {
    const id = c.req.param("id");
    const structure = mockFeeStructures.find((fs) => fs.id === id);

    if (!structure) {
      return c.json({ error: "Fee structure not found" }, 404);
    }

    return c.json({ data: structure });
  })
  .put(
    "/fee-structures/:id",
    zValidator("json", feeStructureSchema.omit({ id: true, createdAt: true, updatedAt: true })),
    async (c) => {
      const id = c.req.param("id");
      const values = c.req.valid("json");

      const structureIndex = mockFeeStructures.findIndex((fs) => fs.id === id);

      if (structureIndex === -1) {
        return c.json({ error: "Fee structure not found" }, 404);
      }

      mockFeeStructures[structureIndex] = {
        ...mockFeeStructures[structureIndex],
        ...values,
        updatedAt: new Date().toISOString(),
      };

      return c.json({ data: mockFeeStructures[structureIndex] });
    }
  )
  .delete("/fee-structures/:id", async (c) => {
    const id = c.req.param("id");
    const structureIndex = mockFeeStructures.findIndex((fs) => fs.id === id);

    if (structureIndex === -1) {
      return c.json({ error: "Fee structure not found" }, 404);
    }

    mockFeeStructures.splice(structureIndex, 1);

    return c.json({ message: "Fee structure deleted successfully" });
  })

  // Fee Payments
  .get("/payments", async (c) => {
    const page = parseInt(c.req.query("page") || "1");
    const limit = parseInt(c.req.query("limit") || "10");
    const studentId = c.req.query("studentId");
    const status = c.req.query("status");
    const paymentMethod = c.req.query("paymentMethod");
    const dateFrom = c.req.query("dateFrom");
    const dateTo = c.req.query("dateTo");

    let filteredPayments = [...mockFeePayments];

    if (studentId) {
      filteredPayments = filteredPayments.filter((p) => p.studentId === studentId);
    }

    if (status) {
      filteredPayments = filteredPayments.filter((p) => p.status === status);
    }

    if (paymentMethod) {
      filteredPayments = filteredPayments.filter((p) => p.paymentMethod === paymentMethod);
    }

    if (dateFrom) {
      filteredPayments = filteredPayments.filter((p) => p.paymentDate >= dateFrom);
    }

    if (dateTo) {
      filteredPayments = filteredPayments.filter((p) => p.paymentDate <= dateTo);
    }

    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedPayments = filteredPayments.slice(startIndex, endIndex);

    return c.json({
      data: paginatedPayments,
      pagination: {
        page,
        limit,
        total: filteredPayments.length,
        totalPages: Math.ceil(filteredPayments.length / limit),
      },
    });
  })
  .post(
    "/payments",
    zValidator("json", feePaymentSchema.omit({ id: true, createdAt: true, updatedAt: true })),
    async (c) => {
      const values = c.req.valid("json");
      
      const newPayment = {
        ...values,
        id: generateId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      mockFeePayments.push(newPayment);

      return c.json({ data: newPayment }, 201);
    }
  )
  .get("/payments/:id", async (c) => {
    const id = c.req.param("id");
    const payment = mockFeePayments.find((p) => p.id === id);

    if (!payment) {
      return c.json({ error: "Payment not found" }, 404);
    }

    return c.json({ data: payment });
  })
  .put(
    "/payments/:id",
    zValidator("json", feePaymentSchema.omit({ id: true, createdAt: true, updatedAt: true })),
    async (c) => {
      const id = c.req.param("id");
      const values = c.req.valid("json");

      const paymentIndex = mockFeePayments.findIndex((p) => p.id === id);

      if (paymentIndex === -1) {
        return c.json({ error: "Payment not found" }, 404);
      }

      mockFeePayments[paymentIndex] = {
        ...mockFeePayments[paymentIndex],
        ...values,
        updatedAt: new Date().toISOString(),
      };

      return c.json({ data: mockFeePayments[paymentIndex] });
    }
  )
  .delete("/payments/:id", async (c) => {
    const id = c.req.param("id");
    const paymentIndex = mockFeePayments.findIndex((p) => p.id === id);

    if (paymentIndex === -1) {
      return c.json({ error: "Payment not found" }, 404);
    }

    mockFeePayments.splice(paymentIndex, 1);

    return c.json({ message: "Payment deleted successfully" });
  })

  // Financial Reports
  .get("/reports/summary", async (c) => {
    const totalCollected = mockFeePayments
      .filter(p => p.status === "completed")
      .reduce((sum, p) => sum + p.amountPaid, 0);
    
    const totalPending = mockFeePayments
      .filter(p => p.status === "pending")
      .reduce((sum, p) => sum + p.amountPaid, 0);

    const totalStructures = mockFeeStructures.length;
    const activeStructures = mockFeeStructures.filter(fs => fs.status === "active").length;

    const paymentMethodStats = mockFeePayments.reduce((acc, payment) => {
      acc[payment.paymentMethod] = (acc[payment.paymentMethod] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    return c.json({
      data: {
        totalCollected,
        totalPending,
        totalStructures,
        activeStructures,
        paymentMethodStats,
        collectionRate: totalCollected / (totalCollected + totalPending) * 100,
      },
    });
  });

export default app;
