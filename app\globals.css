@tailwind base;
@tailwind components;
@tailwind utilities;

* {
  border-color: var(--border);
}

body {
  background-color: var(--background);
  color: var(--foreground);
  font-family: var(--font-sans), system-ui, sans-serif;
}

/* Ensure good contrast for all text */
.text-gray-500 {
  color: #6b7280 !important;
}

.text-gray-600 {
  color: #4b5563 !important;
}

.text-gray-700 {
  color: #374151 !important;
}

.text-gray-900 {
  color: #111827 !important;
}

/* Form styling improvements */
input, select, textarea {
  background-color: #ffffff !important;
  border-color: #d1d5db !important;
  color: #111827 !important;
}

input:focus, select:focus, textarea:focus {
  border-color: #3b82f6 !important;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1) !important;
}

/* Button improvements */
button {
  font-weight: 500;
}

/* Card improvements */
.bg-card {
  background-color: #ffffff !important;
  border-color: #e5e7eb !important;
}
