"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import {
  IndianRupee,
  Search,
  Download,
  Plus,
  MoreHorizontal,
  Edit,
  Trash2,
  Eye,
  BookOpen,
  Loader2,
  RefreshCw,
  Calculator,
  TrendingUp,
} from "lucide-react";
import { useGetFeeStructures, useDeleteFeeStructure } from "@/features/api/use-fee-structures";
import { toast } from "sonner";

export default function AdminFeeStructures() {
  const [user, setUser] = useState<any>(null);
  const [currentPage, setCurrentPage] = useState(1);
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [selectedAcademicYear, setSelectedAcademicYear] = useState("all");
  const router = useRouter();

  // API hooks
  const { data: feeStructuresData, isLoading, error, refetch } = useGetFeeStructures({
    page: currentPage,
    limit: 10,
    search: searchTerm,
    status: selectedStatus === "all" ? undefined : selectedStatus,
    academicYear: selectedAcademicYear === "all" ? undefined : selectedAcademicYear,
  });

  const deleteFeeStructureMutation = useDeleteFeeStructure();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "super_admin") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  const handleDeleteFeeStructure = async (feeStructureId: string, grade: string) => {
    try {
      await deleteFeeStructureMutation.mutateAsync(feeStructureId);
      toast.success(`Fee structure for "${grade}" has been deleted`);
    } catch (error) {
      console.error("Error deleting fee structure:", error);
    }
  };

  const handleExport = () => {
    toast.info("Export functionality coming soon!");
  };

  const handleRefresh = () => {
    refetch();
    toast.success("Fee structures list refreshed!");
  };

  if (!user) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="text-center">
            <Loader2 className="h-8 w-8 animate-spin mx-auto text-blue-600" />
            <p className="mt-2 text-gray-600">Loading...</p>
          </div>
        </div>
      </AppLayout>
    );
  }

  // Get fee structures and pagination from API
  const feeStructures = feeStructuresData?.data || [];
  const pagination = { page: 1, limit: 10, total: feeStructures.length, totalPages: Math.ceil(feeStructures.length / 10) };

  // Calculate statistics
  const feeStats = {
    total: pagination.total,
    active: feeStructures.filter((f: any) => f.status === "active").length,
    inactive: feeStructures.filter((f: any) => f.status === "inactive").length,
    totalRevenue: feeStructures.reduce((sum: number, fee: any) => sum + (fee.totalAmount || 0), 0),
  };

  const getStatusColor = (status: string) => {
    return status === "active"
      ? "bg-green-100 text-green-800"
      : "bg-red-100 text-red-800";
  };

  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat('en-IN', {
      style: 'currency',
      currency: 'INR',
      minimumFractionDigits: 0,
    }).format(amount);
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Fee Structures</h1>
            <p className="text-gray-600">Manage academic fee structures and pricing</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" onClick={handleRefresh} disabled={isLoading}>
              <RefreshCw className={`h-4 w-4 mr-2 ${isLoading ? 'animate-spin' : ''}`} />
              Refresh
            </Button>
            <Button variant="outline" onClick={handleExport}>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button onClick={() => router.push("/admin/fee-structures/new")}>
              <Plus className="h-4 w-4 mr-2" />
              Add Fee Structure
            </Button>
          </div>
        </div>

        {/* Error State */}
        {error && (
          <Card className="border-red-200 bg-red-50">
            <CardContent className="p-4">
              <div className="flex items-center gap-2 text-red-800">
                <Trash2 className="h-5 w-5" />
                <p>Failed to load fee structures. Please try again.</p>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Statistics Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Structures</CardTitle>
              <Calculator className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{feeStats.total}</div>
              <p className="text-xs text-muted-foreground">
                {feeStats.active} active, {feeStats.inactive} inactive
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Active Structures</CardTitle>
              <BookOpen className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{feeStats.active}</div>
              <p className="text-xs text-muted-foreground">
                Currently in use
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
              <TrendingUp className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{formatCurrency(feeStats.totalRevenue)}</div>
              <p className="text-xs text-muted-foreground">
                Expected annual revenue
              </p>
            </CardContent>
          </Card>

          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Average Fee</CardTitle>
              <IndianRupee className="h-4 w-4 text-muted-foreground" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">
                {formatCurrency(feeStats.total > 0 ? feeStats.totalRevenue / feeStats.total : 0)}
              </div>
              <p className="text-xs text-muted-foreground">
                Per structure
              </p>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Search */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    type="text"
                    placeholder="Search fee structures by grade or program..."
                    className="pl-10"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
              <div className="flex gap-2 flex-wrap">
                <Select value={selectedStatus} onValueChange={(value) => setSelectedStatus(value)}>
                  <SelectTrigger className="w-32">
                    <SelectValue placeholder="Status" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Status</SelectItem>
                    <SelectItem value="active">Active</SelectItem>
                    <SelectItem value="inactive">Inactive</SelectItem>
                  </SelectContent>
                </Select>
                <Select value={selectedAcademicYear} onValueChange={(value) => setSelectedAcademicYear(value)}>
                  <SelectTrigger className="w-40">
                    <SelectValue placeholder="Academic Year" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Years</SelectItem>
                    <SelectItem value="2024-25">2024-25</SelectItem>
                    <SelectItem value="2025-26">2025-26</SelectItem>
                    <SelectItem value="2026-27">2026-27</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Fee Structures Table */}
        <Card>
          <CardHeader>
            <CardTitle>Fee Structures List</CardTitle>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex items-center justify-center h-32">
                <Loader2 className="h-8 w-8 animate-spin text-blue-600" />
              </div>
            ) : feeStructures.length === 0 ? (
              <div className="text-center py-8">
                <Calculator className="h-12 w-12 mx-auto text-gray-400 mb-4" />
                <h3 className="text-lg font-medium text-gray-900 mb-2">No fee structures found</h3>
                <p className="text-gray-600 mb-4">
                  {searchTerm || selectedStatus !== "all" || selectedAcademicYear !== "all"
                    ? "No fee structures match your current filters."
                    : "Get started by creating your first fee structure."}
                </p>
                <Button onClick={() => router.push("/admin/fee-structures/new")}>
                  <Plus className="h-4 w-4 mr-2" />
                  Add First Fee Structure
                </Button>
              </div>
            ) : (
              <div className="space-y-4">
                {feeStructures.map((feeStructure: any) => (
                  <div
                    key={feeStructure.id}
                    className="border rounded-lg p-4 hover:shadow-md transition-shadow"
                  >
                    <div className="flex items-start justify-between">
                      <div className="flex-1">
                        <div className="flex items-center gap-3 mb-2">
                          <h3 className="text-lg font-semibold text-gray-900">
                            {feeStructure.grade} - {feeStructure.academicYear}
                          </h3>
                          <Badge className={getStatusColor(feeStructure.status)}>
                            {feeStructure.status.charAt(0).toUpperCase() + feeStructure.status.slice(1)}
                          </Badge>
                        </div>

                        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4 text-sm text-gray-600">
                          <div className="flex items-center gap-2">
                            <IndianRupee className="h-4 w-4" />
                            <span>Total: {formatCurrency(feeStructure.totalAmount)}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Calculator className="h-4 w-4" />
                            <span>Tuition: {formatCurrency(feeStructure.tuitionFee)}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <BookOpen className="h-4 w-4" />
                            <span>Books: {formatCurrency(feeStructure.booksFee || 0)}</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <TrendingUp className="h-4 w-4" />
                            <span>Other: {formatCurrency(feeStructure.otherFees || 0)}</span>
                          </div>
                        </div>

                        {feeStructure.description && (
                          <p className="mt-2 text-sm text-gray-600 line-clamp-2">
                            {feeStructure.description}
                          </p>
                        )}
                      </div>

                      <div className="flex items-center gap-2">
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => router.push(`/admin/fee-structures/${feeStructure.id}`)}
                        >
                          <Eye className="h-4 w-4" />
                        </Button>

                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="outline" size="sm">
                              <MoreHorizontal className="h-4 w-4" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent align="end">
                            <DropdownMenuItem
                              onClick={() => router.push(`/admin/fee-structures/${feeStructure.id}/edit`)}
                            >
                              <Edit className="h-4 w-4 mr-2" />
                              Edit Structure
                            </DropdownMenuItem>
                            <AlertDialog>
                              <AlertDialogTrigger asChild>
                                <DropdownMenuItem
                                  onSelect={(e) => e.preventDefault()}
                                  className="text-red-600 focus:text-red-600"
                                >
                                  <Trash2 className="h-4 w-4 mr-2" />
                                  Delete Structure
                                </DropdownMenuItem>
                              </AlertDialogTrigger>
                              <AlertDialogContent>
                                <AlertDialogHeader>
                                  <AlertDialogTitle>Delete Fee Structure</AlertDialogTitle>
                                  <AlertDialogDescription>
                                    Are you sure you want to delete the fee structure for &ldquo;{feeStructure.grade}&rdquo;?
                                    This action cannot be undone and may affect student billing.
                                  </AlertDialogDescription>
                                </AlertDialogHeader>
                                <AlertDialogFooter>
                                  <AlertDialogCancel>Cancel</AlertDialogCancel>
                                  <AlertDialogAction
                                    onClick={() => handleDeleteFeeStructure(feeStructure.id, feeStructure.grade)}
                                    className="bg-red-600 hover:bg-red-700"
                                  >
                                    Delete
                                  </AlertDialogAction>
                                </AlertDialogFooter>
                              </AlertDialogContent>
                            </AlertDialog>
                          </DropdownMenuContent>
                        </DropdownMenu>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
