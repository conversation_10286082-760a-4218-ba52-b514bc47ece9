# Testing Guide

## Overview
Comprehensive testing guide for the Principal Login and Management System.

## Test Scripts Location
All test scripts are located in `scripts/testing/` directory:

```
scripts/testing/
├── test-principal-functionality.js
├── test-all-principal-routes.js
├── test-principal-add-functionality.js
├── test-corrected-academic-structure.js
└── test-promotion-system.js
```

## Setup Scripts Location
Setup and utility scripts are in `scripts/setup/` directory:

```
scripts/setup/
└── create-missing-principal-routes.js
```

## Running Tests

### 1. Principal Functionality Test
```bash
node scripts/testing/test-principal-functionality.js
```
**Tests:**
- Principal user configuration
- Navigation setup
- Page existence
- API endpoints
- Authentication middleware

### 2. All Principal Routes Test
```bash
node scripts/testing/test-all-principal-routes.js
```
**Tests:**
- All 29 principal routes
- Route accessibility
- Navigation configuration
- Categorized route structure
- Completion rates

### 3. Add Functionality Test
```bash
node scripts/testing/test-principal-add-functionality.js
```
**Tests:**
- Academic leadership add features
- Dialog components
- Form elements
- State management
- Submit handlers

### 4. Academic Structure Test
```bash
node scripts/testing/test-corrected-academic-structure.js
```
**Tests:**
- Class hierarchy (Nursery to Grade 12)
- Section structure
- Subject assignment
- Academic hierarchy
- Grade-specific subjects

### 5. Promotion System Test
```bash
node scripts/testing/test-promotion-system.js
```
**Tests:**
- Promotion page features
- Workflow system
- Navigation integration
- Promotion logic
- Bulk operations

## Manual Testing

### Login Testing
1. **Access URL**: `http://localhost:3000/login`
2. **Credentials**: `<EMAIL>` / `principal123`
3. **Expected**: Redirect to `/principal/dashboard`
4. **Verify**: Principal-specific navigation appears

### Dashboard Testing
1. **URL**: `http://localhost:3000/principal/dashboard`
2. **Check**: Real-time metrics display
3. **Verify**: Quick action buttons work
4. **Test**: Navigation to other sections

### Academic Leadership Testing
1. **Academic Overview**: `/principal/academic`
   - Test class structure display
   - Try adding subjects to classes
   - Verify section management
   
2. **Curriculum Planning**: `/principal/curriculum`
   - Test curriculum creation
   - Verify form validation
   - Check success messages
   
3. **Academic Calendar**: `/principal/calendar`
   - Test event creation
   - Verify date/time pickers
   - Check event categorization
   
4. **Performance Analysis**: `/principal/performance`
   - Test goal creation
   - Verify performance tracking
   - Check analytics display

### Student Promotion Testing
1. **Main Interface**: `/principal/promotion`
   - Test bulk promotion
   - Verify manual review
   - Check session management
   
2. **Workflow System**: `/principal/promotion/workflow`
   - Test workflow controls
   - Verify step progression
   - Check progress tracking

### Navigation Testing
1. **Sidebar Navigation**: Test all menu items
2. **Breadcrumb Navigation**: Verify path tracking
3. **Quick Actions**: Test dashboard shortcuts
4. **Role-Based Access**: Verify principal-only access

## Test Results Interpretation

### Success Criteria
- **100% Route Accessibility**: All routes load without errors
- **100% Feature Functionality**: All buttons and forms work
- **100% Authentication**: Secure login and session management
- **95%+ UI Responsiveness**: Works on desktop, tablet, mobile
- **100% Data Integration**: Mock data displays correctly

### Common Issues & Solutions

#### Missing Component Errors
```
Error: Module not found: Can't resolve '@/components/ui/progress'
```
**Solution**: Install missing Radix UI components
```bash
npm install @radix-ui/react-progress
```

#### Navigation Issues
```
Error: Cannot access /principal/route
```
**Solution**: Check role-based access in middleware and navigation config

#### Form Validation Errors
```
Error: Form submission fails
```
**Solution**: Verify all required fields are filled and validation logic

#### State Management Issues
```
Error: State not updating
```
**Solution**: Check useState hooks and state update functions

## Performance Testing

### Load Testing
1. **Multiple Route Navigation**: Test rapid navigation between routes
2. **Form Submission**: Test multiple form submissions
3. **Data Loading**: Test with large datasets
4. **Concurrent Users**: Test multiple principal sessions

### Browser Compatibility
- **Chrome**: Primary testing browser
- **Firefox**: Secondary testing
- **Safari**: Mac compatibility
- **Edge**: Windows compatibility
- **Mobile Browsers**: Responsive testing

## Automated Testing Setup

### Jest Configuration
```javascript
// jest.config.js
module.exports = {
  testEnvironment: 'jsdom',
  setupFilesAfterEnv: ['<rootDir>/jest.setup.js'],
  moduleNameMapping: {
    '^@/(.*)$': '<rootDir>/$1',
  },
};
```

### Test Examples
```javascript
// __tests__/principal-login.test.js
import { render, screen } from '@testing-library/react';
import PrincipalDashboard from '@/app/principal/dashboard/page';

test('renders principal dashboard', () => {
  render(<PrincipalDashboard />);
  expect(screen.getByText('Principal Dashboard')).toBeInTheDocument();
});
```

## Continuous Integration

### GitHub Actions
```yaml
# .github/workflows/test.yml
name: Test Principal System
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
      - uses: actions/checkout@v2
      - uses: actions/setup-node@v2
        with:
          node-version: '18'
      - run: npm install
      - run: npm run test:principal
```

## Test Reports

### Report Generation
Test scripts automatically generate JSON reports in `docs/reports/`:
- `principal-functionality-report.json`
- `principal-routes-complete-report.json`
- `principal-add-functionality-report.json`
- `corrected-academic-structure-report.json`
- `promotion-system-test-report.json`

### Report Analysis
Each report contains:
- **Timestamp**: When test was run
- **Summary Statistics**: Pass/fail counts and percentages
- **Detailed Results**: Individual test outcomes
- **Recommendations**: Suggested improvements
- **Test Instructions**: How to manually verify results

## Troubleshooting

### Common Test Failures
1. **Route 404 Errors**: Check if all route files exist
2. **Component Import Errors**: Verify component paths and exports
3. **Authentication Failures**: Check user credentials and role setup
4. **Navigation Errors**: Verify navigation configuration
5. **Form Errors**: Check form validation and submission logic

### Debug Mode
Run tests with debug output:
```bash
DEBUG=true node scripts/testing/test-name.js
```

### Verbose Logging
Enable detailed logging in test scripts by setting:
```javascript
const VERBOSE = true;
```

## Best Practices

### Test Writing
1. **Clear Test Names**: Descriptive test function names
2. **Isolated Tests**: Each test should be independent
3. **Mock Data**: Use realistic but controlled test data
4. **Error Handling**: Test both success and failure scenarios
5. **Documentation**: Comment complex test logic

### Test Maintenance
1. **Regular Updates**: Update tests when features change
2. **Cleanup**: Remove obsolete tests
3. **Refactoring**: Keep test code clean and maintainable
4. **Coverage**: Aim for high test coverage
5. **Performance**: Keep tests fast and efficient
