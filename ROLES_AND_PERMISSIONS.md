# School Management System - Roles and Permissions

## Overview

This document provides a comprehensive overview of all user roles in the school management system, their specific features, controls, and access permissions. The system is designed for school-only operations with a hierarchical role structure.

## Role Hierarchy

```
Super Admin (System Level)
    ↓
Principal (School Leadership)
    ↓
Admin (Department Management)
    ↓
Specialized Roles (Teachers, Staff, Officers)
    ↓
End Users (Students, Parents)
```

## 🔐 Role Definitions and Permissions

### 1. **Super Admin** (`super_admin`)

**Purpose**: Complete system administration and configuration
**Dashboard**: `/admin/dashboard`
**Access Level**: Full system access

#### Core Features:
- ✅ **System Configuration**: Institution setup, academic structure
- ✅ **User Management**: Create/manage all user types and roles
- ✅ **Academic Program Management**: Define classes, streams, subjects
- ✅ **System Settings**: Configure system-wide preferences
- ✅ **Audit & Security**: Monitor system activity and security
- ✅ **Data Management**: Backup, restore, data import/export
- ✅ **Financial Oversight**: Complete financial system access
- ✅ **Reports & Analytics**: All system reports and analytics

#### Specific Controls:
- Create and manage institution configuration
- Add/remove/modify all user roles
- Configure academic structure (classes, sections, subjects)
- Set fee structures and financial policies
- Access audit logs and system monitoring
- Manage system backups and data integrity
- Configure system-wide settings and preferences

#### Restrictions:
- None (Full system access)

---

### 2. **Principal** (`principal`)

**Purpose**: School leadership and strategic management
**Dashboard**: `/principal/dashboard`
**Access Level**: School-wide management with strategic oversight

#### Core Features:
- ✅ **School Overview**: Complete school performance dashboard
- ✅ **Academic Leadership**: Curriculum oversight and academic planning
- ✅ **Staff Management**: Teacher and staff performance monitoring
- ✅ **Student Affairs**: Student discipline, achievements, overall welfare
- ✅ **Financial Oversight**: Budget monitoring and financial approvals
- ✅ **Parent Relations**: Parent meetings, communications, feedback
- ✅ **Strategic Planning**: School development and improvement initiatives
- ✅ **External Relations**: Board meetings, community engagement

#### Specific Controls:
- Approve major academic decisions and policies
- Monitor and evaluate teacher performance
- Handle student disciplinary matters
- Approve budget allocations and major expenses
- Conduct parent-teacher meetings and conferences
- Review and approve academic calendar
- Access comprehensive school reports and analytics
- Manage school events and external relations

#### Restrictions:
- Cannot modify system configuration
- Cannot create super admin users
- Cannot access system-level settings

---

### 3. **Admin** (`admin`)

**Purpose**: Day-to-day school administration and operations
**Dashboard**: `/admin/dashboard`
**Access Level**: Operational management with departmental oversight

#### Core Features:
- ✅ **Student Management**: Admissions, records, academic tracking
- ✅ **Teacher Management**: Staff records, scheduling, basic HR
- ✅ **Academic Operations**: Class management, timetables, exams
- ✅ **Finance Management**: Fee collection, payments, basic accounting
- ✅ **Facility Management**: Library, transport, hostel operations
- ✅ **Communication**: Announcements, notifications, basic reporting
- ✅ **Data Entry**: Student information, grades, attendance records

#### Specific Controls:
- Process student admissions and registrations
- Manage teacher assignments and schedules
- Create and modify class structures and sections
- Handle fee collection and payment processing
- Coordinate library, transport, and hostel services
- Generate operational reports and statistics
- Manage day-to-day communications

#### Restrictions:
- Cannot modify system configuration
- Cannot create principal or super admin users
- Cannot approve major financial decisions
- Limited access to strategic planning features

---

### 4. **Teacher** (`teacher`)

**Purpose**: Classroom instruction and student assessment
**Dashboard**: `/teacher/dashboard`
**Access Level**: Class and student-specific access

#### Core Features:
- ✅ **Class Management**: Assigned classes and subjects
- ✅ **Student Assessment**: Grades, assignments, evaluations
- ✅ **Attendance Tracking**: Mark and monitor student attendance
- ✅ **Assignment Management**: Create, distribute, grade assignments
- ✅ **Parent Communication**: Progress reports, parent meetings
- ✅ **Curriculum Delivery**: Lesson planning, teaching resources

#### Specific Controls:
- Access only assigned classes and students
- Mark attendance for assigned classes
- Create and grade assignments and exams
- Enter grades and academic assessments
- Communicate with parents about student progress
- View and update student academic records

#### Restrictions:
- Cannot access other teachers' classes
- Cannot modify student personal information
- Cannot access financial information
- Cannot manage system users or settings

---

### 5. **Student** (`student`)

**Purpose**: Academic learning and progress tracking
**Dashboard**: `/student/dashboard`
**Access Level**: Personal academic information only

#### Core Features:
- ✅ **Academic Progress**: Grades, assignments, performance tracking
- ✅ **Attendance Records**: Personal attendance history
- ✅ **Assignment Submission**: Submit assignments and projects
- ✅ **Exam Information**: Exam schedules, results, report cards
- ✅ **Timetable Access**: Class schedules and academic calendar
- ✅ **Library Services**: Book search, issue history, reservations
- ✅ **Fee Information**: Fee status, payment history, dues

#### Specific Controls:
- View personal academic records and progress
- Submit assignments and projects online
- Check attendance and academic calendar
- Access library resources and services
- View fee status and payment information
- Receive announcements and notifications

#### Restrictions:
- Cannot access other students' information
- Cannot modify grades or attendance
- Cannot access administrative functions
- Read-only access to most information

---

### 6. **Parent** (`parent`)

**Purpose**: Monitor child's academic progress and school engagement
**Dashboard**: `/parent/dashboard`
**Access Level**: Child-specific academic and administrative information

#### Core Features:
- ✅ **Child's Progress**: Academic performance, grades, assessments
- ✅ **Attendance Monitoring**: Child's attendance records and patterns
- ✅ **Communication**: Teacher communications, school announcements
- ✅ **Fee Management**: Fee payments, dues, payment history
- ✅ **Event Participation**: School events, parent-teacher meetings
- ✅ **Academic Calendar**: Important dates, exam schedules, holidays

#### Specific Controls:
- View child's academic progress and reports
- Monitor attendance and academic performance
- Communicate with teachers and school administration
- Make fee payments and view payment history
- Participate in school events and meetings
- Receive school announcements and updates

#### Restrictions:
- Cannot access other children's information
- Cannot modify academic records
- Cannot access administrative functions
- Limited to child-related information only

---

## 🎯 Specialized Administrative Roles

### 7. **Admission Officer** (`admission_officer`)

**Purpose**: Student admissions and enrollment management
**Features**: Application processing, student registration, admission reports
**Access**: Admission-related data and processes only

### 8. **Finance Manager** (`finance_manager`)

**Purpose**: Financial operations and accounting
**Features**: Fee management, financial reports, payment processing, budgeting
**Access**: Financial data and accounting functions

### 9. **Librarian** (`librarian`)

**Purpose**: Library operations and resource management
**Features**: Book management, issue/return, library reports, member management
**Access**: Library system and related student data

### 10. **Transport Manager** (`transport_manager`)

**Purpose**: Transportation services and vehicle management
**Features**: Route management, vehicle maintenance, driver management, transport reports
**Access**: Transport-related data and operations

### 11. **Hostel Manager** (`hostel_manager`)

**Purpose**: Hostel operations and student accommodation
**Features**: Room allocation, hostel fees, maintenance, student welfare
**Access**: Hostel-related data and residential student information

---

## 🔒 Security and Access Control

### Permission Matrix

| Feature | Super Admin | Principal | Admin | Teacher | Student | Parent |
|---------|-------------|-----------|-------|---------|---------|--------|
| System Config | ✅ | ❌ | ❌ | ❌ | ❌ | ❌ |
| User Management | ✅ | ⚠️ | ⚠️ | ❌ | ❌ | ❌ |
| Academic Structure | ✅ | ✅ | ⚠️ | ❌ | ❌ | ❌ |
| Student Records | ✅ | ✅ | ✅ | ⚠️ | ⚠️ | ⚠️ |
| Financial Data | ✅ | ✅ | ⚠️ | ❌ | ⚠️ | ⚠️ |
| Reports & Analytics | ✅ | ✅ | ⚠️ | ⚠️ | ⚠️ | ⚠️ |

**Legend:**
- ✅ Full Access
- ⚠️ Limited Access
- ❌ No Access

### Data Access Levels

1. **System Level**: Super Admin only
2. **School Level**: Principal, Admin (with restrictions)
3. **Department Level**: Specialized roles (within their domain)
4. **Class Level**: Teachers (assigned classes only)
5. **Personal Level**: Students, Parents (own data only)

---

## 🚀 Implementation Guidelines

### Role Assignment Rules

1. **One Primary Role**: Each user has one primary role
2. **Hierarchical Access**: Higher roles can access lower-level functions
3. **Domain Restrictions**: Specialized roles limited to their domain
4. **Audit Trail**: All role changes logged and tracked

### Security Best Practices

1. **Principle of Least Privilege**: Users get minimum required access
2. **Regular Access Reviews**: Periodic review of user permissions
3. **Role-Based Authentication**: Route protection based on roles
4. **Activity Monitoring**: Track user actions and access patterns

### Future Enhancements

1. **Custom Permissions**: Fine-grained permission customization
2. **Temporary Access**: Time-limited role assignments
3. **Multi-Role Support**: Users with multiple role capabilities
4. **Dynamic Permissions**: Context-based access control

---

## 🔄 Recent Updates (School-Only Mode)

### Changes Made for School-Only System

#### 1. **New Principal Role Added**
- ✅ Added `principal` to user role enum in database schema
- ✅ Created dedicated principal dashboard at `/principal/dashboard`
- ✅ Implemented principal-specific navigation and features
- ✅ Updated authentication and authorization systems
- ✅ Added principal permissions and access controls

#### 2. **Role Hierarchy Clarification**
- **Super Admin**: System-level administration
- **Principal**: School leadership and strategic management
- **Admin**: Day-to-day operations and administration
- **Specialized Roles**: Department-specific functions
- **End Users**: Students and parents

#### 3. **Updated Navigation Structure**
- ✅ Principal navigation with 7 main sections:
  - Academic Leadership
  - Staff Management
  - Student Affairs
  - Financial Oversight
  - Parent Relations
  - Strategic Planning
  - Reports & Analytics

#### 4. **Database Schema Updates**
- ✅ Added `principal` to `userRoleEnum`
- ✅ Updated migration files
- ✅ Updated seed data with principal user
- ✅ Fixed user ID conflicts in mock data

#### 5. **Authentication & Authorization**
- ✅ Updated middleware permissions
- ✅ Added principal-specific permissions
- ✅ Updated role redirect components
- ✅ Enhanced access control matrix

#### 6. **UI/UX Improvements**
- ✅ Created principal dashboard with strategic focus
- ✅ Differentiated admin dashboard for operations
- ✅ Updated login page with separate principal option
- ✅ Enhanced role-based navigation

#### 7. **School-Only Features**
- ✅ Removed college-specific functionality
- ✅ Focused on grade-based academic structure
- ✅ Streamlined for K-12 education system
- ✅ Optimized for school management workflows

### Implementation Status

| Component | Status | Notes |
|-----------|--------|-------|
| Database Schema | ✅ Complete | Principal role added to enum |
| Authentication | ✅ Complete | Role-based auth updated |
| Navigation | ✅ Complete | Principal navigation implemented |
| Dashboard | ✅ Complete | Principal dashboard created |
| Permissions | ✅ Complete | Access control updated |
| Mock Data | ✅ Complete | Test users added |
| Documentation | ✅ Complete | Comprehensive role guide |

### Test Credentials

| Role | Email | Password | Access Level |
|------|-------|----------|--------------|
| Super Admin | <EMAIL> | admin123 | Full system access |
| Principal | <EMAIL> | principal123 | School leadership |
| Admin | <EMAIL> | admin123 | Operations management |
| Teacher | <EMAIL> | teacher123 | Class management |
| Student | <EMAIL> | student123 | Personal academic data |
| Parent | <EMAIL> | parent123 | Child's academic data |

### Next Steps

1. **Testing**: Comprehensive testing of all role functionalities
2. **Principal Features**: Implement remaining principal-specific pages
3. **Data Migration**: Migrate existing admin users to appropriate roles
4. **Performance**: Optimize role-based queries and navigation
5. **Documentation**: Update user manuals for new role structure
