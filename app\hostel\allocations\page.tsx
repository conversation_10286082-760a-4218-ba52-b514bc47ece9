"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Building,
  Search,
  Filter,
  Download,
  Plus,
  Eye,
  Edit,
  Trash2,
  Users,
  Bed,
  CheckCircle,
  XCircle,
  AlertTriangle,
  User,
  Calendar,
  Clock,
  MapPin,
  Home,
  IndianRupee,
  UserCheck,
  UserX,
} from "lucide-react";

export default function HostelAllocations() {
  const [user, setUser] = useState<any>(null);
  const [selectedTab, setSelectedTab] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedAllocation, setSelectedAllocation] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "hostel_manager") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  const allocationStats = {
    totalAllocations: 185,
    activeAllocations: 178,
    pendingAllocations: 12,
    expiredAllocations: 7,
    blockAAllocations: 65,
    blockBAllocations: 72,
    blockCAllocations: 48,
    availableRooms: 22,
  };

  const mockAllocations = [
    {
      id: "AL001",
      studentName: "Aarav Sharma",
      studentId: "ST001",
      class: "Class 10-A",
      roomNumber: "A-101",
      hostelBlock: "Block A",
      roomType: "Double",
      allocationDate: "2023-04-15",
      expiryDate: "2024-04-15",
      status: "Active",
      monthlyFee: 15000,
      securityDeposit: 30000,
      roommates: ["Rahul Kumar"],
      preferences: "Ground floor preferred",
      parentApproval: "Approved",
      medicalRequirements: "None",
      allocationReason: "New Admission",
    },
    {
      id: "AL002",
      studentName: "Priya Patel",
      studentId: "ST002",
      class: "Class 12-B",
      roomNumber: "B-201",
      hostelBlock: "Block B",
      roomType: "Triple",
      allocationDate: "2022-06-20",
      expiryDate: "2024-06-20",
      status: "Active",
      monthlyFee: 12000,
      securityDeposit: 24000,
      roommates: ["Sneha Reddy", "Anita Singh"],
      preferences: "AC room required",
      parentApproval: "Approved",
      medicalRequirements: "Asthma - requires ventilation",
      allocationReason: "Room Change Request",
    },
    {
      id: "AL003",
      studentName: "Rahul Kumar",
      studentId: "ST003",
      class: "Class 11-C",
      roomNumber: "Pending",
      hostelBlock: "Pending",
      roomType: "Single",
      allocationDate: "2024-01-20",
      expiryDate: "2024-02-20",
      status: "Pending",
      monthlyFee: 20000,
      securityDeposit: 40000,
      roommates: [],
      preferences: "Single room only",
      parentApproval: "Pending",
      medicalRequirements: "Diabetic - special dietary needs",
      allocationReason: "Medical Requirements",
    },
    {
      id: "AL004",
      studentName: "Sneha Reddy",
      studentId: "ST004",
      class: "Class 9-A",
      roomNumber: "C-301",
      hostelBlock: "Block C",
      roomType: "Single",
      allocationDate: "2023-08-05",
      expiryDate: "2024-08-05",
      status: "Active",
      monthlyFee: 18000,
      securityDeposit: 36000,
      roommates: [],
      preferences: "Top floor preferred",
      parentApproval: "Approved",
      medicalRequirements: "None",
      allocationReason: "New Admission",
    },
    {
      id: "AL005",
      studentName: "Vikram Singh",
      studentId: "ST005",
      class: "Class 8-B",
      roomNumber: "A-205",
      hostelBlock: "Block A",
      roomType: "Double",
      allocationDate: "2023-11-12",
      expiryDate: "2023-12-12",
      status: "Expired",
      monthlyFee: 15000,
      securityDeposit: 30000,
      roommates: ["Arjun Patel"],
      preferences: "Near library",
      parentApproval: "Approved",
      medicalRequirements: "Lactose intolerant",
      allocationReason: "Temporary Accommodation",
    },
  ];

  const filteredAllocations = mockAllocations.filter((allocation) => {
    const matchesSearch = allocation.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         allocation.studentId.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         allocation.class.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         allocation.roomNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         allocation.hostelBlock.toLowerCase().includes(searchTerm.toLowerCase());

    if (selectedTab === "all") return matchesSearch;
    if (selectedTab === "active") return matchesSearch && allocation.status === "Active";
    if (selectedTab === "pending") return matchesSearch && allocation.status === "Pending";
    if (selectedTab === "expired") return matchesSearch && allocation.status === "Expired";
    if (selectedTab === "block_a") return matchesSearch && allocation.hostelBlock === "Block A";
    if (selectedTab === "block_b") return matchesSearch && allocation.hostelBlock === "Block B";
    if (selectedTab === "block_c") return matchesSearch && allocation.hostelBlock === "Block C";

    return matchesSearch;
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Active":
        return <Badge className="bg-green-100 text-green-800">Active</Badge>;
      case "Pending":
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case "Expired":
        return <Badge className="bg-red-100 text-red-800">Expired</Badge>;
      case "Cancelled":
        return <Badge className="bg-gray-100 text-gray-800">Cancelled</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }
  };

  const getApprovalBadge = (approval: string) => {
    switch (approval) {
      case "Approved":
        return <Badge variant="outline" className="text-green-600 border-green-200">Approved</Badge>;
      case "Pending":
        return <Badge variant="outline" className="text-yellow-600 border-yellow-200">Pending</Badge>;
      case "Rejected":
        return <Badge variant="outline" className="text-red-600 border-red-200">Rejected</Badge>;
      default:
        return <Badge variant="outline">{approval}</Badge>;
    }
  };

  const viewAllocationDetails = (allocation: any) => {
    setSelectedAllocation(allocation);
  };

  const editAllocation = (allocationId: string) => {
    alert(`Editing allocation ${allocationId}`);
  };

  const approveAllocation = (allocationId: string) => {
    alert(`Approving allocation ${allocationId}`);
  };

  const rejectAllocation = (allocationId: string) => {
    alert(`Rejecting allocation ${allocationId}`);
  };

  const renewAllocation = (allocationId: string) => {
    alert(`Renewing allocation ${allocationId}`);
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Room Allocations</h1>
            <p className="text-gray-600">Manage student room allocations and assignments</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Allocations
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Allocation
            </Button>
          </div>
        </div>

        {/* Allocation Statistics */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Building className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {allocationStats.totalAllocations}
                  </div>
                  <p className="text-sm text-gray-600">Total Allocations</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {allocationStats.activeAllocations}
                  </div>
                  <p className="text-sm text-gray-600">Active</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-yellow-600">
                    {allocationStats.pendingAllocations}
                  </div>
                  <p className="text-sm text-gray-600">Pending</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Bed className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {allocationStats.availableRooms}
                  </div>
                  <p className="text-sm text-gray-600">Available Rooms</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filter */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search by student name, ID, class, room, or block..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </div>

            {/* Tabs */}
            <div className="flex flex-wrap gap-2 mt-4">
              {[
                { key: "all", label: "All Allocations", count: allocationStats.totalAllocations },
                { key: "active", label: "Active", count: allocationStats.activeAllocations },
                { key: "pending", label: "Pending", count: allocationStats.pendingAllocations },
                { key: "expired", label: "Expired", count: allocationStats.expiredAllocations },
                { key: "block_a", label: "Block A", count: allocationStats.blockAAllocations },
                { key: "block_b", label: "Block B", count: allocationStats.blockBAllocations },
                { key: "block_c", label: "Block C", count: allocationStats.blockCAllocations },
              ].map((tab) => (
                <Button
                  key={tab.key}
                  variant={selectedTab === tab.key ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedTab(tab.key)}
                  className="text-xs"
                >
                  {tab.label} ({tab.count})
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Allocations Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Building className="h-5 w-5 mr-2" />
              Room Allocations ({filteredAllocations.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-4 font-medium text-gray-900">Student Details</th>
                    <th className="text-left p-4 font-medium text-gray-900">Room Assignment</th>
                    <th className="text-left p-4 font-medium text-gray-900">Allocation Period</th>
                    <th className="text-left p-4 font-medium text-gray-900">Status & Approval</th>
                    <th className="text-left p-4 font-medium text-gray-900">Fee Details</th>
                    <th className="text-left p-4 font-medium text-gray-900">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredAllocations.map((allocation) => (
                    <tr key={allocation.id} className="border-b hover:bg-gray-50">
                      <td className="p-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <User className="h-5 w-5 text-blue-600" />
                          </div>
                          <div>
                            <div className="font-medium text-gray-900">{allocation.studentName}</div>
                            <div className="text-sm text-gray-500">{allocation.studentId}</div>
                            <div className="text-sm text-gray-500">{allocation.class}</div>
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-1">
                          <div className="font-medium text-gray-900">
                            {allocation.roomNumber === "Pending" ? "Not Assigned" : allocation.roomNumber}
                          </div>
                          <div className="text-sm text-gray-500">
                            {allocation.hostelBlock === "Pending" ? "Block TBD" : allocation.hostelBlock}
                          </div>
                          <div className="text-sm text-gray-500">{allocation.roomType} Room</div>
                          {allocation.roommates.length > 0 && (
                            <div className="text-xs text-gray-400">
                              Roommates: {allocation.roommates.join(", ")}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-1">
                          <div className="text-sm">
                            <span className="font-medium">From:</span>
                            <div className="text-gray-500">{new Date(allocation.allocationDate).toLocaleDateString()}</div>
                          </div>
                          <div className="text-sm">
                            <span className="font-medium">Until:</span>
                            <div className="text-gray-500">{new Date(allocation.expiryDate).toLocaleDateString()}</div>
                          </div>
                          <div className="text-xs text-gray-400">
                            Reason: {allocation.allocationReason}
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-2">
                          {getStatusBadge(allocation.status)}
                          {getApprovalBadge(allocation.parentApproval)}
                          {allocation.medicalRequirements !== "None" && (
                            <div className="text-xs text-orange-600 flex items-center">
                              <AlertTriangle className="h-3 w-3 mr-1" />
                              Medical needs
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-1">
                          <div className="font-medium text-gray-900">
                            ₹{allocation.monthlyFee.toLocaleString()}/month
                          </div>
                          <div className="text-sm text-gray-500">
                            Deposit: ₹{allocation.securityDeposit.toLocaleString()}
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => viewAllocationDetails(allocation)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => editAllocation(allocation.id)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          {allocation.status === "Pending" && (
                            <>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => approveAllocation(allocation.id)}
                              >
                                <UserCheck className="h-4 w-4" />
                              </Button>
                              <Button
                                size="sm"
                                variant="outline"
                                onClick={() => rejectAllocation(allocation.id)}
                              >
                                <UserX className="h-4 w-4" />
                              </Button>
                            </>
                          )}
                          {allocation.status === "Expired" && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => renewAllocation(allocation.id)}
                            >
                              <Calendar className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}