"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  IndianRupee,
  Users,
  Calendar,
  TrendingUp,
  TrendingDown,
  Plus,
  Edit,
  Eye,
  Download,
  Filter,
  Search,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  CreditCard,
  Receipt,
  FileText,
} from "lucide-react";

export default function FinanceFees() {
  const [user, setUser] = useState<any>(null);
  const [selectedFilter, setSelectedFilter] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "finance_manager" && parsedUser.role !== "admin" && parsedUser.role !== "super_admin") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock fee structures data
  const feeStructures = [
    {
      id: "FEE001",
      name: "Grade 10 - Science Stream",
      academicYear: "2024-2025",
      totalAmount: 25000,
      components: [
        { name: "Tuition Fee", amount: 15000, mandatory: true },
        { name: "Lab Fee", amount: 3000, mandatory: true },
        { name: "Library Fee", amount: 1000, mandatory: true },
        { name: "Sports Fee", amount: 2000, mandatory: false },
        { name: "Transport Fee", amount: 4000, mandatory: false },
      ],
      studentsEnrolled: 120,
      totalCollected: 2850000,
      pendingAmount: 150000,
      status: "Active",
      dueDate: "2024-03-31",
    },
    {
      id: "FEE002",
      name: "Grade 11 - Commerce Stream",
      academicYear: "2024-2025",
      totalAmount: 22000,
      components: [
        { name: "Tuition Fee", amount: 14000, mandatory: true },
        { name: "Computer Lab Fee", amount: 2000, mandatory: true },
        { name: "Library Fee", amount: 1000, mandatory: true },
        { name: "Activity Fee", amount: 1500, mandatory: false },
        { name: "Transport Fee", amount: 3500, mandatory: false },
      ],
      studentsEnrolled: 85,
      totalCollected: 1785000,
      pendingAmount: 85000,
      status: "Active",
      dueDate: "2024-03-31",
    },
    {
      id: "FEE003",
      name: "Grade 12 - Arts Stream",
      academicYear: "2024-2025",
      totalAmount: 20000,
      components: [
        { name: "Tuition Fee", amount: 12000, mandatory: true },
        { name: "Library Fee", amount: 1000, mandatory: true },
        { name: "Project Fee", amount: 2000, mandatory: true },
        { name: "Cultural Activity Fee", amount: 2000, mandatory: false },
        { name: "Transport Fee", amount: 3000, mandatory: false },
      ],
      studentsEnrolled: 65,
      totalCollected: 1235000,
      pendingAmount: 65000,
      status: "Active",
      dueDate: "2024-03-31",
    },
  ];

  const recentPayments = [
    {
      id: "PAY001",
      studentName: "John Doe",
      studentId: "STU001",
      amount: 25000,
      feeType: "Grade 10 - Science Stream",
      paymentDate: "2024-01-20",
      paymentMethod: "Online",
      status: "Completed",
      receiptNo: "RCP001",
    },
    {
      id: "PAY002",
      studentName: "Alice Smith",
      studentId: "STU002",
      amount: 22000,
      feeType: "Grade 11 - Commerce Stream",
      paymentDate: "2024-01-19",
      paymentMethod: "Bank Transfer",
      status: "Completed",
      receiptNo: "RCP002",
    },
    {
      id: "PAY003",
      studentName: "Bob Johnson",
      studentId: "STU003",
      amount: 15000,
      feeType: "Grade 10 - Science Stream",
      paymentDate: "2024-01-18",
      paymentMethod: "Cash",
      status: "Pending Verification",
      receiptNo: "RCP003",
    },
  ];

  const feeStats = {
    totalStructures: feeStructures.length,
    totalStudents: feeStructures.reduce((sum, fee) => sum + fee.studentsEnrolled, 0),
    totalCollected: feeStructures.reduce((sum, fee) => sum + fee.totalCollected, 0),
    totalPending: feeStructures.reduce((sum, fee) => sum + fee.pendingAmount, 0),
    collectionRate: 94.5,
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Active": return "bg-green-100 text-green-700";
      case "Inactive": return "bg-red-100 text-red-700";
      case "Draft": return "bg-yellow-100 text-yellow-700";
      case "Completed": return "bg-blue-100 text-blue-700";
      case "Pending Verification": return "bg-orange-100 text-orange-700";
      default: return "bg-gray-100 text-gray-700";
    }
  };

  const getPaymentStatusIcon = (status: string) => {
    switch (status) {
      case "Completed": return <CheckCircle className="h-4 w-4" />;
      case "Pending Verification": return <Clock className="h-4 w-4" />;
      case "Failed": return <XCircle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const filteredFeeStructures = feeStructures.filter(fee => {
    const matchesSearch = fee.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         fee.academicYear.toLowerCase().includes(searchTerm.toLowerCase());

    if (selectedFilter === "all") return matchesSearch;
    return matchesSearch && fee.status.toLowerCase() === selectedFilter;
  });

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Fee Management</h1>
            <p className="text-gray-600">Manage fee structures and track collections</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Fee Structure
            </Button>
          </div>
        </div>

        {/* Fee Statistics */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <FileText className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {feeStats.totalStructures}
                  </div>
                  <p className="text-sm text-gray-600">Fee Structures</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {feeStats.totalStudents}
                  </div>
                  <p className="text-sm text-gray-600">Total Students</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <IndianRupee className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    ₹{(feeStats.totalCollected / 100000).toFixed(1)}L
                  </div>
                  <p className="text-sm text-gray-600">Total Collected</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <AlertTriangle className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    ₹{(feeStats.totalPending / 1000).toFixed(0)}K
                  </div>
                  <p className="text-sm text-gray-600">Pending Amount</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Collection Rate */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Collection Rate</h3>
              <div className="text-sm text-gray-500">
                Target: 95%
              </div>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-4 mb-2">
              <div
                className="bg-green-500 h-4 rounded-full"
                style={{ width: `${feeStats.collectionRate}%` }}
              />
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Current: {feeStats.collectionRate}%</span>
              <span className="text-green-600 font-medium">
                {feeStats.collectionRate >= 95 ? '✓ Target Achieved' : `${(95 - feeStats.collectionRate).toFixed(1)}% to target`}
              </span>
            </div>
          </CardContent>
        </Card>

        {/* Filters and Search */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search fee structures..."
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>
              <div className="flex gap-2">
                {[
                  { key: "all", label: "All" },
                  { key: "active", label: "Active" },
                  { key: "inactive", label: "Inactive" },
                  { key: "draft", label: "Draft" },
                ].map((filter) => (
                  <Button
                    key={filter.key}
                    variant={selectedFilter === filter.key ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedFilter(filter.key)}
                  >
                    {filter.label}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Main Content Grid */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Fee Structures */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="h-5 w-5 mr-2" />
                Fee Structures ({filteredFeeStructures.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredFeeStructures.map((fee) => (
                  <div key={fee.id} className="border rounded-lg p-6 hover:shadow-md transition-shadow">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <h3 className="text-lg font-semibold text-gray-900 mb-1">{fee.name}</h3>
                        <p className="text-gray-600 mb-2">Academic Year: {fee.academicYear}</p>
                        <div className="text-2xl font-bold text-blue-600 mb-3">
                          ₹{fee.totalAmount.toLocaleString()}
                        </div>
                      </div>
                      <Badge className={getStatusColor(fee.status)}>
                        {fee.status}
                      </Badge>
                    </div>

                    <div className="grid gap-4 md:grid-cols-3 mb-4">
                      <div>
                        <div className="text-sm text-gray-600">Students Enrolled</div>
                        <div className="font-medium text-gray-900">{fee.studentsEnrolled}</div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-600">Total Collected</div>
                        <div className="font-medium text-green-600">
                          ₹{(fee.totalCollected / 100000).toFixed(1)}L
                        </div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-600">Pending Amount</div>
                        <div className="font-medium text-orange-600">
                          ₹{(fee.pendingAmount / 1000).toFixed(0)}K
                        </div>
                      </div>
                    </div>

                    <div className="mb-4">
                      <h4 className="text-sm font-medium text-gray-900 mb-2">Fee Components:</h4>
                      <div className="grid gap-2 md:grid-cols-2">
                        {fee.components.slice(0, 4).map((component, index) => (
                          <div key={index} className="flex items-center justify-between text-sm">
                            <span className={component.mandatory ? "text-gray-900" : "text-gray-500"}>
                              {component.name} {!component.mandatory && "(Optional)"}
                            </span>
                            <span className="font-medium">₹{component.amount.toLocaleString()}</span>
                          </div>
                        ))}
                        {fee.components.length > 4 && (
                          <div className="text-sm text-gray-500">
                            +{fee.components.length - 4} more components
                          </div>
                        )}
                      </div>
                    </div>

                    <div className="flex items-center justify-between pt-4 border-t">
                      <div className="text-sm text-gray-500">
                        Due Date: {fee.dueDate}
                      </div>
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                        <Button variant="outline" size="sm">
                          <Edit className="h-4 w-4 mr-1" />
                          Edit
                        </Button>
                        <Button size="sm">
                          <Receipt className="h-4 w-4 mr-1" />
                          Collect
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Recent Payments */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <CreditCard className="h-5 w-5 mr-2" />
                Recent Payments
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentPayments.map((payment) => (
                  <div key={payment.id} className="p-4 border rounded-lg">
                    <div className="flex items-start justify-between mb-3">
                      <div>
                        <div className="font-medium text-gray-900">{payment.studentName}</div>
                        <div className="text-sm text-gray-500">{payment.studentId}</div>
                      </div>
                      <Badge className={getStatusColor(payment.status)}>
                        {getPaymentStatusIcon(payment.status)}
                        <span className="ml-1">{payment.status}</span>
                      </Badge>
                    </div>

                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-gray-600">Amount:</span>
                        <span className="font-medium">₹{payment.amount.toLocaleString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Method:</span>
                        <span>{payment.paymentMethod}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Date:</span>
                        <span>{payment.paymentDate}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-gray-600">Receipt:</span>
                        <span className="text-blue-600">{payment.receiptNo}</span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  );
}
