import { Hono } from "hono";
import { zValidator } from "@hono/zod-validator";
import { z } from "zod";
import { db } from "@/lib/db";
import { userActivityLogs, users } from "@/lib/db/schema";
import { eq, desc, and, gte, lte, like, or, sql } from "drizzle-orm";

const app = new Hono()

  // Get audit logs with filtering and pagination
  .get("/", async (c) => {
    try {
      const page = parseInt(c.req.query("page") || "1");
      const limit = parseInt(c.req.query("limit") || "20");
      const offset = (page - 1) * limit;
      
      // Filters
      const action = c.req.query("action");
      const userId = c.req.query("userId");
      const startDate = c.req.query("startDate");
      const endDate = c.req.query("endDate");
      const search = c.req.query("search");

      // Build conditions
      const conditions = [];

      if (action) {
        conditions.push(eq(userActivityLogs.action, action));
      }

      if (userId) {
        conditions.push(eq(userActivityLogs.userId, userId));
      }

      if (startDate) {
        conditions.push(gte(userActivityLogs.timestamp, new Date(startDate)));
      }

      if (endDate) {
        conditions.push(lte(userActivityLogs.timestamp, new Date(endDate)));
      }

      if (search) {
        conditions.push(
          or(
            like(userActivityLogs.action, `%${search}%`),
            like(userActivityLogs.details, `%${search}%`)
          )
        );
      }

      // Build the query with conditions
      const logs = await db
        .select({
          id: userActivityLogs.id,
          action: userActivityLogs.action,
          details: userActivityLogs.details,
          ipAddress: userActivityLogs.ipAddress,
          userAgent: userActivityLogs.userAgent,
          timestamp: userActivityLogs.timestamp,
          userId: userActivityLogs.userId,
          targetUserId: userActivityLogs.targetUserId,
          user: {
            id: users.id,
            firstName: users.firstName,
            lastName: users.lastName,
            email: users.email,
            role: users.role,
          },
        })
        .from(userActivityLogs)
        .leftJoin(users, eq(userActivityLogs.userId, users.id))
        .where(conditions.length > 0 ? and(...conditions) : undefined)
        .orderBy(desc(userActivityLogs.timestamp))
        .limit(limit)
        .offset(offset);

      // Get total count with same filters
      const countResult = await db
        .select({ count: sql`count(*)` })
        .from(userActivityLogs)
        .leftJoin(users, eq(userActivityLogs.userId, users.id))
        .where(conditions.length > 0 ? and(...conditions) : undefined);

      const count = Number(countResult[0]?.count || 0);

      return c.json({
        data: logs,
        pagination: {
          page,
          limit,
          total: count,
          pages: Math.ceil(count / limit),
        },
      });
    } catch (error) {
      console.error("Error fetching audit logs:", error);
      return c.json({ error: "Failed to fetch audit logs" }, 500);
    }
  })

  // Create audit log entry
  .post(
    "/",
    zValidator("json", z.object({
      userId: z.string(),
      action: z.string(),
      targetUserId: z.string().optional(),
      details: z.string().optional(),
      ipAddress: z.string().optional(),
      userAgent: z.string().optional(),
    })),
    async (c) => {
      try {
        const values = c.req.valid("json");

        const [log] = await db
          .insert(userActivityLogs)
          .values(values)
          .returning();

        return c.json({ data: log }, 201);
      } catch (error) {
        console.error("Error creating audit log:", error);
        return c.json({ error: "Failed to create audit log" }, 500);
      }
    }
  )

  // Get audit log statistics
  .get("/stats", async (c) => {
    try {
      const timeframe = c.req.query("timeframe") || "7d"; // 1d, 7d, 30d, 90d
      
      let startDate = new Date();
      switch (timeframe) {
        case "1d":
          startDate.setDate(startDate.getDate() - 1);
          break;
        case "7d":
          startDate.setDate(startDate.getDate() - 7);
          break;
        case "30d":
          startDate.setDate(startDate.getDate() - 30);
          break;
        case "90d":
          startDate.setDate(startDate.getDate() - 90);
          break;
      }

      // Get activity counts by action
      const actionStats = await db
        .select({
          action: userActivityLogs.action,
          count: userActivityLogs.id,
        })
        .from(userActivityLogs)
        .where(gte(userActivityLogs.timestamp, startDate));

      const actionCounts = actionStats.reduce((acc, stat) => {
        acc[stat.action] = (acc[stat.action] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Get daily activity for the timeframe
      const dailyActivity = await db
        .select({
          date: userActivityLogs.timestamp,
          count: userActivityLogs.id,
        })
        .from(userActivityLogs)
        .where(gte(userActivityLogs.timestamp, startDate))
        .orderBy(desc(userActivityLogs.timestamp));

      // Group by date
      const dailyCounts = dailyActivity.reduce((acc, activity) => {
        const date = activity.date.toISOString().split('T')[0];
        acc[date] = (acc[date] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Get top users by activity
      const userStats = await db
        .select({
          userId: userActivityLogs.userId,
          count: userActivityLogs.id,
          user: {
            firstName: users.firstName,
            lastName: users.lastName,
            email: users.email,
            role: users.role,
          },
        })
        .from(userActivityLogs)
        .leftJoin(users, eq(userActivityLogs.userId, users.id))
        .where(gte(userActivityLogs.timestamp, startDate));

      const userCounts = userStats.reduce((acc, stat) => {
        if (stat.userId) {
          if (!acc[stat.userId]) {
            acc[stat.userId] = {
              count: 0,
              user: stat.user,
            };
          }
          acc[stat.userId].count += 1;
        }
        return acc;
      }, {} as Record<string, { count: number; user: any }>);

      // Sort top users by activity count
      const topUsers = Object.entries(userCounts)
        .sort(([, a], [, b]) => b.count - a.count)
        .slice(0, 10)
        .map(([userId, data]) => ({
          userId,
          ...data,
        }));

      return c.json({
        data: {
          timeframe,
          totalLogs: actionStats.length,
          actionCounts,
          dailyCounts,
          topUsers,
        },
      });
    } catch (error) {
      console.error("Error fetching audit log stats:", error);
      return c.json({ error: "Failed to fetch audit log statistics" }, 500);
    }
  })

  // Get unique actions for filtering
  .get("/actions", async (c) => {
    try {
      const actions = await db
        .selectDistinct({ action: userActivityLogs.action })
        .from(userActivityLogs)
        .orderBy(userActivityLogs.action);

      return c.json({
        data: actions.map(a => a.action),
      });
    } catch (error) {
      console.error("Error fetching actions:", error);
      return c.json({ error: "Failed to fetch actions" }, 500);
    }
  })

  // Export audit logs
  .get("/export", async (c) => {
    try {
      const format = c.req.query("format") || "csv"; // csv, json
      const startDate = c.req.query("startDate");
      const endDate = c.req.query("endDate");

      const conditions = [];
      if (startDate) {
        conditions.push(gte(userActivityLogs.timestamp, new Date(startDate)));
      }
      if (endDate) {
        conditions.push(lte(userActivityLogs.timestamp, new Date(endDate)));
      }

      const logs = await db
        .select({
          timestamp: userActivityLogs.timestamp,
          action: userActivityLogs.action,
          details: userActivityLogs.details,
          ipAddress: userActivityLogs.ipAddress,
          userEmail: users.email,
          userName: users.firstName,
          userRole: users.role,
        })
        .from(userActivityLogs)
        .leftJoin(users, eq(userActivityLogs.userId, users.id))
        .where(conditions.length > 0 ? and(...conditions) : undefined)
        .orderBy(desc(userActivityLogs.timestamp));

      if (format === "json") {
        return c.json({ data: logs });
      } else {
        // CSV format
        const headers = ["Timestamp", "Action", "User Email", "User Name", "Role", "IP Address", "Details"];
        const csvContent = [
          headers.join(","),
          ...logs.map(log => [
            log.timestamp?.toISOString() || "",
            log.action || "",
            log.userEmail || "",
            log.userName || "",
            log.userRole || "",
            log.ipAddress || "",
            (log.details || "").replace(/"/g, '""'), // Escape quotes
          ].map(field => `"${field}"`).join(","))
        ].join("\n");

        return new Response(csvContent, {
          headers: {
            "Content-Type": "text/csv",
            "Content-Disposition": `attachment; filename="audit-logs-${new Date().toISOString().split('T')[0]}.csv"`,
          },
        });
      }
    } catch (error) {
      console.error("Error exporting audit logs:", error);
      return c.json({ error: "Failed to export audit logs" }, 500);
    }
  });

export default app;
