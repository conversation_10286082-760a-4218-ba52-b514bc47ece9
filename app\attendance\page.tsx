"use client";

import { useState } from "react";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, Card<PERSON>ontent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Search, Calendar, CheckCircle, XCircle, Clock, AlertCircle } from "lucide-react";

// Mock attendance data
const mockAttendance = [
  {
    id: "1",
    studentId: "1",
    studentName: "<PERSON>",
    studentGrade: "10A",
    classId: "1",
    className: "Algebra I",
    date: "2024-01-15",
    status: "present",
    checkInTime: "09:00",
    notes: "",
  },
  {
    id: "2",
    studentId: "2",
    studentName: "<PERSON> Smith",
    studentGrade: "10A",
    classId: "1",
    className: "Algebra I",
    date: "2024-01-15",
    status: "absent",
    checkInTime: null,
    notes: "Sick leave",
  },
  {
    id: "3",
    studentId: "3",
    studentName: "<PERSON>",
    studentGrade: "11B",
    classId: "2",
    className: "Physics I",
    date: "2024-01-15",
    status: "late",
    checkInTime: "10:15",
    notes: "Traffic delay",
  },
  {
    id: "4",
    studentId: "1",
    studentName: "John Doe",
    studentGrade: "10A",
    classId: "1",
    className: "Algebra I",
    date: "2024-01-14",
    status: "present",
    checkInTime: "08:58",
    notes: "",
  },
];

const statusIcons = {
  present: CheckCircle,
  absent: XCircle,
  late: Clock,
  excused: AlertCircle,
};

const statusColors = {
  present: "text-green-600 bg-green-100",
  absent: "text-red-600 bg-red-100",
  late: "text-yellow-600 bg-yellow-100",
  excused: "text-blue-600 bg-blue-100",
};

export default function AttendancePage() {
  const [search, setSearch] = useState("");
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [filters, setFilters] = useState({
    class: "",
    status: "",
  });

  const filteredAttendance = mockAttendance.filter((record) => {
    const matchesSearch = 
      record.studentName.toLowerCase().includes(search.toLowerCase()) ||
      record.className.toLowerCase().includes(search.toLowerCase());
    
    const matchesDate = record.date === selectedDate;
    const matchesClass = !filters.class || record.className === filters.class;
    const matchesStatus = !filters.status || record.status === filters.status;

    return matchesSearch && matchesDate && matchesClass && matchesStatus;
  });

  const attendanceStats = {
    total: filteredAttendance.length,
    present: filteredAttendance.filter(r => r.status === "present").length,
    absent: filteredAttendance.filter(r => r.status === "absent").length,
    late: filteredAttendance.filter(r => r.status === "late").length,
    excused: filteredAttendance.filter(r => r.status === "excused").length,
  };

  const attendanceRate = attendanceStats.total > 0 
    ? Math.round((attendanceStats.present / attendanceStats.total) * 100)
    : 0;

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Attendance</h1>
            <p className="text-gray-600">Track and manage student attendance</p>
          </div>
          <Button className="sm:w-auto">
            <Calendar className="h-4 w-4 mr-2" />
            Take Attendance
          </Button>
        </div>

        {/* Stats Cards */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {attendanceStats.present}
                  </div>
                  <p className="text-sm text-gray-600">Present</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <XCircle className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-red-600">
                    {attendanceStats.absent}
                  </div>
                  <p className="text-sm text-gray-600">Absent</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-yellow-600">
                    {attendanceStats.late}
                  </div>
                  <p className="text-sm text-gray-600">Late</p>
                </div>
              </div>
            </CardContent>
          </Card>
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <div className="h-8 w-8 bg-blue-100 rounded-full flex items-center justify-center">
                  <span className="text-blue-600 font-bold text-lg">{attendanceRate}%</span>
                </div>
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {attendanceRate}%
                  </div>
                  <p className="text-sm text-gray-600">Attendance Rate</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                <Input
                  placeholder="Search students or classes..."
                  value={search}
                  onChange={(e) => setSearch(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="flex gap-2">
                <input
                  type="date"
                  value={selectedDate}
                  onChange={(e) => setSelectedDate(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                />
                <select
                  value={filters.class}
                  onChange={(e) => setFilters({ ...filters, class: e.target.value })}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                >
                  <option value="">All Classes</option>
                  <option value="Algebra I">Algebra I</option>
                  <option value="Physics I">Physics I</option>
                  <option value="English Literature">English Literature</option>
                </select>
                <select
                  value={filters.status}
                  onChange={(e) => setFilters({ ...filters, status: e.target.value })}
                  className="px-3 py-2 border border-gray-300 rounded-md text-sm"
                >
                  <option value="">All Status</option>
                  <option value="present">Present</option>
                  <option value="absent">Absent</option>
                  <option value="late">Late</option>
                  <option value="excused">Excused</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Attendance Records */}
        <Card>
          <CardHeader>
            <CardTitle>
              Attendance Records
              <span className="text-sm font-normal text-gray-500 ml-2">
                ({filteredAttendance.length} records)
              </span>
            </CardTitle>
          </CardHeader>
          <CardContent>
            {filteredAttendance.length === 0 ? (
              <div className="text-center py-8">
                <p className="text-gray-500">No attendance records found for the selected date</p>
              </div>
            ) : (
              <div className="space-y-4">
                {filteredAttendance.map((record) => {
                  const StatusIcon = statusIcons[record.status as keyof typeof statusIcons];
                  return (
                    <div
                      key={record.id}
                      className="flex items-center justify-between p-4 border rounded-lg hover:bg-gray-50 transition-colors"
                    >
                      <div className="flex items-center space-x-4">
                        <div className={`p-2 rounded-full ${statusColors[record.status as keyof typeof statusColors]}`}>
                          <StatusIcon className="h-4 w-4" />
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">
                            {record.studentName}
                          </div>
                          <div className="text-sm text-gray-500">
                            {record.studentGrade} • {record.className}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm font-medium capitalize">
                          {record.status}
                        </div>
                        {record.checkInTime && (
                          <div className="text-xs text-gray-500">
                            {record.checkInTime}
                          </div>
                        )}
                        {record.notes && (
                          <div className="text-xs text-gray-500 mt-1">
                            {record.notes}
                          </div>
                        )}
                      </div>
                    </div>
                  );
                })}
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
