import { Hono } from "hono";
import { z<PERSON>alida<PERSON> } from "@hono/zod-validator";
import { z } from "zod";
import { db } from "@/lib/db";
import { systemBackups, users } from "@/lib/db/schema";
import { eq, desc, sql } from "drizzle-orm";

const app = new Hono()

  // Get all backups
  .get("/", async (c) => {
    try {
      const page = parseInt(c.req.query("page") || "1");
      const limit = parseInt(c.req.query("limit") || "10");
      const offset = (page - 1) * limit;

      const backups = await db
        .select({
          id: systemBackups.id,
          name: systemBackups.name,
          description: systemBackups.description,
          backupType: systemBackups.backupType,
          status: systemBackups.status,
          filePath: systemBackups.filePath,
          fileSize: systemBackups.fileSize,
          startedAt: systemBackups.startedAt,
          completedAt: systemBackups.completedAt,
          error: systemBackups.error,
          createdAt: systemBackups.createdAt,
          createdBy: {
            id: users.id,
            firstName: users.firstName,
            lastName: users.lastName,
            email: users.email,
          },
        })
        .from(systemBackups)
        .leftJoin(users, eq(systemBackups.createdBy, users.id))
        .orderBy(desc(systemBackups.createdAt))
        .limit(limit)
        .offset(offset);

      // Get total count
      const [{ count }] = await db
        .select({ count: sql`count(*)` })
        .from(systemBackups);

      const totalCount = Number(count);

      return c.json({
        data: backups,
        pagination: {
          page,
          limit,
          total: totalCount,
          pages: Math.ceil(totalCount / limit),
        },
      });
    } catch (error) {
      console.error("Error fetching backups:", error);
      return c.json({ error: "Failed to fetch backups" }, 500);
    }
  })

  // Create new backup
  .post(
    "/",
    zValidator("json", z.object({
      name: z.string().min(1, "Backup name is required"),
      description: z.string().optional(),
      backupType: z.enum(["full", "incremental", "manual"]),
      createdBy: z.string(),
    })),
    async (c) => {
      try {
        const values = c.req.valid("json");

        const [backup] = await db
          .insert(systemBackups)
          .values({
            ...values,
            status: "pending",
            startedAt: new Date(),
          })
          .returning();

        // In a real implementation, you would trigger the actual backup process here
        // For now, we'll simulate it
        setTimeout(async () => {
          try {
            // Simulate backup process
            await new Promise(resolve => setTimeout(resolve, 5000));
            
            // Update backup status to completed
            await db
              .update(systemBackups)
              .set({
                status: "completed",
                completedAt: new Date(),
                filePath: `/backups/${backup.id}.sql`,
                fileSize: Math.floor(Math.random() * 1000000) + 500000, // Random size between 500KB-1.5MB
              })
              .where(eq(systemBackups.id, backup.id));
          } catch (error) {
            // Update backup status to failed
            await db
              .update(systemBackups)
              .set({
                status: "failed",
                completedAt: new Date(),
                error: "Backup process failed",
              })
              .where(eq(systemBackups.id, backup.id));
          }
        }, 1000);

        return c.json({ data: backup }, 201);
      } catch (error) {
        console.error("Error creating backup:", error);
        return c.json({ error: "Failed to create backup" }, 500);
      }
    }
  )

  // Get backup by ID
  .get("/:id", async (c) => {
    try {
      const id = c.req.param("id");

      const [backup] = await db
        .select({
          id: systemBackups.id,
          name: systemBackups.name,
          description: systemBackups.description,
          backupType: systemBackups.backupType,
          status: systemBackups.status,
          filePath: systemBackups.filePath,
          fileSize: systemBackups.fileSize,
          startedAt: systemBackups.startedAt,
          completedAt: systemBackups.completedAt,
          error: systemBackups.error,
          createdAt: systemBackups.createdAt,
          createdBy: {
            id: users.id,
            firstName: users.firstName,
            lastName: users.lastName,
            email: users.email,
          },
        })
        .from(systemBackups)
        .leftJoin(users, eq(systemBackups.createdBy, users.id))
        .where(eq(systemBackups.id, id));

      if (!backup) {
        return c.json({ error: "Backup not found" }, 404);
      }

      return c.json({ data: backup });
    } catch (error) {
      console.error("Error fetching backup:", error);
      return c.json({ error: "Failed to fetch backup" }, 500);
    }
  })

  // Delete backup
  .delete("/:id", async (c) => {
    try {
      const id = c.req.param("id");

      const [deletedBackup] = await db
        .delete(systemBackups)
        .where(eq(systemBackups.id, id))
        .returning();

      if (!deletedBackup) {
        return c.json({ error: "Backup not found" }, 404);
      }

      // In a real implementation, you would also delete the actual backup file
      // fs.unlinkSync(deletedBackup.filePath);

      return c.json({ message: "Backup deleted successfully" });
    } catch (error) {
      console.error("Error deleting backup:", error);
      return c.json({ error: "Failed to delete backup" }, 500);
    }
  })

  // Download backup
  .get("/:id/download", async (c) => {
    try {
      const id = c.req.param("id");

      const [backup] = await db
        .select()
        .from(systemBackups)
        .where(eq(systemBackups.id, id));

      if (!backup) {
        return c.json({ error: "Backup not found" }, 404);
      }

      if (backup.status !== "completed") {
        return c.json({ error: "Backup is not ready for download" }, 400);
      }

      // In a real implementation, you would stream the actual backup file
      // For now, return the file path
      return c.json({
        data: {
          downloadUrl: backup.filePath,
          fileName: `${backup.name}.sql`,
          fileSize: backup.fileSize,
        },
      });
    } catch (error) {
      console.error("Error downloading backup:", error);
      return c.json({ error: "Failed to download backup" }, 500);
    }
  })

  // Get backup statistics
  .get("/stats", async (c) => {
    try {
      const stats = await db
        .select({
          status: systemBackups.status,
          count: systemBackups.id,
        })
        .from(systemBackups);

      const statusCounts = stats.reduce((acc, stat) => {
        acc[stat.status] = (acc[stat.status] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

      // Get total backup size
      const [sizeResult] = await db
        .select({
          totalSize: systemBackups.fileSize,
        })
        .from(systemBackups)
        .where(eq(systemBackups.status, "completed"));

      return c.json({
        data: {
          totalBackups: stats.length,
          statusCounts,
          totalSize: sizeResult?.totalSize || 0,
        },
      });
    } catch (error) {
      console.error("Error fetching backup stats:", error);
      return c.json({ error: "Failed to fetch backup statistics" }, 500);
    }
  });

export default app;
