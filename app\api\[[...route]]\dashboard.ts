import { Hono } from "hono";
import { mockStudents, mockTeachers, mockClasses, mockAttendance, mockGrades } from "@/lib/mock-db";

const app = new Hono()
  .get("/stats", async (c) => {
    // Calculate basic statistics
    const totalStudents = mockStudents.length;
    const activeStudents = mockStudents.filter(s => s.status === "active").length;
    const totalTeachers = mockTeachers.length;
    const activeTeachers = mockTeachers.filter(t => t.status === "active").length;
    const totalClasses = mockClasses.length;
    const activeClasses = mockClasses.filter(c => c.status === "active").length;

    // Calculate attendance rate for today (mock data)
    const todayAttendance = mockAttendance.filter(a => a.date === new Date().toISOString().split('T')[0]);
    const presentToday = todayAttendance.filter(a => a.status === "present").length;
    const attendanceRate = todayAttendance.length > 0 ? (presentToday / todayAttendance.length) * 100 : 0;

    // Calculate average grade
    const totalGrades = mockGrades.reduce((sum, grade) => sum + grade.grade, 0);
    const averageGrade = mockGrades.length > 0 ? totalGrades / mockGrades.length : 0;

    // Grade distribution
    const gradeDistribution = {
      A: mockGrades.filter(g => g.grade >= 90).length,
      B: mockGrades.filter(g => g.grade >= 80 && g.grade < 90).length,
      C: mockGrades.filter(g => g.grade >= 70 && g.grade < 80).length,
      D: mockGrades.filter(g => g.grade >= 60 && g.grade < 70).length,
      F: mockGrades.filter(g => g.grade < 60).length,
    };

    // Recent activities (mock data)
    const recentActivities = [
      {
        id: "1",
        type: "student_enrolled",
        message: "New student John Doe enrolled in Grade 10",
        timestamp: new Date().toISOString(),
      },
      {
        id: "2",
        type: "assignment_created",
        message: "New assignment created for Algebra I",
        timestamp: new Date(Date.now() - 3600000).toISOString(),
      },
      {
        id: "3",
        type: "grade_updated",
        message: "Grades updated for Physics I midterm",
        timestamp: new Date(Date.now() - 7200000).toISOString(),
      },
    ];

    const stats = {
      students: {
        total: totalStudents,
        active: activeStudents,
        inactive: totalStudents - activeStudents,
      },
      teachers: {
        total: totalTeachers,
        active: activeTeachers,
        inactive: totalTeachers - activeTeachers,
      },
      classes: {
        total: totalClasses,
        active: activeClasses,
        inactive: totalClasses - activeClasses,
      },
      attendance: {
        rate: Math.round(attendanceRate * 100) / 100,
        present: presentToday,
        total: todayAttendance.length,
      },
      grades: {
        average: Math.round(averageGrade * 100) / 100,
        distribution: gradeDistribution,
      },
      recentActivities,
    };

    return c.json({ data: stats });
  })
  .get("/analytics", async (c) => {
    // Mock analytics data for charts
    const monthlyEnrollment = [
      { month: "Jan", students: 120 },
      { month: "Feb", students: 125 },
      { month: "Mar", students: 130 },
      { month: "Apr", students: 128 },
      { month: "May", students: 135 },
      { month: "Jun", students: 140 },
    ];

    const attendanceTrend = [
      { date: "2024-01-15", rate: 95 },
      { date: "2024-01-16", rate: 92 },
      { date: "2024-01-17", rate: 97 },
      { date: "2024-01-18", rate: 89 },
      { date: "2024-01-19", rate: 94 },
      { date: "2024-01-20", rate: 96 },
      { date: "2024-01-21", rate: 93 },
    ];

    const gradesBySubject = [
      { subject: "Mathematics", average: 85.5 },
      { subject: "Science", average: 82.3 },
      { subject: "English", average: 88.7 },
      { subject: "History", average: 79.2 },
      { subject: "Art", average: 91.4 },
    ];

    const analytics = {
      monthlyEnrollment,
      attendanceTrend,
      gradesBySubject,
    };

    return c.json({ data: analytics });
  });

export default app;
