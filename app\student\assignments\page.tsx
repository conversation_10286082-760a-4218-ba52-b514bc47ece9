"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  FileText,
  Calendar,
  Clock,
  Upload,
  Download,
  CheckCircle,
  AlertTriangle,
  XCircle,
  BookOpen,
  User,
  Filter,
  Search,
  Eye,
  Edit,
} from "lucide-react";

export default function StudentAssignments() {
  const [user, setUser] = useState<any>(null);
  const [activeTab, setActiveTab] = useState("pending");
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "student") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock assignments data
  const assignments = [
    {
      id: "ASG001",
      title: "Calculus Problem Set 3",
      subject: "Mathematics",
      teacher: "Dr. Emily Wilson",
      description: "Solve problems 1-15 from Chapter 8: Integration by Parts",
      assignedDate: "2024-01-10",
      dueDate: "2024-01-25",
      status: "pending",
      priority: "high",
      maxMarks: 100,
      submissionType: "PDF Upload",
      instructions: "Show all working steps clearly. Submit as a single PDF file.",
      attachments: ["calculus_ch8_problems.pdf"],
    },
    {
      id: "ASG002",
      title: "Physics Lab Report - Pendulum Experiment",
      subject: "Physics",
      teacher: "Mr. David Brown",
      description: "Write a comprehensive lab report on the simple pendulum experiment",
      assignedDate: "2024-01-12",
      dueDate: "2024-01-28",
      status: "pending",
      priority: "medium",
      maxMarks: 50,
      submissionType: "Document Upload",
      instructions: "Include hypothesis, methodology, observations, calculations, and conclusion.",
      attachments: ["lab_report_template.docx", "experiment_data.xlsx"],
    },
    {
      id: "ASG003",
      title: "Essay: Environmental Chemistry",
      subject: "Chemistry",
      teacher: "Dr. Sarah Johnson",
      description: "Write a 1500-word essay on the impact of industrial pollution on water bodies",
      assignedDate: "2024-01-08",
      dueDate: "2024-01-22",
      status: "submitted",
      priority: "medium",
      maxMarks: 75,
      submissionType: "Text Submission",
      submittedDate: "2024-01-20",
      grade: 68,
      feedback: "Good analysis but needs more scientific references. Well-structured essay overall.",
      instructions: "Use APA citation format. Minimum 10 academic references required.",
      attachments: ["essay_guidelines.pdf"],
    },
    {
      id: "ASG004",
      title: "Programming Project: Student Management System",
      subject: "Computer Science",
      teacher: "Prof. Michael Chen",
      description: "Develop a basic student management system using Python",
      assignedDate: "2024-01-05",
      dueDate: "2024-01-30",
      status: "in_progress",
      priority: "high",
      maxMarks: 100,
      submissionType: "Code Repository",
      instructions: "Submit GitHub repository link with proper documentation and README file.",
      attachments: ["project_requirements.pdf", "starter_code.zip"],
    },
    {
      id: "ASG005",
      title: "Literature Analysis: Shakespeare's Hamlet",
      subject: "English",
      teacher: "Ms. Lisa Anderson",
      description: "Analyze the theme of revenge in Hamlet with textual evidence",
      assignedDate: "2024-01-15",
      dueDate: "2024-01-18",
      status: "overdue",
      priority: "high",
      maxMarks: 60,
      submissionType: "Essay Submission",
      instructions: "2000 words minimum. Include direct quotes with proper citations.",
      attachments: ["hamlet_text.pdf", "analysis_rubric.pdf"],
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "pending": return "bg-yellow-100 text-yellow-700";
      case "submitted": return "bg-green-100 text-green-700";
      case "in_progress": return "bg-blue-100 text-blue-700";
      case "overdue": return "bg-red-100 text-red-700";
      case "graded": return "bg-purple-100 text-purple-700";
      default: return "bg-gray-100 text-gray-700";
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "high": return "text-red-600";
      case "medium": return "text-yellow-600";
      case "low": return "text-green-600";
      default: return "text-gray-600";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "pending": return <Clock className="h-4 w-4" />;
      case "submitted": return <CheckCircle className="h-4 w-4" />;
      case "in_progress": return <Edit className="h-4 w-4" />;
      case "overdue": return <XCircle className="h-4 w-4" />;
      case "graded": return <CheckCircle className="h-4 w-4" />;
      default: return <FileText className="h-4 w-4" />;
    }
  };

  const filteredAssignments = assignments.filter(assignment => {
    if (activeTab === "pending") return assignment.status === "pending" || assignment.status === "in_progress";
    if (activeTab === "submitted") return assignment.status === "submitted" || assignment.status === "graded";
    if (activeTab === "overdue") return assignment.status === "overdue";
    return true;
  });

  const assignmentStats = {
    total: assignments.length,
    pending: assignments.filter(a => a.status === "pending" || a.status === "in_progress").length,
    submitted: assignments.filter(a => a.status === "submitted" || a.status === "graded").length,
    overdue: assignments.filter(a => a.status === "overdue").length,
  };

  const getDaysUntilDue = (dueDate: string) => {
    const due = new Date(dueDate);
    const today = new Date();
    const diffTime = due.getTime() - today.getTime();
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
    return diffDays;
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">My Assignments</h1>
            <p className="text-gray-600">Manage and track your assignments</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Search className="h-4 w-4 mr-2" />
              Search
            </Button>
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
          </div>
        </div>

        {/* Assignment Statistics */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <FileText className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {assignmentStats.total}
                  </div>
                  <p className="text-sm text-gray-600">Total Assignments</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-yellow-600">
                    {assignmentStats.pending}
                  </div>
                  <p className="text-sm text-gray-600">Pending</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {assignmentStats.submitted}
                  </div>
                  <p className="text-sm text-gray-600">Submitted</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <AlertTriangle className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-red-600">
                    {assignmentStats.overdue}
                  </div>
                  <p className="text-sm text-gray-600">Overdue</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Assignment Tabs */}
        <Card>
          <CardHeader>
            <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
              {[
                { key: "pending", label: "Pending", count: assignmentStats.pending },
                { key: "submitted", label: "Submitted", count: assignmentStats.submitted },
                { key: "overdue", label: "Overdue", count: assignmentStats.overdue },
                { key: "all", label: "All", count: assignmentStats.total },
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setActiveTab(tab.key)}
                  className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                    activeTab === tab.key
                      ? "bg-white text-blue-600 shadow-sm"
                      : "text-gray-600 hover:text-gray-900"
                  }`}
                >
                  {tab.label} ({tab.count})
                </button>
              ))}
            </div>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {filteredAssignments.map((assignment) => (
                <div key={assignment.id} className="border rounded-lg p-6 hover:shadow-md transition-shadow">
                  <div className="flex items-start justify-between mb-4">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3 mb-2">
                        <h3 className="text-lg font-semibold text-gray-900">{assignment.title}</h3>
                        <Badge className={getStatusColor(assignment.status)}>
                          {getStatusIcon(assignment.status)}
                          <span className="ml-1 capitalize">{assignment.status.replace('_', ' ')}</span>
                        </Badge>
                        <span className={`text-sm font-medium ${getPriorityColor(assignment.priority)}`}>
                          {assignment.priority.toUpperCase()} PRIORITY
                        </span>
                      </div>
                      <div className="flex items-center space-x-4 text-sm text-gray-500 mb-3">
                        <div className="flex items-center">
                          <BookOpen className="h-4 w-4 mr-1" />
                          {assignment.subject}
                        </div>
                        <div className="flex items-center">
                          <User className="h-4 w-4 mr-1" />
                          {assignment.teacher}
                        </div>
                        <div className="flex items-center">
                          <Calendar className="h-4 w-4 mr-1" />
                          Due: {assignment.dueDate}
                        </div>
                      </div>
                      <p className="text-gray-700 mb-3">{assignment.description}</p>
                      
                      {assignment.status === "submitted" && assignment.grade && (
                        <div className="bg-green-50 border border-green-200 rounded-lg p-3 mb-3">
                          <div className="flex items-center justify-between mb-2">
                            <span className="font-medium text-green-800">Grade: {assignment.grade}/{assignment.maxMarks}</span>
                            <span className="text-sm text-green-600">
                              {((assignment.grade / assignment.maxMarks) * 100).toFixed(1)}%
                            </span>
                          </div>
                          {assignment.feedback && (
                            <p className="text-sm text-green-700">{assignment.feedback}</p>
                          )}
                        </div>
                      )}
                    </div>
                    
                    <div className="text-right ml-4">
                      <div className="text-sm text-gray-500 mb-2">Max Marks: {assignment.maxMarks}</div>
                      {assignment.status !== "submitted" && assignment.status !== "graded" && (
                        <div className={`text-sm font-medium ${
                          getDaysUntilDue(assignment.dueDate) < 0 ? "text-red-600" :
                          getDaysUntilDue(assignment.dueDate) <= 3 ? "text-orange-600" : "text-green-600"
                        }`}>
                          {getDaysUntilDue(assignment.dueDate) < 0 
                            ? `${Math.abs(getDaysUntilDue(assignment.dueDate))} days overdue`
                            : `${getDaysUntilDue(assignment.dueDate)} days left`
                          }
                        </div>
                      )}
                    </div>
                  </div>

                  <div className="flex items-center justify-between">
                    <div className="flex items-center space-x-4">
                      {assignment.attachments && assignment.attachments.length > 0 && (
                        <div className="flex items-center space-x-2">
                          <span className="text-sm text-gray-500">Attachments:</span>
                          {assignment.attachments.map((file, index) => (
                            <Button key={index} variant="outline" size="sm">
                              <Download className="h-3 w-3 mr-1" />
                              {file}
                            </Button>
                          ))}
                        </div>
                      )}
                    </div>
                    
                    <div className="flex space-x-2">
                      <Button variant="outline" size="sm">
                        <Eye className="h-4 w-4 mr-1" />
                        View Details
                      </Button>
                      {(assignment.status === "pending" || assignment.status === "in_progress") && (
                        <Button size="sm">
                          <Upload className="h-4 w-4 mr-1" />
                          Submit
                        </Button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
