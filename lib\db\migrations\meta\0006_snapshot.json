{"id": "dc16538c-a8e8-476b-9042-486d7dba9755", "prevId": "be62b4ea-32e6-4a54-9e61-44ec962155ff", "version": "7", "dialect": "postgresql", "tables": {"public.academic_batches": {"name": "academic_batches", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "program_id": {"name": "program_id", "type": "uuid", "primaryKey": false, "notNull": true}, "batch_name": {"name": "batch_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "start_year": {"name": "start_year", "type": "integer", "primaryKey": false, "notNull": true}, "end_year": {"name": "end_year", "type": "integer", "primaryKey": false, "notNull": true}, "total_seats": {"name": "total_seats", "type": "integer", "primaryKey": false, "notNull": true}, "occupied_seats": {"name": "occupied_seats", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "available_seats": {"name": "available_seats", "type": "integer", "primaryKey": false, "notNull": true}, "admission_status": {"name": "admission_status", "type": "admission_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'open'"}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"academic_batches_program_id_academic_programs_id_fk": {"name": "academic_batches_program_id_academic_programs_id_fk", "tableFrom": "academic_batches", "tableTo": "academic_programs", "columnsFrom": ["program_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.academic_programs": {"name": "academic_programs", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "code": {"name": "code", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "program_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'school'"}, "class_level": {"name": "class_level", "type": "class_level", "typeSchema": "public", "primaryKey": false, "notNull": true}, "stream_type": {"name": "stream_type", "type": "stream_type", "typeSchema": "public", "primaryKey": false, "notNull": false}, "duration": {"name": "duration", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "eligibility_criteria": {"name": "eligibility_criteria", "type": "text", "primaryKey": false, "notNull": false}, "total_seats": {"name": "total_seats", "type": "integer", "primaryKey": false, "notNull": true, "default": 30}, "admission_status": {"name": "admission_status", "type": "admission_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'open'"}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"academic_programs_code_unique": {"name": "academic_programs_code_unique", "nullsNotDistinct": false, "columns": ["code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.academic_sections": {"name": "academic_sections", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "program_id": {"name": "program_id", "type": "uuid", "primaryKey": false, "notNull": true}, "batch_id": {"name": "batch_id", "type": "uuid", "primaryKey": false, "notNull": true}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "display_name": {"name": "display_name", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "capacity": {"name": "capacity", "type": "integer", "primaryKey": false, "notNull": true, "default": 30}, "occupied_seats": {"name": "occupied_seats", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "available_seats": {"name": "available_seats", "type": "integer", "primaryKey": false, "notNull": true, "default": 30}, "class_teacher_id": {"name": "class_teacher_id", "type": "uuid", "primaryKey": false, "notNull": false}, "room": {"name": "room", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "academic_year": {"name": "academic_year", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "is_active": {"name": "is_active", "type": "boolean", "primaryKey": false, "notNull": true, "default": true}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"academic_sections_program_id_academic_programs_id_fk": {"name": "academic_sections_program_id_academic_programs_id_fk", "tableFrom": "academic_sections", "tableTo": "academic_programs", "columnsFrom": ["program_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "academic_sections_batch_id_academic_batches_id_fk": {"name": "academic_sections_batch_id_academic_batches_id_fk", "tableFrom": "academic_sections", "tableTo": "academic_batches", "columnsFrom": ["batch_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "academic_sections_class_teacher_id_teachers_id_fk": {"name": "academic_sections_class_teacher_id_teachers_id_fk", "tableFrom": "academic_sections", "tableTo": "teachers", "columnsFrom": ["class_teacher_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.assignment_submissions": {"name": "assignment_submissions", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "assignment_id": {"name": "assignment_id", "type": "uuid", "primaryKey": false, "notNull": true}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": true}, "submission_text": {"name": "submission_text", "type": "text", "primaryKey": false, "notNull": false}, "attachments": {"name": "attachments", "type": "text", "primaryKey": false, "notNull": false}, "submitted_at": {"name": "submitted_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "obtained_marks": {"name": "obtained_marks", "type": "integer", "primaryKey": false, "notNull": false}, "feedback": {"name": "feedback", "type": "text", "primaryKey": false, "notNull": false}, "graded_by": {"name": "graded_by", "type": "uuid", "primaryKey": false, "notNull": false}, "graded_at": {"name": "graded_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'submitted'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"assignment_submissions_assignment_id_assignments_id_fk": {"name": "assignment_submissions_assignment_id_assignments_id_fk", "tableFrom": "assignment_submissions", "tableTo": "assignments", "columnsFrom": ["assignment_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "assignment_submissions_student_id_students_id_fk": {"name": "assignment_submissions_student_id_students_id_fk", "tableFrom": "assignment_submissions", "tableTo": "students", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "assignment_submissions_graded_by_teachers_id_fk": {"name": "assignment_submissions_graded_by_teachers_id_fk", "tableFrom": "assignment_submissions", "tableTo": "teachers", "columnsFrom": ["graded_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.assignments": {"name": "assignments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "class_id": {"name": "class_id", "type": "uuid", "primaryKey": false, "notNull": true}, "teacher_id": {"name": "teacher_id", "type": "uuid", "primaryKey": false, "notNull": true}, "subject": {"name": "subject", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "total_marks": {"name": "total_marks", "type": "integer", "primaryKey": false, "notNull": true}, "due_date": {"name": "due_date", "type": "timestamp", "primaryKey": false, "notNull": true}, "assigned_date": {"name": "assigned_date", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "instructions": {"name": "instructions", "type": "text", "primaryKey": false, "notNull": false}, "attachments": {"name": "attachments", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"assignments_class_id_classes_id_fk": {"name": "assignments_class_id_classes_id_fk", "tableFrom": "assignments", "tableTo": "classes", "columnsFrom": ["class_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "assignments_teacher_id_teachers_id_fk": {"name": "assignments_teacher_id_teachers_id_fk", "tableFrom": "assignments", "tableTo": "teachers", "columnsFrom": ["teacher_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.attendance": {"name": "attendance", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": true}, "class_id": {"name": "class_id", "type": "uuid", "primaryKey": false, "notNull": true}, "date": {"name": "date", "type": "date", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "attendance_status", "typeSchema": "public", "primaryKey": false, "notNull": true}, "remarks": {"name": "remarks", "type": "text", "primaryKey": false, "notNull": false}, "marked_by": {"name": "marked_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"attendance_student_id_students_id_fk": {"name": "attendance_student_id_students_id_fk", "tableFrom": "attendance", "tableTo": "students", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "attendance_class_id_classes_id_fk": {"name": "attendance_class_id_classes_id_fk", "tableFrom": "attendance", "tableTo": "classes", "columnsFrom": ["class_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "attendance_marked_by_users_id_fk": {"name": "attendance_marked_by_users_id_fk", "tableFrom": "attendance", "tableTo": "users", "columnsFrom": ["marked_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.book_issues": {"name": "book_issues", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "book_id": {"name": "book_id", "type": "uuid", "primaryKey": false, "notNull": true}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": false}, "teacher_id": {"name": "teacher_id", "type": "uuid", "primaryKey": false, "notNull": false}, "issue_date": {"name": "issue_date", "type": "date", "primaryKey": false, "notNull": true}, "due_date": {"name": "due_date", "type": "date", "primaryKey": false, "notNull": true}, "return_date": {"name": "return_date", "type": "date", "primaryKey": false, "notNull": false}, "fine_amount": {"name": "fine_amount", "type": "numeric(8, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "status": {"name": "status", "type": "issue_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'issued'"}, "issued_by": {"name": "issued_by", "type": "uuid", "primaryKey": false, "notNull": false}, "returned_by": {"name": "returned_by", "type": "uuid", "primaryKey": false, "notNull": false}, "remarks": {"name": "remarks", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"book_issues_book_id_books_id_fk": {"name": "book_issues_book_id_books_id_fk", "tableFrom": "book_issues", "tableTo": "books", "columnsFrom": ["book_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "book_issues_student_id_students_id_fk": {"name": "book_issues_student_id_students_id_fk", "tableFrom": "book_issues", "tableTo": "students", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "book_issues_teacher_id_teachers_id_fk": {"name": "book_issues_teacher_id_teachers_id_fk", "tableFrom": "book_issues", "tableTo": "teachers", "columnsFrom": ["teacher_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "book_issues_issued_by_users_id_fk": {"name": "book_issues_issued_by_users_id_fk", "tableFrom": "book_issues", "tableTo": "users", "columnsFrom": ["issued_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "book_issues_returned_by_users_id_fk": {"name": "book_issues_returned_by_users_id_fk", "tableFrom": "book_issues", "tableTo": "users", "columnsFrom": ["returned_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.books": {"name": "books", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "title": {"name": "title", "type": "<PERSON><PERSON><PERSON>(300)", "primaryKey": false, "notNull": true}, "author": {"name": "author", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "isbn": {"name": "isbn", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "publisher": {"name": "publisher", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": false}, "publication_year": {"name": "publication_year", "type": "integer", "primaryKey": false, "notNull": false}, "total_copies": {"name": "total_copies", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "available_copies": {"name": "available_copies", "type": "integer", "primaryKey": false, "notNull": true, "default": 1}, "location": {"name": "location", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "price": {"name": "price", "type": "numeric(8, 2)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "book_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'available'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"books_isbn_unique": {"name": "books_isbn_unique", "nullsNotDistinct": false, "columns": ["isbn"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.class_enrollments": {"name": "class_enrollments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": true}, "class_id": {"name": "class_id", "type": "uuid", "primaryKey": false, "notNull": true}, "enrollment_date": {"name": "enrollment_date", "type": "date", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"class_enrollments_student_id_students_id_fk": {"name": "class_enrollments_student_id_students_id_fk", "tableFrom": "class_enrollments", "tableTo": "students", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "class_enrollments_class_id_classes_id_fk": {"name": "class_enrollments_class_id_classes_id_fk", "tableFrom": "class_enrollments", "tableTo": "classes", "columnsFrom": ["class_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.classes": {"name": "classes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "program_id": {"name": "program_id", "type": "uuid", "primaryKey": false, "notNull": true}, "batch_id": {"name": "batch_id", "type": "uuid", "primaryKey": false, "notNull": true}, "grade": {"name": "grade", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "section": {"name": "section", "type": "<PERSON><PERSON><PERSON>(5)", "primaryKey": false, "notNull": true}, "subject": {"name": "subject", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "teacher_id": {"name": "teacher_id", "type": "uuid", "primaryKey": false, "notNull": false}, "assistant_teacher_id": {"name": "assistant_teacher_id", "type": "uuid", "primaryKey": false, "notNull": false}, "class_type": {"name": "class_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'theory'"}, "room": {"name": "room", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "capacity": {"name": "capacity", "type": "integer", "primaryKey": false, "notNull": true, "default": 30}, "enrolled_students": {"name": "enrolled_students", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "available_seats": {"name": "available_seats", "type": "integer", "primaryKey": false, "notNull": true, "default": 30}, "academic_year": {"name": "academic_year", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"classes_program_id_academic_programs_id_fk": {"name": "classes_program_id_academic_programs_id_fk", "tableFrom": "classes", "tableTo": "academic_programs", "columnsFrom": ["program_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "classes_batch_id_academic_batches_id_fk": {"name": "classes_batch_id_academic_batches_id_fk", "tableFrom": "classes", "tableTo": "academic_batches", "columnsFrom": ["batch_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "classes_teacher_id_teachers_id_fk": {"name": "classes_teacher_id_teachers_id_fk", "tableFrom": "classes", "tableTo": "teachers", "columnsFrom": ["teacher_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "classes_assistant_teacher_id_teachers_id_fk": {"name": "classes_assistant_teacher_id_teachers_id_fk", "tableFrom": "classes", "tableTo": "teachers", "columnsFrom": ["assistant_teacher_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.exams": {"name": "exams", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "exam_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "grade": {"name": "grade", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "subject": {"name": "subject", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "date": {"name": "date", "type": "date", "primaryKey": false, "notNull": true}, "start_time": {"name": "start_time", "type": "time", "primaryKey": false, "notNull": true}, "end_time": {"name": "end_time", "type": "time", "primaryKey": false, "notNull": true}, "total_marks": {"name": "total_marks", "type": "integer", "primaryKey": false, "notNull": true}, "passing_marks": {"name": "passing_marks", "type": "integer", "primaryKey": false, "notNull": true}, "room": {"name": "room", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "invigilator": {"name": "invigilator", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "instructions": {"name": "instructions", "type": "text", "primaryKey": false, "notNull": false}, "academic_year": {"name": "academic_year", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "semester": {"name": "semester", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "exam_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'scheduled'"}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"exams_created_by_users_id_fk": {"name": "exams_created_by_users_id_fk", "tableFrom": "exams", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.fee_payments": {"name": "fee_payments", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": true}, "fee_structure_id": {"name": "fee_structure_id", "type": "uuid", "primaryKey": false, "notNull": true}, "receipt_number": {"name": "receipt_number", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "amount_paid": {"name": "amount_paid", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "payment_method": {"name": "payment_method", "type": "payment_method", "typeSchema": "public", "primaryKey": false, "notNull": true}, "payment_date": {"name": "payment_date", "type": "date", "primaryKey": false, "notNull": true}, "transaction_id": {"name": "transaction_id", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "remarks": {"name": "remarks", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "payment_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'completed'"}, "processed_by": {"name": "processed_by", "type": "uuid", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"fee_payments_student_id_students_id_fk": {"name": "fee_payments_student_id_students_id_fk", "tableFrom": "fee_payments", "tableTo": "students", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "fee_payments_fee_structure_id_fee_structures_id_fk": {"name": "fee_payments_fee_structure_id_fee_structures_id_fk", "tableFrom": "fee_payments", "tableTo": "fee_structures", "columnsFrom": ["fee_structure_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "fee_payments_processed_by_users_id_fk": {"name": "fee_payments_processed_by_users_id_fk", "tableFrom": "fee_payments", "tableTo": "users", "columnsFrom": ["processed_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"fee_payments_receipt_number_unique": {"name": "fee_payments_receipt_number_unique", "nullsNotDistinct": false, "columns": ["receipt_number"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.fee_structures": {"name": "fee_structures", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "grade": {"name": "grade", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "academic_year": {"name": "academic_year", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "tuition_fee": {"name": "tuition_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "admission_fee": {"name": "admission_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "exam_fee": {"name": "exam_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "library_fee": {"name": "library_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "transport_fee": {"name": "transport_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "hostel_fee": {"name": "hostel_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "miscellaneous_fee": {"name": "miscellaneous_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "total_fee": {"name": "total_fee", "type": "numeric(10, 2)", "primaryKey": false, "notNull": true}, "due_date": {"name": "due_date", "type": "date", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.grades": {"name": "grades", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": true}, "class_id": {"name": "class_id", "type": "uuid", "primaryKey": false, "notNull": true}, "exam_type": {"name": "exam_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "subject": {"name": "subject", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "total_marks": {"name": "total_marks", "type": "integer", "primaryKey": false, "notNull": true}, "obtained_marks": {"name": "obtained_marks", "type": "integer", "primaryKey": false, "notNull": true}, "grade": {"name": "grade", "type": "grade", "typeSchema": "public", "primaryKey": false, "notNull": false}, "percentage": {"name": "percentage", "type": "numeric(5, 2)", "primaryKey": false, "notNull": false}, "exam_date": {"name": "exam_date", "type": "date", "primaryKey": false, "notNull": false}, "graded_by": {"name": "graded_by", "type": "uuid", "primaryKey": false, "notNull": false}, "remarks": {"name": "remarks", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"grades_student_id_students_id_fk": {"name": "grades_student_id_students_id_fk", "tableFrom": "grades", "tableTo": "students", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "grades_class_id_classes_id_fk": {"name": "grades_class_id_classes_id_fk", "tableFrom": "grades", "tableTo": "classes", "columnsFrom": ["class_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "grades_graded_by_teachers_id_fk": {"name": "grades_graded_by_teachers_id_fk", "tableFrom": "grades", "tableTo": "teachers", "columnsFrom": ["graded_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.hostel_allocations": {"name": "hostel_allocations", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": true}, "room_id": {"name": "room_id", "type": "uuid", "primaryKey": false, "notNull": true}, "allocation_date": {"name": "allocation_date", "type": "date", "primaryKey": false, "notNull": true}, "vacation_date": {"name": "vacation_date", "type": "date", "primaryKey": false, "notNull": false}, "monthly_fee": {"name": "monthly_fee", "type": "numeric(8, 2)", "primaryKey": false, "notNull": true}, "security_deposit": {"name": "security_deposit", "type": "numeric(8, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "allocated_by": {"name": "allocated_by", "type": "uuid", "primaryKey": false, "notNull": false}, "remarks": {"name": "remarks", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"hostel_allocations_student_id_students_id_fk": {"name": "hostel_allocations_student_id_students_id_fk", "tableFrom": "hostel_allocations", "tableTo": "students", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "hostel_allocations_room_id_hostel_rooms_id_fk": {"name": "hostel_allocations_room_id_hostel_rooms_id_fk", "tableFrom": "hostel_allocations", "tableTo": "hostel_rooms", "columnsFrom": ["room_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "hostel_allocations_allocated_by_users_id_fk": {"name": "hostel_allocations_allocated_by_users_id_fk", "tableFrom": "hostel_allocations", "tableTo": "users", "columnsFrom": ["allocated_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.hostel_rooms": {"name": "hostel_rooms", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "hostel_id": {"name": "hostel_id", "type": "uuid", "primaryKey": false, "notNull": true}, "room_number": {"name": "room_number", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "room_type": {"name": "room_type", "type": "room_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "floor": {"name": "floor", "type": "integer", "primaryKey": false, "notNull": false}, "capacity": {"name": "capacity", "type": "integer", "primaryKey": false, "notNull": true}, "occupied_beds": {"name": "occupied_beds", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "monthly_rent": {"name": "monthly_rent", "type": "numeric(8, 2)", "primaryKey": false, "notNull": true}, "facilities": {"name": "facilities", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "room_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'available'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"hostel_rooms_hostel_id_hostels_id_fk": {"name": "hostel_rooms_hostel_id_hostels_id_fk", "tableFrom": "hostel_rooms", "tableTo": "hostels", "columnsFrom": ["hostel_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.hostels": {"name": "hostels", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "type": {"name": "type", "type": "hostel_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": true}, "warden_name": {"name": "warden_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "warden_phone": {"name": "warden_phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "warden_email": {"name": "warden_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "total_rooms": {"name": "total_rooms", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "total_beds": {"name": "total_beds", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "facilities": {"name": "facilities", "type": "text", "primaryKey": false, "notNull": false}, "monthly_fee": {"name": "monthly_fee", "type": "numeric(8, 2)", "primaryKey": false, "notNull": true}, "security_deposit": {"name": "security_deposit", "type": "numeric(8, 2)", "primaryKey": false, "notNull": false, "default": "'0'"}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.institution_config": {"name": "institution_config", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "institution_name": {"name": "institution_name", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "institution_code": {"name": "institution_code", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "website": {"name": "website", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "established_year": {"name": "established_year", "type": "integer", "primaryKey": false, "notNull": false}, "affiliation": {"name": "affiliation", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "current_academic_year": {"name": "current_academic_year", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "session_start_month": {"name": "session_start_month", "type": "integer", "primaryKey": false, "notNull": true, "default": 4}, "session_end_month": {"name": "session_end_month", "type": "integer", "primaryKey": false, "notNull": true, "default": 3}, "grading_system": {"name": "grading_system", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'Grade-based (A-F)'"}, "currency": {"name": "currency", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": false, "default": "'INR'"}, "timezone": {"name": "timezone", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false, "default": "'Asia/Kolkata'"}, "date_format": {"name": "date_format", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'DD/MM/YYYY'"}, "language": {"name": "language", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false, "default": "'English'"}, "is_configured": {"name": "is_configured", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "configured_by": {"name": "configured_by", "type": "uuid", "primaryKey": false, "notNull": false}, "configured_at": {"name": "configured_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"institution_config_configured_by_users_id_fk": {"name": "institution_config_configured_by_users_id_fk", "tableFrom": "institution_config", "tableTo": "users", "columnsFrom": ["configured_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"institution_config_institution_code_unique": {"name": "institution_config_institution_code_unique", "nullsNotDistinct": false, "columns": ["institution_code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.student_transport": {"name": "student_transport", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "student_id": {"name": "student_id", "type": "uuid", "primaryKey": false, "notNull": true}, "route_id": {"name": "route_id", "type": "uuid", "primaryKey": false, "notNull": true}, "pickup_point": {"name": "pickup_point", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": false}, "monthly_fee": {"name": "monthly_fee", "type": "numeric(8, 2)", "primaryKey": false, "notNull": true}, "start_date": {"name": "start_date", "type": "date", "primaryKey": false, "notNull": true}, "end_date": {"name": "end_date", "type": "date", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"student_transport_student_id_students_id_fk": {"name": "student_transport_student_id_students_id_fk", "tableFrom": "student_transport", "tableTo": "students", "columnsFrom": ["student_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "student_transport_route_id_transport_routes_id_fk": {"name": "student_transport_route_id_transport_routes_id_fk", "tableFrom": "student_transport", "tableTo": "transport_routes", "columnsFrom": ["route_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.students": {"name": "students", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "roll_number": {"name": "roll_number", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "program_id": {"name": "program_id", "type": "uuid", "primaryKey": false, "notNull": true}, "batch_id": {"name": "batch_id", "type": "uuid", "primaryKey": false, "notNull": true}, "section_id": {"name": "section_id", "type": "uuid", "primaryKey": false, "notNull": false}, "grade": {"name": "grade", "type": "<PERSON><PERSON><PERSON>(10)", "primaryKey": false, "notNull": true}, "section": {"name": "section", "type": "<PERSON><PERSON><PERSON>(5)", "primaryKey": false, "notNull": false}, "student_type": {"name": "student_type", "type": "student_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'regular'"}, "is_lateral_entry": {"name": "is_lateral_entry", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "date_of_birth": {"name": "date_of_birth", "type": "date", "primaryKey": false, "notNull": true}, "address": {"name": "address", "type": "text", "primaryKey": false, "notNull": false}, "parent_name": {"name": "parent_name", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": false}, "parent_phone": {"name": "parent_phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "parent_email": {"name": "parent_email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "admission_date": {"name": "admission_date", "type": "date", "primaryKey": false, "notNull": true}, "blood_group": {"name": "blood_group", "type": "<PERSON><PERSON><PERSON>(5)", "primaryKey": false, "notNull": false}, "emergency_contact": {"name": "emergency_contact", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "medical_info": {"name": "medical_info", "type": "text", "primaryKey": false, "notNull": false}, "previous_education": {"name": "previous_education", "type": "text", "primaryKey": false, "notNull": false}, "transfer_certificate": {"name": "transfer_certificate", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"students_user_id_idx": {"name": "students_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "students_roll_number_idx": {"name": "students_roll_number_idx", "columns": [{"expression": "roll_number", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "students_program_id_idx": {"name": "students_program_id_idx", "columns": [{"expression": "program_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "students_batch_id_idx": {"name": "students_batch_id_idx", "columns": [{"expression": "batch_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "students_section_id_idx": {"name": "students_section_id_idx", "columns": [{"expression": "section_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "students_status_idx": {"name": "students_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "students_admission_date_idx": {"name": "students_admission_date_idx", "columns": [{"expression": "admission_date", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "students_program_batch_idx": {"name": "students_program_batch_idx", "columns": [{"expression": "program_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "batch_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "students_batch_section_idx": {"name": "students_batch_section_idx", "columns": [{"expression": "batch_id", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "section_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "students_status_program_idx": {"name": "students_status_program_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "program_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"students_user_id_users_id_fk": {"name": "students_user_id_users_id_fk", "tableFrom": "students", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "students_program_id_academic_programs_id_fk": {"name": "students_program_id_academic_programs_id_fk", "tableFrom": "students", "tableTo": "academic_programs", "columnsFrom": ["program_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "students_batch_id_academic_batches_id_fk": {"name": "students_batch_id_academic_batches_id_fk", "tableFrom": "students", "tableTo": "academic_batches", "columnsFrom": ["batch_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "students_section_id_academic_sections_id_fk": {"name": "students_section_id_academic_sections_id_fk", "tableFrom": "students", "tableTo": "academic_sections", "columnsFrom": ["section_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"students_roll_number_unique": {"name": "students_roll_number_unique", "nullsNotDistinct": false, "columns": ["roll_number"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.system_backups": {"name": "system_backups", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "name": {"name": "name", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "backup_type": {"name": "backup_type", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'pending'"}, "file_path": {"name": "file_path", "type": "var<PERSON><PERSON>(500)", "primaryKey": false, "notNull": false}, "file_size": {"name": "file_size", "type": "integer", "primaryKey": false, "notNull": false}, "started_at": {"name": "started_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "completed_at": {"name": "completed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_by": {"name": "created_by", "type": "uuid", "primaryKey": false, "notNull": false}, "error": {"name": "error", "type": "text", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"system_backups_status_idx": {"name": "system_backups_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "system_backups_type_idx": {"name": "system_backups_type_idx", "columns": [{"expression": "backup_type", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "system_backups_created_at_idx": {"name": "system_backups_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "system_backups_created_by_idx": {"name": "system_backups_created_by_idx", "columns": [{"expression": "created_by", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"system_backups_created_by_users_id_fk": {"name": "system_backups_created_by_users_id_fk", "tableFrom": "system_backups", "tableTo": "users", "columnsFrom": ["created_by"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.system_settings": {"name": "system_settings", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "category": {"name": "category", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "key": {"name": "key", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "value": {"name": "value", "type": "text", "primaryKey": false, "notNull": true}, "data_type": {"name": "data_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true, "default": "'string'"}, "description": {"name": "description", "type": "text", "primaryKey": false, "notNull": false}, "is_system": {"name": "is_system", "type": "boolean", "primaryKey": false, "notNull": true, "default": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"system_settings_category_key_idx": {"name": "system_settings_category_key_idx", "columns": [{"expression": "category", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "key", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "system_settings_category_idx": {"name": "system_settings_category_idx", "columns": [{"expression": "category", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "system_settings_key_idx": {"name": "system_settings_key_idx", "columns": [{"expression": "key", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "system_settings_category_key_unique": {"name": "system_settings_category_key_unique", "columns": [{"expression": "category", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "key", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.teachers": {"name": "teachers", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "employee_id": {"name": "employee_id", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": true}, "teacher_type": {"name": "teacher_type", "type": "teacher_type", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'permanent'"}, "subjects": {"name": "subjects", "type": "text", "primaryKey": false, "notNull": false}, "primary_subject": {"name": "primary_subject", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "qualification": {"name": "qualification", "type": "text", "primaryKey": false, "notNull": false}, "experience": {"name": "experience", "type": "integer", "primaryKey": false, "notNull": true, "default": 0}, "joining_date": {"name": "joining_date", "type": "date", "primaryKey": false, "notNull": true}, "contract_end_date": {"name": "contract_end_date", "type": "date", "primaryKey": false, "notNull": false}, "salary": {"name": "salary", "type": "numeric(10, 2)", "primaryKey": false, "notNull": false}, "department": {"name": "department", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": false}, "max_classes_per_week": {"name": "max_classes_per_week", "type": "integer", "primaryKey": false, "notNull": false, "default": 20}, "current_class_load": {"name": "current_class_load", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "specialization": {"name": "specialization", "type": "text", "primaryKey": false, "notNull": false}, "certifications": {"name": "certifications", "type": "text", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"teachers_user_id_users_id_fk": {"name": "teachers_user_id_users_id_fk", "tableFrom": "teachers", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"teachers_employee_id_unique": {"name": "teachers_employee_id_unique", "nullsNotDistinct": false, "columns": ["employee_id"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.timetables": {"name": "timetables", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "class_id": {"name": "class_id", "type": "uuid", "primaryKey": false, "notNull": true}, "teacher_id": {"name": "teacher_id", "type": "uuid", "primaryKey": false, "notNull": true}, "subject": {"name": "subject", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "day": {"name": "day", "type": "day", "typeSchema": "public", "primaryKey": false, "notNull": true}, "start_time": {"name": "start_time", "type": "time", "primaryKey": false, "notNull": true}, "end_time": {"name": "end_time", "type": "time", "primaryKey": false, "notNull": true}, "room": {"name": "room", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "academic_year": {"name": "academic_year", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "semester": {"name": "semester", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"timetables_class_id_classes_id_fk": {"name": "timetables_class_id_classes_id_fk", "tableFrom": "timetables", "tableTo": "classes", "columnsFrom": ["class_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "timetables_teacher_id_teachers_id_fk": {"name": "timetables_teacher_id_teachers_id_fk", "tableFrom": "timetables", "tableTo": "teachers", "columnsFrom": ["teacher_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.transport_routes": {"name": "transport_routes", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "route_name": {"name": "route_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "route_code": {"name": "route_code", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "start_point": {"name": "start_point", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "end_point": {"name": "end_point", "type": "<PERSON><PERSON><PERSON>(200)", "primaryKey": false, "notNull": true}, "stops": {"name": "stops", "type": "text", "primaryKey": false, "notNull": false}, "distance": {"name": "distance", "type": "numeric(6, 2)", "primaryKey": false, "notNull": false}, "estimated_time": {"name": "estimated_time", "type": "integer", "primaryKey": false, "notNull": false}, "fee": {"name": "fee", "type": "numeric(8, 2)", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"transport_routes_route_code_unique": {"name": "transport_routes_route_code_unique", "nullsNotDistinct": false, "columns": ["route_code"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.user_activity_logs": {"name": "user_activity_logs", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "user_id": {"name": "user_id", "type": "uuid", "primaryKey": false, "notNull": true}, "action": {"name": "action", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "target_user_id": {"name": "target_user_id", "type": "uuid", "primaryKey": false, "notNull": false}, "details": {"name": "details", "type": "text", "primaryKey": false, "notNull": false}, "ip_address": {"name": "ip_address", "type": "<PERSON><PERSON><PERSON>(45)", "primaryKey": false, "notNull": false}, "user_agent": {"name": "user_agent", "type": "text", "primaryKey": false, "notNull": false}, "timestamp": {"name": "timestamp", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"user_activity_logs_user_id_idx": {"name": "user_activity_logs_user_id_idx", "columns": [{"expression": "user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_activity_logs_action_idx": {"name": "user_activity_logs_action_idx", "columns": [{"expression": "action", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_activity_logs_timestamp_idx": {"name": "user_activity_logs_timestamp_idx", "columns": [{"expression": "timestamp", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "user_activity_logs_target_user_idx": {"name": "user_activity_logs_target_user_idx", "columns": [{"expression": "target_user_id", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {"user_activity_logs_user_id_users_id_fk": {"name": "user_activity_logs_user_id_users_id_fk", "tableFrom": "user_activity_logs", "tableTo": "users", "columnsFrom": ["user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}, "user_activity_logs_target_user_id_users_id_fk": {"name": "user_activity_logs_target_user_id_users_id_fk", "tableFrom": "user_activity_logs", "tableTo": "users", "columnsFrom": ["target_user_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.users": {"name": "users", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "email": {"name": "email", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "password": {"name": "password", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": true}, "first_name": {"name": "first_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "last_name": {"name": "last_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "phone": {"name": "phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "role": {"name": "role", "type": "user_role", "typeSchema": "public", "primaryKey": false, "notNull": true}, "status": {"name": "status", "type": "status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "last_login": {"name": "last_login", "type": "timestamp", "primaryKey": false, "notNull": false}, "password_reset_token": {"name": "password_reset_token", "type": "<PERSON><PERSON><PERSON>(255)", "primaryKey": false, "notNull": false}, "password_reset_expires": {"name": "password_reset_expires", "type": "timestamp", "primaryKey": false, "notNull": false}, "password_changed_at": {"name": "password_changed_at", "type": "timestamp", "primaryKey": false, "notNull": false}, "login_attempts": {"name": "login_attempts", "type": "integer", "primaryKey": false, "notNull": false, "default": 0}, "account_locked_until": {"name": "account_locked_until", "type": "timestamp", "primaryKey": false, "notNull": false}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {"users_email_idx": {"name": "users_email_idx", "columns": [{"expression": "email", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_role_idx": {"name": "users_role_idx", "columns": [{"expression": "role", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_status_idx": {"name": "users_status_idx", "columns": [{"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_created_at_idx": {"name": "users_created_at_idx", "columns": [{"expression": "created_at", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_last_login_idx": {"name": "users_last_login_idx", "columns": [{"expression": "last_login", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_role_status_idx": {"name": "users_role_status_idx", "columns": [{"expression": "role", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "status", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}, "users_name_search_idx": {"name": "users_name_search_idx", "columns": [{"expression": "first_name", "isExpression": false, "asc": true, "nulls": "last"}, {"expression": "last_name", "isExpression": false, "asc": true, "nulls": "last"}], "isUnique": false, "concurrently": false, "method": "btree", "with": {}}}, "foreignKeys": {}, "compositePrimaryKeys": {}, "uniqueConstraints": {"users_email_unique": {"name": "users_email_unique", "nullsNotDistinct": false, "columns": ["email"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}, "public.vehicles": {"name": "vehicles", "schema": "", "columns": {"id": {"name": "id", "type": "uuid", "primaryKey": true, "notNull": true, "default": "gen_random_uuid()"}, "vehicle_number": {"name": "vehicle_number", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "vehicle_type": {"name": "vehicle_type", "type": "vehicle_type", "typeSchema": "public", "primaryKey": false, "notNull": true}, "capacity": {"name": "capacity", "type": "integer", "primaryKey": false, "notNull": true}, "route_id": {"name": "route_id", "type": "uuid", "primaryKey": false, "notNull": false}, "driver_name": {"name": "driver_name", "type": "<PERSON><PERSON><PERSON>(100)", "primaryKey": false, "notNull": true}, "driver_phone": {"name": "driver_phone", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": true}, "driver_license": {"name": "driver_license", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "insurance_number": {"name": "insurance_number", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "insurance_expiry": {"name": "insurance_expiry", "type": "date", "primaryKey": false, "notNull": false}, "permit_number": {"name": "permit_number", "type": "<PERSON><PERSON><PERSON>(50)", "primaryKey": false, "notNull": false}, "permit_expiry": {"name": "permit_expiry", "type": "date", "primaryKey": false, "notNull": false}, "last_maintenance": {"name": "last_maintenance", "type": "date", "primaryKey": false, "notNull": false}, "next_maintenance": {"name": "next_maintenance", "type": "date", "primaryKey": false, "notNull": false}, "fuel_type": {"name": "fuel_type", "type": "<PERSON><PERSON><PERSON>(20)", "primaryKey": false, "notNull": false}, "status": {"name": "status", "type": "vehicle_status", "typeSchema": "public", "primaryKey": false, "notNull": true, "default": "'active'"}, "created_at": {"name": "created_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}, "updated_at": {"name": "updated_at", "type": "timestamp", "primaryKey": false, "notNull": true, "default": "now()"}}, "indexes": {}, "foreignKeys": {"vehicles_route_id_transport_routes_id_fk": {"name": "vehicles_route_id_transport_routes_id_fk", "tableFrom": "vehicles", "tableTo": "transport_routes", "columnsFrom": ["route_id"], "columnsTo": ["id"], "onDelete": "no action", "onUpdate": "no action"}}, "compositePrimaryKeys": {}, "uniqueConstraints": {"vehicles_vehicle_number_unique": {"name": "vehicles_vehicle_number_unique", "nullsNotDistinct": false, "columns": ["vehicle_number"]}}, "policies": {}, "checkConstraints": {}, "isRLSEnabled": false}}, "enums": {"public.admission_status": {"name": "admission_status", "schema": "public", "values": ["open", "closed", "waitlist"]}, "public.attendance_status": {"name": "attendance_status", "schema": "public", "values": ["present", "absent", "late", "excused"]}, "public.book_status": {"name": "book_status", "schema": "public", "values": ["available", "issued", "maintenance", "lost"]}, "public.class_level": {"name": "class_level", "schema": "public", "values": ["nursery", "lkg", "ukg", "grade_1", "grade_2", "grade_3", "grade_4", "grade_5", "grade_6", "grade_7", "grade_8", "grade_9", "grade_10", "grade_11", "grade_12"]}, "public.day": {"name": "day", "schema": "public", "values": ["monday", "tuesday", "wednesday", "thursday", "friday", "saturday", "sunday"]}, "public.exam_status": {"name": "exam_status", "schema": "public", "values": ["scheduled", "ongoing", "completed", "cancelled"]}, "public.exam_type": {"name": "exam_type", "schema": "public", "values": ["midterm", "final", "unit_test", "practical", "oral"]}, "public.grade": {"name": "grade", "schema": "public", "values": ["A+", "A", "A-", "B+", "B", "B-", "C+", "C", "C-", "D+", "D", "F"]}, "public.hostel_type": {"name": "hostel_type", "schema": "public", "values": ["boys", "girls", "mixed"]}, "public.issue_status": {"name": "issue_status", "schema": "public", "values": ["issued", "returned", "overdue"]}, "public.payment_method": {"name": "payment_method", "schema": "public", "values": ["cash", "card", "bank_transfer", "online", "cheque"]}, "public.payment_status": {"name": "payment_status", "schema": "public", "values": ["pending", "completed", "failed", "refunded"]}, "public.program_type": {"name": "program_type", "schema": "public", "values": ["school"]}, "public.room_status": {"name": "room_status", "schema": "public", "values": ["available", "occupied", "maintenance"]}, "public.room_type": {"name": "room_type", "schema": "public", "values": ["single", "double", "triple", "dormitory"]}, "public.status": {"name": "status", "schema": "public", "values": ["active", "inactive", "suspended"]}, "public.stream_type": {"name": "stream_type", "schema": "public", "values": ["science", "commerce", "arts"]}, "public.student_type": {"name": "student_type", "schema": "public", "values": ["regular", "lateral", "distance"]}, "public.teacher_type": {"name": "teacher_type", "schema": "public", "values": ["permanent", "contract", "visiting", "guest"]}, "public.user_role": {"name": "user_role", "schema": "public", "values": ["super_admin", "admin", "teacher", "student", "parent", "admission_officer", "finance_manager", "librarian", "transport_manager", "hostel_manager"]}, "public.vehicle_status": {"name": "vehicle_status", "schema": "public", "values": ["active", "maintenance", "inactive"]}, "public.vehicle_type": {"name": "vehicle_type", "schema": "public", "values": ["bus", "van", "car"]}}, "schemas": {}, "sequences": {}, "roles": {}, "policies": {}, "views": {}, "_meta": {"columns": {}, "schemas": {}, "tables": {}}}