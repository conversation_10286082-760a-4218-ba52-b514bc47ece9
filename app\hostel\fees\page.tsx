"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  IndianRupee,
  Search,
  Filter,
  Download,
  Plus,
  Eye,
  Edit,
  Calendar,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  Receipt,
  CreditCard,
  Building,
  User,
  TrendingUp,
  TrendingDown,
  Target,
} from "lucide-react";

export default function HostelFees() {
  const [user, setUser] = useState<any>(null);
  const [selectedTab, setSelectedTab] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedFee, setSelectedFee] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "hostel_manager") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  const feeStats = {
    totalStudents: 185,
    totalCollected: 2775000,
    totalPending: 425000,
    totalOverdue: 125000,
    paidStudents: 142,
    pendingStudents: 28,
    overdueStudents: 15,
    monthlyTarget: 3200000,
  };

  const mockFees = [
    {
      id: "HF001",
      studentName: "Aarav Sharma",
      studentId: "ST001",
      roomNumber: "A-101",
      hostelBlock: "Block A",
      monthlyFee: 15000,
      securityDeposit: 30000,
      totalDue: 15000,
      amountPaid: 15000,
      balance: 0,
      dueDate: "2024-01-31",
      paymentDate: "2024-01-25",
      status: "Paid",
      paymentMethod: "Online",
      semester: "Spring 2024",
      academicYear: "2023-24",
    },
    {
      id: "HF002",
      studentName: "Priya Patel",
      studentId: "ST002",
      roomNumber: "B-201",
      hostelBlock: "Block B",
      monthlyFee: 12000,
      securityDeposit: 24000,
      totalDue: 12000,
      amountPaid: 0,
      balance: 12000,
      dueDate: "2024-01-31",
      paymentDate: null,
      status: "Pending",
      paymentMethod: null,
      semester: "Spring 2024",
      academicYear: "2023-24",
    },
    {
      id: "HF003",
      studentName: "Rahul Kumar",
      studentId: "ST003",
      roomNumber: "A-102",
      hostelBlock: "Block A",
      monthlyFee: 20000,
      securityDeposit: 40000,
      totalDue: 20000,
      amountPaid: 10000,
      balance: 10000,
      dueDate: "2024-01-15",
      paymentDate: "2024-01-10",
      status: "Overdue",
      paymentMethod: "Cash",
      semester: "Spring 2024",
      academicYear: "2023-24",
    },
    {
      id: "HF004",
      studentName: "Sneha Reddy",
      studentId: "ST004",
      roomNumber: "C-301",
      hostelBlock: "Block C",
      monthlyFee: 18000,
      securityDeposit: 36000,
      totalDue: 18000,
      amountPaid: 18000,
      balance: 0,
      dueDate: "2024-01-31",
      paymentDate: "2024-01-28",
      status: "Paid",
      paymentMethod: "Bank Transfer",
      semester: "Spring 2024",
      academicYear: "2023-24",
    },
    {
      id: "HF005",
      studentName: "Vikram Singh",
      studentId: "ST005",
      roomNumber: "B-202",
      hostelBlock: "Block B",
      monthlyFee: 15000,
      securityDeposit: 30000,
      totalDue: 15000,
      amountPaid: 5000,
      balance: 10000,
      dueDate: "2024-01-20",
      paymentDate: "2024-01-18",
      status: "Partial",
      paymentMethod: "Online",
      semester: "Spring 2024",
      academicYear: "2023-24",
    },
  ];

  const filteredFees = mockFees.filter((fee) => {
    const matchesSearch = fee.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         fee.studentId.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         fee.roomNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         fee.hostelBlock.toLowerCase().includes(searchTerm.toLowerCase());

    if (selectedTab === "all") return matchesSearch;
    if (selectedTab === "paid") return matchesSearch && fee.status === "Paid";
    if (selectedTab === "pending") return matchesSearch && fee.status === "Pending";
    if (selectedTab === "overdue") return matchesSearch && fee.status === "Overdue";
    if (selectedTab === "partial") return matchesSearch && fee.status === "Partial";

    return matchesSearch;
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Paid":
        return <Badge className="bg-green-100 text-green-800">Paid</Badge>;
      case "Pending":
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case "Overdue":
        return <Badge className="bg-red-100 text-red-800">Overdue</Badge>;
      case "Partial":
        return <Badge className="bg-blue-100 text-blue-800">Partial</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }
  };

  const viewFeeDetails = (fee: any) => {
    setSelectedFee(fee);
  };

  const recordPayment = (feeId: string) => {
    alert(`Recording payment for ${feeId}`);
  };

  const sendReminder = (feeId: string) => {
    alert(`Sending payment reminder for ${feeId}`);
  };

  const generateReceipt = (feeId: string) => {
    alert(`Generating receipt for ${feeId}`);
  };

  const collectionRate = Math.round((feeStats.totalCollected / (feeStats.totalCollected + feeStats.totalPending)) * 100);

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Hostel Fee Management</h1>
            <p className="text-gray-600">Manage hostel fees, payments, and financial records</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Record Payment
            </Button>
          </div>
        </div>

        {/* Fee Statistics */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <IndianRupee className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    ₹{(feeStats.totalCollected / 100000).toFixed(1)}L
                  </div>
                  <p className="text-sm text-gray-600">Total Collected</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-yellow-600">
                    ₹{(feeStats.totalPending / 100000).toFixed(1)}L
                  </div>
                  <p className="text-sm text-gray-600">Pending</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <AlertTriangle className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-red-600">
                    ₹{(feeStats.totalOverdue / 100000).toFixed(1)}L
                  </div>
                  <p className="text-sm text-gray-600">Overdue</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Target className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {collectionRate}%
                  </div>
                  <p className="text-sm text-gray-600">Collection Rate</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Student Payment Statistics */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {feeStats.paidStudents}
                  </div>
                  <p className="text-sm text-gray-600">Paid Students</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-yellow-600">
                    {feeStats.pendingStudents}
                  </div>
                  <p className="text-sm text-gray-600">Pending Students</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <XCircle className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-red-600">
                    {feeStats.overdueStudents}
                  </div>
                  <p className="text-sm text-gray-600">Overdue Students</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Building className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {feeStats.totalStudents}
                  </div>
                  <p className="text-sm text-gray-600">Total Students</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filter */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search by student name, ID, room number, or block..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </div>

            {/* Tabs */}
            <div className="flex flex-wrap gap-2 mt-4">
              {[
                { key: "all", label: "All Students", count: feeStats.totalStudents },
                { key: "paid", label: "Paid", count: feeStats.paidStudents },
                { key: "pending", label: "Pending", count: feeStats.pendingStudents },
                { key: "overdue", label: "Overdue", count: feeStats.overdueStudents },
                { key: "partial", label: "Partial", count: 5 },
              ].map((tab) => (
                <Button
                  key={tab.key}
                  variant={selectedTab === tab.key ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedTab(tab.key)}
                  className="text-xs"
                >
                  {tab.label} ({tab.count})
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Fee Records Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <IndianRupee className="h-5 w-5 mr-2" />
              Fee Records ({filteredFees.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-4 font-medium text-gray-900">Student Details</th>
                    <th className="text-left p-4 font-medium text-gray-900">Room & Block</th>
                    <th className="text-left p-4 font-medium text-gray-900">Fee Details</th>
                    <th className="text-left p-4 font-medium text-gray-900">Payment Status</th>
                    <th className="text-left p-4 font-medium text-gray-900">Due Date</th>
                    <th className="text-left p-4 font-medium text-gray-900">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredFees.map((fee) => (
                    <tr key={fee.id} className="border-b hover:bg-gray-50">
                      <td className="p-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <User className="h-5 w-5 text-blue-600" />
                          </div>
                          <div>
                            <div className="font-medium text-gray-900">{fee.studentName}</div>
                            <div className="text-sm text-gray-500">{fee.studentId}</div>
                            <div className="text-sm text-gray-500">{fee.semester}</div>
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-1">
                          <div className="font-medium text-gray-900">{fee.roomNumber}</div>
                          <div className="text-sm text-gray-500">{fee.hostelBlock}</div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-1">
                          <div className="font-medium text-gray-900">
                            ₹{fee.monthlyFee.toLocaleString()}/month
                          </div>
                          <div className="text-sm text-gray-500">
                            Paid: ₹{fee.amountPaid.toLocaleString()}
                          </div>
                          <div className="text-sm text-gray-500">
                            Balance: ₹{fee.balance.toLocaleString()}
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-2">
                          {getStatusBadge(fee.status)}
                          {fee.paymentMethod && (
                            <div className="text-xs text-gray-500">
                              via {fee.paymentMethod}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-1">
                          <div className="text-sm font-medium">
                            {new Date(fee.dueDate).toLocaleDateString()}
                          </div>
                          {fee.paymentDate && (
                            <div className="text-sm text-gray-500">
                              Paid: {new Date(fee.paymentDate).toLocaleDateString()}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => viewFeeDetails(fee)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          {fee.status !== "Paid" && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => recordPayment(fee.id)}
                            >
                              <CreditCard className="h-4 w-4" />
                            </Button>
                          )}
                          {fee.status === "Paid" && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => generateReceipt(fee.id)}
                            >
                              <Receipt className="h-4 w-4" />
                            </Button>
                          )}
                          {(fee.status === "Pending" || fee.status === "Overdue") && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => sendReminder(fee.id)}
                            >
                              <AlertTriangle className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}