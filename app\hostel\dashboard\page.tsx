"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Building,
  Bed,
  Users,
  IndianRupee,
  CheckCircle,
  AlertTriangle,
  User,
  Phone,
  Calendar,
  Plus
} from "lucide-react";

export default function HostelDashboard() {
  const [user, setUser] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "hostel_manager") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock hostel data
  const hostelStats = {
    totalHostels: 4,
    totalRooms: 120,
    occupiedRooms: 98,
    totalBeds: 240,
    occupiedBeds: 185,
    monthlyRevenue: 2775000,
  };

  const hostelOverview = [
    {
      id: "1",
      name: "Boys Hostel Block A",
      type: "boys",
      warden: "Mr. Ramesh Sharma",
      totalRooms: 30,
      occupiedRooms: 28,
      totalBeds: 60,
      occupiedBeds: 52,
      occupancyRate: 87,
    },
    {
      id: "2",
      name: "Girls Hostel Block B",
      type: "girls",
      warden: "Mrs. Priya Gupta",
      totalRooms: 25,
      occupiedRooms: 23,
      totalBeds: 50,
      occupiedBeds: 45,
      occupancyRate: 90,
    },
    {
      id: "3",
      name: "Boys Hostel Block C",
      type: "boys",
      warden: "Mr. Suresh Kumar",
      totalRooms: 35,
      occupiedRooms: 30,
      totalBeds: 70,
      occupiedBeds: 58,
      occupancyRate: 83,
    },
  ];

  const recentAllocations = [
    {
      id: "1",
      studentName: "Alex Johnson",
      hostel: "Boys Hostel Block A",
      room: "A201",
      allocationDate: "2024-01-15",
      status: "allocated",
      monthlyFee: 15000,
    },
    {
      id: "2",
      studentName: "Maria Garcia",
      hostel: "Girls Hostel Block B",
      room: "B105",
      allocationDate: "2024-01-14",
      status: "allocated",
      monthlyFee: 15000,
    },
    {
      id: "3",
      studentName: "David Chen",
      hostel: "Boys Hostel Block C",
      room: "C301",
      allocationDate: "2024-01-13",
      status: "pending",
      monthlyFee: 15000,
    },
  ];

  const maintenanceRequests = [
    {
      id: "1",
      room: "A105",
      hostel: "Boys Hostel Block A",
      issue: "Air conditioning not working",
      reportedBy: "John Doe",
      reportedDate: "2024-01-15",
      priority: "high",
      status: "pending",
    },
    {
      id: "2",
      room: "B203",
      hostel: "Girls Hostel Block B",
      issue: "Plumbing issue in bathroom",
      reportedBy: "Alice Smith",
      reportedDate: "2024-01-14",
      priority: "medium",
      status: "in_progress",
    },
    {
      id: "3",
      room: "C102",
      hostel: "Boys Hostel Block C",
      issue: "Window lock broken",
      reportedBy: "Mike Johnson",
      reportedDate: "2024-01-13",
      priority: "low",
      status: "completed",
    },
  ];

  const upcomingTasks = [
    {
      task: "Monthly room inspection",
      hostel: "All Hostels",
      dueDate: "2024-01-25",
      priority: "high",
    },
    {
      task: "Fee collection reminder",
      hostel: "Boys Hostel Block A",
      dueDate: "2024-01-20",
      priority: "medium",
    },
    {
      task: "Maintenance schedule review",
      hostel: "Girls Hostel Block B",
      dueDate: "2024-01-22",
      priority: "low",
    },
  ];

  const statusColors = {
    allocated: "text-green-600 bg-green-100",
    pending: "text-yellow-600 bg-yellow-100",
    in_progress: "text-blue-600 bg-blue-100",
    completed: "text-gray-600 bg-gray-100",
  };

  const occupancyRate = Math.round((hostelStats.occupiedBeds / hostelStats.totalBeds) * 100);

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Welcome Header */}
        <div className="bg-gradient-to-r from-indigo-500 to-purple-600 rounded-lg p-6 text-white">
          <h1 className="text-2xl font-bold mb-2">
            Welcome, {user.firstName}!
          </h1>
          <p className="text-indigo-100">
            Manage hostel accommodations and student welfare
          </p>
        </div>

        {/* Hostel Stats */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Building className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {hostelStats.totalHostels}
                  </div>
                  <p className="text-sm text-gray-600">Total Hostels</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Bed className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {hostelStats.occupiedBeds}
                  </div>
                  <p className="text-sm text-gray-600">Occupied Beds</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {occupancyRate}%
                  </div>
                  <p className="text-sm text-gray-600">Occupancy Rate</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <IndianRupee className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    ₹{(hostelStats.monthlyRevenue / 100000).toFixed(1)}L
                  </div>
                  <p className="text-sm text-gray-600">Monthly Revenue</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Grid */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Hostel Overview */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center">
                  <Building className="h-5 w-5 mr-2" />
                  Hostel Overview
                </CardTitle>
                <Button variant="outline" size="sm">
                  View All
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {hostelOverview.map((hostel) => (
                  <div key={hostel.id} className="flex items-center justify-between p-4 border rounded-lg">
                    <div className="flex items-center space-x-4">
                      <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                        <Building className="h-6 w-6 text-blue-600" />
                      </div>
                      <div>
                        <div className="font-medium text-gray-900">{hostel.name}</div>
                        <div className="text-sm text-gray-500">
                          Warden: {hostel.warden}
                        </div>
                        <div className="text-xs text-gray-400">
                          {hostel.type.charAt(0).toUpperCase() + hostel.type.slice(1)} Hostel
                        </div>
                      </div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm">
                        <div className="font-medium text-blue-600">
                          {hostel.occupiedRooms}/{hostel.totalRooms} rooms
                        </div>
                        <div className="text-gray-500">
                          {hostel.occupiedBeds}/{hostel.totalBeds} beds
                        </div>
                        <div className="text-xs text-green-600 font-medium">
                          {hostel.occupancyRate}% occupied
                        </div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button className="w-full justify-start" variant="outline">
                <Plus className="h-4 w-4 mr-2" />
                Allocate Room
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <User className="h-4 w-4 mr-2" />
                Student Check-in
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <AlertTriangle className="h-4 w-4 mr-2" />
                Maintenance Request
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <IndianRupee className="h-4 w-4 mr-2" />
                Fee Collection
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <Calendar className="h-4 w-4 mr-2" />
                Room Inspection
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Recent Allocations and Maintenance */}
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Recent Allocations */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Users className="h-5 w-5 mr-2" />
                Recent Allocations
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {recentAllocations.map((allocation) => (
                  <div key={allocation.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div className="font-medium text-gray-900">{allocation.studentName}</div>
                      <div className="text-sm text-gray-500">
                        {allocation.hostel} • Room {allocation.room}
                      </div>
                      <div className="text-xs text-gray-400">
                        Allocated: {allocation.allocationDate}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusColors[allocation.status as keyof typeof statusColors]}`}>
                        {allocation.status.toUpperCase()}
                      </div>
                      <div className="text-xs text-gray-500 mt-1">
                        ₹{allocation.monthlyFee.toLocaleString()}/month
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Maintenance Requests */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertTriangle className="h-5 w-5 mr-2" />
                Maintenance Requests
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {maintenanceRequests.map((request) => (
                  <div key={request.id} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div className="font-medium text-gray-900">{request.issue}</div>
                      <div className="text-sm text-gray-500">
                        {request.hostel} • Room {request.room}
                      </div>
                      <div className="text-xs text-gray-400">
                        Reported by: {request.reportedBy} • {request.reportedDate}
                      </div>
                    </div>
                    <div className="text-right">
                      <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusColors[request.status as keyof typeof statusColors]}`}>
                        {request.status.replace('_', ' ').toUpperCase()}
                      </div>
                      <div className={`text-xs px-2 py-1 rounded-full mt-1 ${
                        request.priority === "high" ? "bg-red-100 text-red-600" :
                        request.priority === "medium" ? "bg-yellow-100 text-yellow-600" : "bg-green-100 text-green-600"
                      }`}>
                        {request.priority.toUpperCase()}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Upcoming Tasks */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Calendar className="h-5 w-5 mr-2" />
              Upcoming Tasks
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3">
              {upcomingTasks.map((task, index) => (
                <div key={index} className="p-4 border rounded-lg">
                  <div className="font-medium text-gray-900 mb-2">{task.task}</div>
                  <div className="text-sm text-gray-500 mb-2">{task.hostel}</div>
                  <div className="flex items-center justify-between">
                    <div className="text-sm font-medium text-blue-600">
                      Due: {task.dueDate}
                    </div>
                    <div className={`text-xs px-2 py-1 rounded-full ${
                      task.priority === "high" ? "bg-red-100 text-red-600" :
                      task.priority === "medium" ? "bg-yellow-100 text-yellow-600" : "bg-green-100 text-green-600"
                    }`}>
                      {task.priority.toUpperCase()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Hostel Analytics */}
        <Card>
          <CardHeader>
            <CardTitle>Hostel Analytics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 md:grid-cols-3">
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 mb-2">
                  {occupancyRate}%
                </div>
                <div className="text-sm text-gray-600">Overall Occupancy</div>
                <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-green-600 h-2 rounded-full" style={{ width: `${occupancyRate}%` }} />
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">
                  ₹{Math.round(hostelStats.monthlyRevenue / hostelStats.occupiedBeds / 1000)}K
                </div>
                <div className="text-sm text-gray-600">Revenue per Student</div>
                <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-600 h-2 rounded-full w-[75%]" />
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600 mb-2">
                  {hostelStats.totalBeds - hostelStats.occupiedBeds}
                </div>
                <div className="text-sm text-gray-600">Available Beds</div>
                <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-purple-600 h-2 rounded-full w-[23%]" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
