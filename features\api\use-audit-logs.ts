import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { client } from "@/lib/hono";
import { toast } from "sonner";

interface AuditLogFilters {
  page?: number;
  limit?: number;
  action?: string;
  userId?: string;
  startDate?: string;
  endDate?: string;
  search?: string;
}

// Get audit logs with filtering
export const useGetAuditLogs = (filters: AuditLogFilters = {}) => {
  const { page = 1, limit = 20, ...otherFilters } = filters;
  
  return useQuery({
    queryKey: ["audit-logs", page, limit, otherFilters],
    queryFn: async () => {
      const queryParams: Record<string, string> = {
        page: page.toString(),
        limit: limit.toString(),
      };

      // Add other filters if they exist
      Object.entries(otherFilters).forEach(([key, value]) => {
        if (value) {
          queryParams[key] = value.toString();
        }
      });

      const response = await client.api["audit-logs"].$get({
        query: queryParams,
      });

      if (!response.ok) {
        const errorData = await response.json() as { error?: string };
        throw new Error(errorData.error || "Failed to fetch audit logs");
      }

      return await response.json();
    },
  });
};

// Create audit log entry
export const useCreateAuditLog = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (data: {
      userId: string;
      action: string;
      targetUserId?: string;
      details?: string;
      ipAddress?: string;
      userAgent?: string;
    }) => {
      const response = await client.api["audit-logs"].$post({
        json: data,
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to create audit log");
      }

      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["audit-logs"] });
      queryClient.invalidateQueries({ queryKey: ["audit-stats"] });
    },
    onError: (error: any) => {
      console.error("Failed to create audit log:", error.message);
    },
  });
};

// Get audit log statistics
export const useGetAuditStats = (timeframe: string = "7d") => {
  return useQuery({
    queryKey: ["audit-stats", timeframe],
    queryFn: async () => {
      const response = await client.api["audit-logs"].stats.$get({
        query: { timeframe },
      });

      if (!response.ok) {
        const errorData = await response.json() as { error?: string };
        throw new Error(errorData.error || "Failed to fetch audit statistics");
      }

      return await response.json();
    },
  });
};

// Get unique actions for filtering
export const useGetAuditActions = () => {
  return useQuery({
    queryKey: ["audit-actions"],
    queryFn: async () => {
      const response = await client.api["audit-logs"].actions.$get();

      if (!response.ok) {
        const errorData = await response.json() as { error?: string };
        throw new Error(errorData.error || "Failed to fetch audit actions");
      }

      return await response.json();
    },
  });
};

// Export audit logs
export const useExportAuditLogs = () => {
  return useMutation({
    mutationFn: async (params: {
      format?: "csv" | "json";
      startDate?: string;
      endDate?: string;
    }) => {
      const queryParams: Record<string, string> = {};
      
      if (params.format) queryParams.format = params.format;
      if (params.startDate) queryParams.startDate = params.startDate;
      if (params.endDate) queryParams.endDate = params.endDate;

      const response = await client.api["audit-logs"].export.$get({
        query: queryParams,
      });

      if (!response.ok) {
        const errorData = await response.json() as { error?: string };
        throw new Error(errorData.error || "Failed to export audit logs");
      }

      if (params.format === "csv") {
        // Handle CSV download
        const blob = await response.blob();
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement("a");
        a.href = url;
        a.download = `audit-logs-${new Date().toISOString().split('T')[0]}.csv`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
        return { success: true };
      } else {
        return await response.json();
      }
    },
    onSuccess: (data, variables) => {
      if (variables.format === "csv") {
        toast.success("Audit logs exported successfully!");
      } else {
        toast.success("Audit logs data retrieved successfully!");
      }
    },
    onError: (error: any) => {
      toast.error(`Failed to export audit logs: ${error.message}`);
    },
  });
};

// Helper function to log user actions
export const useLogUserAction = () => {
  const createAuditLog = useCreateAuditLog();

  return (action: string, details?: string, targetUserId?: string) => {
    const user = JSON.parse(localStorage.getItem("user") || "{}");
    
    if (user.id) {
      createAuditLog.mutate({
        userId: user.id,
        action,
        details,
        targetUserId,
        ipAddress: "127.0.0.1", // In production, get real IP
        userAgent: navigator.userAgent,
      });
    }
  };
};
