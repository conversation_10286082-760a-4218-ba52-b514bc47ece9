"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Users, GraduationCap, BookOpen, TrendingUp } from "lucide-react";
import { useGetDashboardStats } from "@/features/api/use-dashboard";

export function StatsCards() {
  const { data: stats, isLoading, error } = useGetDashboardStats();

  if (isLoading) {
    return (
      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        {[...Array(4)].map((_, i) => (
          <Card key={i}>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Loading...</CardTitle>
              <div className="h-4 w-4 bg-gray-200 rounded animate-pulse" />
            </CardHeader>
            <CardContent>
              <div className="h-8 bg-gray-200 rounded animate-pulse mb-1" />
              <div className="h-4 bg-gray-200 rounded animate-pulse w-3/4" />
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (error) {
    return (
      <div className="text-center py-8">
        <p className="text-red-500">Failed to load dashboard statistics</p>
      </div>
    );
  }

  const cards = [
    {
      title: "Total Students",
      value: stats?.students.total || 0,
      description: `${stats?.students.active || 0} active students`,
      icon: Users,
      color: "text-blue-600",
    },
    {
      title: "Total Teachers",
      value: stats?.teachers.total || 0,
      description: `${stats?.teachers.active || 0} active teachers`,
      icon: GraduationCap,
      color: "text-green-600",
    },
    {
      title: "Total Classes",
      value: stats?.classes.total || 0,
      description: `${stats?.classes.active || 0} active classes`,
      icon: BookOpen,
      color: "text-purple-600",
    },
    {
      title: "Attendance Rate",
      value: `${stats?.attendance.rate || 0}%`,
      description: `${stats?.attendance.present || 0} present today`,
      icon: TrendingUp,
      color: "text-orange-600",
    },
  ];

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {cards.map((card, index) => (
        <Card key={index}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">{card.title}</CardTitle>
            <card.icon className={`h-4 w-4 ${card.color}`} />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{card.value}</div>
            <p className="text-xs text-muted-foreground">{card.description}</p>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
