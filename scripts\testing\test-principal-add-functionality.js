const fs = require('fs');
const path = require('path');

console.log("=== PRINCIPAL ADD FUNCTIONALITY TEST ===\n");

// Academic Leadership pages that should have Add functionality
const academicPages = [
  {
    path: "app/principal/academic/page.tsx",
    route: "/principal/academic",
    expectedFeatures: ["Add Subject", "Add Class", "Add Program", "Dialog", "Form"]
  },
  {
    path: "app/principal/curriculum/page.tsx", 
    route: "/principal/curriculum",
    expectedFeatures: ["Add Curriculum", "Dialog", "Form", "Subject Selection"]
  },
  {
    path: "app/principal/calendar/page.tsx",
    route: "/principal/calendar", 
    expectedFeatures: ["Add Event", "Dialog", "Form", "Date Picker"]
  },
  {
    path: "app/principal/performance/page.tsx",
    route: "/principal/performance",
    expectedFeatures: ["Add New", "Dialog", "Form"]
  }
];

let results = {
  totalPages: academicPages.length,
  pagesWithAdd: 0,
  pagesWithDialog: 0,
  pagesWithForms: 0,
  fullyFunctional: 0,
  details: []
};

console.log(`🔍 Testing Add Functionality in ${academicPages.length} Academic Leadership Pages...\n`);

academicPages.forEach(page => {
  console.log(`📄 Testing: ${page.route}`);
  
  if (!fs.existsSync(page.path)) {
    console.log(`❌ File not found: ${page.path}`);
    results.details.push({
      route: page.route,
      status: 'missing',
      features: []
    });
    return;
  }

  const content = fs.readFileSync(page.path, 'utf8');
  const foundFeatures = [];
  let functionalityScore = 0;

  // Check for Add button functionality
  if (content.includes('Add New') || content.includes('Add Subject') || 
      content.includes('Add Curriculum') || content.includes('Add Event')) {
    foundFeatures.push('Add Button');
    functionalityScore += 20;
    console.log(`  ✅ Add Button: Found`);
  } else {
    console.log(`  ❌ Add Button: Missing`);
  }

  // Check for Dialog component
  if (content.includes('Dialog') && content.includes('DialogContent')) {
    foundFeatures.push('Dialog Component');
    functionalityScore += 25;
    results.pagesWithDialog++;
    console.log(`  ✅ Dialog Component: Implemented`);
  } else {
    console.log(`  ❌ Dialog Component: Missing`);
  }

  // Check for Form elements
  const formElements = ['Input', 'Label', 'Textarea', 'Select'];
  let formElementsFound = 0;
  formElements.forEach(element => {
    if (content.includes(element)) {
      formElementsFound++;
    }
  });
  
  if (formElementsFound >= 3) {
    foundFeatures.push('Form Elements');
    functionalityScore += 25;
    results.pagesWithForms++;
    console.log(`  ✅ Form Elements: ${formElementsFound}/${formElements.length} found`);
  } else {
    console.log(`  ❌ Form Elements: Only ${formElementsFound}/${formElements.length} found`);
  }

  // Check for state management
  if (content.includes('useState') && content.includes('formData')) {
    foundFeatures.push('State Management');
    functionalityScore += 15;
    console.log(`  ✅ State Management: Implemented`);
  } else {
    console.log(`  ❌ State Management: Missing`);
  }

  // Check for submit functionality
  if (content.includes('handleSubmit') || content.includes('onSubmit')) {
    foundFeatures.push('Submit Handler');
    functionalityScore += 15;
    console.log(`  ✅ Submit Handler: Implemented`);
  } else {
    console.log(`  ❌ Submit Handler: Missing`);
  }

  // Determine overall status
  let status = 'basic';
  if (functionalityScore >= 80) {
    status = 'fully-functional';
    results.fullyFunctional++;
  } else if (functionalityScore >= 50) {
    status = 'partially-functional';
  }

  if (foundFeatures.length > 0) {
    results.pagesWithAdd++;
  }

  results.details.push({
    route: page.route,
    status,
    score: functionalityScore,
    features: foundFeatures
  });

  console.log(`  📊 Functionality Score: ${functionalityScore}/100 (${status})\n`);
});

// Summary
console.log(`📊 ADD FUNCTIONALITY SUMMARY:`);
console.log(`✅ Pages with Add Features: ${results.pagesWithAdd}/${results.totalPages} (${Math.round((results.pagesWithAdd/results.totalPages)*100)}%)`);
console.log(`🔧 Pages with Dialogs: ${results.pagesWithDialog}/${results.totalPages} (${Math.round((results.pagesWithDialog/results.totalPages)*100)}%)`);
console.log(`📝 Pages with Forms: ${results.pagesWithForms}/${results.totalPages} (${Math.round((results.pagesWithForms/results.totalPages)*100)}%)`);
console.log(`🎯 Fully Functional: ${results.fullyFunctional}/${results.totalPages} (${Math.round((results.fullyFunctional/results.totalPages)*100)}%)`);

// Detailed breakdown
console.log(`\n📋 DETAILED BREAKDOWN:`);
results.details.forEach(detail => {
  const statusIcon = detail.status === 'fully-functional' ? '🎉' : 
                    detail.status === 'partially-functional' ? '⚠️' : '❌';
  console.log(`${statusIcon} ${detail.route} - ${detail.status} (${detail.score || 0}/100)`);
  if (detail.features.length > 0) {
    console.log(`   Features: ${detail.features.join(', ')}`);
  }
});

// Test specific Add functionality features
console.log(`\n🔍 TESTING SPECIFIC ADD FEATURES:`);

// Test Academic page multiple add types
const academicContent = fs.existsSync('app/principal/academic/page.tsx') ? 
  fs.readFileSync('app/principal/academic/page.tsx', 'utf8') : '';

if (academicContent.includes('handleAddNew') && academicContent.includes('addType')) {
  console.log(`✅ Academic Page: Multiple add types supported (Subject, Class, Program)`);
} else {
  console.log(`❌ Academic Page: Multiple add types not implemented`);
}

// Test Curriculum page specific fields
const curriculumContent = fs.existsSync('app/principal/curriculum/page.tsx') ? 
  fs.readFileSync('app/principal/curriculum/page.tsx', 'utf8') : '';

if (curriculumContent.includes('objectives') && curriculumContent.includes('prerequisites')) {
  console.log(`✅ Curriculum Page: Advanced form fields (objectives, prerequisites)`);
} else {
  console.log(`❌ Curriculum Page: Advanced form fields missing`);
}

// Test Calendar page date/time functionality
const calendarContent = fs.existsSync('app/principal/calendar/page.tsx') ? 
  fs.readFileSync('app/principal/calendar/page.tsx', 'utf8') : '';

if (calendarContent.includes('type="date"') && calendarContent.includes('type="time"')) {
  console.log(`✅ Calendar Page: Date and time pickers implemented`);
} else {
  console.log(`❌ Calendar Page: Date and time pickers missing`);
}

// Overall assessment
console.log(`\n🎯 OVERALL ASSESSMENT:`);
const overallScore = Math.round((results.fullyFunctional / results.totalPages) * 100);

if (overallScore >= 75) {
  console.log(`🎉 EXCELLENT: ${overallScore}% of academic pages have fully functional Add features!`);
} else if (overallScore >= 50) {
  console.log(`👍 GOOD: ${overallScore}% of academic pages have fully functional Add features.`);
} else if (overallScore >= 25) {
  console.log(`⚠️ NEEDS IMPROVEMENT: Only ${overallScore}% of academic pages have fully functional Add features.`);
} else {
  console.log(`❌ CRITICAL: Only ${overallScore}% of academic pages have fully functional Add features.`);
}

console.log(`\n📈 RECOMMENDATIONS:`);
if (results.fullyFunctional < results.totalPages) {
  console.log(`1. Complete Add functionality for ${results.totalPages - results.fullyFunctional} remaining page(s)`);
}
if (results.pagesWithDialog < results.totalPages) {
  console.log(`2. Add Dialog components to ${results.totalPages - results.pagesWithDialog} page(s)`);
}
if (results.pagesWithForms < results.totalPages) {
  console.log(`3. Implement form elements in ${results.totalPages - results.pagesWithForms} page(s)`);
}

console.log(`\n🚀 HOW TO TEST:`);
console.log(`1. Login as Principal: <EMAIL> / principal123`);
console.log(`2. Navigate to Academic Leadership pages`);
console.log(`3. Click "Add New" or "Add [Item]" buttons`);
console.log(`4. Fill out the forms and submit`);
console.log(`5. Verify success messages and data refresh`);

console.log(`\n🎯 WORKING ADD FEATURES:`);
results.details.forEach(detail => {
  if (detail.status === 'fully-functional') {
    console.log(`✅ ${detail.route} - Ready for testing`);
  }
});

// Save detailed report
const report = {
  timestamp: new Date().toISOString(),
  summary: {
    totalPages: results.totalPages,
    pagesWithAdd: results.pagesWithAdd,
    pagesWithDialog: results.pagesWithDialog,
    pagesWithForms: results.pagesWithForms,
    fullyFunctional: results.fullyFunctional,
    overallScore
  },
  pageDetails: results.details,
  testInstructions: {
    loginCredentials: {
      email: "<EMAIL>",
      password: "principal123"
    },
    testSteps: [
      "Login as Principal",
      "Navigate to Academic Leadership pages",
      "Test Add functionality",
      "Verify form submission",
      "Check success messages"
    ]
  }
};

fs.writeFileSync('principal-add-functionality-report.json', JSON.stringify(report, null, 2));
console.log(`\n📊 Detailed report saved to: principal-add-functionality-report.json`);
