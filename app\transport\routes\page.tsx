"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  MapPin,
  Search,
  Filter,
  Download,
  Plus,
  Eye,
  Edit,
  Trash2,
  Route,
  Clock,
  Users,
  Bus,
  Navigation,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Calendar,
  IndianRupee,
} from "lucide-react";

export default function TransportRoutes() {
  const [user, setUser] = useState<any>(null);
  const [selectedTab, setSelectedTab] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedRoute, setSelectedRoute] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "transport_manager") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  const routeStats = {
    totalRoutes: 8,
    activeRoutes: 6,
    inactiveRoutes: 2,
    totalStudents: 456,
    totalDistance: 245,
    avgStudentsPerRoute: 57,
  };

  const mockRoutes = [
    {
      id: "RT001",
      routeName: "Route A - Central Delhi",
      routeCode: "RT-A",
      startPoint: "School Campus",
      endPoint: "Connaught Place",
      totalDistance: 25.5,
      estimatedTime: "45 mins",
      vehicleAssigned: "DL-1CA-1234",
      driverName: "Rajesh Kumar",
      studentsCount: 42,
      maxCapacity: 45,
      status: "Active",
      stops: [
        { name: "School Campus", time: "07:00 AM", students: 0 },
        { name: "Karol Bagh", time: "07:15 AM", students: 12 },
        { name: "Rajouri Garden", time: "07:25 AM", students: 15 },
        { name: "Connaught Place", time: "07:45 AM", students: 15 },
      ],
      monthlyFee: 3500,
      operatingDays: "Mon-Sat",
    },
    {
      id: "RT002",
      routeName: "Route B - South Delhi",
      routeCode: "RT-B",
      startPoint: "School Campus",
      endPoint: "Greater Kailash",
      totalDistance: 18.2,
      estimatedTime: "35 mins",
      vehicleAssigned: "DL-1CB-5678",
      driverName: "Suresh Singh",
      studentsCount: 38,
      maxCapacity: 50,
      status: "Active",
      stops: [
        { name: "School Campus", time: "07:00 AM", students: 0 },
        { name: "Lajpat Nagar", time: "07:12 AM", students: 18 },
        { name: "Greater Kailash", time: "07:35 AM", students: 20 },
      ],
      monthlyFee: 3200,
      operatingDays: "Mon-Sat",
    },
    {
      id: "RT003",
      routeName: "Route C - East Delhi",
      routeCode: "RT-C",
      startPoint: "School Campus",
      endPoint: "Preet Vihar",
      totalDistance: 22.8,
      estimatedTime: "40 mins",
      vehicleAssigned: "DL-1CD-3456",
      driverName: "Amit Sharma",
      studentsCount: 35,
      maxCapacity: 40,
      status: "Active",
      stops: [
        { name: "School Campus", time: "07:00 AM", students: 0 },
        { name: "Laxmi Nagar", time: "07:18 AM", students: 20 },
        { name: "Preet Vihar", time: "07:40 AM", students: 15 },
      ],
      monthlyFee: 3300,
      operatingDays: "Mon-Sat",
    },
    {
      id: "RT004",
      routeName: "Route D - West Delhi",
      routeCode: "RT-D",
      startPoint: "School Campus",
      endPoint: "Janakpuri",
      totalDistance: 28.5,
      estimatedTime: "50 mins",
      vehicleAssigned: "Not Assigned",
      driverName: "Not Assigned",
      studentsCount: 0,
      maxCapacity: 55,
      status: "Inactive",
      stops: [
        { name: "School Campus", time: "07:00 AM", students: 0 },
        { name: "Tilak Nagar", time: "07:20 AM", students: 0 },
        { name: "Janakpuri", time: "07:50 AM", students: 0 },
      ],
      monthlyFee: 3800,
      operatingDays: "Mon-Sat",
    },
  ];

  const filteredRoutes = mockRoutes.filter((route) => {
    const matchesSearch = route.routeName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         route.routeCode.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         route.driverName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         route.vehicleAssigned.toLowerCase().includes(searchTerm.toLowerCase());

    if (selectedTab === "all") return matchesSearch;
    if (selectedTab === "active") return matchesSearch && route.status === "Active";
    if (selectedTab === "inactive") return matchesSearch && route.status === "Inactive";

    return matchesSearch;
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Active":
        return <Badge className="bg-green-100 text-green-800">Active</Badge>;
      case "Inactive":
        return <Badge className="bg-red-100 text-red-800">Inactive</Badge>;
      case "Maintenance":
        return <Badge className="bg-yellow-100 text-yellow-800">Maintenance</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }
  };

  const getOccupancyBadge = (current: number, max: number) => {
    const percentage = (current / max) * 100;
    if (percentage >= 90) {
      return <Badge variant="outline" className="text-red-600 border-red-200">Full</Badge>;
    } else if (percentage >= 70) {
      return <Badge variant="outline" className="text-yellow-600 border-yellow-200">High</Badge>;
    } else {
      return <Badge variant="outline" className="text-green-600 border-green-200">Available</Badge>;
    }
  };

  const viewRouteDetails = (route: any) => {
    setSelectedRoute(route);
  };

  const editRoute = (routeId: string) => {
    alert(`Editing route ${routeId}`);
  };

  const assignVehicle = (routeId: string) => {
    alert(`Assigning vehicle to route ${routeId}`);
  };

  const optimizeRoute = (routeId: string) => {
    alert(`Optimizing route ${routeId}`);
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Transport Route Management</h1>
            <p className="text-gray-600">Manage school transport routes and optimize travel paths</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Routes
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Route
            </Button>
          </div>
        </div>

        {/* Route Statistics */}
        <div className="grid gap-4 md:grid-cols-3 lg:grid-cols-6">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Route className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {routeStats.totalRoutes}
                  </div>
                  <p className="text-sm text-gray-600">Total Routes</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {routeStats.activeRoutes}
                  </div>
                  <p className="text-sm text-gray-600">Active Routes</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <XCircle className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-red-600">
                    {routeStats.inactiveRoutes}
                  </div>
                  <p className="text-sm text-gray-600">Inactive Routes</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {routeStats.totalStudents}
                  </div>
                  <p className="text-sm text-gray-600">Total Students</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Navigation className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    {routeStats.totalDistance}
                  </div>
                  <p className="text-sm text-gray-600">Total KM</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Bus className="h-8 w-8 text-indigo-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-indigo-600">
                    {routeStats.avgStudentsPerRoute}
                  </div>
                  <p className="text-sm text-gray-600">Avg/Route</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filter */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search by route name, code, driver, or vehicle..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </div>

            {/* Tabs */}
            <div className="flex flex-wrap gap-2 mt-4">
              {[
                { key: "all", label: "All Routes", count: routeStats.totalRoutes },
                { key: "active", label: "Active", count: routeStats.activeRoutes },
                { key: "inactive", label: "Inactive", count: routeStats.inactiveRoutes },
              ].map((tab) => (
                <Button
                  key={tab.key}
                  variant={selectedTab === tab.key ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedTab(tab.key)}
                  className="text-xs"
                >
                  {tab.label} ({tab.count})
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Routes Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Route className="h-5 w-5 mr-2" />
              Route Directory ({filteredRoutes.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-4 font-medium text-gray-900">Route Details</th>
                    <th className="text-left p-4 font-medium text-gray-900">Vehicle & Driver</th>
                    <th className="text-left p-4 font-medium text-gray-900">Capacity</th>
                    <th className="text-left p-4 font-medium text-gray-900">Distance & Time</th>
                    <th className="text-left p-4 font-medium text-gray-900">Status</th>
                    <th className="text-left p-4 font-medium text-gray-900">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredRoutes.map((route) => (
                    <tr key={route.id} className="border-b hover:bg-gray-50">
                      <td className="p-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center">
                            <MapPin className="h-6 w-6 text-blue-600" />
                          </div>
                          <div>
                            <div className="font-medium text-gray-900">{route.routeName}</div>
                            <div className="text-sm text-gray-500">{route.routeCode}</div>
                            <div className="text-sm text-gray-500">{route.startPoint} → {route.endPoint}</div>
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-1">
                          <div className="font-medium text-gray-900">{route.vehicleAssigned}</div>
                          <div className="text-sm text-gray-500">{route.driverName}</div>
                          <div className="text-xs text-gray-400">ID: {route.id}</div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-2">
                          <div className="font-medium text-gray-900">
                            {route.studentsCount}/{route.maxCapacity}
                          </div>
                          {getOccupancyBadge(route.studentsCount, route.maxCapacity)}
                          <div className="text-sm text-gray-500">
                            ₹{route.monthlyFee.toLocaleString()}/month
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-1">
                          <div className="text-sm">
                            <span className="font-medium">Distance:</span>
                            <div className="text-gray-500">{route.totalDistance} km</div>
                          </div>
                          <div className="text-sm">
                            <span className="font-medium">Time:</span>
                            <div className="text-gray-500">{route.estimatedTime}</div>
                          </div>
                          <div className="text-sm">
                            <span className="font-medium">Stops:</span>
                            <div className="text-gray-500">{route.stops.length} stops</div>
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-2">
                          {getStatusBadge(route.status)}
                          <div className="text-xs text-gray-500">
                            {route.operatingDays}
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => viewRouteDetails(route)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => editRoute(route.id)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          {route.vehicleAssigned === "Not Assigned" && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => assignVehicle(route.id)}
                            >
                              <Bus className="h-4 w-4" />
                            </Button>
                          )}
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => optimizeRoute(route.id)}
                          >
                            <Navigation className="h-4 w-4" />
                          </Button>
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}