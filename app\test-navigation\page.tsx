"use client";

import { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { 
  getNavigationForRole, 
  getDashboardPathForRole,
  roleNavigationConfig,
  type NavigationItem 
} from "@/lib/navigation-config";
import { 
  User, 
  Shield, 
  GraduationCap, 
  Award, 
  Home, 
  Users, 
  DollarSign, 
  Library, 
  Bus, 
  Building,
  CheckCircle,
  AlertTriangle,
  Info
} from "lucide-react";

const roleIcons: Record<string, any> = {
  super_admin: Shield,
  admin: Shield,
  teacher: GraduationCap,
  student: Award,
  parent: Home,
  admission_officer: Users,
  finance_manager: DollarSign,
  librarian: Library,
  transport_manager: Bus,
  hostel_manager: Building,
};

const roleColors: Record<string, string> = {
  super_admin: "bg-red-500",
  admin: "bg-purple-500",
  teacher: "bg-blue-500",
  student: "bg-green-500",
  parent: "bg-orange-500",
  admission_officer: "bg-teal-500",
  finance_manager: "bg-yellow-500",
  librarian: "bg-indigo-500",
  transport_manager: "bg-cyan-500",
  hostel_manager: "bg-pink-500",
};

export default function TestNavigationPage() {
  const [currentUser, setCurrentUser] = useState<any>(null);
  const [selectedRole, setSelectedRole] = useState<string>("student");
  const [mounted, setMounted] = useState(false);

  useEffect(() => {
    setMounted(true);
    const userData = localStorage.getItem("user");
    if (userData) {
      setCurrentUser(JSON.parse(userData));
    }
  }, []);

  const simulateRole = (role: string) => {
    const mockUser = {
      id: `test-${role}`,
      firstName: "Test",
      lastName: "User",
      email: `test.${role}@school.edu`,
      role: role,
    };
    
    localStorage.setItem("user", JSON.stringify(mockUser));
    setCurrentUser(mockUser);
    setSelectedRole(role);
  };

  const renderNavigationTree = (items: NavigationItem[], level: number = 0) => {
    return (
      <div className={`space-y-2 ${level > 0 ? 'ml-6 border-l border-gray-200 pl-4' : ''}`}>
        {items.map((item, index) => {
          const IconComponent = item.icon;
          return (
            <div key={index} className="space-y-2">
              <div className="flex items-center space-x-3 p-2 bg-gray-50 rounded-lg">
                <IconComponent className="h-4 w-4 text-gray-600" />
                <div className="flex-1">
                  <div className="font-medium text-gray-900">{item.name}</div>
                  <div className="text-sm text-gray-500">{item.href}</div>
                  {item.description && (
                    <div className="text-xs text-gray-400">{item.description}</div>
                  )}
                </div>
                {item.badge && (
                  <Badge variant="secondary" className="text-xs">
                    {item.badge}
                  </Badge>
                )}
              </div>
              {item.children && item.children.length > 0 && (
                <div className="ml-4">
                  {renderNavigationTree(item.children, level + 1)}
                </div>
              )}
            </div>
          );
        })}
      </div>
    );
  };

  if (!mounted) {
    return <div>Loading...</div>;
  }

  const navigation = getNavigationForRole(selectedRole);
  const dashboardPath = getDashboardPathForRole(selectedRole);
  const RoleIcon = roleIcons[selectedRole] || User;
  const roleColor = roleColors[selectedRole] || "bg-gray-500";

  return (
    <div className="min-h-screen bg-gray-50 p-6">
      <div className="max-w-6xl mx-auto space-y-6">
        {/* Header */}
        <div className="bg-white rounded-lg p-6 shadow-sm">
          <h1 className="text-2xl font-bold text-gray-900 mb-2">
            Role-Based Navigation Test
          </h1>
          <p className="text-gray-600">
            Test navigation menus for different user roles
          </p>
        </div>

        {/* Current User Status */}
        {currentUser && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <CheckCircle className="h-5 w-5 mr-2 text-green-600" />
                Current User
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="flex items-center space-x-4">
                <div className={`w-12 h-12 ${roleColor} rounded-full flex items-center justify-center`}>
                  <RoleIcon className="h-6 w-6 text-white" />
                </div>
                <div>
                  <div className="font-medium text-gray-900">
                    {currentUser.firstName} {currentUser.lastName}
                  </div>
                  <div className="text-sm text-gray-500">{currentUser.email}</div>
                  <div className="text-sm text-gray-400 capitalize">
                    {currentUser.role.replace('_', ' ')}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Role Selector */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <User className="h-5 w-5 mr-2" />
              Test Different Roles
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-3 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
              {roleNavigationConfig.map((config) => {
                const RoleIcon = roleIcons[config.role] || User;
                const roleColor = roleColors[config.role] || "bg-gray-500";
                const isSelected = selectedRole === config.role;
                
                return (
                  <Button
                    key={config.role}
                    variant={isSelected ? "default" : "outline"}
                    className={`h-auto p-4 flex flex-col items-center space-y-2 ${
                      isSelected ? roleColor : ""
                    }`}
                    onClick={() => simulateRole(config.role)}
                  >
                    <RoleIcon className="h-6 w-6" />
                    <div className="text-center">
                      <div className="font-medium text-sm">
                        {config.role.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                      </div>
                      <div className="text-xs opacity-75">
                        {config.navigation.length} items
                      </div>
                    </div>
                  </Button>
                );
              })}
            </div>
          </CardContent>
        </Card>

        {/* Navigation Preview */}
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Navigation Menu */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <RoleIcon className="h-5 w-5 mr-2" />
                Navigation Menu for {selectedRole.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex items-center justify-between p-3 bg-blue-50 rounded-lg">
                  <span className="font-medium text-blue-900">Dashboard Path:</span>
                  <code className="text-sm bg-white px-2 py-1 rounded border">
                    {dashboardPath}
                  </code>
                </div>
                
                <div className="space-y-3">
                  <h4 className="font-medium text-gray-900">Menu Items:</h4>
                  {navigation.length > 0 ? (
                    renderNavigationTree(navigation)
                  ) : (
                    <div className="text-center py-8 text-gray-500">
                      <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
                      No navigation items configured for this role
                    </div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Role Statistics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Info className="h-5 w-5 mr-2" />
                Role Statistics
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid gap-4 md:grid-cols-2">
                  <div className="text-center p-4 bg-gray-50 rounded-lg">
                    <div className="text-2xl font-bold text-gray-900">
                      {navigation.length}
                    </div>
                    <div className="text-sm text-gray-600">Main Menu Items</div>
                  </div>
                  
                  <div className="text-center p-4 bg-gray-50 rounded-lg">
                    <div className="text-2xl font-bold text-gray-900">
                      {navigation.reduce((acc, item) => acc + (item.children?.length || 0), 0)}
                    </div>
                    <div className="text-sm text-gray-600">Sub Menu Items</div>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium text-gray-900">All Configured Roles:</h4>
                  <div className="flex flex-wrap gap-2">
                    {roleNavigationConfig.map((config) => {
                      const RoleIcon = roleIcons[config.role] || User;
                      const roleColor = roleColors[config.role] || "bg-gray-500";
                      
                      return (
                        <Badge
                          key={config.role}
                          variant="outline"
                          className="flex items-center space-x-1"
                        >
                          <div className={`w-3 h-3 ${roleColor} rounded-full`} />
                          <span>{config.role.replace('_', ' ')}</span>
                        </Badge>
                      );
                    })}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Instructions */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Info className="h-5 w-5 mr-2" />
              Test Instructions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 text-sm text-gray-600">
              <p>1. Click on different role buttons to simulate different user types</p>
              <p>2. Observe how the navigation menu changes for each role</p>
              <p>3. Check that each role has appropriate menu items for their responsibilities</p>
              <p>4. Verify that dashboard paths are correct for each role</p>
              <p>5. Test the actual navigation by logging in as different users</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}
