import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { client } from "@/lib/hono";
import { toast } from "sonner";

// Get all students with pagination and filters
interface GetStudentsParams {
  page?: number;
  limit?: number;
  search?: string;
  grade?: string;
  section?: string;
  status?: string;
}

export const useGetStudents = (params: GetStudentsParams = {}) => {
  return useQuery({
    queryKey: ["students", params],
    queryFn: async () => {
      const response = await client.api.students.$get({
        query: {
          page: params.page?.toString() || "1",
          limit: params.limit?.toString() || "10",
          ...(params.search && { search: params.search }),
          ...(params.grade && { grade: params.grade }),
          ...(params.section && { section: params.section }),
          ...(params.status && { status: params.status }),
        },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch students");
      }

      const { data, meta } = await response.json();
      return { data, meta };
    },
  });
};

// Get student by ID
export const useGetStudent = (id: string) => {
  return useQuery({
    queryKey: ["students", id],
    queryFn: async () => {
      const response = await client.api.students[":id"].$get({
        param: { id },
      });

      if (!response.ok) {
        throw new Error("Failed to fetch student");
      }

      const { data } = await response.json();
      return data;
    },
    enabled: !!id,
  });
};

// Create student mutation
export const useCreateStudent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (json: any) => {
      const response = await client.api.students.$post({ json });

      if (!response.ok) {
        throw new Error("Failed to create student");
      }

      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["students"] });
      toast.success("Student created successfully!");
    },
    onError: (error: any) => {
      toast.error(`Failed to create student: ${error.message}`);
    },
  });
};

// Update student mutation
export const useUpdateStudent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async ({ id, data }: { id: string; data: any }) => {
      const response = await client.api.students[":id"].$put({
        param: { id },
        json: data,
      });

      if (!response.ok) {
        throw new Error("Failed to update student");
      }

      return await response.json();
    },
    onSuccess: (_, { id }) => {
      queryClient.invalidateQueries({ queryKey: ["students"] });
      queryClient.invalidateQueries({ queryKey: ["students", id] });
      toast.success("Student updated successfully!");
    },
    onError: (error: any) => {
      toast.error(`Failed to update student: ${error.message}`);
    },
  });
};

// Delete student mutation
export const useDeleteStudent = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: async (id: string) => {
      const response = await client.api.students[":id"].$delete({
        param: { id },
      });

      if (!response.ok) {
        throw new Error("Failed to delete student");
      }

      return await response.json();
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["students"] });
      toast.success("Student deleted successfully!");
    },
    onError: (error: any) => {
      toast.error(`Failed to delete student: ${error.message}`);
    },
  });
};
