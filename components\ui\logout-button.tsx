"use client";

import { useLogout } from "@/hooks/use-logout";
import { Button } from "@/components/ui/button";
import { LogOut } from "lucide-react";

interface LogoutButtonProps {
  variant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
  showIcon?: boolean;
  showText?: boolean;
  confirmLogout?: boolean;
}

export function LogoutButton({
  variant = "ghost",
  size = "default",
  className = "",
  showIcon = true,
  showText = true,
  confirmLogout = true,
}: LogoutButtonProps) {
  const { logout, logoutWithoutConfirmation } = useLogout();

  const handleLogout = () => {
    if (confirmLogout) {
      logout();
    } else {
      logoutWithoutConfirmation();
    }
  };

  return (
    <Button
      variant={variant}
      size={size}
      className={`text-red-600 hover:text-red-700 hover:bg-red-50 ${className}`}
      onClick={handleLogout}
    >
      {showIcon && <LogOut className="h-4 w-4" />}
      {showIcon && showText && <span className="ml-2" />}
      {showText && "Logout"}
    </Button>
  );
}
