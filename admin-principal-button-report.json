{"timestamp": "2025-06-08T09:44:20.295Z", "scope": "Admin & Principal Pages", "summary": {"totalButtons": 68, "totalFunctionalButtons": 48, "overallFunctionalityRate": 71, "pagesTestedCount": 16, "pagesFoundCount": 16, "excellentPages": 8, "goodPages": 1, "needsWorkPages": 7}, "pageResults": [{"file": "app/admin/dashboard/page.tsx", "buttons": [{"type": "functional", "element": "<Button variant=\"outline\" onClick={() => router.push(\"/admin/reports\")}>\n       ...", "functionality": "onClick handler, router navigation"}, {"type": "functional", "element": "<Button onClick={() => router.push(\"/admin/settings\")}>\n              <Settings ...", "functionality": "onClick handler, router navigation"}, {"type": "functional", "element": "<Button variant=\"outline\" className=\"w-full\" onClick={() => router.push(\"/admin/...", "functionality": "onClick handler, router navigation"}], "issues": [], "summary": {"totalButtons": 3, "functionalButtons": 3, "nonFunctionalButtons": 0, "functionalityRate": 100}}, {"file": "app/admin/students/page.tsx", "buttons": [{"type": "functional", "element": "<Button variant=\"outline\" onClick={clearFilters}>\n              <RefreshCw class...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button variant=\"outline\" onClick={exportStudents} disabled={filteredStudents.le...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button onClick={() => router.push(\"/admin/students/new\")}>\n              <Plus ...", "functionality": "onClick handler, router navigation"}, {"type": "functional", "element": "<Button\n                      variant=\"ghost\"\n                      size=\"sm\"\n  ...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button\n                                variant=\"outline\"\n                      ...", "functionality": "onClick handler, router navigation"}, {"type": "functional", "element": "<Button\n                                variant=\"outline\"\n                      ...", "functionality": "onClick handler, router navigation"}, {"type": "functional", "element": "<Button\n                                variant=\"outline\"\n                      ...", "functionality": "onClick handler"}], "issues": [], "summary": {"totalButtons": 7, "functionalButtons": 7, "nonFunctionalButtons": 0, "functionalityRate": 100}}, {"file": "app/admin/teachers/page.tsx", "buttons": [{"type": "functional", "element": "<Button\n              variant=\"outline\"\n              onClick={() => {\n         ...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button\n              variant=\"outline\"\n              onClick={() => {\n         ...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button onClick={() => router.push(\"/admin/teachers/new\")}>\n              <Plus ...", "functionality": "onClick handler, router navigation"}, {"type": "functional", "element": "<Button\n                  variant=\"outline\"\n                  size=\"sm\"\n        ...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button\n                            variant=\"outline\"\n                          ...", "functionality": "onClick handler, router navigation"}, {"type": "functional", "element": "<Button\n                            variant=\"outline\"\n                          ...", "functionality": "onClick handler, router navigation"}, {"type": "functional", "element": "<Button\n                            variant=\"outline\"\n                          ...", "functionality": "onClick handler"}], "issues": [], "summary": {"totalButtons": 7, "functionalButtons": 7, "nonFunctionalButtons": 0, "functionalityRate": 100}}, {"file": "app/admin/classes/page.tsx", "buttons": [{"type": "functional", "element": "<Button onClick={resetForm}>\n                <Plus className=\"h-4 w-4 mr-2\" />", "functionality": "onClick handler, dialog trigger"}, {"type": "functional", "element": "<Button type=\"button\" variant=\"outline\" onClick={() => setIsDialogOpen(false)}>\n...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button type=\"submit\">\n                    {editingClass ? \"Update Class\" : \"Cre...", "functionality": "form submission"}, {"type": "functional", "element": "<Button onClick={() => setIsDialogOpen(true)}>\n                  <Plus className...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button\n                        variant=\"ghost\"\n                        size=\"sm...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button\n                        variant=\"ghost\"\n                        size=\"sm...", "functionality": "onClick handler"}], "issues": [], "summary": {"totalButtons": 6, "functionalButtons": 6, "nonFunctionalButtons": 0, "functionalityRate": 100}}, {"file": "app/admin/finance/page.tsx", "buttons": [{"type": "functional", "element": "<Button\n              variant=\"outline\"\n              onClick={() => {\n         ...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button\n              variant=\"outline\"\n              onClick={() => router.push...", "functionality": "onClick handler, router navigation"}, {"type": "functional", "element": "<Button\n                  key={index}\n                  variant=\"outline\"\n      ...", "functionality": "onClick handler, router navigation"}], "issues": [], "summary": {"totalButtons": 3, "functionalButtons": 3, "nonFunctionalButtons": 0, "functionalityRate": 100}}, {"file": "app/admin/reports/page.tsx", "buttons": [{"type": "functional", "element": "<Button\n              variant=\"outline\"\n              onClick={() => {\n         ...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button\n              onClick={() => {\n                toast.success(\"Report gen...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button\n                variant=\"outline\"\n                size=\"sm\"\n            ...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button\n                  key={index}\n                  variant=\"outline\"\n      ...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button\n                            size=\"sm\"\n                            varian...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button\n                            size=\"sm\"\n                            varian...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button\n                  variant=\"outline\"\n                  onClick={() => {\n ...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button\n                            variant=\"outline\"\n                          ...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button\n                            variant=\"outline\"\n                          ...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button\n                            variant=\"outline\"\n                          ...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button\n                      variant=\"outline\"\n                      size=\"sm\"\n...", "functionality": "onClick handler"}], "issues": [], "summary": {"totalButtons": 11, "functionalButtons": 11, "nonFunctionalButtons": 0, "functionalityRate": 100}}, {"file": "app/admin/users/page.tsx", "buttons": [{"type": "functional", "element": "<Button\n                  variant=\"outline\"\n                  onClick={handleBul...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button\n                  variant=\"outline\"\n                  onClick={() => set...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button variant=\"outline\" onClick={handleRefresh} disabled={isLoading}>\n        ...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button variant=\"outline\" onClick={handleExport}>\n              <Download classN...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button onClick={() => router.push(\"/admin/users/new\")}>\n              <UserPlus...", "functionality": "onClick handler, router navigation"}], "issues": [{"type": "missing_functionality", "element": "<Button variant=\"outline\" size=\"sm\">\n                                  <MoreHori...", "line": 537}], "summary": {"totalButtons": 6, "functionalButtons": 5, "nonFunctionalButtons": 1, "functionalityRate": 83}}, {"file": "app/admin/settings/page.tsx", "buttons": [{"type": "functional", "element": "<Button\n              variant=\"outline\"\n              onClick={handleReset}\n    ...", "functionality": "onClick handler"}, {"type": "functional", "element": "<Button\n              onClick={handleSave}\n              disabled={updateSetting...", "functionality": "onClick handler"}], "issues": [], "summary": {"totalButtons": 2, "functionalButtons": 2, "nonFunctionalButtons": 0, "functionalityRate": 100}}, {"file": "app/admin/analytics/page.tsx", "buttons": [], "issues": [{"type": "missing_functionality", "element": "<Button variant=\"outline\">\n              <RefreshCw className=\"h-4 w-4 mr-2\" />", "line": 169}, {"type": "missing_functionality", "element": "<Button variant=\"outline\">\n              <Download className=\"h-4 w-4 mr-2\" />", "line": 173}], "summary": {"totalButtons": 2, "functionalButtons": 0, "nonFunctionalButtons": 2, "functionalityRate": 0}}, {"file": "app/admin/attendance/page.tsx", "buttons": [], "issues": [{"type": "missing_functionality", "element": "<Button variant=\"outline\">\n              <Upload className=\"h-4 w-4 mr-2\" />", "line": 138}, {"type": "missing_functionality", "element": "<Button variant=\"outline\">\n              <Download className=\"h-4 w-4 mr-2\" />", "line": 142}, {"type": "missing_functionality", "element": "<Button>\n              <UserCheck className=\"h-4 w-4 mr-2\" />", "line": 146}, {"type": "missing_functionality", "element": "<Button size=\"sm\" variant=\"outline\" className=\"text-red-600 border-red-300\">\n   ...", "line": 452}], "summary": {"totalButtons": 4, "functionalButtons": 0, "nonFunctionalButtons": 4, "functionalityRate": 0}}, {"file": "app/admin/grades/page.tsx", "buttons": [], "issues": [{"type": "missing_functionality", "element": "<Button variant=\"outline\">\n              <Upload className=\"h-4 w-4 mr-2\" />", "line": 158}, {"type": "missing_functionality", "element": "<Button variant=\"outline\">\n              <Download className=\"h-4 w-4 mr-2\" />", "line": 162}, {"type": "missing_functionality", "element": "<Button>\n              <Plus className=\"h-4 w-4 mr-2\" />", "line": 166}, {"type": "missing_functionality", "element": "<Button size=\"sm\" variant=\"outline\">\n                          <Eye className=\"h...", "line": 339}, {"type": "missing_functionality", "element": "<Button variant=\"outline\" size=\"sm\">\n                            <Eye className=...", "line": 440}, {"type": "missing_functionality", "element": "<Button variant=\"outline\" size=\"sm\">\n                            <Edit className...", "line": 443}, {"type": "missing_functionality", "element": "<Button size=\"sm\" variant=\"outline\" className=\"text-red-600 border-red-300\">\n   ...", "line": 521}], "summary": {"totalButtons": 7, "functionalButtons": 0, "nonFunctionalButtons": 7, "functionalityRate": 0}}, {"file": "app/admin/exams/page.tsx", "buttons": [], "issues": [], "summary": {"totalButtons": 0, "functionalButtons": 0, "nonFunctionalButtons": 0, "functionalityRate": 0}}, {"file": "app/admin/hostel/page.tsx", "buttons": [], "issues": [], "summary": {"totalButtons": 0, "functionalButtons": 0, "nonFunctionalButtons": 0, "functionalityRate": 0}}, {"file": "app/admin/transport/page.tsx", "buttons": [], "issues": [], "summary": {"totalButtons": 0, "functionalButtons": 0, "nonFunctionalButtons": 0, "functionalityRate": 0}}, {"file": "app/admin/library/page.tsx", "buttons": [{"type": "functional", "element": "<Button variant=\"outline\" onClick={() => router.push(\"/library/reports\")}>\n     ...", "functionality": "onClick handler, router navigation"}, {"type": "functional", "element": "<Button onClick={() => router.push(\"/library/books\")}>\n              <Plus class...", "functionality": "onClick handler, router navigation"}], "issues": [{"type": "missing_functionality", "element": "<Button variant=\"outline\" size=\"sm\">\n                  <Filter className=\"h-4 w-...", "line": 268}, {"type": "missing_functionality", "element": "<Button variant=\"outline\" size=\"sm\">\n                            Issue\n         ...", "line": 316}, {"type": "missing_functionality", "element": "<Button variant=\"outline\" size=\"sm\">\n                            Edit\n          ...", "line": 319}, {"type": "missing_functionality", "element": "<Button variant=\"outline\" size=\"sm\">\n                              Return\n      ...", "line": 372}, {"type": "missing_functionality", "element": "<Button variant=\"outline\" size=\"sm\">\n                              Extend\n      ...", "line": 375}, {"type": "missing_functionality", "element": "<Button variant=\"outline\" className=\"w-full\">\n                    View Detailed ...", "line": 455}], "summary": {"totalButtons": 8, "functionalButtons": 2, "nonFunctionalButtons": 6, "functionalityRate": 25}}, {"file": "app/principal/dashboard/page.tsx", "buttons": [{"type": "functional", "element": "<Button variant=\"outline\" onClick={() => router.push(\"/principal/analytics\")}>\n ...", "functionality": "onClick handler, router navigation"}, {"type": "functional", "element": "<Button onClick={() => router.push(\"/principal/planning\")}>\n              <Targe...", "functionality": "onClick handler, router navigation"}], "issues": [], "summary": {"totalButtons": 2, "functionalButtons": 2, "nonFunctionalButtons": 0, "functionalityRate": 100}}]}