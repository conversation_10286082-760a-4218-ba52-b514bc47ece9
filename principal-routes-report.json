{"timestamp": "2025-06-08T10:08:50.935Z", "totalRoutes": 29, "existingRoutes": 4, "createdRoutes": 25, "missingRoutes": 0, "completionRate": 100, "allRoutes": ["/principal/dashboard", "/principal/academic", "/principal/curriculum", "/principal/calendar", "/principal/performance", "/principal/staff", "/principal/staff/reviews", "/principal/staff/development", "/principal/staff/reports", "/principal/students", "/principal/students/discipline", "/principal/students/achievements", "/principal/students/welfare", "/principal/finance", "/principal/finance/approvals", "/principal/finance/reports", "/principal/finance/fees", "/principal/parents", "/principal/parents/meetings", "/principal/parents/feedback", "/principal/parents/communication", "/principal/planning", "/principal/planning/quality", "/principal/planning/external", "/principal/analytics", "/principal/analytics/academic", "/principal/analytics/performance", "/principal/analytics/comparison", "/principal/approvals"], "createdRoutesList": ["/principal/academic", "/principal/curriculum", "/principal/calendar", "/principal/performance", "/principal/staff", "/principal/staff/reviews", "/principal/staff/development", "/principal/staff/reports", "/principal/students", "/principal/students/discipline", "/principal/students/achievements", "/principal/students/welfare", "/principal/finance", "/principal/finance/approvals", "/principal/finance/reports", "/principal/finance/fees", "/principal/parents", "/principal/parents/meetings", "/principal/parents/feedback", "/principal/parents/communication", "/principal/planning/quality", "/principal/planning/external", "/principal/analytics/academic", "/principal/analytics/performance", "/principal/analytics/comparison"]}