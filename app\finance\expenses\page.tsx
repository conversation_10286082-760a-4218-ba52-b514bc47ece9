"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Calculator,
  Plus,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  Trash2,
  IndianRupee,
  Calendar,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  CheckCircle,
  Clock,
  Receipt,
  FileText,
  Users,
  Building,
  Zap,
  BookOpen,
  Wrench,
} from "lucide-react";

export default function FinanceExpenses() {
  const [user, setUser] = useState<any>(null);
  const [selectedCategory, setSelectedCategory] = useState("all");
  const [selectedStatus, setSelectedStatus] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "finance_manager" && parsedUser.role !== "admin" && parsedUser.role !== "super_admin") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock expenses data
  const expenses = [
    {
      id: "EXP001",
      expenseNumber: "EXP-2024-001",
      description: "Teacher Salaries - January 2024",
      category: "Salaries",
      amount: 450000,
      date: "2024-01-31",
      vendor: "Payroll Department",
      status: "Paid",
      paymentMethod: "Bank Transfer",
      approvedBy: "Principal",
      approvalDate: "2024-01-30",
      receiptNumber: "SAL-JAN-2024",
      budgetCategory: "Human Resources",
      budgetAllocated: 500000,
      budgetUsed: 450000,
      remarks: "Monthly salary payment for teaching staff",
    },
    {
      id: "EXP002",
      expenseNumber: "EXP-2024-002",
      description: "Electricity Bill - January 2024",
      category: "Utilities",
      amount: 25000,
      date: "2024-01-25",
      vendor: "State Electricity Board",
      status: "Paid",
      paymentMethod: "Online Banking",
      approvedBy: "Finance Manager",
      approvalDate: "2024-01-24",
      receiptNumber: "EB-JAN-2024",
      budgetCategory: "Utilities",
      budgetAllocated: 30000,
      budgetUsed: 25000,
      remarks: "Monthly electricity consumption charges",
    },
    {
      id: "EXP003",
      expenseNumber: "EXP-2024-003",
      description: "Laboratory Equipment Purchase",
      category: "Infrastructure",
      amount: 85000,
      date: "2024-01-20",
      vendor: "Scientific Instruments Ltd",
      status: "Pending Approval",
      paymentMethod: "Cheque",
      approvedBy: null,
      approvalDate: null,
      receiptNumber: null,
      budgetCategory: "Infrastructure",
      budgetAllocated: 200000,
      budgetUsed: 85000,
      remarks: "New microscopes and lab equipment for science department",
    },
    {
      id: "EXP004",
      expenseNumber: "EXP-2024-004",
      description: "Office Supplies and Stationery",
      category: "Supplies",
      amount: 15000,
      date: "2024-01-18",
      vendor: "Office Mart",
      status: "Approved",
      paymentMethod: "Cash",
      approvedBy: "Admin Head",
      approvalDate: "2024-01-17",
      receiptNumber: "OM-INV-2024-001",
      budgetCategory: "Administrative",
      budgetAllocated: 50000,
      budgetUsed: 15000,
      remarks: "Monthly office supplies procurement",
    },
    {
      id: "EXP005",
      expenseNumber: "EXP-2024-005",
      description: "Building Maintenance - HVAC Service",
      category: "Maintenance",
      amount: 35000,
      date: "2024-01-15",
      vendor: "Cool Air Services",
      status: "Paid",
      paymentMethod: "Bank Transfer",
      approvedBy: "Facilities Manager",
      approvalDate: "2024-01-14",
      receiptNumber: "CAS-2024-001",
      budgetCategory: "Maintenance",
      budgetAllocated: 80000,
      budgetUsed: 35000,
      remarks: "Quarterly HVAC maintenance and repair",
    },
    {
      id: "EXP006",
      expenseNumber: "EXP-2024-006",
      description: "Internet and Communication Services",
      category: "Utilities",
      amount: 12000,
      date: "2024-01-10",
      vendor: "TechNet Communications",
      status: "Rejected",
      paymentMethod: null,
      approvedBy: null,
      approvalDate: null,
      receiptNumber: null,
      budgetCategory: "Utilities",
      budgetAllocated: 15000,
      budgetUsed: 0,
      remarks: "Monthly internet and phone services - rejected due to overcharging",
    },
  ];

  const expenseStats = {
    totalExpenses: expenses.reduce((sum, exp) => sum + (exp.status === "Paid" ? exp.amount : 0), 0),
    pendingExpenses: expenses.filter(exp => exp.status === "Pending Approval").length,
    approvedExpenses: expenses.filter(exp => exp.status === "Approved").length,
    rejectedExpenses: expenses.filter(exp => exp.status === "Rejected").length,
    monthlyBudget: 1200000,
    budgetUsed: 525000,
    budgetRemaining: 675000,
  };

  const categoryBreakdown = [
    { category: "Salaries", amount: 450000, budget: 500000, icon: Users, color: "bg-blue-500" },
    { category: "Infrastructure", amount: 85000, budget: 200000, icon: Building, color: "bg-green-500" },
    { category: "Utilities", amount: 37000, budget: 45000, icon: Zap, color: "bg-yellow-500" },
    { category: "Maintenance", amount: 35000, budget: 80000, icon: Wrench, color: "bg-purple-500" },
    { category: "Supplies", amount: 15000, budget: 50000, icon: BookOpen, color: "bg-orange-500" },
  ];

  const monthlyTrend = [
    { month: "Aug", amount: 1150000, budget: 1200000 },
    { month: "Sep", amount: 1180000, budget: 1200000 },
    { month: "Oct", amount: 1080000, budget: 1200000 },
    { month: "Nov", amount: 1220000, budget: 1200000 },
    { month: "Dec", amount: 1250000, budget: 1200000 },
    { month: "Jan", amount: 525000, budget: 1200000 }, // Partial month
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Paid": return "bg-green-100 text-green-700";
      case "Approved": return "bg-blue-100 text-blue-700";
      case "Pending Approval": return "bg-yellow-100 text-yellow-700";
      case "Rejected": return "bg-red-100 text-red-700";
      default: return "bg-gray-100 text-gray-700";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Paid": return <CheckCircle className="h-4 w-4" />;
      case "Approved": return <CheckCircle className="h-4 w-4" />;
      case "Pending Approval": return <Clock className="h-4 w-4" />;
      case "Rejected": return <AlertTriangle className="h-4 w-4" />;
      default: return <Clock className="h-4 w-4" />;
    }
  };

  const getCategoryIcon = (category: string) => {
    switch (category) {
      case "Salaries": return <Users className="h-4 w-4" />;
      case "Infrastructure": return <Building className="h-4 w-4" />;
      case "Utilities": return <Zap className="h-4 w-4" />;
      case "Maintenance": return <Wrench className="h-4 w-4" />;
      case "Supplies": return <BookOpen className="h-4 w-4" />;
      default: return <Calculator className="h-4 w-4" />;
    }
  };

  const getBudgetUsageColor = (used: number, budget: number) => {
    const percentage = (used / budget) * 100;
    if (percentage <= 70) return "text-green-600";
    if (percentage <= 90) return "text-yellow-600";
    return "text-red-600";
  };

  const filteredExpenses = expenses.filter(expense => {
    const matchesSearch = expense.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         expense.vendor.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         expense.expenseNumber.toLowerCase().includes(searchTerm.toLowerCase());

    const matchesCategory = selectedCategory === "all" || expense.category === selectedCategory;
    const matchesStatus = selectedStatus === "all" || expense.status.toLowerCase().replace(" ", "_") === selectedStatus;

    return matchesSearch && matchesCategory && matchesStatus;
  });

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Expense Management</h1>
            <p className="text-gray-600">Track and manage school expenses and budgets</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Expense
            </Button>
          </div>
        </div>

        {/* Expense Statistics */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Calculator className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-red-600">
                    ₹{(expenseStats.totalExpenses / 100000).toFixed(1)}L
                  </div>
                  <p className="text-sm text-gray-600">Total Expenses</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <IndianRupee className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    ₹{(expenseStats.budgetRemaining / 100000).toFixed(1)}L
                  </div>
                  <p className="text-sm text-gray-600">Budget Remaining</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-yellow-600">
                    {expenseStats.pendingExpenses}
                  </div>
                  <p className="text-sm text-gray-600">Pending Approval</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <AlertTriangle className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-red-600">
                    {expenseStats.rejectedExpenses}
                  </div>
                  <p className="text-sm text-gray-600">Rejected</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Budget Usage */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Monthly Budget Usage</h3>
              <div className="text-sm text-gray-500">
                Budget: ₹{(expenseStats.monthlyBudget / 100000).toFixed(1)}L
              </div>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-4 mb-2">
              <div
                className={`h-4 rounded-full ${
                  (expenseStats.budgetUsed / expenseStats.monthlyBudget) * 100 <= 70
                    ? 'bg-green-500'
                    : (expenseStats.budgetUsed / expenseStats.monthlyBudget) * 100 <= 90
                      ? 'bg-yellow-500'
                      : 'bg-red-500'
                }`}
                style={{ width: `${(expenseStats.budgetUsed / expenseStats.monthlyBudget) * 100}%` }}
              />
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">
                Used: ₹{(expenseStats.budgetUsed / 100000).toFixed(1)}L ({((expenseStats.budgetUsed / expenseStats.monthlyBudget) * 100).toFixed(1)}%)
              </span>
              <span className="text-gray-600">
                Remaining: ₹{(expenseStats.budgetRemaining / 100000).toFixed(1)}L
              </span>
            </div>
          </CardContent>
        </Card>

        {/* Filters and Search */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col lg:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <input
                    type="text"
                    placeholder="Search by description, vendor, or expense number..."
                    className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                  />
                </div>
              </div>

              <div className="flex gap-2 flex-wrap">
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Categories</option>
                  {categoryBreakdown.map((cat) => (
                    <option key={cat.category} value={cat.category}>{cat.category}</option>
                  ))}
                </select>

                {[
                  { key: "all", label: "All Status" },
                  { key: "paid", label: "Paid" },
                  { key: "approved", label: "Approved" },
                  { key: "pending_approval", label: "Pending" },
                  { key: "rejected", label: "Rejected" },
                ].map((filter) => (
                  <Button
                    key={filter.key}
                    variant={selectedStatus === filter.key ? "default" : "outline"}
                    size="sm"
                    onClick={() => setSelectedStatus(filter.key)}
                  >
                    {filter.label}
                  </Button>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Main Content Grid */}
        <div className="grid gap-6 lg:grid-cols-4">
          {/* Expenses List */}
          <Card className="lg:col-span-3">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Calculator className="h-5 w-5 mr-2" />
                Expenses ({filteredExpenses.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {filteredExpenses.map((expense) => (
                  <div key={expense.id} className="border rounded-lg p-6 hover:shadow-md transition-shadow">
                    <div className="flex items-start justify-between mb-4">
                      <div className="flex-1">
                        <div className="flex items-center space-x-3 mb-2">
                          <h3 className="text-lg font-semibold text-gray-900">{expense.description}</h3>
                          <Badge variant="outline" className="text-xs">
                            {expense.expenseNumber}
                          </Badge>
                        </div>
                        <p className="text-gray-600 mb-1">Vendor: {expense.vendor}</p>
                        <div className="text-2xl font-bold text-red-600 mb-2">
                          ₹{expense.amount.toLocaleString()}
                        </div>
                      </div>
                      <div className="text-right">
                        <Badge className={getStatusColor(expense.status)}>
                          {getStatusIcon(expense.status)}
                          <span className="ml-1">{expense.status}</span>
                        </Badge>
                        <div className="text-sm text-gray-500 mt-1">
                          {expense.date}
                        </div>
                      </div>
                    </div>

                    <div className="grid gap-4 md:grid-cols-3 mb-4">
                      <div>
                        <div className="text-sm text-gray-600">Category</div>
                        <div className="flex items-center space-x-2">
                          {getCategoryIcon(expense.category)}
                          <span className="font-medium">{expense.category}</span>
                        </div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-600">Payment Method</div>
                        <div className="font-medium">{expense.paymentMethod || "Not specified"}</div>
                      </div>
                      <div>
                        <div className="text-sm text-gray-600">Budget Usage</div>
                        <div className={`font-medium ${getBudgetUsageColor(expense.budgetUsed, expense.budgetAllocated)}`}>
                          ₹{(expense.budgetUsed / 1000).toFixed(0)}K / ₹{(expense.budgetAllocated / 1000).toFixed(0)}K
                        </div>
                      </div>
                    </div>

                    {expense.approvedBy && (
                      <div className="grid gap-4 md:grid-cols-2 mb-4">
                        <div>
                          <div className="text-sm text-gray-600">Approved By</div>
                          <div className="font-medium">{expense.approvedBy}</div>
                          <div className="text-sm text-gray-500">{expense.approvalDate}</div>
                        </div>
                        {expense.receiptNumber && (
                          <div>
                            <div className="text-sm text-gray-600">Receipt Number</div>
                            <div className="font-medium text-blue-600">{expense.receiptNumber}</div>
                          </div>
                        )}
                      </div>
                    )}

                    {expense.remarks && (
                      <div className="mb-4">
                        <div className="text-sm text-gray-600">Remarks</div>
                        <div className="text-sm text-gray-900 bg-gray-50 p-2 rounded">
                          {expense.remarks}
                        </div>
                      </div>
                    )}

                    <div className="flex items-center justify-between pt-4 border-t">
                      <div className="text-sm text-gray-500">
                        Budget Category: {expense.budgetCategory}
                      </div>
                      <div className="flex space-x-2">
                        <Button variant="outline" size="sm">
                          <Eye className="h-4 w-4 mr-1" />
                          View
                        </Button>
                        {expense.status === "Pending Approval" && (
                          <>
                            <Button variant="outline" size="sm">
                              <Edit className="h-4 w-4 mr-1" />
                              Edit
                            </Button>
                            <Button size="sm">
                              <CheckCircle className="h-4 w-4 mr-1" />
                              Approve
                            </Button>
                          </>
                        )}
                        {expense.receiptNumber && (
                          <Button variant="outline" size="sm">
                            <Receipt className="h-4 w-4 mr-1" />
                            Receipt
                          </Button>
                        )}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Category Breakdown */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <TrendingUp className="h-5 w-5 mr-2" />
                Category Breakdown
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {categoryBreakdown.map((category, index) => (
                  <div key={index} className="p-3 bg-gray-50 rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <div className="flex items-center space-x-2">
                        <div className={`w-3 h-3 ${category.color} rounded-full`} />
                        <span className="font-medium text-gray-900">{category.category}</span>
                      </div>
                      <span className={`text-sm font-bold ${getBudgetUsageColor(category.amount, category.budget)}`}>
                        {((category.amount / category.budget) * 100).toFixed(0)}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2 mb-2">
                      <div
                        className={`h-2 rounded-full ${
                          (category.amount / category.budget) * 100 <= 70
                            ? 'bg-green-500'
                            : (category.amount / category.budget) * 100 <= 90
                              ? 'bg-yellow-500'
                              : 'bg-red-500'
                        }`}
                        style={{ width: `${(category.amount / category.budget) * 100}%` }}
                      />
                    </div>
                    <div className="flex justify-between text-xs text-gray-600">
                      <span>₹{(category.amount / 1000).toFixed(0)}K</span>
                      <span>₹{(category.budget / 1000).toFixed(0)}K</span>
                    </div>
                  </div>
                ))}

                <div className="pt-4 border-t">
                  <div className="text-center">
                    <div className="text-2xl font-bold text-gray-900">
                      ₹{(expenseStats.totalExpenses / 100000).toFixed(1)}L
                    </div>
                    <div className="text-sm text-gray-500">Total Spent</div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  );
}
