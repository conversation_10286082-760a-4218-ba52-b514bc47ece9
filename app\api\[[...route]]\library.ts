import { Hono } from "hono";
import { zValidator } from "@hono/zod-validator";
import { mockBooks, mockBookIssues } from "@/lib/mock-db";
import { bookSchema, bookIssueSchema } from "@/lib/schemas";
import { generateId } from "@/lib/utils";

const app = new Hono()
  // Books
  .get("/books", async (c) => {
    const page = parseInt(c.req.query("page") || "1");
    const limit = parseInt(c.req.query("limit") || "10");
    const search = c.req.query("search");
    const category = c.req.query("category");
    const status = c.req.query("status");

    let filteredBooks = [...mockBooks];

    if (search) {
      const searchLower = search.toLowerCase();
      filteredBooks = filteredBooks.filter(
        (book) =>
          book.title.toLowerCase().includes(searchLower) ||
          book.author.toLowerCase().includes(searchLower) ||
          book.isbn.toLowerCase().includes(searchLower)
      );
    }

    if (category) {
      filteredBooks = filteredBooks.filter((book) => book.category === category);
    }

    if (status) {
      filteredBooks = filteredBooks.filter((book) => book.status === status);
    }

    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedBooks = filteredBooks.slice(startIndex, endIndex);

    return c.json({
      data: paginatedBooks,
      pagination: {
        page,
        limit,
        total: filteredBooks.length,
        totalPages: Math.ceil(filteredBooks.length / limit),
      },
    });
  })
  .post(
    "/books",
    zValidator("json", bookSchema.omit({ id: true, createdAt: true, updatedAt: true })),
    async (c) => {
      const values = c.req.valid("json");
      
      const newBook = {
        ...values,
        id: generateId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      mockBooks.push(newBook);

      return c.json({ data: newBook }, 201);
    }
  )
  .get("/books/:id", async (c) => {
    const id = c.req.param("id");
    const book = mockBooks.find((b) => b.id === id);

    if (!book) {
      return c.json({ error: "Book not found" }, 404);
    }

    return c.json({ data: book });
  })
  .put(
    "/books/:id",
    zValidator("json", bookSchema.omit({ id: true, createdAt: true, updatedAt: true })),
    async (c) => {
      const id = c.req.param("id");
      const values = c.req.valid("json");

      const bookIndex = mockBooks.findIndex((b) => b.id === id);

      if (bookIndex === -1) {
        return c.json({ error: "Book not found" }, 404);
      }

      mockBooks[bookIndex] = {
        ...mockBooks[bookIndex],
        ...values,
        updatedAt: new Date().toISOString(),
      };

      return c.json({ data: mockBooks[bookIndex] });
    }
  )
  .delete("/books/:id", async (c) => {
    const id = c.req.param("id");
    const bookIndex = mockBooks.findIndex((b) => b.id === id);

    if (bookIndex === -1) {
      return c.json({ error: "Book not found" }, 404);
    }

    mockBooks.splice(bookIndex, 1);

    return c.json({ message: "Book deleted successfully" });
  })

  // Book Issues
  .get("/issues", async (c) => {
    const page = parseInt(c.req.query("page") || "1");
    const limit = parseInt(c.req.query("limit") || "10");
    const studentId = c.req.query("studentId");
    const teacherId = c.req.query("teacherId");
    const bookId = c.req.query("bookId");
    const status = c.req.query("status");

    let filteredIssues = [...mockBookIssues];

    if (studentId) {
      filteredIssues = filteredIssues.filter((issue) => issue.studentId === studentId);
    }

    if (teacherId) {
      filteredIssues = filteredIssues.filter((issue) => issue.teacherId === teacherId);
    }

    if (bookId) {
      filteredIssues = filteredIssues.filter((issue) => issue.bookId === bookId);
    }

    if (status) {
      filteredIssues = filteredIssues.filter((issue) => issue.status === status);
    }

    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedIssues = filteredIssues.slice(startIndex, endIndex);

    return c.json({
      data: paginatedIssues,
      pagination: {
        page,
        limit,
        total: filteredIssues.length,
        totalPages: Math.ceil(filteredIssues.length / limit),
      },
    });
  })
  .post(
    "/issues",
    zValidator("json", bookIssueSchema.omit({ id: true, createdAt: true, updatedAt: true })),
    async (c) => {
      const values = c.req.valid("json");
      
      // Check if book is available
      const book = mockBooks.find((b) => b.id === values.bookId);
      if (!book || book.availableCopies <= 0) {
        return c.json({ error: "Book not available" }, 400);
      }

      const newIssue = {
        ...values,
        id: generateId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      mockBookIssues.push(newIssue);

      // Update book availability
      book.availableCopies -= 1;

      return c.json({ data: newIssue }, 201);
    }
  )
  .get("/issues/:id", async (c) => {
    const id = c.req.param("id");
    const issue = mockBookIssues.find((i) => i.id === id);

    if (!issue) {
      return c.json({ error: "Book issue not found" }, 404);
    }

    return c.json({ data: issue });
  })
  .put(
    "/issues/:id",
    zValidator("json", bookIssueSchema.omit({ id: true, createdAt: true, updatedAt: true })),
    async (c) => {
      const id = c.req.param("id");
      const values = c.req.valid("json");

      const issueIndex = mockBookIssues.findIndex((i) => i.id === id);

      if (issueIndex === -1) {
        return c.json({ error: "Book issue not found" }, 404);
      }

      const oldIssue = mockBookIssues[issueIndex];
      
      mockBookIssues[issueIndex] = {
        ...oldIssue,
        ...values,
        updatedAt: new Date().toISOString(),
      };

      // If status changed to returned, update book availability
      if (oldIssue.status !== "returned" && values.status === "returned") {
        const book = mockBooks.find((b) => b.id === oldIssue.bookId);
        if (book) {
          book.availableCopies += 1;
        }
      }

      return c.json({ data: mockBookIssues[issueIndex] });
    }
  )
  .delete("/issues/:id", async (c) => {
    const id = c.req.param("id");
    const issueIndex = mockBookIssues.findIndex((i) => i.id === id);

    if (issueIndex === -1) {
      return c.json({ error: "Book issue not found" }, 404);
    }

    const issue = mockBookIssues[issueIndex];
    
    // If book was issued, return it to available stock
    if (issue.status === "issued") {
      const book = mockBooks.find((b) => b.id === issue.bookId);
      if (book) {
        book.availableCopies += 1;
      }
    }

    mockBookIssues.splice(issueIndex, 1);

    return c.json({ message: "Book issue deleted successfully" });
  })

  // Library Reports
  .get("/reports/summary", async (c) => {
    const totalBooks = mockBooks.reduce((sum, book) => sum + book.totalCopies, 0);
    const availableBooks = mockBooks.reduce((sum, book) => sum + book.availableCopies, 0);
    const issuedBooks = totalBooks - availableBooks;
    const overdueBooks = mockBookIssues.filter(issue => issue.status === "overdue").length;

    const categoryStats = mockBooks.reduce((acc, book) => {
      acc[book.category] = (acc[book.category] || 0) + book.totalCopies;
      return acc;
    }, {} as Record<string, number>);

    const issueStats = {
      totalIssues: mockBookIssues.length,
      activeIssues: mockBookIssues.filter(i => i.status === "issued").length,
      overdueIssues: overdueBooks,
      returnedIssues: mockBookIssues.filter(i => i.status === "returned").length,
    };

    return c.json({
      data: {
        totalBooks,
        availableBooks,
        issuedBooks,
        overdueBooks,
        categoryStats,
        issueStats,
      },
    });
  })

  // Return book
  .post("/issues/:id/return", async (c) => {
    const id = c.req.param("id");
    const issueIndex = mockBookIssues.findIndex((i) => i.id === id);

    if (issueIndex === -1) {
      return c.json({ error: "Book issue not found" }, 404);
    }

    const issue = mockBookIssues[issueIndex];

    if (issue.status === "returned") {
      return c.json({ error: "Book already returned" }, 400);
    }

    // Update issue status
    mockBookIssues[issueIndex] = {
      ...issue,
      status: "returned",
      returnDate: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    // Update book availability
    const book = mockBooks.find((b) => b.id === issue.bookId);
    if (book) {
      book.availableCopies += 1;
    }

    return c.json({ data: mockBookIssues[issueIndex] });
  });

export default app;
