"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Calendar,
  CheckCircle,
  XCircle,
  Clock,
  TrendingUp,
  TrendingDown,
  BarChart3,
  Download,
  AlertTriangle,
  Target,
  User,
  CalendarDays,
  FileText,
} from "lucide-react";

export default function ParentAttendance() {
  const [user, setUser] = useState<any>(null);
  const [selectedChild, setSelectedChild] = useState("child1");
  const [selectedPeriod, setSelectedPeriod] = useState("monthly");
  const [selectedTab, setSelectedTab] = useState("overview");
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "parent") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock data for parent's children
  const children = [
    { id: "child1", name: "John Doe", class: "Grade 10A", rollNo: "10A001" },
    { id: "child2", name: "Jane Doe", class: "Grade 8B", rollNo: "8B015" },
  ];

  const selectedChildData = children.find(child => child.id === selectedChild) || children[0];

  const attendanceOverview = {
    totalDays: 120,
    presentDays: 113,
    absentDays: 7,
    lateDays: 3,
    attendanceRate: 94.2,
    classAverage: 92.5,
    monthlyRate: 96.8,
    weeklyRate: 100.0,
  };

  const monthlyAttendance = [
    { month: "September", totalDays: 22, present: 21, absent: 1, late: 0, rate: 95.5 },
    { month: "October", totalDays: 24, present: 23, absent: 1, late: 1, rate: 95.8 },
    { month: "November", totalDays: 23, present: 22, absent: 1, late: 0, rate: 95.7 },
    { month: "December", totalDays: 20, present: 18, absent: 2, late: 1, rate: 90.0 },
    { month: "January", totalDays: 31, present: 29, absent: 2, late: 1, rate: 93.5 },
  ];

  const recentAttendance = [
    { date: "2024-01-20", status: "Present", time: "08:15 AM", subject: "Mathematics", teacher: "Dr. Sarah Johnson" },
    { date: "2024-01-19", status: "Present", time: "08:12 AM", subject: "Physics", teacher: "Prof. Michael Smith" },
    { date: "2024-01-18", status: "Late", time: "08:35 AM", subject: "Chemistry", teacher: "Dr. Emily Brown" },
    { date: "2024-01-17", status: "Present", time: "08:10 AM", subject: "English", teacher: "Ms. Lisa Wilson" },
    { date: "2024-01-16", status: "Absent", time: "-", subject: "Computer Science", teacher: "Mr. David Chen" },
    { date: "2024-01-15", status: "Present", time: "08:08 AM", subject: "Biology", teacher: "Dr. Maria Garcia" },
    { date: "2024-01-12", status: "Present", time: "08:14 AM", subject: "Mathematics", teacher: "Dr. Sarah Johnson" },
  ];

  const attendanceTrends = [
    { week: "Week 1", rate: 100.0, days: 5 },
    { week: "Week 2", rate: 80.0, days: 5 },
    { week: "Week 3", rate: 100.0, days: 5 },
    { week: "Week 4", rate: 100.0, days: 5 },
    { week: "Current", rate: 100.0, days: 3 },
  ];

  const subjectAttendance = [
    { subject: "Mathematics", totalClasses: 25, attended: 24, rate: 96.0, teacher: "Dr. Sarah Johnson" },
    { subject: "Physics", totalClasses: 22, attended: 21, rate: 95.5, teacher: "Prof. Michael Smith" },
    { subject: "Chemistry", totalClasses: 20, attended: 19, rate: 95.0, teacher: "Dr. Emily Brown" },
    { subject: "English", totalClasses: 18, attended: 17, rate: 94.4, teacher: "Ms. Lisa Wilson" },
    { subject: "Computer Science", totalClasses: 15, attended: 13, rate: 86.7, teacher: "Mr. David Chen" },
    { subject: "Biology", totalClasses: 20, attended: 19, rate: 95.0, teacher: "Dr. Maria Garcia" },
  ];

  const attendanceAlerts = [
    { type: "Low Attendance", subject: "Computer Science", message: "Attendance below 90% threshold", severity: "warning" },
    { type: "Improvement", subject: "Overall", message: "Attendance improved by 3% this month", severity: "success" },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Present": return "text-green-600 bg-green-100";
      case "Absent": return "text-red-600 bg-red-100";
      case "Late": return "text-yellow-600 bg-yellow-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Present": return <CheckCircle className="h-4 w-4" />;
      case "Absent": return <XCircle className="h-4 w-4" />;
      case "Late": return <Clock className="h-4 w-4" />;
      default: return <AlertTriangle className="h-4 w-4" />;
    }
  };

  const getAttendanceColor = (rate: number) => {
    if (rate >= 95) return "text-green-600";
    if (rate >= 90) return "text-blue-600";
    if (rate >= 85) return "text-yellow-600";
    return "text-red-600";
  };

  const getAlertColor = (severity: string) => {
    switch (severity) {
      case "success": return "border-green-200 bg-green-50 text-green-800";
      case "warning": return "border-yellow-200 bg-yellow-50 text-yellow-800";
      case "error": return "border-red-200 bg-red-50 text-red-800";
      default: return "border-gray-200 bg-gray-50 text-gray-800";
    }
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Attendance Tracking</h1>
            <p className="text-gray-600">Monitor your child&apos;s attendance and punctuality</p>
          </div>
          <div className="flex gap-2">
            <select
              value={selectedChild}
              onChange={(e) => setSelectedChild(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              {children.map((child) => (
                <option key={child.id} value={child.id}>
                  {child.name} - {child.class}
                </option>
              ))}
            </select>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Download Report
            </Button>
          </div>
        </div>

        {/* Student Overview */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center space-x-6">
              <div className="w-16 h-16 bg-blue-100 rounded-full flex items-center justify-center">
                <User className="h-8 w-8 text-blue-600" />
              </div>
              <div className="flex-1">
                <h2 className="text-xl font-bold text-gray-900">{selectedChildData.name}</h2>
                <p className="text-gray-600">{selectedChildData.class} • Roll No: {selectedChildData.rollNo}</p>
              </div>
              <div className="grid gap-4 md:grid-cols-3 text-center">
                <div>
                  <div className="text-2xl font-bold text-green-600">{attendanceOverview.attendanceRate}%</div>
                  <div className="text-sm text-gray-500">Overall Rate</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-blue-600">{attendanceOverview.presentDays}</div>
                  <div className="text-sm text-gray-500">Days Present</div>
                </div>
                <div>
                  <div className="text-2xl font-bold text-red-600">{attendanceOverview.absentDays}</div>
                  <div className="text-sm text-gray-500">Days Absent</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Attendance Stats */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <BarChart3 className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {attendanceOverview.attendanceRate}%
                  </div>
                  <p className="text-sm text-gray-600">Overall Attendance</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600">+2.1%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {attendanceOverview.presentDays}
                  </div>
                  <p className="text-sm text-gray-600">Days Present</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <XCircle className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-red-600">
                    {attendanceOverview.absentDays}
                  </div>
                  <p className="text-sm text-gray-600">Days Absent</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-yellow-600">
                    {attendanceOverview.lateDays}
                  </div>
                  <p className="text-sm text-gray-600">Late Arrivals</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Attendance Alerts */}
        {attendanceAlerts.length > 0 && (
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertTriangle className="h-5 w-5 mr-2" />
                Attendance Alerts
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {attendanceAlerts.map((alert, index) => (
                  <div key={index} className={`p-4 border rounded-lg ${getAlertColor(alert.severity)}`}>
                    <div className="flex items-center justify-between">
                      <div>
                        <div className="font-medium">{alert.type}</div>
                        <div className="text-sm">{alert.message}</div>
                      </div>
                      <div className="text-sm font-medium">{alert.subject}</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Attendance Tabs */}
        <Card>
          <CardHeader>
            <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
              {[
                { key: "overview", label: "Monthly View" },
                { key: "recent", label: "Recent Records" },
                { key: "subjects", label: "Subject-wise" },
                { key: "trends", label: "Trends" },
              ].map((tab) => (
                <button
                  key={tab.key}
                  onClick={() => setSelectedTab(tab.key)}
                  className={`flex-1 px-4 py-2 text-sm font-medium rounded-md transition-colors ${
                    selectedTab === tab.key
                      ? "bg-white text-blue-600 shadow-sm"
                      : "text-gray-600 hover:text-gray-900"
                  }`}
                >
                  {tab.label}
                </button>
              ))}
            </div>
          </CardHeader>
          <CardContent>
            {/* Monthly Overview */}
            {selectedTab === "overview" && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Monthly Attendance Summary</h3>
                <div className="space-y-4">
                  {monthlyAttendance.map((month, index) => (
                    <div key={index} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-medium text-gray-900">{month.month}</h4>
                        <span className={`text-lg font-bold ${getAttendanceColor(month.rate)}`}>
                          {month.rate}%
                        </span>
                      </div>

                      <div className="grid gap-3 md:grid-cols-4 text-sm mb-3">
                        <div className="text-center p-3 bg-blue-50 rounded-lg">
                          <div className="font-bold text-blue-600">{month.totalDays}</div>
                          <div className="text-gray-500">Total Days</div>
                        </div>
                        <div className="text-center p-3 bg-green-50 rounded-lg">
                          <div className="font-bold text-green-600">{month.present}</div>
                          <div className="text-gray-500">Present</div>
                        </div>
                        <div className="text-center p-3 bg-red-50 rounded-lg">
                          <div className="font-bold text-red-600">{month.absent}</div>
                          <div className="text-gray-500">Absent</div>
                        </div>
                        <div className="text-center p-3 bg-yellow-50 rounded-lg">
                          <div className="font-bold text-yellow-600">{month.late}</div>
                          <div className="text-gray-500">Late</div>
                        </div>
                      </div>

                      <div className="w-full bg-gray-200 rounded-full h-3">
                        <div
                          className={`h-3 rounded-full ${
                            month.rate >= 95 ? 'bg-green-500' :
                            month.rate >= 90 ? 'bg-blue-500' :
                            month.rate >= 85 ? 'bg-yellow-500' : 'bg-red-500'
                          }`}
                          style={{ width: `${month.rate}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Recent Records */}
            {selectedTab === "recent" && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Recent Attendance Records</h3>
                <div className="overflow-x-auto">
                  <table className="w-full">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Date</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Time</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Subject</th>
                        <th className="text-left py-3 px-4 font-medium text-gray-900">Teacher</th>
                      </tr>
                    </thead>
                    <tbody>
                      {recentAttendance.map((record, index) => (
                        <tr key={index} className="border-b hover:bg-gray-50">
                          <td className="py-3 px-4 font-medium text-gray-900">{record.date}</td>
                          <td className="py-3 px-4">
                            <div className="flex items-center space-x-2">
                              {getStatusIcon(record.status)}
                              <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(record.status)}`}>
                                {record.status}
                              </span>
                            </div>
                          </td>
                          <td className="py-3 px-4 text-gray-600">{record.time}</td>
                          <td className="py-3 px-4 text-gray-600">{record.subject}</td>
                          <td className="py-3 px-4 text-gray-600">{record.teacher}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
              </div>
            )}

            {/* Subject-wise Attendance */}
            {selectedTab === "subjects" && (
              <div className="space-y-4">
                <h3 className="text-lg font-semibold text-gray-900">Subject-wise Attendance</h3>
                <div className="grid gap-4 md:grid-cols-2">
                  {subjectAttendance.map((subject, index) => (
                    <div key={index} className="p-4 border rounded-lg">
                      <div className="flex items-center justify-between mb-3">
                        <div>
                          <h4 className="font-medium text-gray-900">{subject.subject}</h4>
                          <p className="text-sm text-gray-600">{subject.teacher}</p>
                        </div>
                        <span className={`text-lg font-bold ${getAttendanceColor(subject.rate)}`}>
                          {subject.rate}%
                        </span>
                      </div>

                      <div className="grid gap-3 md:grid-cols-2 text-sm mb-3">
                        <div>
                          <div className="text-gray-500">Classes Attended</div>
                          <div className="font-bold text-gray-900">{subject.attended}/{subject.totalClasses}</div>
                        </div>
                        <div>
                          <div className="text-gray-500">Attendance Rate</div>
                          <div className={`font-bold ${getAttendanceColor(subject.rate)}`}>{subject.rate}%</div>
                        </div>
                      </div>

                      <div className="w-full bg-gray-200 rounded-full h-2">
                        <div
                          className={`h-2 rounded-full ${
                            subject.rate >= 95 ? 'bg-green-500' :
                            subject.rate >= 90 ? 'bg-blue-500' :
                            subject.rate >= 85 ? 'bg-yellow-500' : 'bg-red-500'
                          }`}
                          style={{ width: `${subject.rate}%` }}
                        />
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}

            {/* Trends */}
            {selectedTab === "trends" && (
              <div className="space-y-6">
                <h3 className="text-lg font-semibold text-gray-900">Attendance Trends</h3>

                <div className="grid gap-6 lg:grid-cols-2">
                  {/* Weekly Trends */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-4">Weekly Attendance</h4>
                    <div className="space-y-3">
                      {attendanceTrends.map((week, index) => (
                        <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                          <div>
                            <div className="font-medium text-gray-900">{week.week}</div>
                            <div className="text-sm text-gray-500">{week.days} days</div>
                          </div>
                          <div className="flex items-center space-x-3">
                            <div className="w-24 bg-gray-200 rounded-full h-2">
                              <div
                                className={`h-2 rounded-full ${
                                  week.rate >= 95 ? 'bg-green-500' :
                                  week.rate >= 90 ? 'bg-blue-500' :
                                  week.rate >= 85 ? 'bg-yellow-500' : 'bg-red-500'
                                }`}
                                style={{ width: `${week.rate}%` }}
                              />
                            </div>
                            <span className={`font-bold ${getAttendanceColor(week.rate)}`}>
                              {week.rate}%
                            </span>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Performance Comparison */}
                  <div>
                    <h4 className="font-medium text-gray-900 mb-4">Performance Comparison</h4>
                    <div className="space-y-4">
                      <div className="p-4 bg-blue-50 rounded-lg">
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-blue-700">Your Child</span>
                          <span className="font-bold text-blue-800">{attendanceOverview.attendanceRate}%</span>
                        </div>
                        <div className="w-full bg-blue-200 rounded-full h-2">
                          <div
                            className="bg-blue-600 h-2 rounded-full"
                            style={{ width: `${attendanceOverview.attendanceRate}%` }}
                          />
                        </div>
                      </div>

                      <div className="p-4 bg-gray-50 rounded-lg">
                        <div className="flex justify-between items-center mb-2">
                          <span className="text-gray-700">Class Average</span>
                          <span className="font-bold text-gray-800">{attendanceOverview.classAverage}%</span>
                        </div>
                        <div className="w-full bg-gray-200 rounded-full h-2">
                          <div
                            className="bg-gray-600 h-2 rounded-full"
                            style={{ width: `${attendanceOverview.classAverage}%` }}
                          />
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
