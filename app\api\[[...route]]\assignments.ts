import { Hono } from "hono";
import { zValidator } from "@hono/zod-validator";
import { insertAssignmentSchema } from "@/lib/schemas";
import { mockAssignments } from "@/lib/mock-db";
import { generateId } from "@/lib/utils";

const app = new Hono()
  .get("/", async (c) => {
    const page = parseInt(c.req.query("page") || "1");
    const limit = parseInt(c.req.query("limit") || "10");
    const classId = c.req.query("classId");
    const type = c.req.query("type");
    const status = c.req.query("status");

    let filteredAssignments = [...mockAssignments];

    if (classId) {
      filteredAssignments = filteredAssignments.filter(
        (assignment) => assignment.classId === classId
      );
    }

    if (type) {
      filteredAssignments = filteredAssignments.filter(
        (assignment) => assignment.type === type
      );
    }

    if (status) {
      filteredAssignments = filteredAssignments.filter(
        (assignment) => assignment.status === status
      );
    }

    const offset = (page - 1) * limit;
    const paginatedAssignments = filteredAssignments.slice(offset, offset + limit);

    const meta = {
      page,
      limit,
      total: filteredAssignments.length,
      totalPages: Math.ceil(filteredAssignments.length / limit),
      hasNext: page < Math.ceil(filteredAssignments.length / limit),
      hasPrev: page > 1,
    };

    return c.json({ data: paginatedAssignments, meta });
  })
  .get("/:id", async (c) => {
    const id = c.req.param("id");
    const assignment = mockAssignments.find((a) => a.id === id);

    if (!assignment) {
      return c.json({ error: "Assignment not found" }, 404);
    }

    return c.json({ data: assignment });
  })
  .post(
    "/",
    zValidator("json", insertAssignmentSchema),
    async (c) => {
      const values = c.req.valid("json");

      const newAssignment = {
        id: generateId(),
        ...values,
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      mockAssignments.push(newAssignment);

      return c.json({ data: newAssignment }, 201);
    }
  )
  .put(
    "/:id",
    zValidator("json", insertAssignmentSchema),
    async (c) => {
      const id = c.req.param("id");
      const values = c.req.valid("json");

      const assignmentIndex = mockAssignments.findIndex((a) => a.id === id);
      if (assignmentIndex === -1) {
        return c.json({ error: "Assignment not found" }, 404);
      }

      const updatedAssignment = {
        ...mockAssignments[assignmentIndex],
        ...values,
        updatedAt: new Date().toISOString(),
      };

      mockAssignments[assignmentIndex] = updatedAssignment;

      return c.json({ data: updatedAssignment });
    }
  )
  .delete("/:id", async (c) => {
    const id = c.req.param("id");
    const assignmentIndex = mockAssignments.findIndex((a) => a.id === id);

    if (assignmentIndex === -1) {
      return c.json({ error: "Assignment not found" }, 404);
    }

    const deletedAssignment = mockAssignments.splice(assignmentIndex, 1)[0];
    return c.json({ data: deletedAssignment });
  });

export default app;
