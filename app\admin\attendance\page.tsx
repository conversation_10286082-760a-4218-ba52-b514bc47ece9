"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Calendar,
  Users,
  CheckCircle,
  XCircle,
  Clock,
  Search,
  Filter,
  Download,
  Upload,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  BarChart3,
  UserCheck,
  UserX,
  CalendarDays,
  FileText,
} from "lucide-react";

export default function AdminAttendance() {
  const [user, setUser] = useState<any>(null);
  const [selectedDate, setSelectedDate] = useState(new Date().toISOString().split('T')[0]);
  const [selectedClass, setSelectedClass] = useState("all");
  const [selectedView, setSelectedView] = useState("daily");
  const [searchTerm, setSearchTerm] = useState("");
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "super_admin" && parsedUser.role !== "admin") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock attendance data
  const attendanceStats = {
    totalStudents: 1234,
    presentToday: 1167,
    absentToday: 67,
    lateToday: 15,
    attendanceRate: 94.6,
    weeklyAverage: 93.2,
    monthlyAverage: 92.8,
    yearlyAverage: 91.5,
  };

  const classAttendance = [
    { class: "Grade 10A", total: 45, present: 42, absent: 3, late: 1, rate: 93.3 },
    { class: "Grade 10B", total: 44, present: 41, absent: 3, late: 2, rate: 93.2 },
    { class: "Grade 11A", total: 43, present: 40, absent: 2, late: 1, rate: 93.0 },
    { class: "Grade 11B", total: 42, present: 39, absent: 3, late: 0, rate: 92.9 },
    { class: "Grade 12A", total: 41, present: 38, absent: 2, late: 1, rate: 92.7 },
    { class: "Grade 12B", total: 40, present: 37, absent: 3, late: 0, rate: 92.5 },
  ];

  const recentAttendance = [
    { id: "1", name: "John Doe", class: "Grade 10A", rollNo: "10A001", status: "Present", time: "08:15 AM", date: "2024-01-20" },
    { id: "2", name: "Alice Smith", class: "Grade 10A", rollNo: "10A002", status: "Present", time: "08:12 AM", date: "2024-01-20" },
    { id: "3", name: "Michael Johnson", class: "Grade 11B", rollNo: "11B015", status: "Late", time: "08:35 AM", date: "2024-01-20" },
    { id: "4", name: "Emma Davis", class: "Grade 12A", rollNo: "12A008", status: "Absent", time: "-", date: "2024-01-20" },
    { id: "5", name: "Robert Wilson", class: "Grade 10B", rollNo: "10B022", status: "Present", time: "08:10 AM", date: "2024-01-20" },
  ];

  const attendanceTrends = [
    { date: "Jan 15", rate: 92.1 },
    { date: "Jan 16", rate: 93.5 },
    { date: "Jan 17", rate: 91.8 },
    { date: "Jan 18", rate: 94.2 },
    { date: "Jan 19", rate: 93.7 },
    { date: "Jan 20", rate: 94.6 },
  ];

  const lowAttendanceStudents = [
    { name: "Sarah Brown", class: "Grade 11A", rollNo: "11A025", rate: 78.5, daysAbsent: 12, lastAbsent: "2024-01-18" },
    { name: "David Lee", class: "Grade 10B", rollNo: "10B018", rate: 82.3, daysAbsent: 9, lastAbsent: "2024-01-19" },
    { name: "Lisa Wang", class: "Grade 12B", rollNo: "12B012", rate: 85.1, daysAbsent: 8, lastAbsent: "2024-01-17" },
    { name: "James Miller", class: "Grade 11B", rollNo: "11B030", rate: 87.2, daysAbsent: 7, lastAbsent: "2024-01-20" },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Present": return "text-green-600 bg-green-100";
      case "Absent": return "text-red-600 bg-red-100";
      case "Late": return "text-yellow-600 bg-yellow-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Present": return <CheckCircle className="h-4 w-4" />;
      case "Absent": return <XCircle className="h-4 w-4" />;
      case "Late": return <Clock className="h-4 w-4" />;
      default: return <AlertTriangle className="h-4 w-4" />;
    }
  };

  const filteredAttendance = recentAttendance.filter(record => {
    const matchesSearch = record.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         record.rollNo.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesClass = selectedClass === "all" || record.class === selectedClass;
    return matchesSearch && matchesClass;
  });

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Attendance Management</h1>
            <p className="text-gray-600">Monitor and manage student attendance</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Upload className="h-4 w-4 mr-2" />
              Import
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button>
              <UserCheck className="h-4 w-4 mr-2" />
              Mark Attendance
            </Button>
          </div>
        </div>

        {/* Attendance Overview Stats */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {attendanceStats.totalStudents}
                  </div>
                  <p className="text-sm text-gray-600">Total Students</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {attendanceStats.presentToday}
                  </div>
                  <p className="text-sm text-gray-600">Present Today</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600">+2.1%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <XCircle className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-red-600">
                    {attendanceStats.absentToday}
                  </div>
                  <p className="text-sm text-gray-600">Absent Today</p>
                  <div className="flex items-center mt-1">
                    <TrendingDown className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600">-1.2%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <BarChart3 className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {attendanceStats.attendanceRate}%
                  </div>
                  <p className="text-sm text-gray-600">Attendance Rate</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600">+1.4%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Filters and Controls */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search by student name or roll number..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <div className="flex gap-2">
                <input
                  type="date"
                  value={selectedDate}
                  onChange={(e) => setSelectedDate(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                />
                <select
                  value={selectedClass}
                  onChange={(e) => setSelectedClass(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="all">All Classes</option>
                  <option value="Grade 10A">Grade 10A</option>
                  <option value="Grade 10B">Grade 10B</option>
                  <option value="Grade 11A">Grade 11A</option>
                  <option value="Grade 11B">Grade 11B</option>
                  <option value="Grade 12A">Grade 12A</option>
                  <option value="Grade 12B">Grade 12B</option>
                </select>
                <select
                  value={selectedView}
                  onChange={(e) => setSelectedView(e.target.value)}
                  className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
                >
                  <option value="daily">Daily View</option>
                  <option value="weekly">Weekly View</option>
                  <option value="monthly">Monthly View</option>
                </select>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Main Content Grid */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Class-wise Attendance */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center">
                <CalendarDays className="h-5 w-5 mr-2" />
                Class-wise Attendance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {classAttendance.map((classData, index) => (
                  <div key={index} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="font-semibold text-gray-900">{classData.class}</h3>
                      <span className={`text-sm font-bold ${
                        classData.rate >= 95 ? 'text-green-600' :
                        classData.rate >= 90 ? 'text-blue-600' :
                        classData.rate >= 85 ? 'text-yellow-600' : 'text-red-600'
                      }`}>
                        {classData.rate}%
                      </span>
                    </div>
                    
                    <div className="grid gap-3 md:grid-cols-4 text-sm mb-3">
                      <div className="text-center">
                        <div className="text-lg font-bold text-blue-600">{classData.total}</div>
                        <div className="text-gray-500">Total</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-green-600">{classData.present}</div>
                        <div className="text-gray-500">Present</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-red-600">{classData.absent}</div>
                        <div className="text-gray-500">Absent</div>
                      </div>
                      <div className="text-center">
                        <div className="text-lg font-bold text-yellow-600">{classData.late}</div>
                        <div className="text-gray-500">Late</div>
                      </div>
                    </div>

                    <div className="w-full bg-gray-200 rounded-full h-3">
                      <div 
                        className={`h-3 rounded-full ${
                          classData.rate >= 95 ? 'bg-green-500' :
                          classData.rate >= 90 ? 'bg-blue-500' :
                          classData.rate >= 85 ? 'bg-yellow-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${classData.rate}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Attendance Trends */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <TrendingUp className="h-5 w-5 mr-2" />
                Attendance Trends
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="grid gap-3 text-center">
                  <div>
                    <div className="text-sm text-gray-500">Weekly Average</div>
                    <div className="text-lg font-bold text-blue-600">{attendanceStats.weeklyAverage}%</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Monthly Average</div>
                    <div className="text-lg font-bold text-green-600">{attendanceStats.monthlyAverage}%</div>
                  </div>
                  <div>
                    <div className="text-sm text-gray-500">Yearly Average</div>
                    <div className="text-lg font-bold text-purple-600">{attendanceStats.yearlyAverage}%</div>
                  </div>
                </div>

                <div className="space-y-2">
                  <h4 className="font-medium text-gray-900">Last 6 Days</h4>
                  {attendanceTrends.map((trend, index) => (
                    <div key={index} className="flex items-center justify-between">
                      <span className="text-sm text-gray-600">{trend.date}</span>
                      <div className="flex items-center space-x-2">
                        <div className="w-16 bg-gray-200 rounded-full h-2">
                          <div 
                            className="bg-blue-500 h-2 rounded-full"
                            style={{ width: `${trend.rate}%` }}
                          />
                        </div>
                        <span className="text-sm font-medium">{trend.rate}%</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Attendance Records */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <FileText className="h-5 w-5 mr-2" />
              Recent Attendance Records
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Student</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Class</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Roll No.</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Status</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Time</th>
                    <th className="text-left py-3 px-4 font-medium text-gray-900">Date</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredAttendance.map((record) => (
                    <tr key={record.id} className="border-b hover:bg-gray-50">
                      <td className="py-3 px-4 font-medium text-gray-900">{record.name}</td>
                      <td className="py-3 px-4 text-gray-600">{record.class}</td>
                      <td className="py-3 px-4 text-gray-600">{record.rollNo}</td>
                      <td className="py-3 px-4">
                        <div className="flex items-center space-x-2">
                          {getStatusIcon(record.status)}
                          <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(record.status)}`}>
                            {record.status}
                          </span>
                        </div>
                      </td>
                      <td className="py-3 px-4 text-gray-600">{record.time}</td>
                      <td className="py-3 px-4 text-gray-600">{record.date}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>

        {/* Low Attendance Alert */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <AlertTriangle className="h-5 w-5 mr-2 text-red-500" />
              Students with Low Attendance
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {lowAttendanceStudents.map((student, index) => (
                <div key={index} className="p-4 border border-red-200 rounded-lg bg-red-50">
                  <div className="flex items-center justify-between mb-2">
                    <div>
                      <div className="font-medium text-gray-900">{student.name}</div>
                      <div className="text-sm text-gray-600">{student.class} • {student.rollNo}</div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-red-600">{student.rate}%</div>
                      <div className="text-sm text-gray-500">Attendance Rate</div>
                    </div>
                  </div>
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-600">Days Absent: {student.daysAbsent}</span>
                    <span className="text-gray-600">Last Absent: {student.lastAbsent}</span>
                  </div>
                  <div className="mt-2">
                    <Button size="sm" variant="outline" className="text-red-600 border-red-300">
                      Send Alert to Parent
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
