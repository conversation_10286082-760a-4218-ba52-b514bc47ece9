const fs = require('fs');
const path = require('path');

console.log("=== ADMIN & PRINCIPAL BUTTON FUNCTIONALITY TEST ===\n");

// Function to check if a file contains specific button patterns
function checkButtonFunctionality(filePath, relativePath) {
  try {
    const content = fs.readFileSync(filePath, 'utf8');
    const results = {
      file: relativePath,
      buttons: [],
      issues: []
    };

    // Check for buttons without onClick handlers
    const buttonRegex = /<Button[^>]*>/g;
    const onClickRegex = /onClick\s*=\s*{[^}]*}/;
    const routerPushRegex = /router\.push\(/;
    const toastRegex = /toast\./;

    let match;
    let buttonCount = 0;
    let functionalButtons = 0;

    while ((match = buttonRegex.exec(content)) !== null) {
      buttonCount++;
      const buttonTag = match[0];
      const startIndex = match.index;
      
      // Get the full button element (including closing tag)
      const endTagIndex = content.indexOf('</Button>', startIndex);
      const selfClosingIndex = content.indexOf('/>', startIndex);
      
      let buttonElement;
      if (endTagIndex !== -1 && (selfClosingIndex === -1 || endTagIndex < selfClosingIndex)) {
        buttonElement = content.substring(startIndex, endTagIndex + 9);
      } else if (selfClosingIndex !== -1) {
        buttonElement = content.substring(startIndex, selfClosingIndex + 2);
      } else {
        buttonElement = buttonTag;
      }

      // Check if button has functionality
      const hasOnClick = onClickRegex.test(buttonElement);
      const hasRouterPush = routerPushRegex.test(buttonElement);
      const hasToast = toastRegex.test(buttonElement);
      const isFormSubmit = buttonElement.includes('type="submit"');
      const isDialogTrigger = buttonElement.includes('DialogTrigger') || content.substring(Math.max(0, startIndex - 100), startIndex).includes('DialogTrigger');

      if (hasOnClick || hasRouterPush || isFormSubmit || isDialogTrigger) {
        functionalButtons++;
        results.buttons.push({
          type: 'functional',
          element: buttonElement.substring(0, 80) + (buttonElement.length > 80 ? '...' : ''),
          functionality: [
            hasOnClick && 'onClick handler',
            hasRouterPush && 'router navigation',
            isFormSubmit && 'form submission',
            isDialogTrigger && 'dialog trigger'
          ].filter(Boolean).join(', ')
        });
      } else {
        results.issues.push({
          type: 'missing_functionality',
          element: buttonElement.substring(0, 80) + (buttonElement.length > 80 ? '...' : ''),
          line: content.substring(0, startIndex).split('\n').length
        });
      }
    }

    results.summary = {
      totalButtons: buttonCount,
      functionalButtons: functionalButtons,
      nonFunctionalButtons: buttonCount - functionalButtons,
      functionalityRate: buttonCount > 0 ? Math.round((functionalButtons / buttonCount) * 100) : 0
    };

    return results;
  } catch (error) {
    return {
      file: relativePath,
      error: error.message,
      summary: { totalButtons: 0, functionalButtons: 0, nonFunctionalButtons: 0, functionalityRate: 0 }
    };
  }
}

// Admin and Principal pages to test
const adminPrincipalPages = [
  'app/admin/dashboard/page.tsx',
  'app/admin/students/page.tsx',
  'app/admin/teachers/page.tsx',
  'app/admin/classes/page.tsx',
  'app/admin/finance/page.tsx',
  'app/admin/reports/page.tsx',
  'app/admin/users/page.tsx',
  'app/admin/settings/page.tsx',
  'app/admin/analytics/page.tsx',
  'app/admin/attendance/page.tsx',
  'app/admin/grades/page.tsx',
  'app/admin/exams/page.tsx',
  'app/admin/hostel/page.tsx',
  'app/admin/transport/page.tsx',
  'app/admin/library/page.tsx',
  'app/principal/dashboard/page.tsx'
];

console.log("🔍 Testing Admin & Principal Button Functionality...\n");

let totalButtons = 0;
let totalFunctionalButtons = 0;
const pageResults = [];

adminPrincipalPages.forEach(pagePath => {
  const fullPath = path.join(__dirname, pagePath);
  
  if (fs.existsSync(fullPath)) {
    const result = checkButtonFunctionality(fullPath, pagePath);
    pageResults.push(result);
    
    if (result.summary) {
      totalButtons += result.summary.totalButtons;
      totalFunctionalButtons += result.summary.functionalButtons;
      
      const status = result.summary.functionalityRate >= 90 ? '✅' : 
                    result.summary.functionalityRate >= 70 ? '⚠️' : '❌';
      
      console.log(`${status} ${pagePath}`);
      console.log(`   Buttons: ${result.summary.totalButtons} | Functional: ${result.summary.functionalButtons} | Rate: ${result.summary.functionalityRate}%`);
      
      if (result.issues && result.issues.length > 0) {
        console.log(`   Issues: ${result.issues.length} non-functional buttons`);
        result.issues.slice(0, 2).forEach(issue => {
          console.log(`     - Line ${issue.line}: ${issue.element}`);
        });
        if (result.issues.length > 2) {
          console.log(`     - ... and ${result.issues.length - 2} more`);
        }
      }
      console.log();
    } else if (result.error) {
      console.log(`❌ ${pagePath} - Error: ${result.error}\n`);
    }
  } else {
    console.log(`⚠️ ${pagePath} - File not found\n`);
  }
});

// Overall summary
const overallFunctionalityRate = totalButtons > 0 ? Math.round((totalFunctionalButtons / totalButtons) * 100) : 0;

console.log("=== ADMIN & PRINCIPAL SUMMARY ===");
console.log(`Total Buttons Tested: ${totalButtons}`);
console.log(`Functional Buttons: ${totalFunctionalButtons}`);
console.log(`Non-Functional Buttons: ${totalButtons - totalFunctionalButtons}`);
console.log(`Overall Functionality Rate: ${overallFunctionalityRate}%`);

if (overallFunctionalityRate >= 90) {
  console.log("🎉 EXCELLENT: Admin & Principal pages have excellent button functionality!");
} else if (overallFunctionalityRate >= 80) {
  console.log("👍 VERY GOOD: Most admin & principal buttons are functional.");
} else if (overallFunctionalityRate >= 70) {
  console.log("👌 GOOD: Majority of admin & principal buttons work properly.");
} else {
  console.log("⚠️ NEEDS IMPROVEMENT: Admin & principal pages need button fixes.");
}

console.log("\n=== DETAILED BREAKDOWN ===");
const excellentPages = pageResults.filter(p => p.summary && p.summary.functionalityRate >= 90);
const goodPages = pageResults.filter(p => p.summary && p.summary.functionalityRate >= 70 && p.summary.functionalityRate < 90);
const needsWorkPages = pageResults.filter(p => p.summary && p.summary.functionalityRate < 70);

console.log(`✅ Excellent (90%+): ${excellentPages.length} pages`);
excellentPages.forEach(p => console.log(`   - ${p.file} (${p.summary.functionalityRate}%)`));

console.log(`⚠️ Good (70-89%): ${goodPages.length} pages`);
goodPages.forEach(p => console.log(`   - ${p.file} (${p.summary.functionalityRate}%)`));

console.log(`❌ Needs Work (<70%): ${needsWorkPages.length} pages`);
needsWorkPages.forEach(p => console.log(`   - ${p.file} (${p.summary.functionalityRate}%)`));

// Save detailed report
const reportData = {
  timestamp: new Date().toISOString(),
  scope: "Admin & Principal Pages",
  summary: {
    totalButtons,
    totalFunctionalButtons,
    overallFunctionalityRate,
    pagesTestedCount: adminPrincipalPages.length,
    pagesFoundCount: pageResults.filter(r => !r.error).length,
    excellentPages: excellentPages.length,
    goodPages: goodPages.length,
    needsWorkPages: needsWorkPages.length
  },
  pageResults
};

fs.writeFileSync('admin-principal-button-report.json', JSON.stringify(reportData, null, 2));
console.log("\n📊 Detailed report saved to: admin-principal-button-report.json");

console.log("\n=== RECOMMENDATIONS FOR ADMIN/PRINCIPAL ===");
if (needsWorkPages.length > 0) {
  console.log("1. Focus on pages with <70% functionality rate");
  console.log("2. Add onClick handlers to remaining non-functional buttons");
  console.log("3. Implement proper navigation for action buttons");
  console.log("4. Add toast notifications for user feedback");
} else {
  console.log("🎉 All admin and principal pages have good button functionality!");
  console.log("Consider adding loading states and enhanced user feedback.");
}
