"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  IndianRupee,
  TrendingUp,
  TrendingDown,
  Users,
  CreditCard,
  AlertTriangle,
  CheckCircle,
  Calendar,
  FileText,
  PieChart,
  Calculator,
  Receipt,
  Banknote,
  Target,
  BarChart3,
  Download,
  Settings,
  Plus,
} from "lucide-react";
import { toast } from "sonner";

export default function AdminFinance() {
  const [user, setUser] = useState<any>(null);
  const [selectedPeriod, setSelectedPeriod] = useState("monthly");
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "super_admin" && parsedUser.role !== "admin") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock financial overview data
  const financialOverview = {
    totalRevenue: ********,
    totalExpenses: ********,
    netProfit: ********,
    collectionRate: 94.5,
    pendingAmount: 1500000,
    overdueAmount: 750000,
    monthlyTarget: 2500000,
    yearlyTarget: ********,
    studentsEnrolled: 1234,
    feesPaid: 1167,
    feesOverdue: 67,
  };

  const departmentBudgets = [
    { department: "Academic", allocated: 15000000, spent: 12500000, remaining: 2500000, utilization: 83.3 },
    { department: "Infrastructure", allocated: 8000000, spent: 6200000, remaining: 1800000, utilization: 77.5 },
    { department: "Sports & Activities", allocated: 3000000, spent: 2100000, remaining: 900000, utilization: 70.0 },
    { department: "Administration", allocated: 5000000, spent: 4500000, remaining: 500000, utilization: 90.0 },
    { department: "Technology", allocated: 4000000, spent: 3800000, remaining: 200000, utilization: 95.0 },
  ];

  const recentTransactions = [
    { id: "TXN001", type: "Fee Collection", amount: 125000, date: "2024-01-20", status: "Completed", student: "Multiple Students" },
    { id: "TXN002", type: "Salary Payment", amount: -450000, date: "2024-01-19", status: "Completed", department: "Teaching Staff" },
    { id: "TXN003", type: "Infrastructure", amount: -85000, date: "2024-01-18", status: "Pending", department: "Maintenance" },
    { id: "TXN004", type: "Fee Collection", amount: 75000, date: "2024-01-17", status: "Completed", student: "Grade 12 Students" },
    { id: "TXN005", type: "Utility Bills", amount: -25000, date: "2024-01-16", status: "Completed", department: "Administration" },
  ];

  const quickActions = [
    { title: "Fee Management", icon: IndianRupee, color: "bg-green-500", href: "/finance/fees" },
    { title: "Payment Processing", icon: CreditCard, color: "bg-blue-500", href: "/finance/payments" },
    { title: "Invoice Generation", icon: Receipt, color: "bg-purple-500", href: "/finance/invoices" },
    { title: "Expense Tracking", icon: Calculator, color: "bg-orange-500", href: "/finance/expenses" },
    { title: "Financial Reports", icon: BarChart3, color: "bg-indigo-500", href: "/finance/reports" },
    { title: "Budget Planning", icon: Target, color: "bg-pink-500", href: "/admin/budget" },
  ];

  const getUtilizationColor = (utilization: number) => {
    if (utilization >= 90) return "text-red-600";
    if (utilization >= 80) return "text-yellow-600";
    return "text-green-600";
  };

  const getTransactionColor = (type: string) => {
    return type === "Fee Collection" ? "text-green-600" : "text-red-600";
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Finance Management</h1>
            <p className="text-gray-600">Comprehensive financial overview and management</p>
          </div>
          <div className="flex gap-2">
            <select
              value={selectedPeriod}
              onChange={(e) => setSelectedPeriod(e.target.value)}
              className="px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"
            >
              <option value="daily">Daily</option>
              <option value="weekly">Weekly</option>
              <option value="monthly">Monthly</option>
              <option value="quarterly">Quarterly</option>
              <option value="yearly">Yearly</option>
            </select>
            <Button
              variant="outline"
              onClick={() => {
                // Export financial data to CSV
                const csvContent = [
                  ["Department", "Allocated", "Spent", "Remaining", "Utilization"],
                  ...departmentBudgets.map(dept => [
                    dept.department,
                    dept.allocated,
                    dept.spent,
                    dept.remaining,
                    `${dept.utilization}%`
                  ])
                ].map(row => row.join(",")).join("\n");

                const blob = new Blob([csvContent], { type: "text/csv" });
                const url = window.URL.createObjectURL(blob);
                const a = document.createElement("a");
                a.href = url;
                a.download = `financial-report-${new Date().toISOString().split("T")[0]}.csv`;
                a.click();
                window.URL.revokeObjectURL(url);
                toast.success("Financial report exported successfully");
              }}
            >
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button
              variant="outline"
              onClick={() => router.push("/admin/settings")}
            >
              <Settings className="h-4 w-4 mr-2" />
              Settings
            </Button>
          </div>
        </div>

        {/* Financial Overview Cards */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <IndianRupee className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    ₹{(financialOverview.totalRevenue / 10000000).toFixed(1)}Cr
                  </div>
                  <p className="text-sm text-gray-600">Total Revenue</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600">+12.5%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Calculator className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-red-600">
                    ₹{(financialOverview.totalExpenses / 10000000).toFixed(1)}Cr
                  </div>
                  <p className="text-sm text-gray-600">Total Expenses</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 text-red-500 mr-1" />
                    <span className="text-xs text-red-600">+8.2%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <TrendingUp className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    ₹{(financialOverview.netProfit / 10000000).toFixed(1)}Cr
                  </div>
                  <p className="text-sm text-gray-600">Net Profit</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600">+18.3%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Target className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {financialOverview.collectionRate}%
                  </div>
                  <p className="text-sm text-gray-600">Collection Rate</p>
                  <div className="flex items-center mt-1">
                    <TrendingUp className="h-3 w-3 text-green-500 mr-1" />
                    <span className="text-xs text-green-600">+2.1%</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-4 md:grid-cols-3 lg:grid-cols-6">
              {quickActions.map((action, index) => (
                <Button
                  key={index}
                  variant="outline"
                  className="h-auto p-4 flex flex-col items-center space-y-2"
                  onClick={() => router.push(action.href)}
                >
                  <div className={`w-12 h-12 ${action.color} rounded-lg flex items-center justify-center`}>
                    <action.icon className="h-6 w-6 text-white" />
                  </div>
                  <span className="font-medium text-center">{action.title}</span>
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Main Content Grid */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Department Budget Utilization */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center">
                <PieChart className="h-5 w-5 mr-2" />
                Department Budget Utilization
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {departmentBudgets.map((dept, index) => (
                  <div key={index} className="p-4 border rounded-lg">
                    <div className="flex items-center justify-between mb-3">
                      <h3 className="font-semibold text-gray-900">{dept.department}</h3>
                      <span className={`text-sm font-bold ${getUtilizationColor(dept.utilization)}`}>
                        {dept.utilization}%
                      </span>
                    </div>
                    
                    <div className="grid gap-3 md:grid-cols-3 text-sm mb-3">
                      <div>
                        <span className="text-gray-600">Allocated: </span>
                        <span className="font-medium">₹{(dept.allocated / 1000000).toFixed(1)}M</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Spent: </span>
                        <span className="font-medium text-red-600">₹{(dept.spent / 1000000).toFixed(1)}M</span>
                      </div>
                      <div>
                        <span className="text-gray-600">Remaining: </span>
                        <span className="font-medium text-green-600">₹{(dept.remaining / 1000000).toFixed(1)}M</span>
                      </div>
                    </div>

                    <div className="w-full bg-gray-200 rounded-full h-3">
                      <div 
                        className={`h-3 rounded-full ${
                          dept.utilization >= 90 ? 'bg-red-500' :
                          dept.utilization >= 80 ? 'bg-yellow-500' : 'bg-green-500'
                        }`}
                        style={{ width: `${dept.utilization}%` }}
                      />
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Recent Transactions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Receipt className="h-5 w-5 mr-2" />
                Recent Transactions
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentTransactions.map((transaction) => (
                  <div key={transaction.id} className="p-3 border rounded-lg">
                    <div className="flex items-center justify-between mb-2">
                      <span className="font-medium text-gray-900">{transaction.type}</span>
                      <Badge className={transaction.status === "Completed" ? "bg-green-100 text-green-700" : "bg-yellow-100 text-yellow-700"}>
                        {transaction.status}
                      </Badge>
                    </div>
                    <div className={`text-lg font-bold ${getTransactionColor(transaction.type)}`}>
                      {transaction.amount > 0 ? '+' : ''}₹{Math.abs(transaction.amount).toLocaleString()}
                    </div>
                    <div className="text-sm text-gray-500 mt-1">
                      {transaction.date} • {transaction.student || transaction.department}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Financial Summary */}
        <div className="grid gap-6 lg:grid-cols-2">
          <Card>
            <CardHeader>
              <CardTitle>Fee Collection Summary</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid gap-4 md:grid-cols-3">
                <div className="text-center p-4 bg-blue-50 rounded-lg">
                  <Users className="h-8 w-8 text-blue-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-blue-600">{financialOverview.studentsEnrolled}</div>
                  <div className="text-sm text-gray-600">Total Students</div>
                </div>
                <div className="text-center p-4 bg-green-50 rounded-lg">
                  <CheckCircle className="h-8 w-8 text-green-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-green-600">{financialOverview.feesPaid}</div>
                  <div className="text-sm text-gray-600">Fees Paid</div>
                </div>
                <div className="text-center p-4 bg-red-50 rounded-lg">
                  <AlertTriangle className="h-8 w-8 text-red-600 mx-auto mb-2" />
                  <div className="text-2xl font-bold text-red-600">{financialOverview.feesOverdue}</div>
                  <div className="text-sm text-gray-600">Overdue</div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Financial Targets</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Monthly Target</span>
                    <span>₹{(financialOverview.monthlyTarget / 1000000).toFixed(1)}M</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-blue-500 h-2 rounded-full" style={{ width: "85%" }} />
                  </div>
                  <div className="text-xs text-gray-500 mt-1">85% achieved</div>
                </div>
                
                <div>
                  <div className="flex justify-between text-sm mb-2">
                    <span>Yearly Target</span>
                    <span>₹{(financialOverview.yearlyTarget / 10000000).toFixed(1)}Cr</span>
                  </div>
                  <div className="w-full bg-gray-200 rounded-full h-2">
                    <div className="bg-green-500 h-2 rounded-full" style={{ width: "78%" }} />
                  </div>
                  <div className="text-xs text-gray-500 mt-1">78% achieved</div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </AppLayout>
  );
}
