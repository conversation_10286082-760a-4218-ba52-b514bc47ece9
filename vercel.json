{"buildCommand": "npm install --legacy-peer-deps && npm run build", "installCommand": "npm install --legacy-peer-deps", "framework": "nextjs", "functions": {"app/api/[[...route]]/route.ts": {"maxDuration": 30}}, "headers": [{"source": "/api/(.*)", "headers": [{"key": "Access-Control-Allow-Origin", "value": "https://school-management-system-topaz.vercel.app"}, {"key": "Access-Control-Allow-Methods", "value": "GET, POST, PUT, DELETE, OPTIONS"}, {"key": "Access-Control-Allow-Headers", "value": "Content-Type, Authorization"}]}]}