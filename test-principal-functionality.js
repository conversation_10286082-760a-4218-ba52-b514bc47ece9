const fs = require('fs');
const path = require('path');

console.log("=== PRINCIPAL LOGIN & FUNCTIONALITY TEST ===\n");

// Test results tracking
const testResults = {
  passed: 0,
  failed: 0,
  warnings: 0,
  details: []
};

function addResult(test, status, message) {
  testResults.details.push({ test, status, message });
  if (status === 'PASS') testResults.passed++;
  else if (status === 'FAIL') testResults.failed++;
  else testResults.warnings++;
}

// Test 1: Check if principal user exists in mock database
console.log("🔍 Testing Principal User Configuration...");
try {
  const mockDbPath = path.join(__dirname, 'lib/mock-db.ts');
  const mockDbContent = fs.readFileSync(mockDbPath, 'utf8');
  
  if (mockDbContent.includes('role: "principal"')) {
    addResult('Principal User Exists', 'PASS', 'Principal user found in mock database');
    console.log("✅ Principal user exists in mock database");
    
    // Check for enhanced principal data
    if (mockDbContent.includes('principalData:')) {
      addResult('Principal Enhanced Data', 'PASS', 'Principal has enhanced profile data');
      console.log("✅ Principal has enhanced profile data");
    } else {
      addResult('Principal Enhanced Data', 'WARN', 'Principal missing enhanced profile data');
      console.log("⚠️ Principal missing enhanced profile data");
    }
  } else {
    addResult('Principal User Exists', 'FAIL', 'Principal user not found in mock database');
    console.log("❌ Principal user not found in mock database");
  }
} catch (error) {
  addResult('Principal User Configuration', 'FAIL', `Error reading mock database: ${error.message}`);
  console.log("❌ Error reading mock database");
}

// Test 2: Check principal navigation configuration
console.log("\n🔍 Testing Principal Navigation...");
try {
  const navConfigPath = path.join(__dirname, 'lib/navigation-config.ts');
  const navConfigContent = fs.readFileSync(navConfigPath, 'utf8');
  
  if (navConfigContent.includes('principal')) {
    addResult('Principal Navigation', 'PASS', 'Principal navigation configuration found');
    console.log("✅ Principal navigation configuration exists");
    
    // Check for specific principal menu items
    const principalMenuItems = [
      'Dashboard',
      'Analytics',
      'Approvals',
      'Strategic Planning'
    ];
    
    let foundItems = 0;
    principalMenuItems.forEach(item => {
      if (navConfigContent.toLowerCase().includes(item.toLowerCase())) {
        foundItems++;
      }
    });
    
    if (foundItems >= 3) {
      addResult('Principal Menu Items', 'PASS', `Found ${foundItems}/${principalMenuItems.length} expected menu items`);
      console.log(`✅ Found ${foundItems}/${principalMenuItems.length} expected menu items`);
    } else {
      addResult('Principal Menu Items', 'WARN', `Only found ${foundItems}/${principalMenuItems.length} expected menu items`);
      console.log(`⚠️ Only found ${foundItems}/${principalMenuItems.length} expected menu items`);
    }
  } else {
    addResult('Principal Navigation', 'FAIL', 'Principal navigation not configured');
    console.log("❌ Principal navigation not configured");
  }
} catch (error) {
  addResult('Principal Navigation', 'FAIL', `Error reading navigation config: ${error.message}`);
  console.log("❌ Error reading navigation config");
}

// Test 3: Check principal pages exist
console.log("\n🔍 Testing Principal Pages...");
const principalPages = [
  'app/principal/dashboard/page.tsx',
  'app/principal/approvals/page.tsx',
  'app/principal/analytics/page.tsx',
  'app/principal/planning/page.tsx'
];

let existingPages = 0;
principalPages.forEach(pagePath => {
  const fullPath = path.join(__dirname, pagePath);
  if (fs.existsSync(fullPath)) {
    existingPages++;
    addResult(`Page: ${pagePath}`, 'PASS', 'Page exists');
    console.log(`✅ ${pagePath} exists`);
  } else {
    addResult(`Page: ${pagePath}`, 'FAIL', 'Page missing');
    console.log(`❌ ${pagePath} missing`);
  }
});

// Test 4: Check principal API endpoints
console.log("\n🔍 Testing Principal API...");
try {
  const principalApiPath = path.join(__dirname, 'app/api/[[...route]]/principal.ts');
  const routePath = path.join(__dirname, 'app/api/[[...route]]/route.ts');
  
  if (fs.existsSync(principalApiPath)) {
    addResult('Principal API File', 'PASS', 'Principal API file exists');
    console.log("✅ Principal API file exists");
    
    const apiContent = fs.readFileSync(principalApiPath, 'utf8');
    const endpoints = ['/dashboard', '/approvals', '/analytics'];
    let foundEndpoints = 0;
    
    endpoints.forEach(endpoint => {
      if (apiContent.includes(endpoint)) {
        foundEndpoints++;
      }
    });
    
    if (foundEndpoints === endpoints.length) {
      addResult('Principal API Endpoints', 'PASS', `All ${endpoints.length} endpoints found`);
      console.log(`✅ All ${endpoints.length} API endpoints found`);
    } else {
      addResult('Principal API Endpoints', 'WARN', `Only ${foundEndpoints}/${endpoints.length} endpoints found`);
      console.log(`⚠️ Only ${foundEndpoints}/${endpoints.length} API endpoints found`);
    }
  } else {
    addResult('Principal API File', 'FAIL', 'Principal API file missing');
    console.log("❌ Principal API file missing");
  }
  
  // Check if principal API is registered in main route
  if (fs.existsSync(routePath)) {
    const routeContent = fs.readFileSync(routePath, 'utf8');
    if (routeContent.includes('principal')) {
      addResult('Principal API Registration', 'PASS', 'Principal API registered in main route');
      console.log("✅ Principal API registered in main route");
    } else {
      addResult('Principal API Registration', 'FAIL', 'Principal API not registered in main route');
      console.log("❌ Principal API not registered in main route");
    }
  }
} catch (error) {
  addResult('Principal API', 'FAIL', `Error checking API: ${error.message}`);
  console.log("❌ Error checking Principal API");
}

// Test 5: Check authentication middleware
console.log("\n🔍 Testing Authentication...");
try {
  const middlewarePath = path.join(__dirname, 'middleware.ts');
  if (fs.existsSync(middlewarePath)) {
    const middlewareContent = fs.readFileSync(middlewarePath, 'utf8');
    if (middlewareContent.includes('principal')) {
      addResult('Principal Auth Middleware', 'PASS', 'Principal role handled in middleware');
      console.log("✅ Principal role handled in middleware");
    } else {
      addResult('Principal Auth Middleware', 'WARN', 'Principal role may not be handled in middleware');
      console.log("⚠️ Principal role may not be handled in middleware");
    }
  } else {
    addResult('Auth Middleware', 'WARN', 'Middleware file not found');
    console.log("⚠️ Middleware file not found");
  }
} catch (error) {
  addResult('Authentication', 'FAIL', `Error checking authentication: ${error.message}`);
  console.log("❌ Error checking authentication");
}

// Test 6: Check login page supports principal
console.log("\n🔍 Testing Login Page...");
try {
  const loginPagePath = path.join(__dirname, 'app/login/page.tsx');
  if (fs.existsSync(loginPagePath)) {
    const loginContent = fs.readFileSync(loginPagePath, 'utf8');
    if (loginContent.includes('principal')) {
      addResult('Login Principal Support', 'PASS', 'Login page supports principal role');
      console.log("✅ Login page supports principal role");
    } else {
      addResult('Login Principal Support', 'WARN', 'Login page may not explicitly support principal');
      console.log("⚠️ Login page may not explicitly support principal");
    }
  } else {
    addResult('Login Page', 'FAIL', 'Login page not found');
    console.log("❌ Login page not found");
  }
} catch (error) {
  addResult('Login Page', 'FAIL', `Error checking login page: ${error.message}`);
  console.log("❌ Error checking login page");
}

// Summary
console.log("\n=== PRINCIPAL FUNCTIONALITY SUMMARY ===");
console.log(`✅ Passed: ${testResults.passed}`);
console.log(`⚠️ Warnings: ${testResults.warnings}`);
console.log(`❌ Failed: ${testResults.failed}`);
console.log(`📊 Total Tests: ${testResults.details.length}`);

const successRate = Math.round((testResults.passed / testResults.details.length) * 100);
console.log(`🎯 Success Rate: ${successRate}%`);

if (successRate >= 80) {
  console.log("\n🎉 EXCELLENT: Principal functionality is well implemented!");
} else if (successRate >= 60) {
  console.log("\n👍 GOOD: Principal functionality is mostly working, minor improvements needed.");
} else {
  console.log("\n⚠️ NEEDS WORK: Principal functionality requires significant improvements.");
}

// Detailed recommendations
console.log("\n=== RECOMMENDATIONS ===");
if (existingPages === principalPages.length) {
  console.log("✅ All principal pages are created");
} else {
  console.log(`❌ Create missing principal pages (${existingPages}/${principalPages.length} exist)`);
}

console.log("\n=== PRINCIPAL LOGIN CREDENTIALS ===");
console.log("📧 Email: <EMAIL>");
console.log("🔑 Password: principal123");
console.log("🌐 URL: http://localhost:3000/login");
console.log("📱 After login, you'll be redirected to: /principal/dashboard");

console.log("\n=== PRINCIPAL FEATURES AVAILABLE ===");
console.log("📊 Dashboard - School overview and key metrics");
console.log("✅ Approvals - Review and approve requests");
console.log("📈 Analytics - School performance analytics");
console.log("🎯 Strategic Planning - Goals and initiatives");

// Save detailed report
const reportData = {
  timestamp: new Date().toISOString(),
  summary: {
    passed: testResults.passed,
    warnings: testResults.warnings,
    failed: testResults.failed,
    successRate,
    totalTests: testResults.details.length
  },
  testDetails: testResults.details,
  principalCredentials: {
    email: "<EMAIL>",
    password: "principal123",
    loginUrl: "http://localhost:3000/login",
    dashboardUrl: "/principal/dashboard"
  },
  availableFeatures: [
    "Dashboard with school overview",
    "Approval system for requests",
    "Analytics and reporting",
    "Strategic planning tools"
  ]
};

fs.writeFileSync('principal-functionality-report.json', JSON.stringify(reportData, null, 2));
console.log("\n📊 Detailed report saved to: principal-functionality-report.json");
