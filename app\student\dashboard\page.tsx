"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import {
  BookOpen,
  Calendar,
  Award,
  Clock,
  CheckCircle,
  AlertTriangle,
  FileText,
  Users,
  TrendingUp
} from "lucide-react";

export default function StudentDashboard() {
  const [user, setUser] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");

    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);

    if (parsedUser.role !== "student") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock student data
  const studentData = {
    grade: "10A",
    rollNumber: "STU001",
    currentGPA: 3.8,
    attendance: 92,
    pendingAssignments: 3,
    upcomingExams: 2,
  };

  const recentGrades = [
    { subject: "Mathematics", grade: "A", score: 92, date: "2024-01-15" },
    { subject: "Physics", grade: "B+", score: 87, date: "2024-01-12" },
    { subject: "Chemistry", grade: "A-", score: 89, date: "2024-01-10" },
    { subject: "English", grade: "A", score: 94, date: "2024-01-08" },
  ];

  const upcomingAssignments = [
    {
      subject: "Mathematics",
      title: "Quadratic Equations",
      dueDate: "2024-01-20",
      status: "pending",
    },
    {
      subject: "Physics",
      title: "Newton's Laws Lab Report",
      dueDate: "2024-01-22",
      status: "pending",
    },
    {
      subject: "Chemistry",
      title: "Organic Chemistry Quiz",
      dueDate: "2024-01-25",
      status: "pending",
    },
  ];

  const todaySchedule = [
    { time: "09:00 - 10:00", subject: "Mathematics", teacher: "Dr. Emily Wilson", room: "Room 101" },
    { time: "10:00 - 11:00", subject: "Physics", teacher: "Mr. David Brown", room: "Room 201" },
    { time: "11:30 - 12:30", subject: "Chemistry", teacher: "Dr. Sarah Johnson", room: "Room 301" },
    { time: "14:00 - 15:00", subject: "English", teacher: "Ms. Lisa Anderson", room: "Room 102" },
  ];

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Welcome Header */}
        <div className="bg-gradient-to-r from-blue-500 to-purple-600 rounded-lg p-6 text-white">
          <h1 className="text-2xl font-bold mb-2">
            Welcome back, {user.firstName}!
          </h1>
          <p className="text-blue-100">
            Grade {studentData.grade} • Roll No: {studentData.rollNumber}
          </p>
        </div>

        {/* Quick Stats */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Award className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-yellow-600">
                    {studentData.currentGPA}
                  </div>
                  <p className="text-sm text-gray-600">Current GPA</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {studentData.attendance}%
                  </div>
                  <p className="text-sm text-gray-600">Attendance</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <AlertTriangle className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    {studentData.pendingAssignments}
                  </div>
                  <p className="text-sm text-gray-600">Pending Tasks</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Calendar className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {studentData.upcomingExams}
                  </div>
                  <p className="text-sm text-gray-600">Upcoming Exams</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Grid */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Today's Schedule */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle className="flex items-center">
                <Clock className="h-5 w-5 mr-2" />
                Today&apos;s Schedule
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {todaySchedule.map((class_, index) => (
                  <div key={index} className="flex items-center p-3 bg-gray-50 rounded-lg">
                    <div className="flex-shrink-0 w-24 text-sm font-medium text-gray-600">
                      {class_.time}
                    </div>
                    <div className="ml-4 flex-1">
                      <div className="font-medium text-gray-900">{class_.subject}</div>
                      <div className="text-sm text-gray-500">
                        {class_.teacher} • {class_.room}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button className="w-full justify-start" variant="outline">
                <BookOpen className="h-4 w-4 mr-2" />
                View Assignments
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <Award className="h-4 w-4 mr-2" />
                Check Grades
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <Calendar className="h-4 w-4 mr-2" />
                View Timetable
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <FileText className="h-4 w-4 mr-2" />
                Library Books
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Recent Grades and Assignments */}
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Recent Grades */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <TrendingUp className="h-5 w-5 mr-2" />
                Recent Grades
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {recentGrades.map((grade, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div className="font-medium text-gray-900">{grade.subject}</div>
                      <div className="text-sm text-gray-500">{grade.date}</div>
                    </div>
                    <div className="text-right">
                      <div className="text-lg font-bold text-blue-600">{grade.grade}</div>
                      <div className="text-sm text-gray-500">{grade.score}%</div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Upcoming Assignments */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <FileText className="h-5 w-5 mr-2" />
                Upcoming Assignments
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {upcomingAssignments.map((assignment, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div className="font-medium text-gray-900">{assignment.title}</div>
                      <div className="text-sm text-gray-500">{assignment.subject}</div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-orange-600">
                        Due: {assignment.dueDate}
                      </div>
                      <div className="text-xs text-gray-500 capitalize">
                        {assignment.status}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Performance Overview */}
        <Card>
          <CardHeader>
            <CardTitle>Performance Overview</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 md:grid-cols-3">
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 mb-2">A-</div>
                <div className="text-sm text-gray-600">Overall Grade</div>
                <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-green-600 h-2 rounded-full w-[85%]" />
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">92%</div>
                <div className="text-sm text-gray-600">Attendance Rate</div>
                <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-600 h-2 rounded-full w-[92%]" />
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-purple-600 mb-2">15</div>
                <div className="text-sm text-gray-600">Assignments Completed</div>
                <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-purple-600 h-2 rounded-full w-[83%]" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
