"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";
import { getDashboardPathForRole } from "@/lib/navigation-config";

interface RoleRedirectProps {
  children: React.ReactNode;
  allowedRoles?: string[];
  redirectTo?: string;
}

export function RoleRedirect({ 
  children, 
  allowedRoles = [], 
  redirectTo 
}: RoleRedirectProps) {
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    
    if (!userData) {
      router.push("/login");
      return;
    }

    const user = JSON.parse(userData);
    
    // If specific roles are required and user doesn't have access
    if (allowedRoles.length > 0 && !allowedRoles.includes(user.role)) {
      // Redirect to their appropriate dashboard
      const userDashboard = getDashboardPathForRole(user.role);
      router.push(redirectTo || userDashboard);
      return;
    }
  }, [router, allowedRoles, redirectTo]);

  return <>{children}</>;
}

// Higher-order component for role-based page protection
export function withRoleProtection(
  Component: React.ComponentType<any>,
  allowedRoles: string[] = []
) {
  return function ProtectedComponent(props: any) {
    return (
      <RoleRedirect allowedRoles={allowedRoles}>
        <Component {...props} />
      </RoleRedirect>
    );
  };
}

// Specific role protection components
export const AdminOnly = ({ children }: { children: React.ReactNode }) => (
  <RoleRedirect allowedRoles={["super_admin", "principal", "admin"]}>
    {children}
  </RoleRedirect>
);

export const PrincipalOnly = ({ children }: { children: React.ReactNode }) => (
  <RoleRedirect allowedRoles={["principal"]}>
    {children}
  </RoleRedirect>
);

export const TeacherOnly = ({ children }: { children: React.ReactNode }) => (
  <RoleRedirect allowedRoles={["teacher"]}>
    {children}
  </RoleRedirect>
);

export const StudentOnly = ({ children }: { children: React.ReactNode }) => (
  <RoleRedirect allowedRoles={["student"]}>
    {children}
  </RoleRedirect>
);

export const ParentOnly = ({ children }: { children: React.ReactNode }) => (
  <RoleRedirect allowedRoles={["parent"]}>
    {children}
  </RoleRedirect>
);

export const StaffOnly = ({ children }: { children: React.ReactNode }) => (
  <RoleRedirect allowedRoles={[
    "super_admin", 
    "admin", 
    "teacher", 
    "admission_officer", 
    "finance_manager", 
    "librarian", 
    "transport_manager", 
    "hostel_manager"
  ]}>
    {children}
  </RoleRedirect>
);
