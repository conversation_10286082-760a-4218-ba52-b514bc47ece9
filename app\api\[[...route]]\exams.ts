import { Hono } from "hono";
import { zValidator } from "@hono/zod-validator";
import { mockExams } from "@/lib/mock-db";
import { examSchema } from "@/lib/schemas";
import { generateId } from "@/lib/utils";

const app = new Hono()
  .get("/", async (c) => {
    const page = parseInt(c.req.query("page") || "1");
    const limit = parseInt(c.req.query("limit") || "10");
    const search = c.req.query("search");
    const grade = c.req.query("grade");
    const subject = c.req.query("subject");
    const type = c.req.query("type");
    const status = c.req.query("status");
    const dateFrom = c.req.query("dateFrom");
    const dateTo = c.req.query("dateTo");

    let filteredExams = [...mockExams];

    if (search) {
      const searchLower = search.toLowerCase();
      filteredExams = filteredExams.filter(
        (exam) =>
          exam.name.toLowerCase().includes(searchLower) ||
          exam.subject.toLowerCase().includes(searchLower) ||
          exam.room.toLowerCase().includes(searchLower) ||
          exam.invigilator.toLowerCase().includes(searchLower)
      );
    }

    if (grade) {
      filteredExams = filteredExams.filter((exam) => exam.grade === grade);
    }

    if (subject) {
      filteredExams = filteredExams.filter((exam) => exam.subject === subject);
    }

    if (type) {
      filteredExams = filteredExams.filter((exam) => exam.type === type);
    }

    if (status) {
      filteredExams = filteredExams.filter((exam) => exam.status === status);
    }

    if (dateFrom) {
      filteredExams = filteredExams.filter((exam) => exam.date >= dateFrom);
    }

    if (dateTo) {
      filteredExams = filteredExams.filter((exam) => exam.date <= dateTo);
    }

    const startIndex = (page - 1) * limit;
    const endIndex = startIndex + limit;
    const paginatedExams = filteredExams.slice(startIndex, endIndex);

    return c.json({
      data: paginatedExams,
      pagination: {
        page,
        limit,
        total: filteredExams.length,
        totalPages: Math.ceil(filteredExams.length / limit),
      },
    });
  })
  .post(
    "/",
    zValidator("json", examSchema.omit({ id: true, createdAt: true, updatedAt: true })),
    async (c) => {
      const values = c.req.valid("json");
      
      const newExam = {
        ...values,
        id: generateId(),
        createdAt: new Date().toISOString(),
        updatedAt: new Date().toISOString(),
      };

      mockExams.push(newExam);

      return c.json({ data: newExam }, 201);
    }
  )
  .get("/:id", async (c) => {
    const id = c.req.param("id");
    const exam = mockExams.find((e) => e.id === id);

    if (!exam) {
      return c.json({ error: "Exam not found" }, 404);
    }

    return c.json({ data: exam });
  })
  .put(
    "/:id",
    zValidator("json", examSchema.omit({ id: true, createdAt: true, updatedAt: true })),
    async (c) => {
      const id = c.req.param("id");
      const values = c.req.valid("json");

      const examIndex = mockExams.findIndex((e) => e.id === id);

      if (examIndex === -1) {
        return c.json({ error: "Exam not found" }, 404);
      }

      mockExams[examIndex] = {
        ...mockExams[examIndex],
        ...values,
        updatedAt: new Date().toISOString(),
      };

      return c.json({ data: mockExams[examIndex] });
    }
  )
  .delete("/:id", async (c) => {
    const id = c.req.param("id");
    const examIndex = mockExams.findIndex((e) => e.id === id);

    if (examIndex === -1) {
      return c.json({ error: "Exam not found" }, 404);
    }

    mockExams.splice(examIndex, 1);

    return c.json({ message: "Exam deleted successfully" });
  })

  // Exam Reports
  .get("/reports/summary", async (c) => {
    const totalExams = mockExams.length;
    const scheduledExams = mockExams.filter(e => e.status === "scheduled").length;
    const ongoingExams = mockExams.filter(e => e.status === "ongoing").length;
    const completedExams = mockExams.filter(e => e.status === "completed").length;
    const cancelledExams = mockExams.filter(e => e.status === "cancelled").length;

    const examTypeStats = mockExams.reduce((acc, exam) => {
      acc[exam.type] = (acc[exam.type] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const gradeStats = mockExams.reduce((acc, exam) => {
      acc[exam.grade] = (acc[exam.grade] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    const subjectStats = mockExams.reduce((acc, exam) => {
      acc[exam.subject] = (acc[exam.subject] || 0) + 1;
      return acc;
    }, {} as Record<string, number>);

    // Upcoming exams (next 7 days)
    const today = new Date();
    const nextWeek = new Date(today.getTime() + 7 * 24 * 60 * 60 * 1000);
    const upcomingExams = mockExams.filter(exam => {
      const examDate = new Date(exam.date);
      return examDate >= today && examDate <= nextWeek && exam.status === "scheduled";
    });

    return c.json({
      data: {
        totalExams,
        scheduledExams,
        ongoingExams,
        completedExams,
        cancelledExams,
        examTypeStats,
        gradeStats,
        subjectStats,
        upcomingExams: upcomingExams.length,
        upcomingExamsList: upcomingExams,
      },
    });
  })

  // Start exam
  .post("/:id/start", async (c) => {
    const id = c.req.param("id");
    const examIndex = mockExams.findIndex((e) => e.id === id);

    if (examIndex === -1) {
      return c.json({ error: "Exam not found" }, 404);
    }

    const exam = mockExams[examIndex];

    if (exam.status !== "scheduled") {
      return c.json({ error: "Exam is not scheduled" }, 400);
    }

    mockExams[examIndex] = {
      ...exam,
      status: "ongoing",
      updatedAt: new Date().toISOString(),
    };

    return c.json({ data: mockExams[examIndex] });
  })

  // Complete exam
  .post("/:id/complete", async (c) => {
    const id = c.req.param("id");
    const examIndex = mockExams.findIndex((e) => e.id === id);

    if (examIndex === -1) {
      return c.json({ error: "Exam not found" }, 404);
    }

    const exam = mockExams[examIndex];

    if (exam.status !== "ongoing") {
      return c.json({ error: "Exam is not ongoing" }, 400);
    }

    mockExams[examIndex] = {
      ...exam,
      status: "completed",
      updatedAt: new Date().toISOString(),
    };

    return c.json({ data: mockExams[examIndex] });
  })

  // Cancel exam
  .post("/:id/cancel", async (c) => {
    const id = c.req.param("id");
    const examIndex = mockExams.findIndex((e) => e.id === id);

    if (examIndex === -1) {
      return c.json({ error: "Exam not found" }, 404);
    }

    const exam = mockExams[examIndex];

    if (exam.status === "completed") {
      return c.json({ error: "Cannot cancel completed exam" }, 400);
    }

    mockExams[examIndex] = {
      ...exam,
      status: "cancelled",
      updatedAt: new Date().toISOString(),
    };

    return c.json({ data: mockExams[examIndex] });
  })

  // Get exam schedule for a specific date
  .get("/schedule/:date", async (c) => {
    const date = c.req.param("date");
    const examsOnDate = mockExams.filter(exam => exam.date === date);

    // Sort by start time
    examsOnDate.sort((a, b) => a.startTime.localeCompare(b.startTime));

    return c.json({ data: examsOnDate });
  })

  // Get exam schedule for a grade
  .get("/grade/:grade", async (c) => {
    const grade = c.req.param("grade");
    const gradeExams = mockExams.filter(exam => exam.grade === grade);

    // Sort by date and time
    gradeExams.sort((a, b) => {
      const dateCompare = a.date.localeCompare(b.date);
      if (dateCompare === 0) {
        return a.startTime.localeCompare(b.startTime);
      }
      return dateCompare;
    });

    return c.json({ data: gradeExams });
  });

export default app;
