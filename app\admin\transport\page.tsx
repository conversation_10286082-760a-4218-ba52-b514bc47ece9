"use client";

import { useEffect } from "react";
import { useRouter } from "next/navigation";

export default function AdminTransportRedirect() {
  const router = useRouter();

  useEffect(() => {
    // Redirect to the main transport management page
    router.replace("/transport");
  }, [router]);

  return (
    <div className="flex items-center justify-center h-64">
      <div className="text-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
        <p className="mt-2 text-gray-600">Redirecting to Transport Management...</p>
      </div>
    </div>
  );
}
