"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { createUserSchema, CreateUser, UserRole } from "@/lib/schemas";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select";
import {
  User,
  Mail,
  Phone,
  Calendar,
  MapPin,
  Shield,
  Key,
  UserPlus,
  Building,
  GraduationCap,
  BookOpen,
  Users,
  CreditCard,
  Car,
  Home
} from "lucide-react";

interface UserFormProps {
  onSubmit: (data: CreateUser) => void;
  isLoading?: boolean;
  initialData?: Partial<CreateUser>;
  mode?: "create" | "edit";
}

const roleOptions = [
  { value: "super_admin", label: "Super Admin", icon: Shield, color: "bg-red-500" },
  { value: "admin", label: "Admin", icon: User, color: "bg-blue-500" },
  { value: "teacher", label: "Teacher", icon: GraduationCap, color: "bg-green-500" },
  { value: "admission_officer", label: "Admission Officer", icon: UserPlus, color: "bg-teal-500" },
  { value: "finance_manager", label: "Finance Manager", icon: CreditCard, color: "bg-yellow-500" },
  { value: "librarian", label: "Librarian", icon: BookOpen, color: "bg-indigo-500" },
  { value: "transport_manager", label: "Transport Manager", icon: Car, color: "bg-gray-500" },
  { value: "hostel_manager", label: "Hostel Manager", icon: Home, color: "bg-pink-500" },
];

const statusOptions = [
  { value: "active", label: "Active", color: "bg-green-100 text-green-800" },
  { value: "inactive", label: "Inactive", color: "bg-red-100 text-red-800" },
];

export function UserForm({ onSubmit, isLoading, initialData, mode = "create" }: UserFormProps) {
  const [selectedRole, setSelectedRole] = useState<UserRole | "">(initialData?.role || "");
  const [generatePassword, setGeneratePassword] = useState(true);

  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<CreateUser>({
    resolver: zodResolver(createUserSchema),
    defaultValues: {
      generatePassword: true,
      status: "active",
      ...initialData,
    },
  });

  const watchedRole = watch("role");
  const watchedGeneratePassword = watch("generatePassword");

  const handleFormSubmit = (data: CreateUser) => {
    onSubmit(data);
  };

  const getRoleSpecificFields = () => {
    if (!selectedRole) return null;

    switch (selectedRole) {
      case "teacher":
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="employeeId">Employee ID</Label>
              <Input
                id="employeeId"
                {...register("employeeId")}
                placeholder="Enter employee ID"
              />
            </div>
            <div>
              <Label htmlFor="department">Department</Label>
              <Input
                id="department"
                {...register("department")}
                placeholder="Enter department"
              />
            </div>
            <div>
              <Label htmlFor="joiningDate">Joining Date</Label>
              <Input
                id="joiningDate"
                type="date"
                {...register("joiningDate")}
              />
            </div>
          </div>
        );

      case "admission_officer":
      case "finance_manager":
      case "librarian":
      case "transport_manager":
      case "hostel_manager":
        return (
          <div className="space-y-4">
            <div>
              <Label htmlFor="employeeId">Employee ID</Label>
              <Input
                id="employeeId"
                {...register("employeeId")}
                placeholder="Enter employee ID"
              />
            </div>
            <div>
              <Label htmlFor="department">Department</Label>
              <Input
                id="department"
                {...register("department")}
                placeholder="Enter department"
              />
            </div>
            <div>
              <Label htmlFor="joiningDate">Joining Date</Label>
              <Input
                id="joiningDate"
                type="date"
                {...register("joiningDate")}
              />
            </div>
          </div>
        );

      default:
        return null;
    }
  };

  return (
    <form onSubmit={handleSubmit(handleFormSubmit)} className="space-y-6">
      {/* Basic Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Basic Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid gap-4 md:grid-cols-2">
            <div>
              <Label htmlFor="firstName">First Name *</Label>
              <Input
                id="firstName"
                {...register("firstName")}
                placeholder="Enter first name"
                className={errors.firstName ? "border-red-500" : ""}
              />
              {errors.firstName && (
                <p className="text-sm text-red-500 mt-1">{errors.firstName.message}</p>
              )}
            </div>
            <div>
              <Label htmlFor="lastName">Last Name *</Label>
              <Input
                id="lastName"
                {...register("lastName")}
                placeholder="Enter last name"
                className={errors.lastName ? "border-red-500" : ""}
              />
              {errors.lastName && (
                <p className="text-sm text-red-500 mt-1">{errors.lastName.message}</p>
              )}
            </div>
          </div>

          <div>
            <Label htmlFor="email">Email Address *</Label>
            <div className="relative">
              <Mail className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                id="email"
                type="email"
                {...register("email")}
                placeholder="Enter email address"
                className={`pl-10 ${errors.email ? "border-red-500" : ""}`}
              />
            </div>
            {errors.email && (
              <p className="text-sm text-red-500 mt-1">{errors.email.message}</p>
            )}
          </div>

          <div>
            <Label htmlFor="phone">Phone Number *</Label>
            <div className="relative">
              <Phone className="absolute left-3 top-3 h-4 w-4 text-gray-400" />
              <Input
                id="phone"
                {...register("phone")}
                placeholder="Enter phone number"
                className={`pl-10 ${errors.phone ? "border-red-500" : ""}`}
              />
            </div>
            {errors.phone && (
              <p className="text-sm text-red-500 mt-1">{errors.phone.message}</p>
            )}
          </div>
        </CardContent>
      </Card>

      {/* Role and Access */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Role and Access
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="role">User Role *</Label>
            <Select
              value={selectedRole}
              onValueChange={(value: UserRole) => {
                setSelectedRole(value);
                setValue("role", value);
              }}
            >
              <SelectTrigger className={errors.role ? "border-red-500" : ""}>
                <SelectValue placeholder="Select user role" />
              </SelectTrigger>
              <SelectContent>
                {roleOptions.map((role) => {
                  const Icon = role.icon;
                  return (
                    <SelectItem key={role.value} value={role.value}>
                      <div className="flex items-center gap-2">
                        <div className={`w-3 h-3 rounded-full ${role.color}`} />
                        <Icon className="h-4 w-4" />
                        {role.label}
                      </div>
                    </SelectItem>
                  );
                })}
              </SelectContent>
            </Select>
            {errors.role && (
              <p className="text-sm text-red-500 mt-1">{errors.role.message}</p>
            )}
          </div>

          <div>
            <Label htmlFor="status">Status</Label>
            <Select
              defaultValue="active"
              onValueChange={(value) => setValue("status", value as "active" | "inactive")}
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {statusOptions.map((status) => (
                  <SelectItem key={status.value} value={status.value}>
                    <Badge className={status.color} variant="secondary">
                      {status.label}
                    </Badge>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Password Settings */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Key className="h-5 w-5" />
            Password Settings
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="generatePassword"
              checked={generatePassword}
              onCheckedChange={(checked) => {
                setGeneratePassword(checked as boolean);
                setValue("generatePassword", checked as boolean);
              }}
            />
            <Label htmlFor="generatePassword">Generate random password</Label>
          </div>

          {!generatePassword && (
            <div>
              <Label htmlFor="password">Password *</Label>
              <Input
                id="password"
                type="password"
                {...register("password")}
                placeholder="Enter password (min 8 characters)"
                className={errors.password ? "border-red-500" : ""}
              />
              {errors.password && (
                <p className="text-sm text-red-500 mt-1">{errors.password.message}</p>
              )}
            </div>
          )}
        </CardContent>
      </Card>

      {/* Role-specific Fields */}
      {selectedRole && getRoleSpecificFields() && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Building className="h-5 w-5" />
              Role-specific Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            {getRoleSpecificFields()}
          </CardContent>
        </Card>
      )}

      {/* Additional Information */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MapPin className="h-5 w-5" />
            Additional Information
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div>
            <Label htmlFor="address">Address</Label>
            <Input
              id="address"
              {...register("address")}
              placeholder="Enter address"
            />
          </div>
          <div>
            <Label htmlFor="emergencyContact">Emergency Contact</Label>
            <Input
              id="emergencyContact"
              {...register("emergencyContact")}
              placeholder="Enter emergency contact number"
            />
          </div>
        </CardContent>
      </Card>

      {/* Submit Button */}
      <div className="flex justify-end gap-4">
        <Button type="submit" disabled={isLoading} className="min-w-32">
          {isLoading ? "Creating..." : mode === "create" ? "Create User" : "Update User"}
        </Button>
      </div>
    </form>
  );
}
