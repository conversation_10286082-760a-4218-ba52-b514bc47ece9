"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Users,
  Search,
  Filter,
  Download,
  Plus,
  Eye,
  Edit,
  Trash2,
  MapPin,
  Bus,
  Clock,
  Phone,
  Mail,
  Home,
  CheckCircle,
  XCircle,
  AlertTriangle,
  User,
  GraduationCap,
  IndianRupee,
  Calendar,
} from "lucide-react";

export default function TransportStudents() {
  const [user, setUser] = useState<any>(null);
  const [selectedTab, setSelectedTab] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedStudent, setSelectedStudent] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "transport_manager") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  const transportStats = {
    totalStudents: 456,
    activeStudents: 398,
    inactiveStudents: 58,
    routeAStudents: 115,
    routeBStudents: 98,
    routeCStudents: 87,
    routeDStudents: 98,
    totalRevenue: 1596000,
  };

  const mockStudents = [
    {
      id: "TS001",
      studentName: "Aarav Sharma",
      studentId: "ST001",
      class: "Class 10-A",
      rollNumber: "10A001",
      parentName: "Rajesh Sharma",
      parentPhone: "+91 98765 43210",
      parentEmail: "<EMAIL>",
      address: "123 Main Street, Karol Bagh, Delhi",
      routeAssigned: "Route A - Central Delhi",
      vehicleNumber: "DL-1CA-1234",
      pickupPoint: "Karol Bagh Metro Station",
      pickupTime: "07:15 AM",
      dropTime: "02:30 PM",
      monthlyFee: 3500,
      feeStatus: "Paid",
      transportStatus: "Active",
      joinDate: "2023-04-15",
      emergencyContact: "+91 98765 43211",
    },
    {
      id: "TS002",
      studentName: "Priya Patel",
      studentId: "ST002",
      class: "Class 12-B",
      rollNumber: "12B015",
      parentName: "Suresh Patel",
      parentPhone: "+91 98765 43212",
      parentEmail: "<EMAIL>",
      address: "456 Park Avenue, Lajpat Nagar, Delhi",
      routeAssigned: "Route B - South Delhi",
      vehicleNumber: "DL-1CB-5678",
      pickupPoint: "Lajpat Nagar Central Market",
      pickupTime: "07:12 AM",
      dropTime: "02:25 PM",
      monthlyFee: 3200,
      feeStatus: "Pending",
      transportStatus: "Active",
      joinDate: "2022-06-20",
      emergencyContact: "+91 98765 43213",
    },
    {
      id: "TS003",
      studentName: "Rahul Kumar",
      studentId: "ST003",
      class: "Class 11-C",
      rollNumber: "11C008",
      parentName: "Amit Kumar",
      parentPhone: "+91 98765 43214",
      parentEmail: "<EMAIL>",
      address: "789 Garden Road, Laxmi Nagar, Delhi",
      routeAssigned: "Route C - East Delhi",
      vehicleNumber: "DL-1CD-3456",
      pickupPoint: "Laxmi Nagar Bus Stand",
      pickupTime: "07:18 AM",
      dropTime: "02:35 PM",
      monthlyFee: 3300,
      feeStatus: "Overdue",
      transportStatus: "Suspended",
      joinDate: "2023-01-10",
      emergencyContact: "+91 98765 43215",
    },
    {
      id: "TS004",
      studentName: "Sneha Reddy",
      studentId: "ST004",
      class: "Class 9-A",
      rollNumber: "09A022",
      parentName: "Vikram Reddy",
      parentPhone: "+91 98765 43216",
      parentEmail: "<EMAIL>",
      address: "321 Hill View, Greater Kailash, Delhi",
      routeAssigned: "Route B - South Delhi",
      vehicleNumber: "DL-1CB-5678",
      pickupPoint: "Greater Kailash M Block Market",
      pickupTime: "07:35 AM",
      dropTime: "02:25 PM",
      monthlyFee: 3200,
      feeStatus: "Paid",
      transportStatus: "Active",
      joinDate: "2023-08-05",
      emergencyContact: "+91 98765 43217",
    },
    {
      id: "TS005",
      studentName: "Vikram Singh",
      studentId: "ST005",
      class: "Class 8-B",
      rollNumber: "08B012",
      parentName: "Ravi Singh",
      parentPhone: "+91 98765 43218",
      parentEmail: "<EMAIL>",
      address: "654 Valley Street, Preet Vihar, Delhi",
      routeAssigned: "Route C - East Delhi",
      vehicleNumber: "DL-1CD-3456",
      pickupPoint: "Preet Vihar Community Center",
      pickupTime: "07:40 AM",
      dropTime: "02:35 PM",
      monthlyFee: 3300,
      feeStatus: "Paid",
      transportStatus: "Active",
      joinDate: "2023-11-12",
      emergencyContact: "+91 98765 43219",
    },
  ];

  const filteredStudents = mockStudents.filter((student) => {
    const matchesSearch = student.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         student.studentId.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         student.class.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         student.parentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         student.routeAssigned.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         student.pickupPoint.toLowerCase().includes(searchTerm.toLowerCase());

    if (selectedTab === "all") return matchesSearch;
    if (selectedTab === "active") return matchesSearch && student.transportStatus === "Active";
    if (selectedTab === "inactive") return matchesSearch && student.transportStatus === "Inactive";
    if (selectedTab === "suspended") return matchesSearch && student.transportStatus === "Suspended";
    if (selectedTab === "route_a") return matchesSearch && student.routeAssigned.includes("Route A");
    if (selectedTab === "route_b") return matchesSearch && student.routeAssigned.includes("Route B");
    if (selectedTab === "route_c") return matchesSearch && student.routeAssigned.includes("Route C");

    return matchesSearch;
  });

  const getTransportStatusBadge = (status: string) => {
    switch (status) {
      case "Active":
        return <Badge className="bg-green-100 text-green-800">Active</Badge>;
      case "Inactive":
        return <Badge className="bg-gray-100 text-gray-800">Inactive</Badge>;
      case "Suspended":
        return <Badge className="bg-red-100 text-red-800">Suspended</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }
  };

  const getFeeStatusBadge = (status: string) => {
    switch (status) {
      case "Paid":
        return <Badge variant="outline" className="text-green-600 border-green-200">Paid</Badge>;
      case "Pending":
        return <Badge variant="outline" className="text-yellow-600 border-yellow-200">Pending</Badge>;
      case "Overdue":
        return <Badge variant="outline" className="text-red-600 border-red-200">Overdue</Badge>;
      default:
        return <Badge variant="outline">{status}</Badge>;
    }
  };

  const viewStudentDetails = (student: any) => {
    setSelectedStudent(student);
  };

  const editStudent = (studentId: string) => {
    alert(`Editing student transport details ${studentId}`);
  };

  const assignRoute = (studentId: string) => {
    alert(`Assigning route to student ${studentId}`);
  };

  const suspendTransport = (studentId: string) => {
    alert(`Suspending transport for student ${studentId}`);
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Transport Students</h1>
            <p className="text-gray-600">Manage student transport assignments and route allocations</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Student List
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Add Student
            </Button>
          </div>
        </div>

        {/* Transport Statistics */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {transportStats.totalStudents}
                  </div>
                  <p className="text-sm text-gray-600">Total Students</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {transportStats.activeStudents}
                  </div>
                  <p className="text-sm text-gray-600">Active</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <XCircle className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-red-600">
                    {transportStats.inactiveStudents}
                  </div>
                  <p className="text-sm text-gray-600">Inactive</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <IndianRupee className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    ₹{(transportStats.totalRevenue / 100000).toFixed(1)}L
                  </div>
                  <p className="text-sm text-gray-600">Monthly Revenue</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filter */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search by student name, ID, class, parent, route, or pickup point..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </div>

            {/* Tabs */}
            <div className="flex flex-wrap gap-2 mt-4">
              {[
                { key: "all", label: "All Students", count: transportStats.totalStudents },
                { key: "active", label: "Active", count: transportStats.activeStudents },
                { key: "inactive", label: "Inactive", count: transportStats.inactiveStudents },
                { key: "route_a", label: "Route A", count: transportStats.routeAStudents },
                { key: "route_b", label: "Route B", count: transportStats.routeBStudents },
                { key: "route_c", label: "Route C", count: transportStats.routeCStudents },
                { key: "suspended", label: "Suspended", count: 5 },
              ].map((tab) => (
                <Button
                  key={tab.key}
                  variant={selectedTab === tab.key ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedTab(tab.key)}
                  className="text-xs"
                >
                  {tab.label} ({tab.count})
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Students Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Users className="h-5 w-5 mr-2" />
              Transport Students ({filteredStudents.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-4 font-medium text-gray-900">Student Details</th>
                    <th className="text-left p-4 font-medium text-gray-900">Parent Contact</th>
                    <th className="text-left p-4 font-medium text-gray-900">Route & Pickup</th>
                    <th className="text-left p-4 font-medium text-gray-900">Timing</th>
                    <th className="text-left p-4 font-medium text-gray-900">Fee & Status</th>
                    <th className="text-left p-4 font-medium text-gray-900">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredStudents.map((student) => (
                    <tr key={student.id} className="border-b hover:bg-gray-50">
                      <td className="p-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                            <User className="h-5 w-5 text-blue-600" />
                          </div>
                          <div>
                            <div className="font-medium text-gray-900">{student.studentName}</div>
                            <div className="text-sm text-gray-500">{student.studentId}</div>
                            <div className="text-sm text-gray-500">{student.class} • {student.rollNumber}</div>
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-1">
                          <div className="font-medium text-gray-900">{student.parentName}</div>
                          <div className="text-sm text-gray-500 flex items-center">
                            <Phone className="h-3 w-3 mr-1" />
                            {student.parentPhone}
                          </div>
                          <div className="text-sm text-gray-500 flex items-center">
                            <Mail className="h-3 w-3 mr-1" />
                            {student.parentEmail}
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-1">
                          <div className="font-medium text-gray-900">{student.routeAssigned}</div>
                          <div className="text-sm text-gray-500">{student.vehicleNumber}</div>
                          <div className="text-sm text-gray-500 flex items-center">
                            <MapPin className="h-3 w-3 mr-1" />
                            {student.pickupPoint}
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-1">
                          <div className="text-sm">
                            <Clock className="h-3 w-3 inline mr-1" />
                            <span className="font-medium">Pickup:</span> {student.pickupTime}
                          </div>
                          <div className="text-sm">
                            <Clock className="h-3 w-3 inline mr-1" />
                            <span className="font-medium">Drop:</span> {student.dropTime}
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-2">
                          <div className="font-medium text-gray-900">
                            ₹{student.monthlyFee.toLocaleString()}/month
                          </div>
                          {getFeeStatusBadge(student.feeStatus)}
                          {getTransportStatusBadge(student.transportStatus)}
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => viewStudentDetails(student)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => editStudent(student.id)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => assignRoute(student.id)}
                          >
                            <Bus className="h-4 w-4" />
                          </Button>
                          {student.transportStatus === "Active" && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => suspendTransport(student.id)}
                            >
                              <XCircle className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}