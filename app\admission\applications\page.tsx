"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Users,
  FileText,
  Search,
  Filter,
  Download,
  Eye,
  Edit,
  CheckCircle,
  XCircle,
  Clock,
  AlertTriangle,
  Calendar,
  Phone,
  Mail,
  MapPin,
  User,
  GraduationCap,
  IndianRupee,
  Star,
} from "lucide-react";

export default function AdmissionApplications() {
  const [user, setUser] = useState<any>(null);
  const [selectedTab, setSelectedTab] = useState("pending");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedApplication, setSelectedApplication] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "admission") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  const applicationStats = {
    totalApplications: 156,
    pendingReview: 45,
    approved: 89,
    rejected: 22,
    interviewScheduled: 28,
  };

  const applications = [
    {
      id: "APP001",
      studentName: "Arjun Sharma",
      fatherName: "Rajesh Sharma",
      motherName: "Priya Sharma",
      dateOfBirth: "2010-05-15",
      gender: "Male",
      appliedClass: "Grade 6",
      appliedProgram: "Regular",
      applicationDate: "2024-01-15",
      status: "Pending Review",
      priority: "High",
      phone: "+91 9876543210",
      email: "<EMAIL>",
      address: "123 MG Road, Mumbai, Maharashtra",
      previousSchool: "ABC Primary School",
      academicScore: 92,
      documents: ["Birth Certificate", "Transfer Certificate", "Academic Records"],
      fees: 125000,
      interviewDate: null,
      remarks: "Excellent academic record",
    },
    {
      id: "APP002",
      studentName: "Sneha Patel",
      fatherName: "Amit Patel",
      motherName: "Kavita Patel",
      dateOfBirth: "2009-08-22",
      gender: "Female",
      appliedClass: "Grade 7",
      appliedProgram: "Regular",
      applicationDate: "2024-01-12",
      status: "Interview Scheduled",
      priority: "Medium",
      phone: "+91 **********",
      email: "<EMAIL>",
      address: "456 Park Street, Delhi, Delhi",
      previousSchool: "XYZ Public School",
      academicScore: 88,
      documents: ["Birth Certificate", "Transfer Certificate", "Academic Records", "Medical Certificate"],
      fees: 125000,
      interviewDate: "2024-01-25",
      remarks: "Good extracurricular activities",
    },
    {
      id: "APP003",
      studentName: "Rahul Kumar",
      fatherName: "Suresh Kumar",
      motherName: "Sunita Kumar",
      dateOfBirth: "2008-12-10",
      gender: "Male",
      appliedClass: "Grade 8",
      appliedProgram: "Science Stream",
      applicationDate: "2024-01-10",
      status: "Approved",
      priority: "High",
      phone: "+91 **********",
      email: "<EMAIL>",
      address: "789 Gandhi Nagar, Bangalore, Karnataka",
      previousSchool: "PQR International School",
      academicScore: 95,
      documents: ["Birth Certificate", "Transfer Certificate", "Academic Records", "Medical Certificate"],
      fees: 135000,
      interviewDate: "2024-01-20",
      remarks: "Outstanding performance in entrance test",
    },
    {
      id: "APP004",
      studentName: "Ananya Singh",
      fatherName: "Vikram Singh",
      motherName: "Meera Singh",
      dateOfBirth: "2011-03-18",
      gender: "Female",
      appliedClass: "Grade 5",
      appliedProgram: "Regular",
      applicationDate: "2024-01-08",
      status: "Rejected",
      priority: "Low",
      phone: "+91 **********",
      email: "<EMAIL>",
      address: "321 Lake View, Chennai, Tamil Nadu",
      previousSchool: "LMN Convent School",
      academicScore: 75,
      documents: ["Birth Certificate", "Academic Records"],
      fees: 115000,
      interviewDate: null,
      remarks: "Incomplete documentation",
    },
    {
      id: "APP005",
      studentName: "Karan Gupta",
      fatherName: "Ashok Gupta",
      motherName: "Ritu Gupta",
      dateOfBirth: "2009-11-05",
      gender: "Male",
      appliedClass: "Grade 7",
      appliedProgram: "Sports Quota",
      applicationDate: "2024-01-05",
      status: "Pending Review",
      priority: "Medium",
      phone: "+91 9876543214",
      email: "<EMAIL>",
      address: "654 Sports Complex Road, Pune, Maharashtra",
      previousSchool: "Sports Academy School",
      academicScore: 82,
      documents: ["Birth Certificate", "Transfer Certificate", "Sports Certificates"],
      fees: 120000,
      interviewDate: null,
      remarks: "State level cricket player",
    },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Pending Review": return "text-yellow-600 bg-yellow-100";
      case "Interview Scheduled": return "text-blue-600 bg-blue-100";
      case "Approved": return "text-green-600 bg-green-100";
      case "Rejected": return "text-red-600 bg-red-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "Pending Review": return <Clock className="h-4 w-4" />;
      case "Interview Scheduled": return <Calendar className="h-4 w-4" />;
      case "Approved": return <CheckCircle className="h-4 w-4" />;
      case "Rejected": return <XCircle className="h-4 w-4" />;
      default: return <AlertTriangle className="h-4 w-4" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case "High": return "text-red-600 bg-red-100";
      case "Medium": return "text-yellow-600 bg-yellow-100";
      case "Low": return "text-green-600 bg-green-100";
      default: return "text-gray-600 bg-gray-100";
    }
  };

  const getTabApplications = () => {
    let filtered = applications;

    switch (selectedTab) {
      case "pending":
        filtered = applications.filter(app => app.status === "Pending Review");
        break;
      case "interview":
        filtered = applications.filter(app => app.status === "Interview Scheduled");
        break;
      case "approved":
        filtered = applications.filter(app => app.status === "Approved");
        break;
      case "rejected":
        filtered = applications.filter(app => app.status === "Rejected");
        break;
      default:
        filtered = applications;
    }

    return filtered.filter(app =>
      app.studentName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      app.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      app.fatherName.toLowerCase().includes(searchTerm.toLowerCase())
    );
  };

  const updateApplicationStatus = (applicationId: string, newStatus: string) => {
    alert(`Application ${applicationId} status updated to: ${newStatus}`);
  };

  const calculateAge = (dateOfBirth: string) => {
    const today = new Date();
    const birth = new Date(dateOfBirth);
    let age = today.getFullYear() - birth.getFullYear();
    const monthDiff = today.getMonth() - birth.getMonth();
    if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birth.getDate())) {
      age--;
    }
    return age;
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Admission Applications</h1>
            <p className="text-gray-600">Manage and review student admission applications</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Data
            </Button>
            <Button>
              <FileText className="h-4 w-4 mr-2" />
              New Application
            </Button>
          </div>
        </div>

        {/* Application Stats */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-5">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {applicationStats.totalApplications}
                  </div>
                  <p className="text-sm text-gray-600">Total Applications</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-yellow-600">
                    {applicationStats.pendingReview}
                  </div>
                  <p className="text-sm text-gray-600">Pending Review</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Calendar className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {applicationStats.interviewScheduled}
                  </div>
                  <p className="text-sm text-gray-600">Interviews</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {applicationStats.approved}
                  </div>
                  <p className="text-sm text-gray-600">Approved</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <XCircle className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-red-600">
                    {applicationStats.rejected}
                  </div>
                  <p className="text-sm text-gray-600">Rejected</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Applications List */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <div className="flex items-center justify-between">
                <div className="flex space-x-1 bg-gray-100 p-1 rounded-lg">
                  {[
                    { key: "all", label: "All", count: applications.length },
                    { key: "pending", label: "Pending", count: applicationStats.pendingReview },
                    { key: "interview", label: "Interview", count: applicationStats.interviewScheduled },
                    { key: "approved", label: "Approved", count: applicationStats.approved },
                    { key: "rejected", label: "Rejected", count: applicationStats.rejected },
                  ].map((tab) => (
                    <button
                      key={tab.key}
                      onClick={() => setSelectedTab(tab.key)}
                      className={`px-3 py-2 text-sm font-medium rounded-md transition-colors ${
                        selectedTab === tab.key
                          ? "bg-white text-blue-600 shadow-sm"
                          : "text-gray-600 hover:text-gray-900"
                      }`}
                    >
                      {tab.label} ({tab.count})
                    </button>
                  ))}
                </div>
                <div className="flex gap-2">
                  <div className="relative">
                    <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                    <Input
                      placeholder="Search applications..."
                      value={searchTerm}
                      onChange={(e) => setSearchTerm(e.target.value)}
                      className="pl-10 w-64"
                    />
                  </div>
                  <Button variant="outline" size="sm">
                    <Filter className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {getTabApplications().map((application) => (
                  <div
                    key={application.id}
                    onClick={() => setSelectedApplication(application)}
                    className={`p-4 border rounded-lg cursor-pointer transition-colors hover:bg-gray-50 ${
                      selectedApplication?.id === application.id ? 'bg-blue-50 border-blue-200' : ''
                    }`}
                  >
                    <div className="flex items-start justify-between mb-3">
                      <div className="flex items-start space-x-4">
                        <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center">
                          <User className="h-6 w-6 text-blue-600" />
                        </div>
                        <div>
                          <h4 className="font-medium text-gray-900">{application.studentName}</h4>
                          <p className="text-sm text-gray-600">ID: {application.id}</p>
                          <p className="text-sm text-gray-600">Applied for: {application.appliedClass}</p>
                        </div>
                      </div>
                      <div className="text-right">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(application.status)}`}>
                          {getStatusIcon(application.status)}
                          <span className="ml-1">{application.status}</span>
                        </span>
                        <div className="text-xs text-gray-500 mt-1">{application.applicationDate}</div>
                      </div>
                    </div>

                    <div className="grid gap-2 md:grid-cols-3 text-sm">
                      <div className="flex items-center text-gray-600">
                        <Phone className="h-3 w-3 mr-1" />
                        {application.phone}
                      </div>
                      <div className="flex items-center text-gray-600">
                        <GraduationCap className="h-3 w-3 mr-1" />
                        Score: {application.academicScore}%
                      </div>
                      <div className="flex items-center text-gray-600">
                        <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getPriorityColor(application.priority)}`}>
                          {application.priority}
                        </span>
                      </div>
                    </div>

                    <div className="flex items-center justify-between mt-3 pt-3 border-t">
                      <span className="text-sm text-gray-600">{application.remarks}</span>
                      <div className="flex gap-2">
                        <Button variant="outline" size="sm">
                          <Eye className="h-3 w-3" />
                        </Button>
                        <Button variant="outline" size="sm">
                          <Edit className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Application Details */}
          <div className="space-y-6">
            {selectedApplication ? (
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center justify-between">
                    <span>Application Details</span>
                    <span className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(selectedApplication.status)}`}>
                      {selectedApplication.status}
                    </span>
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-4">
                    {/* Student Information */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Student Information</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Name:</span>
                          <span className="font-medium">{selectedApplication.studentName}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Age:</span>
                          <span className="font-medium">{calculateAge(selectedApplication.dateOfBirth)} years</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Gender:</span>
                          <span className="font-medium">{selectedApplication.gender}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Applied Class:</span>
                          <span className="font-medium">{selectedApplication.appliedClass}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Program:</span>
                          <span className="font-medium">{selectedApplication.appliedProgram}</span>
                        </div>
                      </div>
                    </div>

                    {/* Parent Information */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Parent Information</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Father:</span>
                          <span className="font-medium">{selectedApplication.fatherName}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Mother:</span>
                          <span className="font-medium">{selectedApplication.motherName}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Phone:</span>
                          <span className="font-medium">{selectedApplication.phone}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Email:</span>
                          <span className="font-medium">{selectedApplication.email}</span>
                        </div>
                      </div>
                    </div>

                    {/* Academic Information */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Academic Information</h4>
                      <div className="space-y-2 text-sm">
                        <div className="flex justify-between">
                          <span className="text-gray-600">Previous School:</span>
                          <span className="font-medium">{selectedApplication.previousSchool}</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Academic Score:</span>
                          <span className="font-medium">{selectedApplication.academicScore}%</span>
                        </div>
                        <div className="flex justify-between">
                          <span className="text-gray-600">Annual Fees:</span>
                          <span className="font-medium">₹{selectedApplication.fees.toLocaleString()}</span>
                        </div>
                      </div>
                    </div>

                    {/* Documents */}
                    <div>
                      <h4 className="font-medium text-gray-900 mb-2">Documents</h4>
                      <div className="space-y-1">
                        {selectedApplication.documents.map((doc: string, index: number) => (
                          <div key={index} className="flex items-center justify-between text-sm">
                            <span className="text-gray-600">{doc}</span>
                            <CheckCircle className="h-4 w-4 text-green-500" />
                          </div>
                        ))}
                      </div>
                    </div>

                    {/* Actions */}
                    <div className="space-y-2">
                      {selectedApplication.status === "Pending Review" && (
                        <>
                          <Button
                            className="w-full"
                            onClick={() => updateApplicationStatus(selectedApplication.id, "Interview Scheduled")}
                          >
                            Schedule Interview
                          </Button>
                          <div className="grid grid-cols-2 gap-2">
                            <Button
                              variant="outline"
                              onClick={() => updateApplicationStatus(selectedApplication.id, "Approved")}
                              className="text-green-600 border-green-300 hover:bg-green-50"
                            >
                              Approve
                            </Button>
                            <Button
                              variant="outline"
                              onClick={() => updateApplicationStatus(selectedApplication.id, "Rejected")}
                              className="text-red-600 border-red-300 hover:bg-red-50"
                            >
                              Reject
                            </Button>
                          </div>
                        </>
                      )}
                      {selectedApplication.status === "Interview Scheduled" && (
                        <div className="grid grid-cols-2 gap-2">
                          <Button
                            onClick={() => updateApplicationStatus(selectedApplication.id, "Approved")}
                            className="text-green-600 bg-green-50 border-green-300 hover:bg-green-100"
                          >
                            Approve
                          </Button>
                          <Button
                            variant="outline"
                            onClick={() => updateApplicationStatus(selectedApplication.id, "Rejected")}
                            className="text-red-600 border-red-300 hover:bg-red-50"
                          >
                            Reject
                          </Button>
                        </div>
                      )}
                    </div>
                  </div>
                </CardContent>
              </Card>
            ) : (
              <Card>
                <CardContent className="p-6 text-center">
                  <FileText className="h-12 w-12 text-gray-400 mx-auto mb-4" />
                  <h3 className="text-lg font-medium text-gray-900 mb-2">Select an Application</h3>
                  <p className="text-gray-600">Choose an application from the list to view details</p>
                </CardContent>
              </Card>
            )}

            {/* Quick Stats */}
            <Card>
              <CardHeader>
                <CardTitle>Quick Stats</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="flex justify-between">
                    <span className="text-gray-600">Today&apos;s Applications:</span>
                    <span className="font-medium">12</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">This Week:</span>
                    <span className="font-medium">45</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Approval Rate:</span>
                    <span className="font-medium">78%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-gray-600">Avg. Processing Time:</span>
                    <span className="font-medium">5 days</span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </AppLayout>
  );
}
