"use client";

import { useEffect, useState } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Bus,
  Route,
  Users,
  Wrench,
  CheckCircle,
  AlertTriangle,
  MapPin,
  Clock,
  Fuel,
  Plus
} from "lucide-react";

export default function TransportDashboard() {
  const [user, setUser] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "transport_manager") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock transport data
  const transportStats = {
    totalVehicles: 12,
    activeVehicles: 10,
    maintenanceVehicles: 2,
    totalRoutes: 8,
    studentsTransported: 456,
    monthlyFuelCost: 85000,
  };

  const vehicleStatus = [
    {
      id: "1",
      vehicleNumber: "SCH-001",
      route: "Route A - City Center",
      driver: "Rajesh Kumar",
      status: "active",
      lastMaintenance: "2024-01-01",
      nextService: "2024-02-01",
      fuelLevel: 85,
    },
    {
      id: "2",
      vehicleNumber: "SCH-002",
      route: "Route B - Residential",
      driver: "Suresh Patel",
      status: "maintenance",
      lastMaintenance: "2024-01-10",
      nextService: "2024-01-20",
      fuelLevel: 45,
    },
    {
      id: "3",
      vehicleNumber: "SCH-003",
      route: "Route C - Industrial",
      driver: "Amit Singh",
      status: "active",
      lastMaintenance: "2023-12-15",
      nextService: "2024-01-25",
      fuelLevel: 92,
    },
  ];

  const todaySchedule = [
    {
      time: "07:00 AM",
      route: "Route A - Morning Pickup",
      vehicle: "SCH-001",
      driver: "Rajesh Kumar",
      students: 45,
      status: "completed",
    },
    {
      time: "07:30 AM",
      route: "Route B - Morning Pickup",
      vehicle: "SCH-002",
      driver: "Suresh Patel",
      students: 32,
      status: "in_progress",
    },
    {
      time: "02:00 PM",
      route: "Route A - Afternoon Drop",
      vehicle: "SCH-001",
      driver: "Rajesh Kumar",
      students: 45,
      status: "scheduled",
    },
  ];

  const maintenanceAlerts = [
    {
      vehicle: "SCH-003",
      type: "Service Due",
      message: "Regular service due in 3 days",
      priority: "medium",
      dueDate: "2024-01-25",
    },
    {
      vehicle: "SCH-004",
      type: "Insurance Renewal",
      message: "Insurance expires in 15 days",
      priority: "high",
      dueDate: "2024-02-05",
    },
    {
      vehicle: "SCH-005",
      type: "Permit Renewal",
      message: "Transport permit renewal required",
      priority: "high",
      dueDate: "2024-01-30",
    },
  ];

  const statusColors = {
    active: "text-green-600 bg-green-100",
    maintenance: "text-yellow-600 bg-yellow-100",
    inactive: "text-red-600 bg-red-100",
    completed: "text-green-600 bg-green-100",
    in_progress: "text-blue-600 bg-blue-100",
    scheduled: "text-purple-600 bg-purple-100",
  };

  const statusIcons = {
    active: CheckCircle,
    maintenance: Wrench,
    inactive: AlertTriangle,
    completed: CheckCircle,
    in_progress: Clock,
    scheduled: Clock,
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Welcome Header */}
        <div className="bg-gradient-to-r from-orange-500 to-red-600 rounded-lg p-6 text-white">
          <h1 className="text-2xl font-bold mb-2">
            Welcome, {user.firstName}!
          </h1>
          <p className="text-orange-100">
            Manage school transportation and fleet operations
          </p>
        </div>

        {/* Transport Stats */}
        <div className="grid gap-4 md:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Bus className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {transportStats.totalVehicles}
                  </div>
                  <p className="text-sm text-gray-600">Total Vehicles</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {transportStats.activeVehicles}
                  </div>
                  <p className="text-sm text-gray-600">Active Vehicles</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Users className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {transportStats.studentsTransported}
                  </div>
                  <p className="text-sm text-gray-600">Students Transported</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Fuel className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    ₹{(transportStats.monthlyFuelCost / 1000).toFixed(0)}K
                  </div>
                  <p className="text-sm text-gray-600">Monthly Fuel Cost</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Main Content Grid */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Vehicle Status */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center">
                  <Bus className="h-5 w-5 mr-2" />
                  Vehicle Status
                </CardTitle>
                <Button variant="outline" size="sm">
                  View All
                </Button>
              </div>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {vehicleStatus.map((vehicle) => {
                  const StatusIcon = statusIcons[vehicle.status as keyof typeof statusIcons];

                  return (
                    <div key={vehicle.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center space-x-4">
                        <div className={`p-2 rounded-full ${statusColors[vehicle.status as keyof typeof statusColors]}`}>
                          <StatusIcon className="h-4 w-4" />
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">{vehicle.vehicleNumber}</div>
                          <div className="text-sm text-gray-500">{vehicle.route}</div>
                          <div className="text-xs text-gray-400">
                            Driver: {vehicle.driver}
                          </div>
                        </div>
                      </div>
                      <div className="text-right">
                        <div className="text-sm">
                          <div className="font-medium">Fuel: {vehicle.fuelLevel}%</div>
                          <div className="text-gray-500">Next Service: {vehicle.nextService}</div>
                        </div>
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* Quick Actions */}
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <Button className="w-full justify-start" variant="outline">
                <Plus className="h-4 w-4 mr-2" />
                Add Vehicle
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <Route className="h-4 w-4 mr-2" />
                Create Route
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <Wrench className="h-4 w-4 mr-2" />
                Schedule Maintenance
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <Fuel className="h-4 w-4 mr-2" />
                Fuel Management
              </Button>
              <Button className="w-full justify-start" variant="outline">
                <MapPin className="h-4 w-4 mr-2" />
                Track Vehicles
              </Button>
            </CardContent>
          </Card>
        </div>

        {/* Today's Schedule and Maintenance Alerts */}
        <div className="grid gap-6 lg:grid-cols-2">
          {/* Today's Schedule */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <Clock className="h-5 w-5 mr-2" />
                Today&apos;s Schedule
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                {todaySchedule.map((schedule, index) => {
                  const StatusIcon = statusIcons[schedule.status as keyof typeof statusIcons];

                  return (
                    <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                      <div className="flex items-center space-x-3">
                        <div className="text-sm font-medium text-gray-600 w-20">
                          {schedule.time}
                        </div>
                        <div>
                          <div className="font-medium text-gray-900">{schedule.route}</div>
                          <div className="text-sm text-gray-500">
                            {schedule.vehicle} • {schedule.driver}
                          </div>
                          <div className="text-xs text-gray-400">
                            {schedule.students} students
                          </div>
                        </div>
                      </div>
                      <div className={`inline-flex items-center px-2 py-1 rounded-full text-xs font-medium ${statusColors[schedule.status as keyof typeof statusColors]}`}>
                        <StatusIcon className="h-3 w-3 mr-1" />
                        {schedule.status.replace('_', ' ').toUpperCase()}
                      </div>
                    </div>
                  );
                })}
              </div>
            </CardContent>
          </Card>

          {/* Maintenance Alerts */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <AlertTriangle className="h-5 w-5 mr-2" />
                Maintenance Alerts
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {maintenanceAlerts.map((alert, index) => (
                  <div key={index} className="flex items-center justify-between p-3 border rounded-lg">
                    <div>
                      <div className="font-medium text-gray-900">{alert.type}</div>
                      <div className="text-sm text-gray-500">{alert.vehicle}</div>
                      <div className="text-xs text-gray-400">{alert.message}</div>
                    </div>
                    <div className="text-right">
                      <div className="text-sm font-medium text-gray-900">
                        {alert.dueDate}
                      </div>
                      <div className={`text-xs px-2 py-1 rounded-full ${
                        alert.priority === "high" ? "bg-red-100 text-red-600" :
                        alert.priority === "medium" ? "bg-yellow-100 text-yellow-600" : "bg-green-100 text-green-600"
                      }`}>
                        {alert.priority.toUpperCase()}
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Fleet Analytics */}
        <Card>
          <CardHeader>
            <CardTitle>Fleet Analytics</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 md:grid-cols-3">
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 mb-2">
                  {Math.round((transportStats.activeVehicles / transportStats.totalVehicles) * 100)}%
                </div>
                <div className="text-sm text-gray-600">Fleet Availability</div>
                <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-green-600 h-2 rounded-full w-[83%]" />
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-blue-600 mb-2">
                  {Math.round(transportStats.studentsTransported / transportStats.totalVehicles)}
                </div>
                <div className="text-sm text-gray-600">Avg Students per Vehicle</div>
                <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-blue-600 h-2 rounded-full w-[76%]" />
                </div>
              </div>
              <div className="text-center">
                <div className="text-3xl font-bold text-orange-600 mb-2">
                  ₹{Math.round(transportStats.monthlyFuelCost / transportStats.totalVehicles / 1000)}K
                </div>
                <div className="text-sm text-gray-600">Avg Fuel Cost per Vehicle</div>
                <div className="mt-2 w-full bg-gray-200 rounded-full h-2">
                  <div className="bg-orange-600 h-2 rounded-full w-[65%]" />
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
