"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Calendar,
  CheckCircle,
  XCircle,
  Clock,
  TrendingUp,
  TrendingDown,
  AlertTriangle,
  BarChart3,
  Download,
  Filter,
  ChevronLeft,
  ChevronRight,
  Target,
  Award,
} from "lucide-react";

export default function StudentAttendance() {
  const [user, setUser] = useState<any>(null);
  const [selectedMonth, setSelectedMonth] = useState(new Date().getMonth());
  const [selectedYear, setSelectedYear] = useState(new Date().getFullYear());
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "student") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  // Mock attendance data
  const attendanceStats = {
    totalDays: 22,
    presentDays: 20,
    absentDays: 2,
    lateArrivals: 1,
    percentage: 90.9,
    monthlyTarget: 95,
    yearlyPercentage: 92.5,
  };

  const monthlyAttendance = [
    { month: "Aug", percentage: 95.2, present: 21, total: 22 },
    { month: "Sep", percentage: 91.3, present: 21, total: 23 },
    { month: "Oct", percentage: 88.0, present: 22, total: 25 },
    { month: "Nov", percentage: 94.1, present: 16, total: 17 },
    { month: "Dec", percentage: 96.4, present: 27, total: 28 },
    { month: "Jan", percentage: 90.9, present: 20, total: 22 },
  ];

  const subjectAttendance = [
    { subject: "Mathematics", present: 18, total: 20, percentage: 90.0, teacher: "Dr. Emily Wilson" },
    { subject: "Physics", present: 19, total: 20, percentage: 95.0, teacher: "Mr. David Brown" },
    { subject: "Chemistry", present: 17, total: 20, percentage: 85.0, teacher: "Dr. Sarah Johnson" },
    { subject: "English", present: 20, total: 20, percentage: 100.0, teacher: "Ms. Lisa Anderson" },
    { subject: "Computer Science", present: 19, total: 20, percentage: 95.0, teacher: "Prof. Michael Chen" },
  ];

  // Mock calendar data for current month
  const calendarData = [
    { date: 1, status: "present", day: "Mon" },
    { date: 2, status: "present", day: "Tue" },
    { date: 3, status: "absent", day: "Wed" },
    { date: 4, status: "present", day: "Thu" },
    { date: 5, status: "present", day: "Fri" },
    { date: 8, status: "present", day: "Mon" },
    { date: 9, status: "present", day: "Tue" },
    { date: 10, status: "late", day: "Wed" },
    { date: 11, status: "present", day: "Thu" },
    { date: 12, status: "present", day: "Fri" },
    { date: 15, status: "present", day: "Mon" },
    { date: 16, status: "present", day: "Tue" },
    { date: 17, status: "present", day: "Wed" },
    { date: 18, status: "absent", day: "Thu" },
    { date: 19, status: "present", day: "Fri" },
    { date: 22, status: "present", day: "Mon" },
    { date: 23, status: "present", day: "Tue" },
    { date: 24, status: "present", day: "Wed" },
    { date: 25, status: "present", day: "Thu" },
    { date: 26, status: "present", day: "Fri" },
  ];

  const getStatusColor = (status: string) => {
    switch (status) {
      case "present": return "bg-green-500 text-white";
      case "absent": return "bg-red-500 text-white";
      case "late": return "bg-yellow-500 text-white";
      case "holiday": return "bg-gray-300 text-gray-600";
      default: return "bg-gray-100 text-gray-400";
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case "present": return <CheckCircle className="h-4 w-4" />;
      case "absent": return <XCircle className="h-4 w-4" />;
      case "late": return <Clock className="h-4 w-4" />;
      default: return <Calendar className="h-4 w-4" />;
    }
  };

  const getPercentageColor = (percentage: number) => {
    if (percentage >= 95) return "text-green-600";
    if (percentage >= 85) return "text-blue-600";
    if (percentage >= 75) return "text-yellow-600";
    return "text-red-600";
  };

  const monthNames = [
    "January", "February", "March", "April", "May", "June",
    "July", "August", "September", "October", "November", "December"
  ];

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">My Attendance</h1>
            <p className="text-gray-600">Track your attendance record and patterns</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Filter className="h-4 w-4 mr-2" />
              Filter
            </Button>
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Download Report
            </Button>
          </div>
        </div>

        {/* Attendance Statistics */}
        <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {attendanceStats.presentDays}
                  </div>
                  <p className="text-sm text-gray-600">Days Present</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <XCircle className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-red-600">
                    {attendanceStats.absentDays}
                  </div>
                  <p className="text-sm text-gray-600">Days Absent</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Target className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {attendanceStats.percentage}%
                  </div>
                  <p className="text-sm text-gray-600">This Month</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Award className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    {attendanceStats.yearlyPercentage}%
                  </div>
                  <p className="text-sm text-gray-600">Yearly Average</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Attendance Target */}
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-lg font-semibold text-gray-900">Monthly Target Progress</h3>
              <div className="text-sm text-gray-500">
                Target: {attendanceStats.monthlyTarget}%
              </div>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-4 mb-2">
              <div 
                className={`h-4 rounded-full ${
                  attendanceStats.percentage >= attendanceStats.monthlyTarget 
                    ? 'bg-green-500' 
                    : attendanceStats.percentage >= 85 
                      ? 'bg-blue-500' 
                      : 'bg-red-500'
                }`}
                style={{ width: `${Math.min((attendanceStats.percentage / attendanceStats.monthlyTarget) * 100, 100)}%` }}
              />
            </div>
            <div className="flex justify-between text-sm">
              <span className="text-gray-600">Current: {attendanceStats.percentage}%</span>
              <span className={`font-medium ${
                attendanceStats.percentage >= attendanceStats.monthlyTarget 
                  ? 'text-green-600' 
                  : 'text-red-600'
              }`}>
                {attendanceStats.percentage >= attendanceStats.monthlyTarget 
                  ? '✓ Target Achieved' 
                  : `${(attendanceStats.monthlyTarget - attendanceStats.percentage).toFixed(1)}% to target`
                }
              </span>
            </div>
          </CardContent>
        </Card>

        {/* Main Content Grid */}
        <div className="grid gap-6 lg:grid-cols-3">
          {/* Attendance Calendar */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <div className="flex items-center justify-between">
                <CardTitle className="flex items-center">
                  <Calendar className="h-5 w-5 mr-2" />
                  Attendance Calendar
                </CardTitle>
                <div className="flex items-center space-x-2">
                  <Button variant="outline" size="sm">
                    <ChevronLeft className="h-4 w-4" />
                  </Button>
                  <span className="text-sm font-medium">
                    {monthNames[selectedMonth]} {selectedYear}
                  </span>
                  <Button variant="outline" size="sm">
                    <ChevronRight className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-7 gap-2 mb-4">
                {["Mon", "Tue", "Wed", "Thu", "Fri", "Sat", "Sun"].map((day) => (
                  <div key={day} className="text-center text-sm font-medium text-gray-500 p-2">
                    {day}
                  </div>
                ))}
              </div>
              
              <div className="grid grid-cols-7 gap-2">
                {Array.from({ length: 35 }, (_, index) => {
                  const dayData = calendarData.find(d => d.date === index + 1);
                  return (
                    <div
                      key={index}
                      className={`aspect-square flex items-center justify-center text-sm rounded-lg border ${
                        dayData 
                          ? getStatusColor(dayData.status)
                          : "bg-gray-50 text-gray-300"
                      }`}
                    >
                      {dayData ? dayData.date : index + 1 <= 31 ? index + 1 : ""}
                    </div>
                  );
                })}
              </div>

              <div className="flex items-center justify-center space-x-6 mt-6 text-sm">
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-green-500 rounded" />
                  <span>Present</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-red-500 rounded" />
                  <span>Absent</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-yellow-500 rounded" />
                  <span>Late</span>
                </div>
                <div className="flex items-center space-x-2">
                  <div className="w-4 h-4 bg-gray-300 rounded" />
                  <span>Holiday</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Subject-wise Attendance */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center">
                <BarChart3 className="h-5 w-5 mr-2" />
                Subject-wise Attendance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {subjectAttendance.map((subject, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <h4 className="font-medium text-gray-900">{subject.subject}</h4>
                      <span className={`text-sm font-bold ${getPercentageColor(subject.percentage)}`}>
                        {subject.percentage}%
                      </span>
                    </div>
                    <div className="w-full bg-gray-200 rounded-full h-2">
                      <div 
                        className={`h-2 rounded-full ${
                          subject.percentage >= 95 ? 'bg-green-500' :
                          subject.percentage >= 85 ? 'bg-blue-500' :
                          subject.percentage >= 75 ? 'bg-yellow-500' : 'bg-red-500'
                        }`}
                        style={{ width: `${subject.percentage}%` }}
                      />
                    </div>
                    <div className="flex justify-between text-xs text-gray-500">
                      <span>{subject.present}/{subject.total} classes</span>
                      <span>{subject.teacher}</span>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Monthly Trends */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <TrendingUp className="h-5 w-5 mr-2" />
              Monthly Attendance Trends
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid gap-6 md:grid-cols-6">
              {monthlyAttendance.map((month, index) => (
                <div key={index} className="text-center">
                  <div className="text-sm font-medium text-gray-900 mb-2">{month.month}</div>
                  <div className="w-full bg-gray-200 rounded h-24 flex flex-col justify-end mb-2">
                    <div 
                      className={`rounded-b ${
                        month.percentage >= 95 ? 'bg-green-500' :
                        month.percentage >= 85 ? 'bg-blue-500' :
                        month.percentage >= 75 ? 'bg-yellow-500' : 'bg-red-500'
                      }`}
                      style={{ height: `${month.percentage}%` }}
                    />
                  </div>
                  <div className={`text-sm font-bold ${getPercentageColor(month.percentage)}`}>
                    {month.percentage}%
                  </div>
                  <div className="text-xs text-gray-500">
                    {month.present}/{month.total}
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
