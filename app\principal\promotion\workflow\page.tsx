"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import {
  ArrowLeft,
  CheckCircle,
  Clock,
  AlertTriangle,
  Users,
  GraduationCap,
  Calendar,
  FileText,
  Settings,
  ArrowRight,
  Play,
  Pause,
  RotateCcw
} from "lucide-react";
import { toast } from "sonner";

interface WorkflowStep {
  id: string;
  title: string;
  description: string;
  status: "pending" | "in_progress" | "completed" | "failed";
  progress: number;
  estimatedTime: string;
  dependencies?: string[];
}

export default function PromotionWorkflowPage() {
  const [user, setUser] = useState<any>(null);
  const router = useRouter();
  const [loading, setLoading] = useState(true);
  const [workflowSteps, setWorkflowSteps] = useState<WorkflowStep[]>([]);
  const [currentStep, setCurrentStep] = useState(0);
  const [workflowStatus, setWorkflowStatus] = useState<"not_started" | "running" | "paused" | "completed">("not_started");

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "principal") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
    loadWorkflowData();
  }, [router]);

  const loadWorkflowData = async () => {
    try {
      setLoading(true);
      
      // Mock workflow steps
      const steps: WorkflowStep[] = [
        {
          id: "1",
          title: "Validate Academic Records",
          description: "Verify all student marks, attendance, and academic records are complete",
          status: "completed",
          progress: 100,
          estimatedTime: "15 minutes"
        },
        {
          id: "2",
          title: "Apply Promotion Criteria",
          description: "Check students against minimum percentage, attendance, and other criteria",
          status: "completed",
          progress: 100,
          estimatedTime: "10 minutes",
          dependencies: ["1"]
        },
        {
          id: "3",
          title: "Generate Promotion Lists",
          description: "Create lists of promoted, detained, and failed students",
          status: "in_progress",
          progress: 65,
          estimatedTime: "20 minutes",
          dependencies: ["2"]
        },
        {
          id: "4",
          title: "Review Special Cases",
          description: "Manual review of borderline cases and special circumstances",
          status: "pending",
          progress: 0,
          estimatedTime: "30 minutes",
          dependencies: ["3"]
        },
        {
          id: "5",
          title: "Create New Academic Session",
          description: "Set up new academic year structure and class assignments",
          status: "pending",
          progress: 0,
          estimatedTime: "25 minutes",
          dependencies: ["4"]
        },
        {
          id: "6",
          title: "Assign Students to New Classes",
          description: "Move promoted students to their new classes and sections",
          status: "pending",
          progress: 0,
          estimatedTime: "40 minutes",
          dependencies: ["5"]
        },
        {
          id: "7",
          title: "Update Student Records",
          description: "Update all student academic records with new class information",
          status: "pending",
          progress: 0,
          estimatedTime: "20 minutes",
          dependencies: ["6"]
        },
        {
          id: "8",
          title: "Generate Reports",
          description: "Create promotion reports, certificates, and notifications",
          status: "pending",
          progress: 0,
          estimatedTime: "15 minutes",
          dependencies: ["7"]
        },
        {
          id: "9",
          title: "Notify Stakeholders",
          description: "Send notifications to students, parents, and teachers",
          status: "pending",
          progress: 0,
          estimatedTime: "10 minutes",
          dependencies: ["8"]
        }
      ];

      setWorkflowSteps(steps);
      setCurrentStep(2); // Currently on step 3 (0-indexed)
      setWorkflowStatus("running");
      
      toast.success("Workflow data loaded successfully");
    } catch (error) {
      console.error("Error loading workflow data:", error);
      toast.error("Failed to load workflow data");
    } finally {
      setLoading(false);
    }
  };

  const handleStartWorkflow = () => {
    setWorkflowStatus("running");
    toast.success("Promotion workflow started!");
  };

  const handlePauseWorkflow = () => {
    setWorkflowStatus("paused");
    toast.info("Promotion workflow paused");
  };

  const handleResumeWorkflow = () => {
    setWorkflowStatus("running");
    toast.success("Promotion workflow resumed");
  };

  const getStepIcon = (step: WorkflowStep) => {
    switch (step.status) {
      case "completed":
        return <CheckCircle className="h-5 w-5 text-green-600" />;
      case "in_progress":
        return <Clock className="h-5 w-5 text-blue-600" />;
      case "failed":
        return <AlertTriangle className="h-5 w-5 text-red-600" />;
      default:
        return <Clock className="h-5 w-5 text-gray-400" />;
    }
  };

  const getStepColor = (step: WorkflowStep) => {
    switch (step.status) {
      case "completed":
        return "border-green-200 bg-green-50";
      case "in_progress":
        return "border-blue-200 bg-blue-50";
      case "failed":
        return "border-red-200 bg-red-50";
      default:
        return "border-gray-200 bg-gray-50";
    }
  };

  const overallProgress = Math.round(
    (workflowSteps.reduce((acc, step) => acc + step.progress, 0) / (workflowSteps.length * 100)) * 100
  );

  if (!user) {
    return (
      <AppLayout>
        <div className="flex items-center justify-center h-64">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        </div>
      </AppLayout>
    );
  }

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div className="flex items-center gap-4">
            <Button
              variant="outline"
              size="sm"
              onClick={() => router.push("/principal/promotion")}
            >
              <ArrowLeft className="h-4 w-4 mr-2" />
              Back to Promotion
            </Button>
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Promotion Workflow</h1>
              <p className="text-gray-600">
                Step-by-step automated student promotion process
              </p>
            </div>
          </div>
          <div className="flex gap-2">
            {workflowStatus === "not_started" && (
              <Button onClick={handleStartWorkflow}>
                <Play className="h-4 w-4 mr-2" />
                Start Workflow
              </Button>
            )}
            {workflowStatus === "running" && (
              <Button variant="outline" onClick={handlePauseWorkflow}>
                <Pause className="h-4 w-4 mr-2" />
                Pause
              </Button>
            )}
            {workflowStatus === "paused" && (
              <Button onClick={handleResumeWorkflow}>
                <Play className="h-4 w-4 mr-2" />
                Resume
              </Button>
            )}
            <Button variant="outline">
              <RotateCcw className="h-4 w-4 mr-2" />
              Reset
            </Button>
          </div>
        </div>

        {/* Workflow Overview */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center justify-between">
              <span>Workflow Progress</span>
              <Badge variant={
                workflowStatus === "completed" ? "default" :
                workflowStatus === "running" ? "secondary" :
                workflowStatus === "paused" ? "outline" : "secondary"
              }>
                {workflowStatus.replace("_", " ").toUpperCase()}
              </Badge>
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <div className="flex justify-between text-sm mb-2">
                  <span>Overall Progress</span>
                  <span>{overallProgress}%</span>
                </div>
                <Progress value={overallProgress} className="h-3" />
              </div>
              
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                <div>
                  <span className="text-gray-600">Total Steps:</span>
                  <div className="font-medium">{workflowSteps.length}</div>
                </div>
                <div>
                  <span className="text-gray-600">Completed:</span>
                  <div className="font-medium text-green-600">
                    {workflowSteps.filter(s => s.status === "completed").length}
                  </div>
                </div>
                <div>
                  <span className="text-gray-600">In Progress:</span>
                  <div className="font-medium text-blue-600">
                    {workflowSteps.filter(s => s.status === "in_progress").length}
                  </div>
                </div>
                <div>
                  <span className="text-gray-600">Remaining:</span>
                  <div className="font-medium text-gray-600">
                    {workflowSteps.filter(s => s.status === "pending").length}
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Workflow Steps */}
        <div className="space-y-4">
          {workflowSteps.map((step, index) => (
            <Card key={step.id} className={`${getStepColor(step)} transition-all duration-200`}>
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex items-start space-x-4 flex-1">
                    <div className="flex items-center justify-center w-8 h-8 rounded-full bg-white border-2 border-current">
                      <span className="text-sm font-medium">{index + 1}</span>
                    </div>
                    <div className="flex-1">
                      <div className="flex items-center gap-3 mb-2">
                        {getStepIcon(step)}
                        <h3 className="font-semibold text-gray-900">{step.title}</h3>
                        <Badge variant="outline" className="text-xs">
                          {step.estimatedTime}
                        </Badge>
                      </div>
                      <p className="text-gray-600 mb-3">{step.description}</p>
                      
                      {step.status === "in_progress" && (
                        <div className="mb-3">
                          <div className="flex justify-between text-sm mb-1">
                            <span>Progress</span>
                            <span>{step.progress}%</span>
                          </div>
                          <Progress value={step.progress} className="h-2" />
                        </div>
                      )}
                      
                      {step.dependencies && (
                        <div className="text-xs text-gray-500">
                          <span>Dependencies: Step {step.dependencies.join(", Step ")}</span>
                        </div>
                      )}
                    </div>
                  </div>
                  
                  <div className="flex items-center space-x-2">
                    {step.status === "in_progress" && (
                      <div className="animate-pulse">
                        <div className="w-2 h-2 bg-blue-600 rounded-full"></div>
                      </div>
                    )}
                    {step.status === "pending" && index === currentStep + 1 && (
                      <Button size="sm" variant="outline">
                        Start Step
                      </Button>
                    )}
                    {step.status === "completed" && (
                      <Button size="sm" variant="outline">
                        <FileText className="h-4 w-4 mr-2" />
                        View Details
                      </Button>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Workflow Instructions */}
        <Card>
          <CardHeader>
            <CardTitle>Workflow Instructions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4 text-sm">
              <div className="bg-blue-50 p-4 rounded-lg">
                <h4 className="font-medium text-blue-900 mb-2">🔄 Automated Process</h4>
                <p className="text-blue-800">
                  This workflow runs automatically once started. Most steps require no manual intervention.
                  You&apos;ll only need to review special cases in Step 4.
                </p>
              </div>
              
              <div className="bg-yellow-50 p-4 rounded-lg">
                <h4 className="font-medium text-yellow-900 mb-2">⚠️ Important Notes</h4>
                <ul className="text-yellow-800 space-y-1">
                  <li>• Ensure all student marks and attendance are finalized before starting</li>
                  <li>• The process cannot be reversed once students are moved to new classes</li>
                  <li>• Manual review in Step 4 is crucial for borderline cases</li>
                  <li>• Backup of current data is recommended before starting</li>
                </ul>
              </div>
              
              <div className="bg-green-50 p-4 rounded-lg">
                <h4 className="font-medium text-green-900 mb-2">✅ After Completion</h4>
                <p className="text-green-800">
                  Once completed, all students will be automatically assigned to their new classes,
                  reports will be generated, and notifications sent to parents and teachers.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}
