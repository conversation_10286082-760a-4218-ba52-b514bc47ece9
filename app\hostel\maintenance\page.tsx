"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { AppLayout } from "@/components/layout/app-layout";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
  Wrench,
  Search,
  Filter,
  Download,
  Plus,
  Eye,
  Edit,
  Calendar,
  CheckCircle,
  XCircle,
  AlertTriangle,
  Clock,
  IndianRupee,
  Building,
  User,
  FileText,
  Settings,
  TrendingUp,
  Home,
  Zap,
  Droplets,
  Wifi,
} from "lucide-react";

export default function HostelMaintenance() {
  const [user, setUser] = useState<any>(null);
  const [selectedTab, setSelectedTab] = useState("all");
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedRequest, setSelectedRequest] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    const userData = localStorage.getItem("user");
    if (!userData) {
      router.push("/login");
      return;
    }

    const parsedUser = JSON.parse(userData);
    if (parsedUser.role !== "hostel_manager") {
      router.push("/login");
      return;
    }

    setUser(parsedUser);
  }, [router]);

  if (!user) {
    return <div>Loading...</div>;
  }

  const maintenanceStats = {
    totalRequests: 67,
    pendingRequests: 23,
    inProgressRequests: 15,
    completedRequests: 24,
    urgentRequests: 8,
    totalCost: 85000,
    avgResolutionTime: 3.2,
  };

  const mockRequests = [
    {
      id: "MR001",
      requestType: "Electrical",
      description: "AC not working in room A-101",
      roomNumber: "A-101",
      hostelBlock: "Block A",
      reportedBy: "Aarav Sharma",
      studentId: "ST001",
      reportedDate: "2024-01-20",
      assignedTo: "Rajesh Kumar",
      status: "In Progress",
      priority: "High",
      estimatedCost: 5000,
      actualCost: 0,
      completedDate: null,
      notes: "AC compressor needs replacement",
      category: "Room Maintenance",
    },
    {
      id: "MR002",
      requestType: "Plumbing",
      description: "Water leakage in bathroom",
      roomNumber: "B-201",
      hostelBlock: "Block B",
      reportedBy: "Priya Patel",
      studentId: "ST002",
      reportedDate: "2024-01-18",
      assignedTo: "Suresh Singh",
      status: "Completed",
      priority: "Medium",
      estimatedCost: 2500,
      actualCost: 2200,
      completedDate: "2024-01-22",
      notes: "Pipe joint replaced, issue resolved",
      category: "Room Maintenance",
    },
    {
      id: "MR003",
      requestType: "Furniture",
      description: "Study table drawer broken",
      roomNumber: "C-301",
      hostelBlock: "Block C",
      reportedBy: "Sneha Reddy",
      studentId: "ST004",
      reportedDate: "2024-01-22",
      assignedTo: "Unassigned",
      status: "Pending",
      priority: "Low",
      estimatedCost: 800,
      actualCost: 0,
      completedDate: null,
      notes: "Waiting for carpenter availability",
      category: "Room Maintenance",
    },
    {
      id: "MR004",
      requestType: "Internet",
      description: "WiFi connectivity issues in entire floor",
      roomNumber: "B-2nd Floor",
      hostelBlock: "Block B",
      reportedBy: "Multiple Students",
      studentId: "N/A",
      reportedDate: "2024-01-21",
      assignedTo: "Tech Support Team",
      status: "In Progress",
      priority: "High",
      estimatedCost: 3000,
      actualCost: 0,
      completedDate: null,
      notes: "Router replacement in progress",
      category: "Common Area",
    },
    {
      id: "MR005",
      requestType: "Cleaning",
      description: "Deep cleaning required in common washroom",
      roomNumber: "Common Area",
      hostelBlock: "Block A",
      reportedBy: "Hostel Warden",
      studentId: "N/A",
      reportedDate: "2024-01-19",
      assignedTo: "Cleaning Staff",
      status: "Completed",
      priority: "Medium",
      estimatedCost: 1500,
      actualCost: 1500,
      completedDate: "2024-01-20",
      notes: "Deep cleaning completed, sanitization done",
      category: "Common Area",
    },
  ];

  const filteredRequests = mockRequests.filter((request) => {
    const matchesSearch = request.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         request.roomNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         request.hostelBlock.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         request.reportedBy.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         request.requestType.toLowerCase().includes(searchTerm.toLowerCase());

    if (selectedTab === "all") return matchesSearch;
    if (selectedTab === "pending") return matchesSearch && request.status === "Pending";
    if (selectedTab === "in_progress") return matchesSearch && request.status === "In Progress";
    if (selectedTab === "completed") return matchesSearch && request.status === "Completed";
    if (selectedTab === "urgent") return matchesSearch && request.priority === "High";

    return matchesSearch;
  });

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Pending":
        return <Badge className="bg-yellow-100 text-yellow-800">Pending</Badge>;
      case "In Progress":
        return <Badge className="bg-blue-100 text-blue-800">In Progress</Badge>;
      case "Completed":
        return <Badge className="bg-green-100 text-green-800">Completed</Badge>;
      case "Cancelled":
        return <Badge className="bg-red-100 text-red-800">Cancelled</Badge>;
      default:
        return <Badge className="bg-gray-100 text-gray-800">{status}</Badge>;
    }
  };

  const getPriorityBadge = (priority: string) => {
    switch (priority) {
      case "High":
        return <Badge variant="outline" className="text-red-600 border-red-200">High</Badge>;
      case "Medium":
        return <Badge variant="outline" className="text-yellow-600 border-yellow-200">Medium</Badge>;
      case "Low":
        return <Badge variant="outline" className="text-green-600 border-green-200">Low</Badge>;
      default:
        return <Badge variant="outline">{priority}</Badge>;
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case "Electrical":
        return <Zap className="h-4 w-4 text-yellow-600" />;
      case "Plumbing":
        return <Droplets className="h-4 w-4 text-blue-600" />;
      case "Internet":
        return <Wifi className="h-4 w-4 text-purple-600" />;
      case "Furniture":
        return <Home className="h-4 w-4 text-brown-600" />;
      case "Cleaning":
        return <Settings className="h-4 w-4 text-green-600" />;
      default:
        return <Wrench className="h-4 w-4 text-gray-600" />;
    }
  };

  const viewRequestDetails = (request: any) => {
    setSelectedRequest(request);
  };

  const editRequest = (requestId: string) => {
    alert(`Editing maintenance request ${requestId}`);
  };

  const assignTechnician = (requestId: string) => {
    alert(`Assigning technician to request ${requestId}`);
  };

  const markCompleted = (requestId: string) => {
    alert(`Marking request ${requestId} as completed`);
  };

  return (
    <AppLayout>
      <div className="space-y-6">
        {/* Page Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold text-gray-900">Hostel Maintenance</h1>
            <p className="text-gray-600">Track and manage hostel maintenance requests and repairs</p>
          </div>
          <div className="flex gap-2">
            <Button variant="outline">
              <Download className="h-4 w-4 mr-2" />
              Export Report
            </Button>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              New Request
            </Button>
          </div>
        </div>

        {/* Maintenance Statistics */}
        <div className="grid gap-4 md:grid-cols-4 lg:grid-cols-7">
          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <FileText className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {maintenanceStats.totalRequests}
                  </div>
                  <p className="text-sm text-gray-600">Total Requests</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Clock className="h-8 w-8 text-yellow-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-yellow-600">
                    {maintenanceStats.pendingRequests}
                  </div>
                  <p className="text-sm text-gray-600">Pending</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <Settings className="h-8 w-8 text-blue-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-blue-600">
                    {maintenanceStats.inProgressRequests}
                  </div>
                  <p className="text-sm text-gray-600">In Progress</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <CheckCircle className="h-8 w-8 text-green-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-green-600">
                    {maintenanceStats.completedRequests}
                  </div>
                  <p className="text-sm text-gray-600">Completed</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <AlertTriangle className="h-8 w-8 text-red-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-red-600">
                    {maintenanceStats.urgentRequests}
                  </div>
                  <p className="text-sm text-gray-600">Urgent</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <IndianRupee className="h-8 w-8 text-purple-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-purple-600">
                    ₹{(maintenanceStats.totalCost / 1000).toFixed(0)}K
                  </div>
                  <p className="text-sm text-gray-600">Total Cost</p>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardContent className="p-6">
              <div className="flex items-center">
                <TrendingUp className="h-8 w-8 text-orange-600" />
                <div className="ml-4">
                  <div className="text-2xl font-bold text-orange-600">
                    {maintenanceStats.avgResolutionTime}
                  </div>
                  <p className="text-sm text-gray-600">Avg Days</p>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Search and Filter */}
        <Card>
          <CardContent className="p-6">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 h-4 w-4" />
                  <Input
                    placeholder="Search by description, room, block, reporter, or type..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-10"
                  />
                </div>
              </div>
              <Button variant="outline">
                <Filter className="h-4 w-4 mr-2" />
                Filter
              </Button>
            </div>

            {/* Tabs */}
            <div className="flex flex-wrap gap-2 mt-4">
              {[
                { key: "all", label: "All Requests", count: maintenanceStats.totalRequests },
                { key: "pending", label: "Pending", count: maintenanceStats.pendingRequests },
                { key: "in_progress", label: "In Progress", count: maintenanceStats.inProgressRequests },
                { key: "completed", label: "Completed", count: maintenanceStats.completedRequests },
                { key: "urgent", label: "Urgent", count: maintenanceStats.urgentRequests },
              ].map((tab) => (
                <Button
                  key={tab.key}
                  variant={selectedTab === tab.key ? "default" : "outline"}
                  size="sm"
                  onClick={() => setSelectedTab(tab.key)}
                  className="text-xs"
                >
                  {tab.label} ({tab.count})
                </Button>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Maintenance Requests Table */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center">
              <Wrench className="h-5 w-5 mr-2" />
              Maintenance Requests ({filteredRequests.length})
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="overflow-x-auto">
              <table className="w-full">
                <thead>
                  <tr className="border-b">
                    <th className="text-left p-4 font-medium text-gray-900">Request Details</th>
                    <th className="text-left p-4 font-medium text-gray-900">Location</th>
                    <th className="text-left p-4 font-medium text-gray-900">Reporter</th>
                    <th className="text-left p-4 font-medium text-gray-900">Status & Priority</th>
                    <th className="text-left p-4 font-medium text-gray-900">Cost & Timeline</th>
                    <th className="text-left p-4 font-medium text-gray-900">Actions</th>
                  </tr>
                </thead>
                <tbody>
                  {filteredRequests.map((request) => (
                    <tr key={request.id} className="border-b hover:bg-gray-50">
                      <td className="p-4">
                        <div className="flex items-center space-x-3">
                          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
                            {getTypeIcon(request.requestType)}
                          </div>
                          <div>
                            <div className="font-medium text-gray-900">{request.requestType}</div>
                            <div className="text-sm text-gray-500">{request.description}</div>
                            <div className="text-xs text-gray-400">ID: {request.id}</div>
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-1">
                          <div className="font-medium text-gray-900">{request.roomNumber}</div>
                          <div className="text-sm text-gray-500">{request.hostelBlock}</div>
                          <div className="text-xs text-gray-400">{request.category}</div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-1">
                          <div className="font-medium text-gray-900">{request.reportedBy}</div>
                          {request.studentId !== "N/A" && (
                            <div className="text-sm text-gray-500">{request.studentId}</div>
                          )}
                          <div className="text-sm text-gray-500">
                            {new Date(request.reportedDate).toLocaleDateString()}
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-2">
                          {getStatusBadge(request.status)}
                          {getPriorityBadge(request.priority)}
                          <div className="text-xs text-gray-500">
                            Assigned: {request.assignedTo}
                          </div>
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="space-y-1">
                          <div className="text-sm">
                            <span className="font-medium">Est:</span> ₹{request.estimatedCost.toLocaleString()}
                          </div>
                          {request.actualCost > 0 && (
                            <div className="text-sm">
                              <span className="font-medium">Actual:</span> ₹{request.actualCost.toLocaleString()}
                            </div>
                          )}
                          {request.completedDate && (
                            <div className="text-sm text-green-600">
                              Completed: {new Date(request.completedDate).toLocaleDateString()}
                            </div>
                          )}
                        </div>
                      </td>
                      <td className="p-4">
                        <div className="flex space-x-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => viewRequestDetails(request)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => editRequest(request.id)}
                          >
                            <Edit className="h-4 w-4" />
                          </Button>
                          {request.assignedTo === "Unassigned" && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => assignTechnician(request.id)}
                            >
                              <User className="h-4 w-4" />
                            </Button>
                          )}
                          {request.status === "In Progress" && (
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => markCompleted(request.id)}
                            >
                              <CheckCircle className="h-4 w-4" />
                            </Button>
                          )}
                        </div>
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </CardContent>
        </Card>
      </div>
    </AppLayout>
  );
}